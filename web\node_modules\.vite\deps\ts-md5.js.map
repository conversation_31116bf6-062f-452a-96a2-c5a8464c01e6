{"version": 3, "sources": ["../../ts-md5/src/md5.ts", "../../ts-md5/src/md5_file_hasher.ts", "../../ts-md5/src/parallel_hasher.ts"], "sourcesContent": ["/*\n\nTypeScript Md5\n==============\n\nBased on work by\n* <PERSON>: http://www.myersdaily.org/joseph/javascript/md5-text.html\n* <PERSON>: https://github.com/satazor/SparkMD5\n* <PERSON>: https://github.com/gorhill/yamd5.js\n\nEffectively a TypeScrypt re-write of Raymond Hill JS Library\n\nThe MIT License (MIT)\n\nCopyright (C) 2014 Raymond Hill\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n\n\n\n            DO WHAT YOU WANT TO PUBLIC LICENSE\n                    Version 2, December 2004\n\n Copyright (C) 2015 André Cruz <<EMAIL>>\n\n Everyone is permitted to copy and distribute verbatim or modified\n copies of this license document, and changing it is allowed as long\n as the name is changed.\n\n            DO WHAT YOU WANT TO PUBLIC LICENSE\n   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION\n\n  0. You just DO WHAT YOU WANT TO.\n\n\n*/\n\n\ninterface HasherState {\n    buffer: string;\n    buflen: number;\n    length: number;\n    state: number[];\n};\n\nexport class Md5 {\n\n    /**\n     * Hash a UTF-8 string on the spot\n     * @param str String to hash\n     * @param raw Whether to return the value as an `Int32Array`\n     */\n    public static hashStr(str: string, raw?: false): string;\n    public static hashStr(str: string, raw: true): Int32Array;\n    public static hashStr(str: string, raw: boolean = false) {\n        return this.onePassHasher\n            .start()\n            .appendStr(str)\n            .end(raw);\n    }\n\n    /**\n     * Hash a ASCII string on the spot\n     * @param str String to hash\n     * @param raw Whether to return the value as an `Int32Array`\n     */\n    public static hashAsciiStr(str: string, raw?: false): string;\n    public static hashAsciiStr(str: string, raw: true): Int32Array;\n    public static hashAsciiStr(str: string, raw: boolean = false) {\n        return this.onePassHasher\n            .start()\n            .appendAsciiStr(str)\n            .end(raw);\n    }\n        // Private Static Variables\n    private static stateIdentity = new Int32Array([1732584193, -271733879, -1732584194, 271733878]);\n    private static buffer32Identity = new Int32Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\n    private static hexChars = '0123456789abcdef';\n    private static hexOut: string[] = [];\n\n    // Permanent instance is to use for one-call hashing\n    private static onePassHasher = new Md5();\n\n    private static _hex(x: Int32Array): string {\n        const hc = Md5.hexChars;\n        const ho = Md5.hexOut;\n        let n;\n        let offset;\n        let j;\n        let i;\n\n        for (i = 0; i < 4; i += 1) {\n            offset = i * 8;\n            n = x[i];\n            for (j = 0; j < 8; j += 2) {\n                ho[offset + 1 + j] = hc.charAt(n & 0x0F);\n                n >>>= 4;\n                ho[offset + 0 + j] = hc.charAt(n & 0x0F);\n                n >>>= 4;\n            }\n        }\n        return ho.join('');\n    }\n\n    private static _md5cycle(x: Int32Array|Uint32Array, k: Int32Array|Uint32Array) {\n        let a = x[0];\n        let b = x[1];\n        let c = x[2];\n        let d = x[3];\n        // ff()\n        a += (b & c | ~b & d) + k[0] - 680876936 | 0;\n        a = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[1] - 389564586 | 0;\n        d = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[2] + 606105819 | 0;\n        c = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[3] - 1044525330 | 0;\n        b = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[4] - 176418897 | 0;\n        a = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[5] + 1200080426 | 0;\n        d = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[6] - 1473231341 | 0;\n        c = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[7] - 45705983 | 0;\n        b = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[8] + 1770035416 | 0;\n        a = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[9] - 1958414417 | 0;\n        d = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[10] - 42063 | 0;\n        c = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[11] - 1990404162 | 0;\n        b = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[12] + 1804603682 | 0;\n        a = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[13] - 40341101 | 0;\n        d = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[14] - 1502002290 | 0;\n        c = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[15] + 1236535329 | 0;\n        b = (b << 22 | b >>> 10) + c | 0;\n        // gg()\n        a += (b & d | c & ~d) + k[1] - 165796510 | 0;\n        a = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[6] - 1069501632 | 0;\n        d = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[11] + 643717713 | 0;\n        c = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[0] - 373897302 | 0;\n        b = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[5] - 701558691 | 0;\n        a = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[10] + 38016083 | 0;\n        d = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[15] - 660478335 | 0;\n        c = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[4] - 405537848 | 0;\n        b = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[9] + 568446438 | 0;\n        a = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[14] - 1019803690 | 0;\n        d = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[3] - 187363961 | 0;\n        c = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[8] + 1163531501 | 0;\n        b = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[13] - 1444681467 | 0;\n        a = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[2] - 51403784 | 0;\n        d = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[7] + 1735328473 | 0;\n        c = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[12] - 1926607734 | 0;\n        b = (b << 20 | b >>> 12) + c | 0;\n        // hh()\n        a += (b ^ c ^ d) + k[5] - 378558 | 0;\n        a = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[8] - 2022574463 | 0;\n        d = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[11] + 1839030562 | 0;\n        c = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[14] - 35309556 | 0;\n        b = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[1] - 1530992060 | 0;\n        a = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[4] + 1272893353 | 0;\n        d = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[7] - 155497632 | 0;\n        c = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[10] - 1094730640 | 0;\n        b = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[13] + 681279174 | 0;\n        a = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[0] - 358537222 | 0;\n        d = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[3] - 722521979 | 0;\n        c = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[6] + 76029189 | 0;\n        b = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[9] - 640364487 | 0;\n        a = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[12] - 421815835 | 0;\n        d = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[15] + 530742520 | 0;\n        c = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[2] - 995338651 | 0;\n        b = (b << 23 | b >>> 9) + c | 0;\n        // ii()\n        a += (c ^ (b | ~d)) + k[0] - 198630844 | 0;\n        a = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[7] + 1126891415 | 0;\n        d = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[14] - 1416354905 | 0;\n        c = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[5] - 57434055 | 0;\n        b = (b << 21 | b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[12] + 1700485571 | 0;\n        a = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[3] - 1894986606 | 0;\n        d = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[10] - 1051523 | 0;\n        c = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[1] - 2054922799 | 0;\n        b = (b << 21 | b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[8] + 1873313359 | 0;\n        a = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[15] - 30611744 | 0;\n        d = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[6] - 1560198380 | 0;\n        c = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[13] + 1309151649 | 0;\n        b = (b << 21 | b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[4] - 145523070 | 0;\n        a = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[11] - 1120210379 | 0;\n        d = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[2] + 718787259 | 0;\n        c = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[9] - 343485551 | 0;\n        b = (b << 21 | b >>> 11) + c | 0;\n\n        x[0] = a + x[0] | 0;\n        x[1] = b + x[1] | 0;\n        x[2] = c + x[2] | 0;\n        x[3] = d + x[3] | 0;\n    }\n\n    private _dataLength = 0;\n    private _bufferLength = 0;\n\n    private _state: Int32Array = new Int32Array(4);\n    private _buffer: ArrayBuffer = new ArrayBuffer(68);\n    private _buffer8: Uint8Array;\n    private _buffer32: Uint32Array;\n\n    constructor() {\n        this._buffer8 = new Uint8Array(this._buffer, 0, 68);\n        this._buffer32 = new Uint32Array(this._buffer, 0, 17);\n        this.start();\n    }\n\n    /**\n     * Initialise buffer to be hashed\n     */\n    public start() {\n        this._dataLength = 0;\n        this._bufferLength = 0;\n        this._state.set(Md5.stateIdentity);\n        return this;\n    }\n\n    // Char to code point to to array conversion:\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/charCodeAt\n    // #Example.3A_Fixing_charCodeAt_to_handle_non-Basic-Multilingual-Plane_characters_if_their_presence_earlier_in_the_string_is_unknown\n\n    /**\n     * Append a UTF-8 string to the hash buffer\n     * @param str String to append\n     */\n    public appendStr(str: string) {\n        const buf8 = this._buffer8;\n        const buf32 = this._buffer32;\n        let bufLen = this._bufferLength;\n        let code;\n        let i;\n\n        for (i = 0; i < str.length; i += 1) {\n            code = str.charCodeAt(i);\n            if (code < 128) {\n                buf8[bufLen++] = code;\n            } else if (code < 0x800) {\n                buf8[bufLen++] = (code >>> 6) + 0xC0;\n                buf8[bufLen++] = code & 0x3F | 0x80;\n            } else if (code < 0xD800 || code > 0xDBFF) {\n                buf8[bufLen++] = (code >>> 12) + 0xE0;\n                buf8[bufLen++] = (code >>> 6 & 0x3F) | 0x80;\n                buf8[bufLen++] = (code & 0x3F) | 0x80;\n            } else {\n                code = ((code - 0xD800) * 0x400) + (str.charCodeAt(++i) - 0xDC00) + 0x10000;\n                if (code > 0x10FFFF) {\n                    throw new Error('Unicode standard supports code points up to U+10FFFF');\n                }\n                buf8[bufLen++] = (code >>> 18) + 0xF0;\n                buf8[bufLen++] = (code >>> 12 & 0x3F) | 0x80;\n                buf8[bufLen++] = (code >>> 6 & 0x3F) | 0x80;\n                buf8[bufLen++] = (code & 0x3F) | 0x80;\n            }\n            if (bufLen >= 64) {\n                this._dataLength += 64;\n                Md5._md5cycle(this._state, buf32);\n                bufLen -= 64;\n                buf32[0] = buf32[16];\n            }\n        }\n        this._bufferLength = bufLen;\n        return this;\n    }\n\n    /**\n     * Append an ASCII string to the hash buffer\n     * @param str String to append\n     */\n    public appendAsciiStr(str: string) {\n        const buf8 = this._buffer8;\n        const buf32 = this._buffer32;\n        let bufLen = this._bufferLength;\n        let i;\n        let j = 0;\n\n        for (; ;) {\n            i = Math.min(str.length - j, 64 - bufLen);\n            while (i--) {\n                buf8[bufLen++] = str.charCodeAt(j++);\n            }\n            if (bufLen < 64) {\n                break;\n            }\n            this._dataLength += 64;\n            Md5._md5cycle(this._state, buf32);\n            bufLen = 0;\n        }\n        this._bufferLength = bufLen;\n        return this;\n    }\n\n    /**\n     * Append a byte array to the hash buffer\n     * @param input array to append\n     */\n    public appendByteArray(input: Uint8Array) {\n        const buf8 = this._buffer8;\n        const buf32 = this._buffer32;\n        let bufLen = this._bufferLength;\n        let i;\n        let j = 0;\n\n        for (; ;) {\n            i = Math.min(input.length - j, 64 - bufLen);\n            while (i--) {\n                buf8[bufLen++] = input[j++];\n            }\n            if (bufLen < 64) {\n                break;\n            }\n            this._dataLength += 64;\n            Md5._md5cycle(this._state, buf32);\n            bufLen = 0;\n        }\n        this._bufferLength = bufLen;\n        return this;\n    }\n\n    /**\n     * Get the state of the hash buffer\n     */\n    public getState(): HasherState {\n        const s = this._state;\n\n        return {\n            buffer: String.fromCharCode.apply(null, Array.from(this._buffer8)),\n            buflen: this._bufferLength,\n            length: this._dataLength,\n            state: [s[0], s[1], s[2], s[3]]\n        };\n    }\n\n    /**\n     * Override the current state of the hash buffer\n     * @param state New hash buffer state\n     */\n    public setState(state: HasherState) {\n        const buf = state.buffer;\n        const x = state.state;\n        const s = this._state;\n        let i;\n\n        this._dataLength = state.length;\n        this._bufferLength = state.buflen;\n        s[0] = x[0];\n        s[1] = x[1];\n        s[2] = x[2];\n        s[3] = x[3];\n\n        for (i = 0; i < buf.length; i += 1) {\n            this._buffer8[i] = buf.charCodeAt(i);\n        }\n    }\n\n    /**\n     * Hash the current state of the hash buffer and return the result\n     * @param raw Whether to return the value as an `Int32Array`\n     */\n    public end(raw: boolean = false) {\n        const bufLen = this._bufferLength;\n        const buf8 = this._buffer8;\n        const buf32 = this._buffer32;\n        const i = (bufLen >> 2) + 1;\n\n        this._dataLength += bufLen;\n        const dataBitsLen = this._dataLength * 8\n\n        buf8[bufLen] = 0x80;\n        buf8[bufLen + 1] = buf8[bufLen + 2] = buf8[bufLen + 3] = 0;\n        buf32.set(Md5.buffer32Identity.subarray(i), i);\n\n        if (bufLen > 55) {\n            Md5._md5cycle(this._state, buf32);\n            buf32.set(Md5.buffer32Identity);\n        }\n\n        // Do the final computation based on the tail and length\n        // Beware that the final length may not fit in 32 bits so we take care of that\n        if (dataBitsLen <= 0xFFFFFFFF) {\n            buf32[14] = dataBitsLen;\n        } else {\n            const matches = dataBitsLen.toString(16).match(/(.*?)(.{0,8})$/);\n            if (matches === null) {\n                return;\n            }\n\n            const lo = parseInt(matches[2], 16);\n            const hi = parseInt(matches[1], 16) || 0;\n\n            buf32[14] = lo;\n            buf32[15] = hi;\n        }\n\n        Md5._md5cycle(this._state, buf32);\n\n        return raw ? this._state : Md5._hex(this._state);\n    }\n}\n\nif (Md5.hashStr('hello') !== '5d41402abc4b2a76b9719d911017c592') {\n    throw new Error('Md5 self test failed.');\n}\n", "import {Md5} from './md5';\n\ndeclare let FileReaderSync: any;\n\nexport interface HashingResponse {\n    success: boolean;\n    result?: string | Int32Array;\n}\n\n// Hashes any blob\nexport class Md5FileHasher {\n    private _reader: any;\n\n    private _md5!: Md5;\n    private _part!: number;\n    // private _length!: number;\n    private _blob: any;\n\n\n    constructor(\n        private _callback: (r: HashingResponse) => void,    // Callback to return the result\n        private _async: boolean = true,                     // Async version is not always available in a web worker\n        private _partSize: number = 1048576,                // 1mb\n    ) {\n        this._configureReader();\n    }\n\n    /**\n     * Hash a blob of data in the worker\n     * @param blob Data to hash\n     */\n    public hash(blob: any) {\n        const self = this;\n\n        self._blob = blob;\n        // self._length = Math.ceil(blob.size / self._partSize);\n        self._part = 0;\n        self._md5 = new Md5();\n        self._processPart();\n    }\n\n\n    private _fail() {\n        this._callback({\n            success: false,\n            result: 'data read failed'\n        });\n    }\n\n    private _hashData(e: any) {\n        let self = this;\n\n        self._md5.appendByteArray(new Uint8Array(e.target.result));\n        if (self._part * self._partSize >= self._blob.size) {\n            self._callback({\n                success: true,\n                result: self._md5.end()\n            });\n        } else {\n            self._processPart();\n        }\n    }\n\n    private _processPart() {\n        const self = this;\n        let endbyte = 0;\n        let current_part: any;\n\n        self._part += 1;\n\n        if (self._blob.size > self._partSize) {        // If blob bigger then part_size we will slice it up\n            endbyte = self._part * self._partSize;\n            if (endbyte > self._blob.size) {\n                endbyte = self._blob.size;\n            }\n            current_part = self._blob.slice((self._part - 1) * self._partSize, endbyte);\n        } else {\n            current_part = self._blob;\n        }\n\n        if (self._async) {\n            self._reader.readAsArrayBuffer(current_part);\n        } else {\n            setTimeout(() => {\n                try {\n                    self._hashData({\n                        target: {\n                            result: self._reader.readAsArrayBuffer(current_part)\n                        },\n                    });\n                } catch (e) {\n                    self._fail();\n                }\n            }, 0);\n        }\n    }\n\n    private _configureReader() {\n        const self = this;\n\n        if (self._async) {\n            self._reader = new FileReader();\n            self._reader.onload = self._hashData.bind(self);\n            self._reader.onerror = self._fail.bind(self);\n            self._reader.onabort = self._fail.bind(self);\n        } else {\n            self._reader = new FileReaderSync();\n        }\n    }\n}\n", "export interface WorkerOptions {\n    credentials?: 'omit' | 'same-origin' | 'include';\n    name?: string;\n    type?: 'classic' | 'module';\n}\n\ndeclare var Worker: {\n    prototype: Worker;\n    new (stringUrl: string, options?: WorkerOptions): Worker;\n};\n\ninterface HashingRequest {\n    blob: any;\n    resolve: (...d: any) => void;\n    reject: (...d: any) => void;\n};\n\nexport class ParallelHasher {\n    private _queue: HashingRequest[] = [];\n    private _hashWorker: any;\n    private _processing?: HashingRequest;\n\n    private _ready: boolean = true;\n\n    constructor(workerUri: string, workerOptions?: WorkerOptions) {\n        const self = this;\n\n        if (Worker) {\n            self._hashWorker = new Worker(workerUri, workerOptions);\n            self._hashWorker.onmessage = self._recievedMessage.bind(self);\n            self._hashWorker.onerror = (err: any) => {\n                self._ready = false;\n                console.error('Hash worker failure', err);\n            };\n        } else {\n            self._ready = false;\n            console.error('Web Workers are not supported in this browser');\n        }\n    }\n\n    /**\n     * Hash a blob of data in the worker\n     * @param blob Data to hash\n     * @returns Promise of the Hashed result\n     */\n    public hash(blob: any) {\n        const self = this;\n        let promise;\n\n        promise = new Promise((resolve, reject) => {\n            self._queue.push({\n                blob,\n                resolve,\n                reject,\n            });\n\n            self._processNext();\n        });\n\n        return promise;\n    }\n\n    /** Terminate any existing hash requests */\n    public terminate() {\n        this._ready = false;\n        this._hashWorker.terminate();\n    }\n\n    // Processes the next item in the queue\n    private _processNext() {\n        if (this._ready && !this._processing && this._queue.length > 0) {\n            this._processing = this._queue.pop();\n            this._hashWorker.postMessage(this._processing!.blob);\n        }\n    }\n\n    // Hash result is returned from the worker\n    private _recievedMessage(evt: any) {\n        const data = evt.data;\n\n        if (data.success) {\n            this._processing?.resolve(data.result);\n        } else {\n            this._processing?.reject(data.result);\n        }\n\n        this._processing = undefined;\n        this._processNext();\n    }\n}\n"], "mappings": ";;;AA6DM,IAAO,MAAP,MAAO,KAAG;EAmNZ,cAAA;AARQ,SAAA,cAAc;AACd,SAAA,gBAAgB;AAEhB,SAAA,SAAqB,IAAI,WAAW,CAAC;AACrC,SAAA,UAAuB,IAAI,YAAY,EAAE;AAK7C,SAAK,WAAW,IAAI,WAAW,KAAK,SAAS,GAAG,EAAE;AAClD,SAAK,YAAY,IAAI,YAAY,KAAK,SAAS,GAAG,EAAE;AACpD,SAAK,MAAK;EACd;EA9MO,OAAO,QAAQ,KAAa,MAAe,OAAK;AACnD,WAAO,KAAK,cACP,MAAK,EACL,UAAU,GAAG,EACb,IAAI,GAAG;EAChB;EASO,OAAO,aAAa,KAAa,MAAe,OAAK;AACxD,WAAO,KAAK,cACP,MAAK,EACL,eAAe,GAAG,EAClB,IAAI,GAAG;EAChB;EAUQ,OAAO,KAAK,GAAa;AAC7B,UAAM,KAAK,KAAI;AACf,UAAM,KAAK,KAAI;AACf,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACvB,eAAS,IAAI;AACb,UAAI,EAAE,CAAC;AACP,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACvB,WAAG,SAAS,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAI;AACvC,eAAO;AACP,WAAG,SAAS,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAI;AACvC,eAAO;;;AAGf,WAAO,GAAG,KAAK,EAAE;EACrB;EAEQ,OAAO,UAAU,GAA2B,GAAyB;AACzE,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AAEX,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,WAAW;AAC1C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,QAAQ;AACxC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,WAAW;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAE/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,YAAY;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,WAAW;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,YAAY;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,WAAW;AAC1C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAE/B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,SAAS;AACnC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AACxC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,WAAW;AACtC,SAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AACxC,SAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,WAAW;AACrC,SAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,SAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAE9B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW;AACxC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,UAAU;AACxC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,WAAW;AACzC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,SAAK,KAAK,IAAI,MAAM,MAAM,IAAI;AAC9B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAC/B,UAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,SAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAE/B,MAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,MAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,MAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,MAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;EACtB;;;;EAmBO,QAAK;AACR,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,OAAO,IAAI,KAAI,aAAa;AACjC,WAAO;EACX;;;;;;;;EAUO,UAAU,KAAW;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,SAAS,KAAK;AAClB,QAAI;AACJ,QAAI;AAEJ,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAChC,aAAO,IAAI,WAAW,CAAC;AACvB,UAAI,OAAO,KAAK;AACZ,aAAK,QAAQ,IAAI;iBACV,OAAO,MAAO;AACrB,aAAK,QAAQ,KAAK,SAAS,KAAK;AAChC,aAAK,QAAQ,IAAI,OAAO,KAAO;iBACxB,OAAO,SAAU,OAAO,OAAQ;AACvC,aAAK,QAAQ,KAAK,SAAS,MAAM;AACjC,aAAK,QAAQ,IAAK,SAAS,IAAI,KAAQ;AACvC,aAAK,QAAQ,IAAK,OAAO,KAAQ;aAC9B;AACH,gBAAS,OAAO,SAAU,QAAU,IAAI,WAAW,EAAE,CAAC,IAAI,SAAU;AACpE,YAAI,OAAO,SAAU;AACjB,gBAAM,IAAI,MAAM,sDAAsD;;AAE1E,aAAK,QAAQ,KAAK,SAAS,MAAM;AACjC,aAAK,QAAQ,IAAK,SAAS,KAAK,KAAQ;AACxC,aAAK,QAAQ,IAAK,SAAS,IAAI,KAAQ;AACvC,aAAK,QAAQ,IAAK,OAAO,KAAQ;;AAErC,UAAI,UAAU,IAAI;AACd,aAAK,eAAe;AACpB,aAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,kBAAU;AACV,cAAM,CAAC,IAAI,MAAM,EAAE;;;AAG3B,SAAK,gBAAgB;AACrB,WAAO;EACX;;;;;EAMO,eAAe,KAAW;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,SAAS,KAAK;AAClB,QAAI;AACJ,QAAI,IAAI;AAER,eAAU;AACN,UAAI,KAAK,IAAI,IAAI,SAAS,GAAG,KAAK,MAAM;AACxC,aAAO,KAAK;AACR,aAAK,QAAQ,IAAI,IAAI,WAAW,GAAG;;AAEvC,UAAI,SAAS,IAAI;AACb;;AAEJ,WAAK,eAAe;AACpB,WAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,eAAS;;AAEb,SAAK,gBAAgB;AACrB,WAAO;EACX;;;;;EAMO,gBAAgB,OAAiB;AACpC,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,SAAS,KAAK;AAClB,QAAI;AACJ,QAAI,IAAI;AAER,eAAU;AACN,UAAI,KAAK,IAAI,MAAM,SAAS,GAAG,KAAK,MAAM;AAC1C,aAAO,KAAK;AACR,aAAK,QAAQ,IAAI,MAAM,GAAG;;AAE9B,UAAI,SAAS,IAAI;AACb;;AAEJ,WAAK,eAAe;AACpB,WAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,eAAS;;AAEb,SAAK,gBAAgB;AACrB,WAAO;EACX;;;;EAKO,WAAQ;AACX,UAAM,IAAI,KAAK;AAEf,WAAO;MACH,QAAQ,OAAO,aAAa,MAAM,MAAM,MAAM,KAAK,KAAK,QAAQ,CAAC;MACjE,QAAQ,KAAK;MACb,QAAQ,KAAK;MACb,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;;EAEtC;;;;;EAMO,SAAS,OAAkB;AAC9B,UAAM,MAAM,MAAM;AAClB,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,KAAK;AACf,QAAI;AAEJ,SAAK,cAAc,MAAM;AACzB,SAAK,gBAAgB,MAAM;AAC3B,MAAE,CAAC,IAAI,EAAE,CAAC;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AAEV,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAChC,WAAK,SAAS,CAAC,IAAI,IAAI,WAAW,CAAC;;EAE3C;;;;;EAMO,IAAI,MAAe,OAAK;AAC3B,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,UAAM,KAAK,UAAU,KAAK;AAE1B,SAAK,eAAe;AACpB,UAAM,cAAc,KAAK,cAAc;AAEvC,SAAK,MAAM,IAAI;AACf,SAAK,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;AACzD,UAAM,IAAI,KAAI,iBAAiB,SAAS,CAAC,GAAG,CAAC;AAE7C,QAAI,SAAS,IAAI;AACb,WAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,YAAM,IAAI,KAAI,gBAAgB;;AAKlC,QAAI,eAAe,YAAY;AAC3B,YAAM,EAAE,IAAI;WACT;AACH,YAAM,UAAU,YAAY,SAAS,EAAE,EAAE,MAAM,gBAAgB;AAC/D,UAAI,YAAY,MAAM;AAClB;;AAGJ,YAAM,KAAK,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,YAAM,KAAK,SAAS,QAAQ,CAAC,GAAG,EAAE,KAAK;AAEvC,YAAM,EAAE,IAAI;AACZ,YAAM,EAAE,IAAI;;AAGhB,SAAI,UAAU,KAAK,QAAQ,KAAK;AAEhC,WAAO,MAAM,KAAK,SAAS,KAAI,KAAK,KAAK,MAAM;EACnD;;AAxXe,IAAA,gBAAgB,IAAI,WAAW,CAAC,YAAY,YAAY,aAAa,SAAS,CAAC;AAC/E,IAAA,mBAAmB,IAAI,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAClF,IAAA,WAAW;AACX,IAAA,SAAmB,CAAA;AAGnB,IAAA,gBAAgB,IAAI,IAAG;AAqX1C,IAAI,IAAI,QAAQ,OAAO,MAAM,oCAAoC;AAC7D,QAAM,IAAI,MAAM,uBAAuB;;;;AC7crC,IAAO,gBAAP,MAAoB;EAStB,YACY,WACA,SAAkB,MAClB,YAAoB,SAAO;AAF3B,SAAA,YAAA;AACA,SAAA,SAAA;AACA,SAAA,YAAA;AAER,SAAK,iBAAgB;EACzB;;;;;EAMO,KAAK,MAAS;AACjB,UAAM,OAAO;AAEb,SAAK,QAAQ;AAEb,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,IAAG;AACnB,SAAK,aAAY;EACrB;EAGQ,QAAK;AACT,SAAK,UAAU;MACX,SAAS;MACT,QAAQ;KACX;EACL;EAEQ,UAAU,GAAM;AACpB,QAAI,OAAO;AAEX,SAAK,KAAK,gBAAgB,IAAI,WAAW,EAAE,OAAO,MAAM,CAAC;AACzD,QAAI,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,MAAM;AAChD,WAAK,UAAU;QACX,SAAS;QACT,QAAQ,KAAK,KAAK,IAAG;OACxB;WACE;AACH,WAAK,aAAY;;EAEzB;EAEQ,eAAY;AAChB,UAAM,OAAO;AACb,QAAI,UAAU;AACd,QAAI;AAEJ,SAAK,SAAS;AAEd,QAAI,KAAK,MAAM,OAAO,KAAK,WAAW;AAClC,gBAAU,KAAK,QAAQ,KAAK;AAC5B,UAAI,UAAU,KAAK,MAAM,MAAM;AAC3B,kBAAU,KAAK,MAAM;;AAEzB,qBAAe,KAAK,MAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,WAAW,OAAO;WACvE;AACH,qBAAe,KAAK;;AAGxB,QAAI,KAAK,QAAQ;AACb,WAAK,QAAQ,kBAAkB,YAAY;WACxC;AACH,iBAAW,MAAK;AACZ,YAAI;AACA,eAAK,UAAU;YACX,QAAQ;cACJ,QAAQ,KAAK,QAAQ,kBAAkB,YAAY;;WAE1D;iBACI,GAAG;AACR,eAAK,MAAK;;MAElB,GAAG,CAAC;;EAEZ;EAEQ,mBAAgB;AACpB,UAAM,OAAO;AAEb,QAAI,KAAK,QAAQ;AACb,WAAK,UAAU,IAAI,WAAU;AAC7B,WAAK,QAAQ,SAAS,KAAK,UAAU,KAAK,IAAI;AAC9C,WAAK,QAAQ,UAAU,KAAK,MAAM,KAAK,IAAI;AAC3C,WAAK,QAAQ,UAAU,KAAK,MAAM,KAAK,IAAI;WACxC;AACH,WAAK,UAAU,IAAI,eAAc;;EAEzC;;;;AC3FE,IAAO,iBAAP,MAAqB;EAOvB,YAAY,WAAmB,eAA6B;AANpD,SAAA,SAA2B,CAAA;AAI3B,SAAA,SAAkB;AAGtB,UAAM,OAAO;AAEb,QAAI,QAAQ;AACR,WAAK,cAAc,IAAI,OAAO,WAAW,aAAa;AACtD,WAAK,YAAY,YAAY,KAAK,iBAAiB,KAAK,IAAI;AAC5D,WAAK,YAAY,UAAU,CAAC,QAAY;AACpC,aAAK,SAAS;AACd,gBAAQ,MAAM,uBAAuB,GAAG;MAC5C;WACG;AACH,WAAK,SAAS;AACd,cAAQ,MAAM,+CAA+C;;EAErE;;;;;;EAOO,KAAK,MAAS;AACjB,UAAM,OAAO;AACb,QAAI;AAEJ,cAAU,IAAI,QAAQ,CAAC,SAAS,WAAU;AACtC,WAAK,OAAO,KAAK;QACb;QACA;QACA;OACH;AAED,WAAK,aAAY;IACrB,CAAC;AAED,WAAO;EACX;;EAGO,YAAS;AACZ,SAAK,SAAS;AACd,SAAK,YAAY,UAAS;EAC9B;;EAGQ,eAAY;AAChB,QAAI,KAAK,UAAU,CAAC,KAAK,eAAe,KAAK,OAAO,SAAS,GAAG;AAC5D,WAAK,cAAc,KAAK,OAAO,IAAG;AAClC,WAAK,YAAY,YAAY,KAAK,YAAa,IAAI;;EAE3D;;EAGQ,iBAAiB,KAAQ;;AAC7B,UAAM,OAAO,IAAI;AAEjB,QAAI,KAAK,SAAS;AACd,OAAA,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,KAAK,MAAM;WAClC;AACH,OAAA,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,KAAK,MAAM;;AAGxC,SAAK,cAAc;AACnB,SAAK,aAAY;EACrB;;", "names": []}