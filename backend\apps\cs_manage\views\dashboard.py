"""
CS_MANAGE 仪表盘视图

提供数据卡片和图表数据的API接口
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from django.db.models import Sum, Avg, Count, Q
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response

from dvadmin.utils.json_response import SuccessResponse, ErrorResponse
from dvadmin.utils.viewset import CustomModelViewSet
from apps.cs_manage.models import DailyMetricGroup
from apps.cs_manage.services.qiyu_client import QiyuDataClient
from apps.cs_manage.models import CsEmotionAnalysis

logger = logging.getLogger(__name__)


class CsManageDashboardViewSet(CustomModelViewSet):
    """
    CS_MANAGE 仪表盘视图集
    
    提供数据卡片、图表数据和筛选选项
    """
    
    queryset = DailyMetricGroup.objects.all()
    
    def get_queryset(self):
        """重写queryset以支持筛选"""
        queryset = super().get_queryset()
        
        # 日期筛选
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
            
        # 组别筛选
        group_id = self.request.query_params.get('group_id')
        if group_id is not None:
            queryset = queryset.filter(group_id=group_id)
            
        return queryset.order_by('-date')
    
    @action(detail=False, methods=['get'])
    def overview(self, request):
        """
        获取仪表盘概览数据
        
        包括数据卡片和筛选选项
        """
        try:
            # 获取筛选参数
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            group_id = request.query_params.get('group_id')
            
            # 默认获取昨天的数据
            if not start_date and not end_date:
                yesterday = (timezone.now() - timedelta(days=1)).date()
                start_date = end_date = yesterday.isoformat()
            
            # 获取可用的组别选项
            groups = self._get_available_groups()
            
            # 获取数据卡片
            cards_data = self._get_cards_data(start_date, end_date, group_id)
            
            # 获取图表数据
            charts_data = self._get_charts_data(start_date, end_date, group_id)
            
            return SuccessResponse(data={
                'cards': cards_data,
                'charts': charts_data,
                'filters': {
                    'groups': groups,
                    'current_filters': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'group_id': group_id
                    }
                }
            })
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取概览数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取数据失败: {str(e)}")
    
    def _get_available_groups(self) -> List[Dict]:
        """获取可用的组别选项（仅返回数据库中有数据的组别）"""
        try:
            # 从数据库中获取有数据的组别
            from django.db.models import Q

            # 获取最近30天内有数据的组别
            recent_date = timezone.now().date() - timedelta(days=30)

            available_group_ids = DailyMetricGroup.objects.filter(
                date__gte=recent_date
            ).values_list('group_id', flat=True).distinct()

            groups = [{'id': 0, 'name': '全部'}]  # 添加全部选项

            if available_group_ids:
                # 获取七鱼组别信息
                client = QiyuDataClient()
                staff_groups = client.get_staff_groups()

                # 创建组别ID到名称的映射
                group_mapping = {group.get('id'): group.get('name', f"组别{group.get('id')}")
                               for group in staff_groups}

                # 只返回有数据的组别
                for group_id in available_group_ids:
                    if group_id != 0:  # 排除全部组别，因为已经添加了
                        group_name = group_mapping.get(group_id, f"组别{group_id}")
                        groups.append({
                            'id': group_id,
                            'name': group_name
                        })

                # 按名称排序
                groups[1:] = sorted(groups[1:], key=lambda x: x['name'])

            logger.info(f"[仪表盘] 获取到 {len(groups)} 个有数据的组别")
            return groups

        except Exception as e:
            logger.error(f"[仪表盘] 获取组别列表失败: {str(e)}")
            return [{'id': 0, 'name': '全部'}]
    
    def _get_cards_data(self, start_date: str, end_date: str, group_id: str) -> Dict:
        """获取数据卡片数据"""
        try:
            # 构建查询条件
            queryset = DailyMetricGroup.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )

            # 修复：当group_id为None时，默认获取group_id=0的汇总数据
            if group_id is not None:
                queryset = queryset.filter(group_id=group_id)
            else:
                # group_id为None时，获取汇总数据（group_id=0）
                queryset = queryset.filter(group_id=0)
            
            # 聚合计算
            aggregated = queryset.aggregate(
                total_sessions=Sum('total_sessions'),
                ai_sessions=Sum('ai_sessions'),
                manual_sessions=Sum('manual_sessions'),
                worksheet_count=Sum('worksheet_count'),
                avg_satisfaction=Avg('manual_satisfaction'),
                avg_fcr=Avg('fcr_ratio'),
                avg_resp_30=Avg('resp_30_ratio'),
                avg_first_resp=Avg('avg_first_resp'),
                total_eva_count=Sum('eva_count'),
                total_invite_count=Sum('invite_count')
            )
            
            # 计算AI转人工率
            total_sessions = aggregated['total_sessions'] or 0
            ai_sessions = aggregated['ai_sessions'] or 0
            ai_transfer_rate = 0
            if ai_sessions > 0:
                ai_transfer_rate = ((total_sessions - ai_sessions) / ai_sessions) * 100
            
            # 获取情绪数据（仅全部组别）
            emotion_score = None
            if not group_id or group_id == '0':
                emotion_score = self._get_emotion_score(start_date, end_date)
            
            return {
                'total_sessions': total_sessions,
                'ai_sessions': ai_sessions,
                'manual_sessions': aggregated['manual_sessions'] or 0,
                'worksheet_count': aggregated['worksheet_count'] or 0,
                'ai_transfer_rate': round(ai_transfer_rate, 2),
                'manual_satisfaction': round(aggregated['avg_satisfaction'] or 0, 2),
                'fcr_ratio': round(aggregated['avg_fcr'] or 0, 2),
                'resp_30_ratio': round(aggregated['avg_resp_30'] or 0, 2),
                'avg_first_resp': int(aggregated['avg_first_resp'] or 0),
                'eva_count': aggregated['total_eva_count'] or 0,
                'invite_count': aggregated['total_invite_count'] or 0,
                'emotion_score': emotion_score
            }
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取卡片数据失败: {str(e)}")
            return {}
    
    def _get_emotion_score(self, start_date: str, end_date: str) -> Optional[float]:
        """
        实时计算情绪分值（仅全部组别有效）

        不从DailyMetricGroup存储，而是实时从CsEmotionAnalysis计算
        这样避免了重复存储，保持数据的实时性
        """
        try:
            from apps.cs_manage.models import CsEmotionAnalysis

            # 检查是否有情绪分析数据
            emotion_queryset = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            )

            emotion_count = emotion_queryset.count()
            if emotion_count == 0:
                logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间无情绪分析数据")
                return None

            # 计算平均情绪变化分值（这是最有意义的指标）
            emotion_data = emotion_queryset.aggregate(
                avg_emotion_change=Avg('emotion_change_score')
            )

            avg_emotion = emotion_data['avg_emotion_change']
            logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间情绪分析: {emotion_count}条记录，平均变化: {avg_emotion}")

            return round(avg_emotion, 2) if avg_emotion is not None else None

        except Exception as e:
            logger.error(f"[仪表盘] 获取情绪数据失败: {str(e)}")
            return None
    
    def _get_charts_data(self, start_date: str, end_date: str, group_id: str) -> Dict:
        """获取图表数据"""
        try:
            # 构建查询条件
            queryset = DailyMetricGroup.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )

            # 修复：统一处理group_id逻辑
            if group_id is not None:
                queryset = queryset.filter(group_id=group_id)
            else:
                # group_id为None时，获取汇总数据（group_id=0）
                queryset = queryset.filter(group_id=0)

            # 简化逻辑：直接获取数据，不再区分全部组别和特定组别
            daily_data = list(queryset.values(
                'date', 'total_sessions', 'ai_sessions', 'manual_sessions',
                'manual_satisfaction', 'fcr_ratio', 'resp_30_ratio', 'worksheet_count'
            ).order_by('date'))

            # 重命名字段以保持一致性
            for item in daily_data:
                item['avg_satisfaction'] = item.pop('manual_satisfaction', 0)
                item['avg_fcr'] = item.pop('fcr_ratio', 0)
                item['avg_resp_30'] = item.pop('resp_30_ratio', 0)

            # 获取情绪数据（仅全部组别）
            emotion_data = {}
            if not group_id or group_id == '0':
                emotion_data = self._get_emotion_chart_data(start_date, end_date)

            return {
                'session_trend': self._format_session_trend_data(daily_data),
                'worksheet_trend': self._format_worksheet_trend_data(daily_data),
                'quality_metrics': self._format_quality_metrics_data(daily_data),
                'emotion_trend': emotion_data
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取图表数据失败: {str(e)}")
            return {}
    
    def _format_session_trend_data(self, daily_data: List[Dict]) -> Dict:
        """格式化会话趋势数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '总会话量', 'data': [], 'type': 'bar', 'stack': 'total'},
                    {'name': 'AI会话', 'data': [], 'type': 'bar', 'stack': 'total'},
                    {'name': '人工会话', 'data': [], 'type': 'bar', 'stack': 'total'}
                ]
            }

        # 提取日期和数据
        dates = []
        total_sessions = []
        ai_sessions = []
        manual_sessions = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式，更适合图表显示
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            total_sessions.append(item['total_sessions'] or 0)
            ai_sessions.append(item['ai_sessions'] or 0)
            manual_sessions.append(item['manual_sessions'] or 0)

        return {
            'dates': dates,
            'series': [
                {'name': 'AI会话', 'data': ai_sessions, 'type': 'bar', 'stack': 'total'},
                {'name': '人工会话', 'data': manual_sessions, 'type': 'bar', 'stack': 'total'}
            ]
        }

    def _format_worksheet_trend_data(self, daily_data: List[Dict]) -> Dict:
        """格式化工单趋势数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '工单量', 'data': [], 'type': 'line'}
                ]
            }

        # 提取日期和数据
        dates = []
        worksheet_counts = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式，更适合图表显示
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            worksheet_counts.append(item.get('worksheet_count', 0) or 0)

        return {
            'dates': dates,
            'series': [
                {
                    'name': '工单量',
                    'data': worksheet_counts,
                    'type': 'line'
                }
            ]
        }

    def _format_quality_metrics_data(self, daily_data: List[Dict]) -> Dict:
        """格式化质量指标数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '满意度(%)', 'data': [], 'type': 'line'},
                    {'name': 'FCR(%)', 'data': [], 'type': 'line'},
                    {'name': '30秒应答率(%)', 'data': [], 'type': 'line'}
                ]
            }

        # 提取日期和数据
        dates = []
        satisfaction = []
        fcr = []
        resp_30 = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            # 使用新的字段名
            satisfaction.append(round(item.get('avg_satisfaction', 0) or 0, 2))
            fcr.append(round(item.get('avg_fcr', 0) or 0, 2))
            resp_30.append(round(item.get('avg_resp_30', 0) or 0, 2))

        return {
            'dates': dates,
            'series': [
                {'name': '满意度(%)', 'data': satisfaction, 'type': 'line'},
                {'name': 'FCR(%)', 'data': fcr, 'type': 'line'},
                {'name': '30秒应答率(%)', 'data': resp_30, 'type': 'line'}
            ]
        }
    
    def _get_emotion_chart_data(self, start_date: str, end_date: str) -> Dict:
        """获取情绪趋势图表数据"""
        try:
            # 检查是否有情绪数据
            emotion_count = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            ).count()

            if emotion_count == 0:
                logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间无情绪分析数据")
                return {
                    'dates': [],
                    'series': [
                        {'name': '初始情绪', 'data': [], 'type': 'line'},
                        {'name': '最终情绪', 'data': [], 'type': 'line'},
                        {'name': '情绪变化', 'data': [], 'type': 'line'}
                    ]
                }

            # 按日期聚合情绪数据
            emotion_daily = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            ).extra(
                select={'date': 'DATE(create_datetime)'}
            ).values('date').annotate(
                avg_initial=Avg('initial_emotion_score'),
                avg_final=Avg('final_emotion_score'),
                avg_change=Avg('emotion_change_score'),
                count=Count('id')
            ).order_by('date')

            dates = []
            initial_scores = []
            final_scores = []
            change_scores = []

            for item in emotion_daily:
                # 格式化日期为 MM-DD 格式
                date_obj = item['date']
                formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
                dates.append(formatted_date)

                initial_scores.append(round(item['avg_initial'] or 0, 2))
                final_scores.append(round(item['avg_final'] or 0, 2))
                change_scores.append(round(item['avg_change'] or 0, 2))

            return {
                'dates': dates,
                'series': [
                    {'name': '初始情绪', 'data': initial_scores, 'type': 'line'},
                    {'name': '最终情绪', 'data': final_scores, 'type': 'line'},
                    {'name': '情绪变化', 'data': change_scores, 'type': 'line'}
                ]
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取情绪图表数据失败: {str(e)}")
            return {
                'dates': [],
                'series': [
                    {'name': '初始情绪', 'data': [], 'type': 'line'},
                    {'name': '最终情绪', 'data': [], 'type': 'line'},
                    {'name': '情绪变化', 'data': [], 'type': 'line'}
                ]
            }
