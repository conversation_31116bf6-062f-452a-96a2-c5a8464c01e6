"""
七鱼API字段定义配置

该模块定义了所有七鱼API返回数据的字段结构和说明，
用于自动生成文档、验证数据格式等。
"""

from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class FieldDefinition:
    """字段定义"""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None

class QiyuAPISchemas:
    """七鱼API数据结构定义"""
    
    # 会话消息字段定义
    SESSION_MESSAGE_FIELDS = [
        FieldDefinition("id", "bigint", "消息id，全局唯一"),
        FieldDefinition("sessionId", "bigint", "会话id，全局唯一"),
        FieldDefinition("time", "bigint", "消息创建时间，单位毫秒"),
        FieldDefinition("mType", "tinyint", "消息类型，系统消息：0-系统消息；1-文本消息；2-图片消息；3-语音消息；4-文件消息；5-视频消息；6-系统提示消息；100-自定义消息；110-机器人答案；111-机器人答案反馈；112-超时关闭前提醒；113-超时关闭提醒；114-工单流程消息；115-富文本消息；116-敏感词屏蔽消息；117-客服拒绝转接；118-客服提交工单消息；119-客服发送邀请评价；120-访客参评；121-主企业客服转接会话到子企业信息；122-客服邀请评价详情；123-emoji消息；124-客服转接到主企业"),
        FieldDefinition("from", "tinyint", "消息流向，1=来自访客，0=来自客服"),
        FieldDefinition("msg", "varchar", "消息体，或为空"),
        FieldDefinition("status", "tinyint", "消息状态，1-正常，2-已撤回"),
        FieldDefinition("staffId", "bigint", "客服id，<=0则为机器人"),
        FieldDefinition("staffName", "varchar", "客服名"),
        FieldDefinition("userId", "bigint", "访客id"),
        FieldDefinition("userName", "varchar", "访客名"),
        FieldDefinition("autoReply", "tinyint", "消息回复类型，0=手动，1=自动"),
        FieldDefinition("isRichMedia", "tinyint", "是否富媒体类型，消息类型为：2-图片消息；3-语音消息；4-文件消息；5-视频消息；115-富文本消息，值为1，其他为0"),
        FieldDefinition("evaluation", "tinyint", "机器人答案评价，0-不显示，1-没有评价，2-有用，3-没用"),
        FieldDefinition("msgEvaluationContent", "text", "差评内容，或为空"),
        FieldDefinition("matchType", "tinyint", "匹配类型，0：未匹配，1：精确匹配，2：相似匹配，只对应机器人消息，访客和客服消息默认为0"),
        FieldDefinition("matchKnowledgeId", "bigint", "匹配知识ID，匹配知识ID，大于0表示精确匹配命中，其他小于等于0或者为空表示相似匹配或未匹配，只对应机器人消息，访客和客服消息默认为0"),
    ]
    
    # 会话详情字段定义
    SESSION_DETAIL_FIELDS = [
        FieldDefinition("id", "bigint", "会话ID，全局唯一"),
        FieldDefinition("startTime", "bigint", "会话开始时间，单位毫秒"),
        FieldDefinition("endTime", "bigint", "会话结束时间，单位毫秒"),
        FieldDefinition("sType", "tinyint", "会话类型，0：正常会话，1(2)：离线留言，3：未接入会话，4：群聊，5：群聊离线留言"),
        FieldDefinition("treatedTime", "bigint", "留言处理时间(如果该会话是留言)，单位毫秒"),
        FieldDefinition("interaction", "tinyint", "会话交互类型，0：客服正常会话，1：机器人会话，2：呼叫中心会话，3：推送消息"),
        FieldDefinition("closeReason", "tinyint", "会话结束原因"),
        FieldDefinition("isValid", "tinyint", "是否是有效会话，0：无效会话，1：有效会话"),
        FieldDefinition("category", "varchar", "会话末级咨询分类"),
        FieldDefinition("categoryDetail", "varchar", "会话咨询分类明细，一级分类名/二级分类名/三级分类名/四级分类名/五级分类名"),
        FieldDefinition("evaluation", "tinyint", "满意度值，2：(100满意; 1不满意)；3：(100满意; 50一般; 1不满意)；5：(100非常满意; 75满意; 50一般; 25不满意; 1非常不满意)，否则未评价"),
        FieldDefinition("evaluationType", "bigint", "满意度类型"),
        FieldDefinition("evaluationRemark", "varchar", "满意度评价内容"),
        FieldDefinition("staffInvitedEvaluateTime", "bigint", "客服邀评时间，单位毫秒，返回-1则为不存在该时间值"),
        FieldDefinition("userJoinEvaluateTime", "bigint", "访客参评时间，单位毫秒，返回-1则为不存在该时间值"),
        FieldDefinition("satisfactionTags", "list", "评价标签，访客评价时选择的标签"),
        FieldDefinition("isEvaluationInvited", "tinyint", "是否邀请评价，0-没有邀评，1-客服主动邀评，2-系统自动邀评"),
        FieldDefinition("staffId", "bigint", "接待客服ID，<=0则为机器人"),
        FieldDefinition("staffName", "varchar", "接待客服名字，或为\"机器人\""),
        FieldDefinition("staffAccount", "varchar", "客服账号"),
        FieldDefinition("userId", "varchar", "访客ID"),
        FieldDefinition("foreignId", "varchar", "外部ID，由企业提供"),
        FieldDefinition("userName", "varchar", "访客名"),
        FieldDefinition("vipLevel", "tinyint", "VIP级别，0=非VIP用户"),
        FieldDefinition("fromIp", "varchar", "访客来源ip"),
        FieldDefinition("fromPage", "varchar", "来源页"),
        FieldDefinition("fromTitle", "varchar", "来源页标题"),
        FieldDefinition("fromType", "varchar", "来源类型，Ios：苹果设备；Android:安卓设备；Web:网页版（含H5）；Wx：微信；wx_ma：微信小程序；Wb：微博；Open:消息接口"),
        FieldDefinition("fromSubType", "varchar", "终端明细，来源类型的详细信息"),
        FieldDefinition("originPlatform", "varchar", "来源渠道"),
        FieldDefinition("searchKey", "varchar", "关键字"),
        FieldDefinition("landPage", "varchar", "着陆页url"),
        FieldDefinition("fromGroup", "varchar", "分流客服组名"),
        FieldDefinition("fromGroupId", "bigint", "分流客服组ID"),
        FieldDefinition("fromStaff", "varchar", "会话来自哪个客服"),
        FieldDefinition("categoryId", "bigint", "客服组ID，默认为0"),
        FieldDefinition("relatedType", "tinyint", "关联会话类型，0：无关联；1：从机器人转接过来；2：机器人会话转接人工；3：历史会话发起；4：客服间转接；5：被接管；6：排队中会话提前进线；7：留言分配；8：多机器人切换会话；9：主企业转出到子企业；10：从主企业转过来的会话；11：子企业转出到主企业；12：从子企业转过来的会话"),
        FieldDefinition("relatedId", "bigint", "被关联的会话ID，若是无关联则此字段未定义"),
        FieldDefinition("transferRgType", "varchar", "转人工类型，机器人会话转成人工会话的触发原因"),
        FieldDefinition("fromHumanTransfer", "tinyint", "是否来自客服转接，0：非转接会话；1：客服转接过来的会话"),
        FieldDefinition("transferFromStaffName", "varchar", "转接来源分流客服名称"),
        FieldDefinition("transferFromGroup", "varchar", "转接来源分流客服组名称"),
        FieldDefinition("transferRemarks", "varchar", "转接来源分流会话备注"),
        FieldDefinition("humanTransferSessionId", "bigint", "客服转接来源会话ID，如果该会话不是客服转接过来的，该值为null"),
        FieldDefinition("transferType", "varchar", "转接类型，静默转接/人工转接"),
        FieldDefinition("inQueueTime", "bigint", "开始排队时间，若<=0忽略此字段"),
        FieldDefinition("visitRange", "bigint", "与上一次来访的时间差，默认一年(360天)，单位毫秒"),
        FieldDefinition("firstReplyCost", "bigint", "客服首次响应时长，访客首条消息与客服首次回复消息的时间间隔"),
        FieldDefinition("avgRespDuration", "bigint", "客服平均响应时长，客服整个会话中的平均响应时长"),
        FieldDefinition("staffFirstReplyTime", "bigint", "首次回复时间，客服首次响应的时间"),
        FieldDefinition("stickDuration", "bigint", "置顶时长，会话置顶时长"),
        FieldDefinition("clientFirstMessageTime", "bigint", "访客首条消息时间"),
        FieldDefinition("createTime", "varchar", "访客进入时间"),
        FieldDefinition("queueDuration", "varchar", "访客排队时长，格式为<时：分：秒>"),
        FieldDefinition("sessionDuration", "varchar", "会话时长，会话持续时长，格式为<时：分：秒>"),
        FieldDefinition("staffReceptionDuration", "varchar", "人工接待时长，格式为<时：分：秒>"),
        FieldDefinition("staffMessageCount", "tinyint", "客服消息数，客服在整个会话中的消息总量"),
        FieldDefinition("userMessageCount", "tinyint", "用户消息数，用户在整个会话中的消息总量"),
        FieldDefinition("roundNumber", "bigint", "会话回合数"),
        FieldDefinition("satisfyMsgCount", "int", "答案好评数"),
        FieldDefinition("status", "tinyint", "客服解决状态，0：未解决，1：已解决，2：解决中"),
        FieldDefinition("userResolvedStatus", "tinyint", "评价解决状态，0：未选择，1：已解决，2：未解决"),
        FieldDefinition("beginer", "tinyint", "会话发起方，1：访客，2：客服"),
        FieldDefinition("ender", "tinyint", "会话终止方，1：访客，2：客服，3：系统"),
        FieldDefinition("visitTimes", "varchar", "重复咨询，大于0才展示"),
        FieldDefinition("customFiled", "varchar", "自定义字段，所有自定义字段，是一个KV类型的JSON字段"),
        FieldDefinition("description", "varchar", "备注内容"),
        FieldDefinition("worksheetIds", "varchar", "会话关联工单ID，会话关联工单id，逗号分隔"),
        FieldDefinition("overflowFrom", "varchar", "溢出来源，会话溢出的来源客服组"),
        FieldDefinition("overflowRuleName", "varchar", "溢出原因"),
        FieldDefinition("overflowCondition", "varchar", "溢出条件"),
        FieldDefinition("startReason", "varchar", "会话开始来源，会话开始的来源，比如\"转自机器人\""),
        FieldDefinition("userIpCity", "varchar", "用户IP省市，用户IP所在省市"),
        FieldDefinition("alarmTimes", "tinyint", "告警次数"),
        FieldDefinition("videoRecordUrlList", "List", "视频记录地址，视频客服的存储地址，可自行拉取下载"),
        FieldDefinition("preQueue", "tinyint", "是否是提前进线，1：提前进线，0：非提前进线"),
    ]
    
    # 统计概览字段定义
    STATISTICS_OVERVIEW_FIELDS = [
        # 会话量相关
        FieldDefinition("sessions", "int", "总会话量"),
        FieldDefinition("sessionInCount", "int", "接入会话量"),
        FieldDefinition("sessionNotInCount", "int", "未接入会话量"),
        FieldDefinition("unAssignedSessions", "int", "未分配会话量"),
        FieldDefinition("callBackCount", "int", "客服发起会话量"),
        FieldDefinition("effectSessions", "int", "有效会话量"),
        FieldDefinition("visit", "int", "总来访量"),
        FieldDefinition("notInServiceCount", "int", "非服务时间来访量"),
        FieldDefinition("messages", "int", "总消息量"),
        FieldDefinition("userMessages", "int", "用户消息量"),
        FieldDefinition("staffMessages", "int", "客服消息量"),
        
        # 接入率相关
        FieldDefinition("assignedRatio", "float", "实际接入率"),
        FieldDefinition("effectAssignedRatio", "float", "有效接入率"),
        FieldDefinition("realSessionInRatio", "float", "接入率"),
        
        # 转出量相关
        FieldDefinition("redirectSessionsCount", "int", "总转出量"),
        FieldDefinition("slientTransCount", "int", "系统转出量"),
        FieldDefinition("humanTransCount", "int", "人工转出量"),
        FieldDefinition("overflowCount", "int", "系统溢出量"),
        
        # 响应时间相关
        FieldDefinition("avgFirstRespTime", "int", "平均首次响应时间（毫秒）"),
        FieldDefinition("avgRespTime", "int", "平均响应时间（毫秒）"),
        FieldDefinition("specialAnswerRatio", "float", "30s应答率"),
        FieldDefinition("specialAnswer45Ratio", "float", "45s应答率"),
        FieldDefinition("answerRatio", "float", "答问比"),
        FieldDefinition("answerThirtyRatioCount", "int", "30s应答数"),
        FieldDefinition("answerThirtyRatioAll", "int", "30s应答总数"),
        FieldDefinition("firstResponseTimeCount", "int", "首次响应时间统计数"),
        FieldDefinition("firstResponseTimeAll", "int", "首次响应时间统计总数"),
        
        # 会话时长相关
        FieldDefinition("avgTime", "int", "平均会话时长（毫秒）"),
        FieldDefinition("avgRgTime", "int", "平均人工接待时长（毫秒）"),
        FieldDefinition("sessionLength", "int", "会话时长统计数"),
        FieldDefinition("sessionLengthAll", "int", "会话时长统计总数"),
        
        # 满意度相关
        FieldDefinition("satisfactionRatio", "float", "相对满意度"),
        FieldDefinition("evaRatio", "float", "参评率"),
        FieldDefinition("evaFromCount", "int", "邀评数"),
        FieldDefinition("evaFromRatio", "float", "邀评率"),
        FieldDefinition("evaInviteRatio", "float", "邀评率"),
        FieldDefinition("validInviteEvalPercent", "float", "有效邀评率"),
        FieldDefinition("relativelySatisfiedCount", "int", "相对满意数"),
        FieldDefinition("relativelySatisfiedAll", "int", "相对满意总数"),
        FieldDefinition("evaluatedCount", "int", "已评价数"),
        FieldDefinition("evaluatedCountAll", "int", "已评价总数"),
        
        # 一次性解决率相关
        FieldDefinition("oneOffRatio", "float", "24h一次性解决率"),
        FieldDefinition("userResolvedRatio", "int", "评价已解决率（-1表示无数据）"),
        FieldDefinition("userResolvedCount", "int", "用户已解决数"),
        FieldDefinition("userUnResolvedCount", "int", "用户未解决数"),
        
        # 排队相关
        FieldDefinition("queueCount", "int", "排队量"),
        FieldDefinition("avgSucQueueTime", "int", "平均排队时长（毫秒）"),
        FieldDefinition("avgSucQueueTimeCount", "int", "平均排队时长统计数"),
        FieldDefinition("avgSucQueueTimeAll", "int", "平均排队时长统计总数"),
        FieldDefinition("maxQueueTime", "int", "最长排队时间（毫秒）"),
        FieldDefinition("avgFailureQueueTime", "int", "平均放弃时长（毫秒）"),
        FieldDefinition("queueCountMessage", "int", "排队转留言"),
        
        # 咨询相关
        FieldDefinition("totalConsultCount", "int", "总咨询来访量"),
        FieldDefinition("einsteinTransCount", "int", "机器人转人工量"),
        FieldDefinition("einsteinCount", "int", "机器人会话总数"),
        FieldDefinition("einsteinTransValidCount", "int", "机器人转人工有效数"),
        FieldDefinition("einsteinValidCount", "int", "机器人有效会话数"),
        
        # 会话邀请相关
        FieldDefinition("sessionInviteCount", "int", "会话邀请发起量"),
        FieldDefinition("sessionInviteSuccessCount", "int", "邀请成功会话量"),
        FieldDefinition("sessionInviteRatio", "int", "邀请成功率（-1表示无数据）"),
        
        # 其他统计
        FieldDefinition("loginStaffCount", "int", "登录客服数"),
        FieldDefinition("resumeDuration", "float", "恢复时长"),
        FieldDefinition("carrier", "str", "运营商信息"),
        FieldDefinition("responseTotalLength", "int", "响应总长度"),
        FieldDefinition("responseTotalLengthAll", "int", "响应总长度统计"),
    ]
    
    # 客服工作量字段定义
    STAFF_WORK_FIELDS = [
        FieldDefinition("id", "int", "客服ID"),
        FieldDefinition("name", "str", "客服名字"),
        FieldDefinition("namePinyin", "str", "客服名字拼音"),
        FieldDefinition("role", "int", "客服角色，1=普通客服"),
        FieldDefinition("timestamp", "int", "统计时间戳"),
        FieldDefinition("totalSessionCount", "int", "会话总量"),
        FieldDefinition("sessionsCount", "int", "接入会话量"),
        FieldDefinition("validSessionCount", "int", "有效会话量"),
        FieldDefinition("effectSessionCount", "int", "有效会话数"),
        FieldDefinition("clewSessionCount", "int", "线索会话数"),
        FieldDefinition("redirectSessionsCount", "int", "转出量"),
        FieldDefinition("uselessSessionsCount", "int", "无效会话量"),
        FieldDefinition("initiativeSessionsCount", "int", "发起会话量"),
        FieldDefinition("noReplySessionsCount", "int", "未回复会话量"),
        FieldDefinition("noReplyRatio", "float", "未回复率"),
        FieldDefinition("messageDealCount", "int", "留言处理量"),
        FieldDefinition("sessionStickDuration", "int", "会话置顶时长"),
        FieldDefinition("startTime", "int", "开始时间"),
        FieldDefinition("endTime", "int", "结束时间"),
        FieldDefinition("loginDuration", "int", "登录时长（毫秒）"),
        FieldDefinition("onlineDuration", "int", "在线时长（毫秒）"),
        FieldDefinition("pcOnlineDuration", "int", "PC在线时长（毫秒）"),
        FieldDefinition("mobileOnlineDuration", "int", "手机在线时长（毫秒）"),
        FieldDefinition("adminDuration", "int", "管理员时长（毫秒）"),
        FieldDefinition("restDuration", "int", "小休时长（毫秒）"),
        FieldDefinition("restSessionDuration", "int", "小休会话时长（毫秒）"),
        FieldDefinition("pendDuration", "int", "挂起时长（毫秒）"),
        FieldDefinition("hangSessionDuration", "int", "挂起会话时长（毫秒）"),
        FieldDefinition("resumeFreeDuration", "int", "空闲时长（毫秒）"),
        FieldDefinition("serviceTimeOnlineDuration", "int", "服务时间在线时长（毫秒）"),
        FieldDefinition("rest", "str", "小休详情JSON字符串，包含各类小休时长"),
        FieldDefinition("rest1", "int", "就餐时长（毫秒）"),
        FieldDefinition("rest2", "int", "会议时长（毫秒）"),
        FieldDefinition("rest3", "int", "培训时长（毫秒）"),
        FieldDefinition("rest4", "int", "休息时长（毫秒）"),
        FieldDefinition("rest5", "int", "洗手间时长（毫秒）"),
        FieldDefinition("rest90", "int", "其他时长（毫秒）"),
        FieldDefinition("dynamicRestDuration", "List[Dict]", "动态小休时长列表"),
        FieldDefinition("dynamicHistoryRestDuration", "List", "动态历史小休时长列表"),
        FieldDefinition("onlineRatio", "float", "在线率"),
        FieldDefinition("restRatio", "float", "小休率"),
        FieldDefinition("pendRatio", "float", "挂起率"),
        FieldDefinition("evaFromCount", "int", "邀评数"),
        FieldDefinition("evaFromRatio", "float", "邀评率"),
        FieldDefinition("restValidSession", "int", "小休有效会话"),
        FieldDefinition("hangValidSession", "int", "挂起有效会话"),
    ]
    
    # 客服质量字段定义
    STAFF_QUALITY_FIELDS = [
        FieldDefinition("id", "int", "客服ID"),
        FieldDefinition("name", "str", "客服姓名"),
        FieldDefinition("namePinyin", "str", "客服姓名拼音"),
        FieldDefinition("role", "int", "客服角色，1=普通客服"),
        FieldDefinition("timestamp", "int", "统计时间戳"),
        FieldDefinition("startTime", "int", "开始时间（通常为0）"),
        FieldDefinition("endTime", "int", "结束时间（通常为0）"),
        FieldDefinition("avgFirstRespTime", "int", "平均首次响应时长（毫秒）"),
        FieldDefinition("avgRespTime", "int", "平均响应时长（毫秒）"),
        FieldDefinition("avgRgTime", "int", "平均人工接待时长（毫秒）"),
        FieldDefinition("avgTime", "int", "平均会话时长（毫秒）"),
        FieldDefinition("replyRatio", "float", "应答率（0-1之间的比例）"),
        FieldDefinition("evaRatio", "float", "参评率（0-1之间的比例）"),
        FieldDefinition("satisfactionRatio", "float", "满意度（0-1之间的比例）"),
        FieldDefinition("answerReplyRatio", "float", "答问比"),
        FieldDefinition("oneOffRatio", "float", "一次性解决率（0-1之间的比例）"),
        FieldDefinition("userResolvedRatio", "int", "用户解决率（-1表示无数据）"),
        FieldDefinition("evaluationDetail", "str", "评价详情描述文本"),
        FieldDefinition("dynamicTitle", "Dict", "动态评价统计"),
        FieldDefinition("evaFromCount", "int", "邀评数"),
        FieldDefinition("evaFromAll", "int", "邀评总数"),
        FieldDefinition("evaFromRatio", "float", "邀评率（0-1之间的比例）"),
        FieldDefinition("rest", "str", "小休详情（通常为null）"),
        FieldDefinition("rest1", "int", "就餐时长（毫秒）"),
        FieldDefinition("rest2", "int", "会议时长（毫秒）"),
        FieldDefinition("rest3", "int", "培训时长（毫秒）"),
        FieldDefinition("rest4", "int", "休息时长（毫秒）"),
        FieldDefinition("rest5", "int", "洗手间时长（毫秒）"),
        FieldDefinition("rest90", "int", "其他时长（毫秒）"),
        FieldDefinition("withdrawCnt", "int", "撤回消息数"),
    ]
    
    # 客服考勤字段定义
    STAFF_ATTENDANCE_FIELDS = [
        FieldDefinition("id", "int", "客服ID"),
        FieldDefinition("name", "str", "客服姓名"),
        FieldDefinition("date", "int", "日期时间戳（毫秒）"),
        FieldDefinition("firstLoginStr", "str", "首次登录时间（HH:mm:ss格式）"),
        FieldDefinition("firstOnlineStr", "str", "首次在线时间（HH:mm:ss格式）"),
        FieldDefinition("lastHangStr", "str", "最后挂起时间（HH:mm:ss格式）"),
        FieldDefinition("lastLogoutStr", "str", "最后登出时间（HH:mm:ss格式）"),
        FieldDefinition("breakTimes", "int", "小休次数（账号切换到小休状态的次数）"),
        FieldDefinition("hangCount", "int", "挂起次数（账号切换到挂起状态的次数）"),
        FieldDefinition("seatRatio", "str", "坐席利用率（在线接待总时长与登录总时长的比值）"),
        FieldDefinition("staffValidRatio", "float", "坐席有效利用率（在线/小休/挂起状态的接待总时长与登录总时长的比值）"),
    ]
    
    # 会话历史记录字段定义
    SESSION_HISTORY_FIELDS = [
        # 基本信息
        FieldDefinition("id", "bigint", "会话ID，全局唯一"),
        FieldDefinition("description", "str", "会话描述或备注"),
        FieldDefinition("status", "int", "会话状态"),
        FieldDefinition("type", "int", "会话类型，0：正常会话"),
        FieldDefinition("version", "int", "会话版本号"),
        FieldDefinition("isValid", "int", "是否有效会话，0：无效，1：有效"),
        FieldDefinition("valid", "int", "有效性标识"),
        
        # 时间相关
        FieldDefinition("startTime", "bigint", "会话开始时间戳（毫秒）"),
        FieldDefinition("closeTime", "bigint", "会话结束时间戳（毫秒）"),
        FieldDefinition("inTime", "bigint", "进入时间戳（毫秒）"),
        FieldDefinition("sessionDuration", "int", "会话持续时长（毫秒）"),
        FieldDefinition("staffReceptionDuration", "int", "客服接待时长（毫秒）"),
        FieldDefinition("queueDuration", "int", "排队时长（毫秒）"),
        FieldDefinition("waitInQueueTime", "int", "等待排队时间（毫秒）"),
        FieldDefinition("assignStaffTime", "int", "分配客服时间戳（毫秒）"),
        FieldDefinition("firstMsgTime", "int", "首条消息时间戳（毫秒）"),
        FieldDefinition("firstRespTime", "int", "首次响应时间戳（毫秒）"),
        FieldDefinition("reConsultTime", "int", "重复咨询时间间隔（小时）"),
        
        # 用户信息
        FieldDefinition("user", "Dict", "用户信息对象"),
        FieldDefinition("foreignId", "str", "外部用户ID"),
        FieldDefinition("vipLevel", "int", "VIP等级"),
        FieldDefinition("userAgent", "str", "用户代理字符串"),
        FieldDefinition("platform", "str", "访问平台"),
        FieldDefinition("address", "str", "用户地址信息"),
        FieldDefinition("fromip", "str", "用户IP地址"),
        FieldDefinition("userTags", "List", "用户标签列表"),
        
        # 客服信息
        FieldDefinition("kefu", "Dict", "客服信息对象"),
        FieldDefinition("route", "str", "客服分流路由"),
        FieldDefinition("categoryDescription", "str", "分类描述"),
        
        # 会话状态
        FieldDefinition("userResolvedStatus", "int", "用户解决状态，0：未选择，1：已解决，2：未解决"),
        FieldDefinition("treatedStatus", "int", "处理状态"),
        FieldDefinition("treatedTime", "int", "处理时间戳（毫秒）"),
        FieldDefinition("beginer", "int", "会话发起方，1：用户，2：客服"),
        FieldDefinition("ender", "int", "会话结束方，1：用户，2：客服，3：系统"),
        FieldDefinition("closeType", "int", "关闭类型"),
        FieldDefinition("closeReason", "str", "关闭原因"),
        FieldDefinition("startReason", "int", "开始原因"),
        
        # 来源信息
        FieldDefinition("referrer", "Dict", "来源信息对象"),
        FieldDefinition("fromType", "int", "来源类型"),
        FieldDefinition("entryName", "str", "入口名称"),
        
        # 会话交互
        FieldDefinition("interaction", "int", "交互类型，0：客服正常会话，1：机器人会话"),
        FieldDefinition("relatedSessionType", "int", "关联会话类型"),
        FieldDefinition("relatedSessionId", "int", "关联会话ID"),
        FieldDefinition("visitTimes", "int", "访问次数"),
        FieldDefinition("roundNumber", "int", "会话轮次"),
        
        # 消息统计
        FieldDefinition("staffMessageCount", "int", "客服消息数量"),
        FieldDefinition("userMessageCount", "int", "用户消息数量"),
        FieldDefinition("unread", "int", "未读消息数"),
        FieldDefinition("lastMessage", "Dict", "最后一条消息信息"),
        FieldDefinition("withdraw", "int", "撤回消息数"),
        FieldDefinition("withdrawMessageCount", "int", "撤回消息总数"),
        
        # 响应时间
        FieldDefinition("firstRespDuration", "int", "首次响应时长（毫秒）"),
        FieldDefinition("avgRespDuration", "int", "平均响应时长（毫秒）"),
        
        # 评价相关
        FieldDefinition("satisfaction", "int", "满意度评分"),
        FieldDefinition("satisfactionType", "int", "满意度类型"),
        FieldDefinition("satisfactionName", "str", "满意度名称"),
        FieldDefinition("satisfactionRemarks", "str", "满意度备注"),
        FieldDefinition("isEvaluationInvited", "int", "是否邀请评价，0：否，1：是"),
        FieldDefinition("staffInvitedEvaluateTime", "int", "客服邀请评价时间戳（毫秒），-1表示未邀请"),
        FieldDefinition("userJoinEvaluateTime", "int", "用户参与评价时间戳（毫秒），-1表示未参与"),
        FieldDefinition("evaSource", "int", "评价来源"),
        FieldDefinition("inviteSource", "int", "邀请来源，-1表示无邀请"),
        FieldDefinition("fromInvite", "str", "是否来自邀请"),
        
        # 扩展信息
        FieldDefinition("sessionExt", "Dict", "会话扩展信息对象"),
        FieldDefinition("category", "Dict", "分类信息对象"),
        FieldDefinition("warnCount", "int", "告警次数"),
        FieldDefinition("warnInfo", "List", "告警信息列表"),
        FieldDefinition("cannotCallBack", "bool", "是否可以回呼"),
        FieldDefinition("isSendWorksheet", "int", "是否发送工单"),
        FieldDefinition("isAssociatedWorksheet", "int", "是否关联工单"),
        FieldDefinition("aiClassify", "int", "AI分类标识"),
        FieldDefinition("parentCorpId", "int", "父企业ID"),
        FieldDefinition("platformId", "int", "平台ID"),
        FieldDefinition("oneOff", "int", "是否一次性解决"),
    ]
    
    @classmethod
    def get_field_docs(cls, schema_name: str) -> str:
        """
        获取字段文档字符串
        
        Args:
            schema_name: 字段定义名称
            
        Returns:
            格式化的字段文档字符串
        """
        field_mapping = {
            'session_messages': cls.SESSION_MESSAGE_FIELDS,
            'session_detail': cls.SESSION_DETAIL_FIELDS,
            'session_history': cls.SESSION_HISTORY_FIELDS,
            'statistics_overview': cls.STATISTICS_OVERVIEW_FIELDS,
            'staff_work': cls.STAFF_WORK_FIELDS,
            'staff_quality': cls.STAFF_QUALITY_FIELDS,
            'staff_attendance': cls.STAFF_ATTENDANCE_FIELDS,
        }
        
        fields = field_mapping.get(schema_name, [])
        if not fields:
            return ""
        
        docs = []
        current_group = ""
        
        for field in fields:
            # 简单的分组逻辑（可以根据需要扩展）
            if field.name in ['id', 'name', 'timestamp']:
                group = "基本信息"
            elif 'time' in field.name.lower() or 'duration' in field.name.lower():
                group = "时间相关"
            elif 'ratio' in field.name.lower() or 'count' in field.name.lower():
                group = "统计指标"
            elif 'eva' in field.name.lower() or 'satisfaction' in field.name.lower():
                group = "评价相关"
            elif 'rest' in field.name.lower():
                group = "小休相关"
            else:
                group = "其他"
            
            if group != current_group:
                if current_group:
                    docs.append("")
                docs.append(f"{group}:")
                current_group = group
            
            docs.append(f"- {field.name} ({field.type}): {field.description}")
        
        return "\n".join(docs) 