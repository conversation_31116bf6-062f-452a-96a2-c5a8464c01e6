/**
 * Dashboard数据卡片配置
 */

export interface CardConfig {
  key: string
  title: string
  icon: string
  color: string
  suffix?: string
  description?: string
  group?: string
}

// 数据卡片配置
export const CARD_CONFIGS: CardConfig[] = [
  // 第一行：基础会话数据
  {
    key: 'total_sessions',
    title: '总会话量',
    icon: 'ChatDotRound',
    color: 'blue',
    group: 'session',
    description: '当日总会话数'
  },
  {
    key: 'ai_sessions',
    title: 'AI会话量',
    icon: 'Service',
    color: 'green',
    group: 'session',
    description: 'AI客服处理的会话数'
  },
  {
    key: 'manual_sessions',
    title: '人工会话量',
    icon: 'User',
    color: 'purple',
    group: 'session',
    description: '人工客服处理的会话数'
  },
  {
    key: 'worksheet_count',
    title: '工单量',
    icon: 'Document',
    color: 'orange',
    group: 'session',
    description: '当日工单数量'
  },

  // 第二行：质量指标
  {
    key: 'ai_transfer_rate',
    title: 'AI转人工率',
    icon: 'SwitchButton',
    color: 'red',
    suffix: '%',
    group: 'quality',
    description: 'AI转人工的比例'
  },
  {
    key: 'manual_satisfaction',
    title: '满意度',
    icon: 'Headset',
    color: 'yellow',
    suffix: '%',
    group: 'quality',
    description: '人工客服满意度'
  },
  {
    key: 'fcr_ratio',
    title: 'FCR',
    icon: 'CircleCheck',
    color: 'teal',
    suffix: '%',
    group: 'quality',
    description: '一次性解决率'
  },
  {
    key: 'resp_30_ratio',
    title: '30秒应答率',
    icon: 'Timer',
    color: 'indigo',
    suffix: '%',
    group: 'quality',
    description: '30秒内应答的比例'
  },

  // 第三行：扩展指标
  {
    key: 'online_ratio',
    title: '在线接入率',
    icon: 'Connection',
    color: 'emerald',
    suffix: '%',
    group: 'extended',
    description: '在线接入率'
  },
  {
    key: 'eva_count',
    title: '参评数',
    icon: 'Star',
    color: 'pink',
    group: 'extended',
    description: '评价总数'
  },
  {
    key: 'invite_count',
    title: '邀评数',
    icon: 'Message',
    color: 'cyan',
    group: 'extended',
    description: '总邀评数'
  },
  {
    key: 'avg_first_resp',
    title: '平均首响时间',
    icon: 'AlarmClock',
    color: 'lime',
    suffix: '秒',
    group: 'extended',
    description: '平均首次响应时间'
  },

  // 情绪分析（条件显示）
  {
    key: 'emotion_score',
    title: '情绪分值',
    icon: 'MagicStick',
    color: 'rose',
    group: 'emotion',
    description: '情绪分析分值'
  }
]

// 按组分组配置
export const CARD_GROUPS = {
  session: '基础会话数据',
  quality: '服务质量指标', 
  extended: '扩展指标',
  emotion: '情绪分析'
}

// 获取指定组的卡片配置
export function getCardsByGroup(group: string): CardConfig[] {
  return CARD_CONFIGS.filter(card => card.group === group)
}

// 获取所有卡片配置（按组分组）
export function getGroupedCards(): Record<string, CardConfig[]> {
  const grouped: Record<string, CardConfig[]> = {}
  
  Object.keys(CARD_GROUPS).forEach(group => {
    grouped[group] = getCardsByGroup(group)
  })
  
  return grouped
}
