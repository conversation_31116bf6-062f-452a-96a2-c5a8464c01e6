"""
指标计算器

负责将原始数据转换为业务指标和统计数据
"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """
    指标计算器
    
    职责：
    1. 计算个人客服指标
    2. 计算团队汇总指标
    3. 计算部门平均值
    4. 数据清洗和异常值处理
    """
    
    def __init__(self):
        # 异常值过滤阈值
        self.MAX_HOURLY_EFFICIENCY = 50  # 小时效率上限
        self.MIN_ONLINE_DURATION = 0.1   # 最小在线时长（小时）
    
    def calculate_staff_metrics(
        self, 
        reports_data: Dict[str, Any], 
        emotion_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        计算客服综合指标
        
        Args:
            reports_data: 七鱼报表数据
            emotion_data: 情绪分析数据
            
        Returns:
            Dict: 包含每个客服综合指标的字典
        """
        staff_metrics = {}
        
        # 1. 处理质量报表数据
        self._process_quality_reports(staff_metrics, reports_data.get('quality', []))
        
        # 2. 处理工作量报表数据
        self._process_workload_reports(staff_metrics, reports_data.get('workload', []))
        
        # 3. 合并情绪分析数据
        self._merge_emotion_data(staff_metrics, emotion_data)
        
        # 4. 数据清洗：过滤异常值
        cleaned_metrics = self._clean_staff_metrics(staff_metrics)
        
        return cleaned_metrics
    
    def _process_quality_reports(self, staff_metrics: Dict[str, Any], quality_reports: List[Dict]):
        """处理质量报表数据"""
        for quality_item in quality_reports:
            staff_id = str(quality_item.get('id', ''))
            if not staff_id:
                continue
                
            staff_metrics[staff_id] = {
                # 基本信息
                'staff_id': staff_id,
                'staff_name': quality_item.get('name', ''),
                'staff_account': quality_item.get('namePinyin', ''),
                
                # 质量指标 - 转换为百分比
                'avg_first_resp_time': quality_item.get('avgFirstRespTime', 0),
                'avg_resp_time': quality_item.get('avgRespTime', 0),
                'reply_ratio': self._to_percentage(quality_item.get('replyRatio', 0)),
                'satisfaction_ratio': self._to_percentage(quality_item.get('satisfactionRatio', 0)),
                'eva_ratio': self._to_percentage(quality_item.get('evaRatio', 0)),
                'invitation_ratio': self._to_percentage(quality_item.get('evaFromRatio', 0)),
                'one_off_ratio': self._to_percentage(quality_item.get('oneOffRatio', 0)),
                'user_resolved_ratio': self._to_percentage(quality_item.get('userResolvedRatio', 0)),
                
                # 初始化其他指标
                **self._get_default_metrics()
            }
    
    def _process_workload_reports(self, staff_metrics: Dict[str, Any], workload_reports: List[Dict]):
        """处理工作量报表数据"""
        for workload_item in workload_reports:
            staff_id = str(workload_item.get('id', ''))
            staff_name = workload_item.get('name', '')
            
            # 如果客服不在staff_metrics中，创建基础记录
            if staff_id not in staff_metrics:
                staff_metrics[staff_id] = {
                    'staff_id': staff_id,
                    'staff_name': staff_name,
                    'staff_account': workload_item.get('namePinyin', ''),
                    **self._get_default_quality_metrics(),
                    **self._get_default_metrics()
                }
            
            # 处理工作量数据
            total_sessions = workload_item.get('totalSessionCount', 0)
            valid_sessions = workload_item.get('validSessionCount', 0)
            effect_sessions = workload_item.get('effectSessionCount', 0)
            
            # 使用有效会话数，如果为0则使用生效会话数
            final_valid_sessions = valid_sessions if valid_sessions > 0 else effect_sessions
            
            # 时间数据（毫秒转小时）
            duration_data = self._convert_durations(workload_item)
            
            # 计算小时效率
            hourly_efficiency = (
                final_valid_sessions / duration_data['online_duration'] 
                if duration_data['online_duration'] > 0 else 0
            )
            
            staff_metrics[staff_id].update({
                'total_sessions': total_sessions,
                'valid_sessions': final_valid_sessions,
                'effect_sessions': effect_sessions,
                'hourly_efficiency': hourly_efficiency,
                'online_ratio': self._to_percentage(workload_item.get('onlineRatio', 0)),
                'no_reply_ratio': self._to_percentage(workload_item.get('noReplyRatio', 0)),
                **duration_data
            })
    
    def _merge_emotion_data(self, staff_metrics: Dict[str, Any], emotion_data: Dict[str, Any]):
        """合并情绪分析数据"""
        for staff_id, metrics in staff_metrics.items():
            emotion_stats = emotion_data.get(staff_id, {})
            
            # 情绪贡献度计算
            avg_emotion_change = emotion_stats.get('avg_emotion_change', 0)
            analysis_count = emotion_stats.get('analysis_count', 0)
            online_duration = metrics.get('online_duration', 0)
            
            # 个人情绪贡献度 = 平均情绪变化分数
            metrics['emotion_contribution'] = avg_emotion_change
            
            # 每小时情绪贡献度
            if online_duration > 0 and analysis_count > 0:
                metrics['emotion_contribution_per_hour'] = avg_emotion_change / online_duration
            else:
                metrics['emotion_contribution_per_hour'] = 0
            
            # 综合绩效分数（暂时设为0）
            metrics['comprehensive_score'] = 0
            
            # 添加情绪分析详细数据
            metrics.update({
                'emotion_analysis_count': emotion_stats.get('analysis_count', 0),
                'avg_emotion_change': emotion_stats.get('avg_emotion_change', 0),
                'positive_emotion_changes': emotion_stats.get('positive_changes', 0),
                'negative_emotion_changes': emotion_stats.get('negative_changes', 0)
            })
    
    def _clean_staff_metrics(self, staff_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """数据清洗：过滤异常值"""
        cleaned_metrics = {}
        
        for staff_id, metrics in staff_metrics.items():
            # 过滤小时效率异常高的数据
            if metrics.get('hourly_efficiency', 0) > self.MAX_HOURLY_EFFICIENCY:
                logger.warning(f"Filtered staff {staff_id} due to abnormal efficiency: {metrics['hourly_efficiency']}")
                continue
                
            cleaned_metrics[staff_id] = metrics
        
        return cleaned_metrics
    
    def calculate_team_summary(
        self, 
        staff_metrics: Dict[str, Any], 
        team_overview: Dict[str, Any] = None,
        date_range_days: int = 1
    ) -> Dict[str, Any]:
        """
        计算团队汇总数据
        
        Args:
            staff_metrics: 客服个人指标数据
            team_overview: 团队概览数据（可选）
            date_range_days: 日期范围天数
            
        Returns:
            Dict: 团队汇总指标
        """
        if not staff_metrics:
            return self._get_empty_team_summary()
        
        # 过滤有效数据的客服
        valid_staff = self._filter_valid_staff(staff_metrics)
        active_staff = self._filter_active_staff(valid_staff)
        
        if not valid_staff:
            return self._get_empty_team_summary()
        
        # 计算基础统计
        total_staff = len(valid_staff)
        total_sessions = sum(m['total_sessions'] for m in valid_staff.values())
        total_valid_sessions = sum(m['valid_sessions'] for m in valid_staff.values())
        total_emotion_analyses = sum(m['emotion_analysis_count'] for m in valid_staff.values())
        
        # 计算平均值
        summary = {
            'total_staff': total_staff,
            'total_sessions': total_sessions,
            'total_valid_sessions': total_valid_sessions,
            'total_emotion_analyses': total_emotion_analyses,
            
            # 质量指标平均值
            **self._calculate_quality_averages(valid_staff),
            
            # 时长指标平均值
            **self._calculate_duration_averages(valid_staff, active_staff, date_range_days),
            
            # 效率指标平均值
            **self._calculate_efficiency_averages(valid_staff),
            
            # 情绪指标平均值
            **self._calculate_emotion_averages(valid_staff)
        }
        
        # 合并团队概览数据
        if team_overview:
            summary = self._merge_team_overview(summary, team_overview)
        
        return summary
    
    def calculate_department_averages(
        self, 
        staff_metrics: Dict[str, Any], 
        dept_name: str = None
    ) -> Dict[str, Any]:
        """
        计算部门平均值
        
        Args:
            staff_metrics: 所有客服的指标数据
            dept_name: 部门名称，为None时计算全部平均值
            
        Returns:
            Dict: 部门平均质量指标
        """
        # 筛选部门内的客服
        if dept_name:
            dept_staff = [
                staff for staff in staff_metrics.values() 
                if staff.get('dept_name', '') == dept_name and staff.get('total_sessions', 0) > 0
            ]
        else:
            dept_staff = [
                staff for staff in staff_metrics.values() 
                if staff.get('total_sessions', 0) > 0
            ]
        
        # 如果部门人数太少，使用全部数据
        if len(dept_staff) < 2:
            dept_staff = [
                staff for staff in staff_metrics.values() 
                if staff.get('total_sessions', 0) > 0
            ]
        
        if not dept_staff:
            return self._get_empty_department_averages(dept_name)
        
        # 计算各项平均值
        return {
            'satisfaction_ratio': self._safe_average([s['satisfaction_ratio'] for s in dept_staff if s.get('satisfaction_ratio', 0) > 0]),
            'reply_ratio': self._safe_average([s['reply_ratio'] for s in dept_staff if s.get('reply_ratio', 0) > 0]),
            'one_off_ratio': self._safe_average([s['one_off_ratio'] for s in dept_staff if s.get('one_off_ratio', 0) > 0]),
            'eva_ratio': self._safe_average([s['eva_ratio'] for s in dept_staff if s.get('eva_ratio', 0) > 0]),
            'invitation_ratio': self._safe_average([s['invitation_ratio'] for s in dept_staff if s.get('invitation_ratio', 0) > 0]),
            'hourly_efficiency': self._safe_average([s['hourly_efficiency'] for s in dept_staff if s.get('hourly_efficiency', 0) > 0]),
            'dept_name': dept_name or '全部',
            'staff_count': len(dept_staff)
        }
    
    def supplement_empty_staff(
        self, 
        staff_metrics: Dict[str, Any], 
        all_staff_info: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        补充没有数据的客服信息
        
        Args:
            staff_metrics: 已有数据的客服指标
            all_staff_info: 所有客服的基础信息
            
        Returns:
            Dict: 补充后的客服指标数据
        """
        for staff_id, staff_info in all_staff_info.items():
            if staff_id not in staff_metrics:
                staff_metrics[staff_id] = {
                    'staff_id': staff_id,
                    'staff_name': staff_info['staff_name'],
                    'staff_account': staff_info['staff_account'],
                    'email': staff_info.get('email', ''),
                    'dept_name': staff_info.get('dept_name', ''),
                    
                    # 所有指标设为0
                    **self._get_default_quality_metrics(),
                    **self._get_default_metrics(),
                    
                    # 标识这是补充的空数据
                    'is_empty_record': True
                }
        
        return staff_metrics
    
    # 辅助方法
    def _to_percentage(self, value: float) -> float:
        """将小数转换为百分比"""
        if value is None:
            return 0
        return value * 100 if value <= 1 else value
    
    def _convert_durations(self, workload_item: Dict) -> Dict[str, float]:
        """转换时间数据（毫秒转小时）"""
        login_duration = workload_item.get('loginDuration', 0) / (1000 * 3600)
        online_duration = workload_item.get('onlineDuration', 0) / (1000 * 3600)
        rest_duration = workload_item.get('restDuration', 0) / (1000 * 3600)
        pend_duration = workload_item.get('pendDuration', 0) / (1000 * 3600)
        resume_free_duration = workload_item.get('resumeFreeDuration', 0) / (1000 * 3600)
        hang_session_duration = workload_item.get('hangSessionDuration', 0) / (1000 * 3600)
        
        return {
            'login_duration': login_duration,
            'online_duration': online_duration,
            'rest_duration': rest_duration,
            'pend_duration': pend_duration,
            'resume_free_duration': resume_free_duration,
            'hang_session_duration': hang_session_duration,
            'total_non_work_duration': rest_duration + pend_duration + resume_free_duration + hang_session_duration
        }
    
    def _get_default_quality_metrics(self) -> Dict[str, float]:
        """获取默认质量指标"""
        return {
            'avg_first_resp_time': 0,
            'avg_resp_time': 0,
            'reply_ratio': 0,
            'satisfaction_ratio': 0,
            'eva_ratio': 0,
            'invitation_ratio': 0,
            'one_off_ratio': 0,
            'user_resolved_ratio': 0
        }
    
    def _get_default_metrics(self) -> Dict[str, float]:
        """获取默认指标"""
        return {
            'online_duration': 0,
            'login_duration': 0,
            'rest_duration': 0,
            'pend_duration': 0,
            'resume_free_duration': 0,
            'hang_session_duration': 0,
            'total_non_work_duration': 0,
            'total_sessions': 0,
            'valid_sessions': 0,
            'hourly_efficiency': 0,
            'emotion_contribution': 0,
            'emotion_contribution_per_hour': 0,
            'comprehensive_score': 0,
            'emotion_analysis_count': 0,
            'avg_emotion_change': 0,
            'positive_emotion_changes': 0,
            'negative_emotion_changes': 0
        }
    
    def _filter_valid_staff(self, staff_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """过滤有效数据的客服"""
        return {
            k: v for k, v in staff_metrics.items() 
            if v.get('total_sessions', 0) > 0 or v.get('online_duration', 0) > 0
        }
    
    def _filter_active_staff(self, valid_staff: Dict[str, Any]) -> Dict[str, Any]:
        """过滤真正活跃的客服"""
        return {
            k: v for k, v in valid_staff.items() 
            if v.get('online_duration', 0) > self.MIN_ONLINE_DURATION
        }
    
    def _safe_average(self, values: List[float]) -> float:
        """安全计算平均值"""
        return sum(values) / len(values) if values else 0
    
    def _calculate_quality_averages(self, valid_staff: Dict[str, Any]) -> Dict[str, float]:
        """计算质量指标平均值"""
        satisfaction_values = [m['satisfaction_ratio'] for m in valid_staff.values() if m['satisfaction_ratio'] > 0]
        reply_values = [m['reply_ratio'] for m in valid_staff.values() if m['reply_ratio'] > 0]
        oneoff_values = [m['one_off_ratio'] for m in valid_staff.values() if m['one_off_ratio'] > 0]
        
        return {
            'avg_satisfaction': round(self._safe_average(satisfaction_values), 2),
            'avg_reply_ratio': round(self._safe_average(reply_values), 2),
            'avg_one_off_ratio': round(self._safe_average(oneoff_values), 2)
        }
    
    def _calculate_duration_averages(
        self, 
        valid_staff: Dict[str, Any], 
        active_staff: Dict[str, Any], 
        date_range_days: int
    ) -> Dict[str, float]:
        """计算时长指标平均值"""
        total_staff = len(valid_staff)
        
        if active_staff:
            total_online_duration = sum(m['online_duration'] for m in active_staff.values())
            avg_online_duration = total_online_duration / len(active_staff)
            avg_daily_online_duration = avg_online_duration / date_range_days if date_range_days > 0 else 0
        else:
            avg_online_duration = 0
            avg_daily_online_duration = 0
        
        # 其他时长指标
        total_login_duration = sum(m.get('login_duration', 0) for m in valid_staff.values())
        total_rest_duration = sum(m.get('rest_duration', 0) for m in valid_staff.values())
        total_pend_duration = sum(m.get('pend_duration', 0) for m in valid_staff.values())
        total_resume_free_duration = sum(m.get('resume_free_duration', 0) for m in valid_staff.values())
        total_hang_session_duration = sum(m.get('hang_session_duration', 0) for m in valid_staff.values())
        total_non_work_duration = sum(m.get('total_non_work_duration', 0) for m in valid_staff.values())
        
        return {
            'avg_online_duration': round(avg_online_duration, 2),
            'avg_daily_online_duration': round(avg_daily_online_duration, 2),
            'avg_login_duration': round(total_login_duration / total_staff if total_staff > 0 else 0, 2),
            'avg_rest_duration': round(total_rest_duration / total_staff if total_staff > 0 else 0, 2),
            'avg_pend_duration': round(total_pend_duration / total_staff if total_staff > 0 else 0, 2),
            'avg_resume_free_duration': round(total_resume_free_duration / total_staff if total_staff > 0 else 0, 2),
            'avg_hang_session_duration': round(total_hang_session_duration / total_staff if total_staff > 0 else 0, 2),
            'avg_total_non_work_duration': round(total_non_work_duration / total_staff if total_staff > 0 else 0, 2)
        }
    
    def _calculate_efficiency_averages(self, valid_staff: Dict[str, Any]) -> Dict[str, float]:
        """计算效率指标平均值"""
        efficiency_values = [m['hourly_efficiency'] for m in valid_staff.values()]
        total_valid_sessions = sum(m['valid_sessions'] for m in valid_staff.values())
        total_online_duration = sum(m['online_duration'] for m in valid_staff.values())
        
        return {
            'avg_hourly_efficiency': round(self._safe_average(efficiency_values), 2),
            'team_total_hourly_efficiency': round(
                total_valid_sessions / total_online_duration if total_online_duration > 0 else 0, 2
            )
        }
    
    def _calculate_emotion_averages(self, valid_staff: Dict[str, Any]) -> Dict[str, float]:
        """计算情绪指标平均值"""
        emotion_contrib_values = [
            m['emotion_contribution'] for m in valid_staff.values() 
            if m['emotion_analysis_count'] > 0
        ]
        emotion_contrib_per_hour_values = [
            m['emotion_contribution_per_hour'] for m in valid_staff.values() 
            if m['emotion_analysis_count'] > 0 and m.get('online_duration', 0) > 0
        ]
        
        return {
            'avg_emotion_contribution': round(self._safe_average(emotion_contrib_values), 4),
            'avg_emotion_contribution_per_hour': round(self._safe_average(emotion_contrib_per_hour_values), 4),
            'avg_comprehensive_score': 0  # 暂时设为0
        }
    
    def _merge_team_overview(self, summary: Dict[str, Any], team_overview: Dict[str, Any]) -> Dict[str, Any]:
        """合并团队概览数据"""
        try:
            def safe_get_value(data, key, default=0):
                value = data.get(key)
                if value is None:
                    return default
                if isinstance(value, dict):
                    return value.get('value', default)
                return value
            
            # 使用七鱼的准确满意度数据
            satisfaction_value = safe_get_value(team_overview, 'satisfactionRatio')
            if satisfaction_value is not None and satisfaction_value >= 0:
                accurate_satisfaction = satisfaction_value * 100 if satisfaction_value <= 1 else satisfaction_value
                if accurate_satisfaction >= 0:
                    summary['avg_satisfaction'] = round(accurate_satisfaction, 2)
            
            # 参评数据
            eva_value = safe_get_value(team_overview, 'evaRatio')
            if eva_value is not None and eva_value >= 0:
                accurate_eva_ratio = eva_value * 100 if eva_value <= 1 else eva_value
                summary['avg_eva_ratio'] = round(accurate_eva_ratio, 2)
            
            # 会话数据
            session_in_count = team_overview.get('sessionInCount', 0)
            effect_session_count = team_overview.get('effectSessionCount', 0)
            
            if session_in_count and isinstance(session_in_count, (int, float)):
                summary['total_sessions_overview'] = int(session_in_count)
                
            if effect_session_count and isinstance(effect_session_count, (int, float)):
                summary['total_valid_sessions_overview'] = int(effect_session_count)
            
            # 评价数据
            eva_count = team_overview.get('evaCount', 0)
            if eva_count and isinstance(eva_count, (int, float)):
                summary['total_evaluations'] = int(eva_count)
            
            good_eva_count = safe_get_value(team_overview, 'goodEvaCount')
            bad_eva_count = safe_get_value(team_overview, 'badEvaCount')
            
            if good_eva_count and isinstance(good_eva_count, (int, float)):
                summary['good_evaluations'] = int(good_eva_count)
                
            if bad_eva_count and isinstance(bad_eva_count, (int, float)):
                summary['bad_evaluations'] = int(bad_eva_count)
            
            summary['data_enhanced_by_overview'] = True
            
        except Exception as e:
            logger.error(f"Failed to merge team overview: {str(e)}")
            summary['data_merge_error'] = str(e)
        
        return summary
    
    def _get_empty_team_summary(self) -> Dict[str, Any]:
        """获取空的团队汇总数据"""
        return {
            'total_staff': 0,
            'total_sessions': 0,
            'total_valid_sessions': 0,
            'avg_satisfaction': 0,
            'avg_reply_ratio': 0,
            'avg_one_off_ratio': 0,
            'avg_online_duration': 0,
            'avg_daily_online_duration': 0,
            'avg_login_duration': 0,
            'avg_rest_duration': 0,
            'avg_pend_duration': 0,
            'avg_resume_free_duration': 0,
            'avg_hang_session_duration': 0,
            'avg_total_non_work_duration': 0,
            'avg_emotion_contribution': 0,
            'avg_emotion_contribution_per_hour': 0,
            'avg_comprehensive_score': 0,
            'avg_hourly_efficiency': 0,
            'team_total_hourly_efficiency': 0,
            'total_emotion_analyses': 0
        }
    
    def _get_empty_department_averages(self, dept_name: str = None) -> Dict[str, Any]:
        """获取空的部门平均值"""
        return {
            'satisfaction_ratio': 0,
            'reply_ratio': 0,
            'one_off_ratio': 0,
            'eva_ratio': 0,
            'invitation_ratio': 0,
            'hourly_efficiency': 0,
            'dept_name': dept_name or '未知',
            'staff_count': 0
        }


# 全局指标计算器实例
metrics_calculator = MetricsCalculator() 