<template>
  <div class="game-trend-chart">
    <!-- AI会话趋势图 -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-800 mb-3">各游戏AI会话量趋势</h4>
      <div ref="aiChartRef" class="w-full h-80"></div>
    </div>
    
    <!-- 人工会话趋势图 -->
    <div>
      <h4 class="text-md font-medium text-gray-800 mb-3">各游戏人工会话量趋势</h4>
      <div ref="manualChartRef" class="w-full h-80"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface GameTrendData {
  ai_chart?: {
    dates: string[]
    series: Array<{
      name: string
      data: number[]
      type: string
      stack: string
    }>
    title: string
  }
  manual_chart?: {
    dates: string[]
    series: Array<{
      name: string
      data: number[]
      type: string
      stack: string
    }>
    title: string
  }
}

interface Props {
  data?: GameTrendData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const aiChartRef = ref<HTMLDivElement>()
const manualChartRef = ref<HTMLDivElement>()
let aiChart: echarts.ECharts | null = null
let manualChart: echarts.ECharts | null = null

// 游戏颜色配置
const gameColors = [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
]

const initCharts = async () => {
  await nextTick()
  
  if (aiChartRef.value && !aiChart) {
    aiChart = echarts.init(aiChartRef.value)
  }
  
  if (manualChartRef.value && !manualChart) {
    manualChart = echarts.init(manualChartRef.value)
  }
  
  updateCharts()
}

const updateCharts = () => {
  if (props.loading) {
    aiChart?.showLoading()
    manualChart?.showLoading()
    return
  }
  
  aiChart?.hideLoading()
  manualChart?.hideLoading()
  
  // 更新AI会话图表
  if (aiChart && props.data?.ai_chart) {
    const aiOption = createChartOption(props.data.ai_chart, 'AI会话量')
    aiChart.setOption(aiOption, true)
  }
  
  // 更新人工会话图表
  if (manualChart && props.data?.manual_chart) {
    const manualOption = createChartOption(props.data.manual_chart, '人工会话量')
    manualChart.setOption(manualOption, true)
  }
}

const createChartOption = (chartData: any, yAxisName: string) => {
  if (!chartData.dates || !chartData.series) {
    return getEmptyOption(yAxisName)
  }
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        let total = 0
        
        params.forEach((param: any, index: number) => {
          total += param.value
          result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${param.value}<br/>`
        })
        
        result += `<strong>总计: ${total}</strong>`
        return result
      }
    },
    legend: {
      data: chartData.series.map((s: any) => s.name),
      top: 10,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameTextStyle: {
        color: '#666'
      }
    },
    series: chartData.series.map((s: any, index: number) => ({
      ...s,
      itemStyle: {
        color: gameColors[index % gameColors.length]
      },
      emphasis: {
        focus: 'series'
      }
    }))
  }
}

const getEmptyOption = (yAxisName: string) => ({
  title: {
    text: '暂无数据',
    left: 'center',
    top: 'center',
    textStyle: {
      color: '#999',
      fontSize: 14
    }
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    name: yAxisName
  }
})

const handleResize = () => {
  aiChart?.resize()
  manualChart?.resize()
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

watch(() => props.data, updateCharts, { deep: true })
watch(() => props.loading, updateCharts)

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  aiChart?.dispose()
  manualChart?.dispose()
  aiChart = null
  manualChart = null
}

// 组件卸载时清理
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(cleanup)
</script>

<style scoped>
.game-trend-chart {
  min-height: 700px;
}
</style>
