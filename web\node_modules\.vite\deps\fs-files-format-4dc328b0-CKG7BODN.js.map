{"version": 3, "sources": ["../../@fast-crud/fast-extends/src/uploader/components/fs-files-format.vue", "../../@fast-crud/fast-extends/src/uploader/components/fs-files-format.vue"], "sourcesContent": ["<template>\n  <div class=\"fs-files-format\">\n    <template v-if=\"computedProps.type === 'text'\">\n      <span v-for=\"item in itemsRef\" :key=\"item.url\" class=\"fs-files-item\">\n        <a :href=\"item.url\" target=\"_blank\" v-bind=\"computedProps.a\">\n          {{ item.name }}\n        </a>\n      </span>\n    </template>\n    <template v-else>\n      <component\n        :is=\"ui.tag.name\"\n        v-for=\"item in itemsRef\"\n        :key=\"item.url\"\n        class=\"fs-tag-item\"\n        :color=\"item.color\"\n        v-bind=\"computedProps.tag\"\n      >\n        <a :href=\"item.url\" target=\"_blank\" v-bind=\"computedProps.a\">{{ item.name }}</a>\n      </component>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, Ref, ref, watch } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\n/**\n * 文件格式化展示组件\n */\nexport default defineComponent({\n  name: \"FsFilesFormat\",\n  props: {\n    /**\n     * 文件列表\n     * 支持格式： `url , {url} , [url1,url2] ,  [{url:url1},{url:url2}]`\n     */\n    modelValue: {},\n    // tag颜色，`【primary, success, warning, danger ,info】`\n    color: {\n      default: \"\"\n    },\n    // 展示类型`【text, tag】`\n    type: {\n      default: \"tag\" // `可选【text,tag】`\n    },\n    // 链接配置\n    a: {},\n    // tag配置\n    tag: {},\n    // 构建下载url方法，支持异步\n    buildUrl: {},\n    // 批量构建下载url方法，支持异步\n    buildUrls: {},\n    // 根据value构建文件名\n    getFileName: {}\n  } as any,\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    const getFileName = computed(() => {\n      return (\n        // @ts-ignore\n        props.getFileName ||\n        function (url: any) {\n          if (typeof url !== \"string\") {\n            console.warn(\"获取文件名失败，请配置getFileName\");\n            return url;\n          }\n          if (url?.lastIndexOf(\"/\") >= 0) {\n            return url.substring(url.lastIndexOf(\"/\") + 1);\n          }\n          return url;\n        }\n      );\n    });\n    function getItem(value: any): any {\n      return {\n        url: undefined,\n        value: value,\n        name: getFileName.value(value),\n        // @ts-ignore\n        color: props.color\n      };\n    }\n\n    async function buildFileListUrls(list: any[]) {\n      // @ts-ignore\n      if (props.buildUrls) {\n        const values = list.map((item) => item.value);\n        // @ts-ignore\n        const urls = await props.buildUrls(values);\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = urls[i];\n        }\n        // @ts-ignore\n      } else if (props.buildUrl) {\n        for (let item of list) {\n          // @ts-ignore\n          item.url = await props.buildUrl(item.value);\n        }\n      } else {\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = list[i].value;\n        }\n      }\n    }\n\n    async function buildItems() {\n      // @ts-ignore\n      if (props.modelValue == null || props.modelValue === \"\") {\n        return [];\n      }\n      let valueArr = [];\n      // @ts-ignore\n      if (typeof props.modelValue === \"string\") {\n        // @ts-ignore\n        valueArr = [getItem(props.modelValue)];\n        // @ts-ignore\n      } else if (props.modelValue instanceof Array) {\n        // 本来就是数组的\n        valueArr = [];\n        // @ts-ignore\n        for (const val of props.modelValue) {\n          valueArr.push(getItem(val));\n        }\n      }\n      await buildFileListUrls(valueArr);\n      return valueArr;\n    }\n    const itemsRef: Ref = ref([]);\n    watch(\n      () => {\n        // @ts-ignore\n        return props.modelValue;\n      },\n      async () => {\n        itemsRef.value = await buildItems();\n      },\n      {\n        immediate: true\n      }\n    );\n    const computedProps = computed(() => {\n      return {\n        ...props\n      };\n    });\n    return {\n      ui,\n      itemsRef,\n      computedProps\n    };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-files-format {\n  display: flex;\n  flex-wrap: wrap;\n  .fs-form-item,\n  .fs-tag-item {\n    margin: 1px;\n    a {\n      text-decoration: none;\n    }\n  }\n  .tag-item {\n    margin-right: 10px;\n  }\n  .el-divider__text,\n  .el-link {\n    font-size: inherit;\n  }\n}\n</style>\n", "<template>\n  <div class=\"fs-files-format\">\n    <template v-if=\"computedProps.type === 'text'\">\n      <span v-for=\"item in itemsRef\" :key=\"item.url\" class=\"fs-files-item\">\n        <a :href=\"item.url\" target=\"_blank\" v-bind=\"computedProps.a\">\n          {{ item.name }}\n        </a>\n      </span>\n    </template>\n    <template v-else>\n      <component\n        :is=\"ui.tag.name\"\n        v-for=\"item in itemsRef\"\n        :key=\"item.url\"\n        class=\"fs-tag-item\"\n        :color=\"item.color\"\n        v-bind=\"computedProps.tag\"\n      >\n        <a :href=\"item.url\" target=\"_blank\" v-bind=\"computedProps.a\">{{ item.name }}</a>\n      </component>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, Ref, ref, watch } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\n/**\n * 文件格式化展示组件\n */\nexport default defineComponent({\n  name: \"FsFilesFormat\",\n  props: {\n    /**\n     * 文件列表\n     * 支持格式： `url , {url} , [url1,url2] ,  [{url:url1},{url:url2}]`\n     */\n    modelValue: {},\n    // tag颜色，`【primary, success, warning, danger ,info】`\n    color: {\n      default: \"\"\n    },\n    // 展示类型`【text, tag】`\n    type: {\n      default: \"tag\" // `可选【text,tag】`\n    },\n    // 链接配置\n    a: {},\n    // tag配置\n    tag: {},\n    // 构建下载url方法，支持异步\n    buildUrl: {},\n    // 批量构建下载url方法，支持异步\n    buildUrls: {},\n    // 根据value构建文件名\n    getFileName: {}\n  } as any,\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    const getFileName = computed(() => {\n      return (\n        // @ts-ignore\n        props.getFileName ||\n        function (url: any) {\n          if (typeof url !== \"string\") {\n            console.warn(\"获取文件名失败，请配置getFileName\");\n            return url;\n          }\n          if (url?.lastIndexOf(\"/\") >= 0) {\n            return url.substring(url.lastIndexOf(\"/\") + 1);\n          }\n          return url;\n        }\n      );\n    });\n    function getItem(value: any): any {\n      return {\n        url: undefined,\n        value: value,\n        name: getFileName.value(value),\n        // @ts-ignore\n        color: props.color\n      };\n    }\n\n    async function buildFileListUrls(list: any[]) {\n      // @ts-ignore\n      if (props.buildUrls) {\n        const values = list.map((item) => item.value);\n        // @ts-ignore\n        const urls = await props.buildUrls(values);\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = urls[i];\n        }\n        // @ts-ignore\n      } else if (props.buildUrl) {\n        for (let item of list) {\n          // @ts-ignore\n          item.url = await props.buildUrl(item.value);\n        }\n      } else {\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = list[i].value;\n        }\n      }\n    }\n\n    async function buildItems() {\n      // @ts-ignore\n      if (props.modelValue == null || props.modelValue === \"\") {\n        return [];\n      }\n      let valueArr = [];\n      // @ts-ignore\n      if (typeof props.modelValue === \"string\") {\n        // @ts-ignore\n        valueArr = [getItem(props.modelValue)];\n        // @ts-ignore\n      } else if (props.modelValue instanceof Array) {\n        // 本来就是数组的\n        valueArr = [];\n        // @ts-ignore\n        for (const val of props.modelValue) {\n          valueArr.push(getItem(val));\n        }\n      }\n      await buildFileListUrls(valueArr);\n      return valueArr;\n    }\n    const itemsRef: Ref = ref([]);\n    watch(\n      () => {\n        // @ts-ignore\n        return props.modelValue;\n      },\n      async () => {\n        itemsRef.value = await buildItems();\n      },\n      {\n        immediate: true\n      }\n    );\n    const computedProps = computed(() => {\n      return {\n        ...props\n      };\n    });\n    return {\n      ui,\n      itemsRef,\n      computedProps\n    };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-files-format {\n  display: flex;\n  flex-wrap: wrap;\n  .fs-form-item,\n  .fs-tag-item {\n    margin: 1px;\n    a {\n      text-decoration: none;\n    }\n  }\n  .tag-item {\n    margin-right: 10px;\n  }\n  .el-divider__text,\n  .el-link {\n    font-size: inherit;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAAA,IAAeC,gBAAgB;EAC7B,MAAM;EACN,OAAO;;;;;IAKL,YAAY,CAAC;;IAEb,OAAO;MACL,SAAS;IACX;;IAEA,MAAM;MACJ,SAAS;;IACX;;IAEA,GAAG,CAAC;;IAEJ,KAAK,CAAC;;IAEN,UAAU,CAAC;;IAEX,WAAW,CAAC;;IAEZ,aAAa,CAAC;EAChB;EACA,MAAMC,GAAYC,GAAK;AACf,UAAA,EAAE,IAAAC,EAAAA,IAAOC,EAAAA,GACTC,IAAcC,SAAS;;MAGzBL,EAAM,eACN,SAAUM,GAAU;AACd,eAAA,OAAOA,KAAQ,YACjB,QAAQ,KAAK,wBAAwB,GAC9BA,MAELA,KAAA,OAAA,SAAAA,EAAK,YAAY,GAAA,MAAQ,IACpBA,EAAI,UAAUA,EAAI,YAAY,GAAG,IAAI,CAAC,IAExCA;MACT;KAEH;AACD,aAASC,EAAQC,GAAiB;AACzB,aAAA;QACL,KAAK;QACL,OAAAA;QACA,MAAMJ,EAAY,MAAMI,CAAK;;QAE7B,OAAOR,EAAM;MAAA;IAEjB;AAEA,mBAAeS,EAAkBC,GAAa;AAE5C,UAAIV,EAAM,WAAW;AACnB,cAAMW,IAASD,EAAK,IAAI,CAACE,MAASA,EAAK,KAAK,GAEtCC,IAAO,MAAMb,EAAM,UAAUW,CAAM;AACzC,iBAASG,IAAI,GAAGA,IAAIJ,EAAK,QAAQI;AAC/BJ,YAAKI,CAAC,EAAE,MAAMD,EAAKC,CAAC;MACtB,WAESd,EAAM;AACf,iBAASY,KAAQF;AAEfE,YAAK,MAAM,MAAMZ,EAAM,SAASY,EAAK,KAAK;;AAG5C,iBAASE,IAAI,GAAGA,IAAIJ,EAAK,QAAQI;AAC/BJ,YAAKI,CAAC,EAAE,MAAMJ,EAAKI,CAAC,EAAE;IAG5B;AAEA,mBAAeC,IAAa;AAE1B,UAAIf,EAAM,cAAc,QAAQA,EAAM,eAAe;AACnD,eAAO,CAAA;AAET,UAAIgB,IAAW,CAAA;AAEX,UAAA,OAAOhB,EAAM,cAAe;AAE9BgB,YAAW,CAACT,EAAQP,EAAM,UAAU,CAAC;eAE5BA,EAAM,sBAAsB,OAAO;AAE5CgB,YAAW,CAAA;AAEA,mBAAAC,KAAOjB,EAAM;AACbgB,YAAA,KAAKT,EAAQU,CAAG,CAAC;MAE9B;AACA,aAAA,MAAMR,EAAkBO,CAAQ,GACzBA;IACT;AACM,UAAAE,IAAgBC,IAAI,CAAA,CAAE;AAC5BC;MACE,MAESpB,EAAM;MAEf,YAAY;AACDkB,UAAA,QAAQ,MAAMH,EAAAA;MACzB;MACA;QACE,WAAW;MACb;IAAA;AAEI,UAAAM,IAAgBhB,SAAS,OACtB;MACL,GAAGL;IAAA,EAEN;AACM,WAAA;MACL,IAAAE;MACA,UAAAgB;MACA,eAAAG;IAAA;EAEJ;AACF,CAAC;ACzJD,IAAAC,KAAA,EAAA,OAAA,kBAAA;AAAA,IAAA,IAAA,CAAA,MAAA;AAAA,IAAA,IAAA,CAAA,MAAA;AAEoBD,SAAAA,EAAAA,GAAcE,GAAIC,GAAAC,GAAAC,GAAAC,GAAA;AAChC,SAAAC,UAAA,GAAAC,mBAAA,OAIOP,IAPb;IAGMQ,EAAA,cAAA,SAAA,UAAAF,UAAqC,IAAK,GAAGC,mBAAAE,UAAA,EAAA,KAAA,EAAA,GAAAC,WAAAF,EAAA,UAAA,CAAAlB,OAAOgB,UAAA,GAAgBC,mBAAA,QAAA;MAAA,KAAAjB,EAAA;MAClE,OAAA;IAAA,GAAA;MAAmCqB,gBAAA,KAAAC,WAAA;QAJ3C,MAAAtB,EAAA;QAIoDS,QAAAA;QAAAA,SAAAA;MAAAA,GAAAA,EAAAA,cAAAA,CAAAA,GAAAA,gBAAAA,EAAAA,IAAAA,GAAAA,IAAAA,CAAAA;OAStC,GAAA,GAAA,MAAAO,UAAA,IAAK,GAAGC,mBAAAE,UAAA,EAAA,KAAA,EAAA,GAAAC,WAAAF,EAAA,UAAA,CAAAlB,OACTgB,UAAA,GAAcO,YAAAC,wBAAAN,EAAA,GAAA,IAAA,IAAA,GAAAI,WAAA;MAClB,KAAKtB,EAAE;MAfhB,OAAA;MAgBgBS,OAAAA,EAAAA;MAhBhB,SAAA;IAAA,GAAAS,EAkBQ,cAAgF,GAAA,GAAA;MAAA,SAAxEO,QAAU,MAAA;QAAiBJ,gBAAA,KAAAC,WAAA;UAlB3C,MAAAtB,EAAA;UAkBoDS,QAAAA;UAAAA,SAAAA;QAlBpD,GAAAS,EAAA,cAAA,CAAA,GAAAQ,gBAAA1B,EAAA,IAAA,GAAA,IAAA2B,CAAA;MAAA,CAAA;MAAA,GAAA;;;;;", "names": ["_sfc_main", "defineComponent", "props", "ctx", "ui", "useUi", "getFileName", "computed", "url", "getItem", "value", "buildFileListUrls", "list", "values", "item", "urls", "i", "buildItems", "valueArr", "val", "itemsRef", "ref", "watch", "computedProps", "_hoisted_1", "_cache", "$props", "$setup", "$data", "$options", "_openBlock", "_createElementBlock", "_ctx", "_Fragment", "_renderList", "_createElementVNode", "_mergeProps", "_createBlock", "_resolveDynamicComponent", "_withCtx", "_toDisplayString", "_hoisted_3"]}