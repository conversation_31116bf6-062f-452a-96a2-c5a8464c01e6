<template>
  <div class="cs-manage-dashboard min-h-screen bg-gray-50 p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">客服数据管理</h1>
      <p class="text-gray-600 mt-1">客服会话数据统计与分析</p>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center gap-2 flex-nowrap">
          <label class="text-sm font-medium text-gray-700 whitespace-nowrap">日期范围:</label>
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange"
            class="!w-64" :clearable="false" :editable="false" :shortcuts="shortcuts" />
        </div>

        <div class="flex items-center gap-2 flex-nowrap">
          <label class="text-sm font-medium text-gray-700 whitespace-nowrap">客服组:</label>
          <el-select v-model="selectedGroupId" placeholder="选择客服组" @change="handleGroupChange" class="!w-56"
            :popper-class="'max-h-80 overflow-y-auto'" :clearable="false">
            <el-option v-for="group in availableGroups" :key="group.id" :label="group.name" :value="group.id">
              <div class="truncate max-w-48">{{ group.name }}</div>
            </el-option>
          </el-select>
        </div>

        <el-button type="primary" @click="refreshData" :loading="loading">
          <el-icon class="mr-1">
            <Refresh />
          </el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 数据卡片 - 配置化渲染 -->
    <template v-for="(groupCards, groupKey) in groupedCards" :key="groupKey">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <template v-for="card in groupCards" :key="card.key">
          <DataCard
            v-if="shouldShowCard(card)"
            :title="card.title"
            :value="getCardValue(card.key)"
            :suffix="card.suffix"
            :icon="card.icon"
            :color="card.color"
            :loading="loading"
          />
        </template>
      </div>
    </template>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 会话趋势图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">会话量趋势</h3>
        <SessionTrendChart :data="dashboardData.charts?.session_trend" :loading="loading" />
      </div>

      <!-- 工单趋势图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">工单量趋势</h3>
        <WorksheetTrendChart :data="dashboardData.charts?.worksheet_trend" :loading="loading" />
      </div>
    </div>

    <!-- 第二行图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <!-- 质量指标图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">质量指标趋势</h3>
        <QualityMetricsChart :data="dashboardData.charts?.quality_metrics" :loading="loading" />
      </div>

      <!-- 情绪趋势图 -->
      <div v-if="dashboardData.charts?.emotion_trend && Object.keys(dashboardData.charts.emotion_trend).length > 0"
        class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">情绪分析趋势</h3>
        <EmotionTrendChart :data="dashboardData.charts?.emotion_trend" :loading="loading" />
      </div>
    </div>

    <!-- 游戏趋势图表（仅全部组别显示） -->
    <div v-if="selectedGroupId === 0 && dashboardData.charts?.game_trend" class="bg-white rounded-lg shadow-sm p-6 mt-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">各游戏会话量趋势</h3>
      <GameTrendChart :data="dashboardData.charts?.game_trend" :loading="loading" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, ChatDotRound, Service, User, Document, SwitchButton,
         SmileFilled, CircleCheck, Timer, Star, Message, AlarmClock, MagicStick, Connection } from '@element-plus/icons-vue'
import DataCard from './components/DataCard.vue'
import SessionTrendChart from './components/SessionTrendChart.vue'
import WorksheetTrendChart from './components/WorksheetTrendChart.vue'
import QualityMetricsChart from './components/QualityMetricsChart.vue'
import EmotionTrendChart from './components/EmotionTrendChart.vue'
import GameTrendChart from './components/GameTrendChart.vue'
import { getDashboardOverview } from './api'
import { getGroupedCards, type CardConfig } from './config/cardConfig'
import moment from 'moment'

// 响应式数据
const loading = ref(false)
const dateRange = ref<[string, string]>()
const selectedGroupId = ref<number>(0)
const availableGroups = ref<Array<{id: number, name: string}>>([])

const dashboardData = reactive({
  cards: {},
  charts: {},
  filters: {}
})

const shortcuts = [
  {
    text: '昨天 ',
    value: () => {
      const start = moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD')
      // 加一天
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  },
  {
    text: '最近一周',
    value: () => {
      const start = moment().subtract(8, 'days').startOf('day').format('YYYY-MM-DD')
      // 加一天
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const start = moment().subtract(30, 'days').startOf('day').format('YYYY-MM-DD')
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  }
]

// 计算属性
const currentFilters = computed(() => ({
  start_date: dateRange.value?.[0],
  end_date: dateRange.value?.[1],
  group_id: selectedGroupId.value === 0 ? undefined : selectedGroupId.value
}))

// 配置化数据卡片
const groupedCards = computed(() => getGroupedCards())

// 卡片相关方法
const getCardValue = (key: string) => {
  return dashboardData.cards?.[key] || 0
}

const shouldShowCard = (card: CardConfig) => {
  // 情绪分值卡片只在有数据时显示
  if (card.key === 'emotion_score') {
    return dashboardData.cards?.emotion_score !== null && dashboardData.cards?.emotion_score !== undefined
  }
  return true
}

// 方法
const initializeDefaultDate = () => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const dateStr = yesterday.toISOString().split('T')[0]
  dateRange.value = [dateStr, dateStr]
}

const fetchDashboardData = async () => {
  try {
    loading.value = true
    
    const params = {
      start_date: currentFilters.value.start_date,
      end_date: currentFilters.value.end_date,
      group_id: currentFilters.value.group_id
    }
    
    const response = await getDashboardOverview(params)
    
    if (response.code === 2000) {
      // 更新数据
      Object.assign(dashboardData.cards, response.data.cards)
      Object.assign(dashboardData.charts, response.data.charts)
      Object.assign(dashboardData.filters, response.data.filters)
      
      // 更新可用组别
      availableGroups.value = response.data.filters.groups || []
      
      ElMessage.success('数据加载成功')
    } else {
      ElMessage.error('获取数据失败，请稍后重试')
    }
  } catch (error) {
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const handleDateChange = () => {
  fetchDashboardData()
}

const handleGroupChange = () => {
  fetchDashboardData()
}

const refreshData = () => {
  fetchDashboardData()
}

// 生命周期
onMounted(() => {
  initializeDefaultDate()
  fetchDashboardData()
})
</script>

<style scoped>
.cs-manage-dashboard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
