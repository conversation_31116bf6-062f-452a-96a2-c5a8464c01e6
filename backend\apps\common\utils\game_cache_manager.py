"""
Game缓存管理器

为Game模型提供缓存功能，减少数据库查询频率
"""

import logging
from typing import Optional, Dict, Any
from django.core.cache import cache
from apps.kcs.models import Game

logger = logging.getLogger(__name__)

class GameCacheManager:
    """
    Game缓存管理器
    
    功能：
    1. 缓存Game对象，减少数据库查询
    2. 支持按game_id查询缓存
    3. 自动缓存失效和更新
    4. 简单的缓存接口
    """
    
    # 缓存配置
    CACHE_PREFIX = "game_cache"
    CACHE_TIMEOUT = 3600 * 1
    
    @classmethod
    def _get_cache_key(cls, game_id: int) -> str:
        """生成缓存键"""
        return f"{cls.CACHE_PREFIX}:{game_id}"
    
    @classmethod
    def get_game_by_id(cls, game_id: int) -> Optional[Game]:
        """
        根据game_id获取Game对象，优先从缓存获取
        
        Args:
            game_id: 游戏ID
            
        Returns:
            Optional[Game]: Game对象或None
        """
        if not game_id:
            return None
            
        cache_key = cls._get_cache_key(game_id)
        
        # 尝试从缓存获取
        cached_game = cache.get(cache_key)
        if cached_game is not None:
            logger.debug(f"[Game缓存] 缓存命中: game_id={game_id}")
            return cached_game
        
        # 缓存未命中，从数据库查询
        try:
            game_obj = Game.objects.filter(game_id=game_id).first()
            if game_obj:
                # 缓存Game对象
                cache.set(cache_key, game_obj, cls.CACHE_TIMEOUT)
                logger.debug(f"[Game缓存] 缓存新数据: game_id={game_id}")
            else:
                # 缓存None值，避免缓存穿透
                cache.set(cache_key, None, cls.CACHE_TIMEOUT // 2)  # 较短时间
                logger.debug(f"[Game缓存] 缓存空值: game_id={game_id}")
            
            return game_obj
            
        except Exception as e:
            logger.error(f"[Game缓存] 数据库查询失败: game_id={game_id}, error={e}")
            return None
    
    @classmethod
    def clear_game_cache(cls, game_id: int):
        """
        清除指定Game的缓存
        
        Args:
            game_id: 游戏ID
        """
        cache_key = cls._get_cache_key(game_id)
        cache.delete(cache_key)
        logger.debug(f"[Game缓存] 清除缓存: game_id={game_id}")
    
    @classmethod
    def clear_all_game_cache(cls):
        """清除所有Game缓存"""
        # 注意：这里需要根据实际的缓存实现来清除
        # 如果使用Redis，可以使用模式匹配删除
        try:
            # 获取所有相关缓存键并删除
            cache_keys = cache.keys(f"{cls.CACHE_PREFIX}:*")
            if cache_keys:
                cache.delete_many(cache_keys)
                logger.info(f"[Game缓存] 清除所有Game缓存，共 {len(cache_keys)} 个")
        except Exception as e:
            logger.error(f"[Game缓存] 清除所有缓存失败: {e}")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            cache_keys = cache.keys(f"{cls.CACHE_PREFIX}:*")
            return {
                "total_cached_games": len(cache_keys),
                "cache_prefix": cls.CACHE_PREFIX,
                "cache_timeout": cls.CACHE_TIMEOUT
            }
        except Exception as e:
            logger.error(f"[Game缓存] 获取缓存统计失败: {e}")
            return {"error": str(e)} 