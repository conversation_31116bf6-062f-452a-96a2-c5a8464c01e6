import {
  die
} from "./chunk-R5IZP54R.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/monaco.contribution-824f7607.mjs
var c = Object.defineProperty;
var l = Object.getOwnPropertyDescriptor;
var d = Object.getOwnPropertyNames;
var p = Object.prototype.hasOwnProperty;
var s = (e, t, n, a) => {
  if (t && typeof t == "object" || typeof t == "function")
    for (let r of d(t))
      !p.call(e, r) && r !== n && c(e, r, { get: () => t[r], enumerable: !(a = l(t, r)) || a.enumerable });
  return e;
};
var h = (e, t, n) => (s(e, t, "default"), n && s(n, t, "default"));
var o = {};
h(o, die);
var m = class {
  constructor(e, t, n) {
    this._onDidChange = new o.Emitter(), this._languageId = e, this.setDiagnosticsOptions(t), this.setModeConfiguration(n);
  }
  get onDidChange() {
    return this._onDidChange.event;
  }
  get languageId() {
    return this._languageId;
  }
  get modeConfiguration() {
    return this._modeConfiguration;
  }
  get diagnosticsOptions() {
    return this._diagnosticsOptions;
  }
  setDiagnosticsOptions(e) {
    this._diagnosticsOptions = e || /* @__PURE__ */ Object.create(null), this._onDidChange.fire(this);
  }
  setModeConfiguration(e) {
    this._modeConfiguration = e || /* @__PURE__ */ Object.create(null), this._onDidChange.fire(this);
  }
};
var _ = {
  validate: true,
  allowComments: true,
  schemas: [],
  enableSchemaRequest: false,
  schemaRequest: "warning",
  schemaValidation: "warning",
  comments: "error",
  trailingCommas: "error"
};
var f = {
  documentFormattingEdits: true,
  documentRangeFormattingEdits: true,
  completionItems: true,
  hovers: true,
  documentSymbols: true,
  tokens: true,
  colors: true,
  foldingRanges: true,
  diagnostics: true,
  selectionRanges: true
};
var i = new m(
  "json",
  _,
  f
);
var O = () => g().then((e) => e.getWorker());
o.languages.json = { jsonDefaults: i, getWorker: O };
function g() {
  return import("./jsonMode-4fba8acc-K5H5GQWD.js");
}
o.languages.register({
  id: "json",
  extensions: [".json", ".bowerrc", ".jshintrc", ".jscsrc", ".eslintrc", ".babelrc", ".har"],
  aliases: ["JSON", "json"],
  mimetypes: ["application/json"]
});
o.languages.onLanguage("json", () => {
  g().then((e) => e.setupMode(i));
});
export {
  O as getWorker,
  i as jsonDefaults
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/monaco.contribution-824f7607.mjs:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=monaco.contribution-824f7607-V6PKSAFD.js.map
