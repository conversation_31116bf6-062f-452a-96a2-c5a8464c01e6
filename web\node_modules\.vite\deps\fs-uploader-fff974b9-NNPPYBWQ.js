import {
  Fe,
  bt
} from "./chunk-OFUQC3BW.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  defineComponent
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/fs-uploader-fff974b9.mjs
var l = defineComponent({
  name: "FsUploader",
  props: {
    type: {}
  },
  setup(e) {
    async function t() {
      const { getDefaultType: o } = bt(), p = e.type || o();
      return await Fe(p);
    }
    return {
      getUploaderRef: t
    };
  }
});
export {
  l as default
};
//# sourceMappingURL=fs-uploader-fff974b9-NNPPYBWQ.js.map
