{"version": 3, "sources": ["../../node_modules/.pnpm/humanize-duration@3.32.1/node_modules/humanize-duration/humanize-duration.js", "../../@fast-crud/fast-extends/src/time/components/fs-time-humanize.vue"], "sourcesContent": ["// HumanizeDuration.js - https://git.io/j0HgmQ\n\n// @ts-check\n\n/**\n * @typedef {string | ((unitCount: number) => string)} Unit\n */\n\n/**\n * @typedef {(\"y\" | \"mo\" | \"w\" | \"d\" | \"h\" | \"m\" | \"s\" | \"ms\")} UnitName\n */\n\n/**\n * @typedef {Object} UnitMeasures\n * @prop {number} y\n * @prop {number} mo\n * @prop {number} w\n * @prop {number} d\n * @prop {number} h\n * @prop {number} m\n * @prop {number} s\n * @prop {number} ms\n */\n\n/**\n * @internal\n * @typedef {[string, string, string, string, string, string, string, string, string, string]} DigitReplacements\n */\n\n/**\n * @typedef {Object} Language\n * @prop {Unit} y\n * @prop {Unit} mo\n * @prop {Unit} w\n * @prop {Unit} d\n * @prop {Unit} h\n * @prop {Unit} m\n * @prop {Unit} s\n * @prop {Unit} ms\n * @prop {string} [decimal]\n * @prop {string} [delimiter]\n * @prop {DigitReplacements} [_digitReplacements]\n * @prop {boolean} [_numberFirst]\n * @prop {boolean} [_hideCountIf2]\n */\n\n/**\n * @typedef {Object} Options\n * @prop {string} [language]\n * @prop {Record<string, Language>} [languages]\n * @prop {string[]} [fallbacks]\n * @prop {string} [delimiter]\n * @prop {string} [spacer]\n * @prop {boolean} [round]\n * @prop {number} [largest]\n * @prop {UnitName[]} [units]\n * @prop {string} [decimal]\n * @prop {string} [conjunction]\n * @prop {number} [maxDecimalPoints]\n * @prop {UnitMeasures} [unitMeasures]\n * @prop {boolean} [serialComma]\n * @prop {DigitReplacements} [digitReplacements]\n */\n\n/**\n * @internal\n * @typedef {Required<Options>} NormalizedOptions\n */\n\n(function () {\n  // Fallback for `Object.assign` if relevant.\n  var assign =\n    Object.assign ||\n    /** @param {...any} destination */\n    function (destination) {\n      var source;\n      for (var i = 1; i < arguments.length; i++) {\n        source = arguments[i];\n        for (var prop in source) {\n          if (has(source, prop)) {\n            destination[prop] = source[prop];\n          }\n        }\n      }\n      return destination;\n    };\n\n  // Fallback for `Array.isArray` if relevant.\n  var isArray =\n    Array.isArray ||\n    function (arg) {\n      return Object.prototype.toString.call(arg) === \"[object Array]\";\n    };\n\n  // This has to be defined separately because of a bug: we want to alias\n  // `gr` and `el` for backwards-compatiblity. In a breaking change, we can\n  // remove `gr` entirely.\n  // See https://github.com/EvanHahn/HumanizeDuration.js/issues/143 for more.\n  var GREEK = language(\n    function (c) {\n      return c === 1 ? \"χρόνος\" : \"χρόνια\";\n    },\n    function (c) {\n      return c === 1 ? \"μήνας\" : \"μήνες\";\n    },\n    function (c) {\n      return c === 1 ? \"εβδομάδα\" : \"εβδομάδες\";\n    },\n    function (c) {\n      return c === 1 ? \"μέρα\" : \"μέρες\";\n    },\n    function (c) {\n      return c === 1 ? \"ώρα\" : \"ώρες\";\n    },\n    function (c) {\n      return c === 1 ? \"λεπτό\" : \"λεπτά\";\n    },\n    function (c) {\n      return c === 1 ? \"δευτερόλεπτο\" : \"δευτερόλεπτα\";\n    },\n    function (c) {\n      return (c === 1 ? \"χιλιοστό\" : \"χιλιοστά\") + \" του δευτερολέπτου\";\n    },\n    \",\"\n  );\n\n  /**\n   * @internal\n   * @type {Record<string, Language>}\n   */\n  var LANGUAGES = {\n    af: language(\n      \"jaar\",\n      function (c) {\n        return \"maand\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weke\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dae\";\n      },\n      function (c) {\n        return c === 1 ? \"uur\" : \"ure\";\n      },\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minute\";\n      },\n      function (c) {\n        return \"sekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    am: language(\"ዓመት\", \"ወር\", \"ሳምንት\", \"ቀን\", \"ሰዓት\", \"ደቂቃ\", \"ሰከንድ\", \"ሚሊሰከንድ\"),\n    ar: assign(\n      language(\n        function (c) {\n          return [\"سنة\", \"سنتان\", \"سنوات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"شهر\", \"شهران\", \"أشهر\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"أسبوع\", \"أسبوعين\", \"أسابيع\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"يوم\", \"يومين\", \"أيام\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ساعة\", \"ساعتين\", \"ساعات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"دقيقة\", \"دقيقتان\", \"دقائق\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ثانية\", \"ثانيتان\", \"ثواني\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"جزء من الثانية\", \"جزآن من الثانية\", \"أجزاء من الثانية\"][\n            getArabicForm(c)\n          ];\n        },\n        \",\"\n      ),\n      {\n        delimiter: \" ﻭ \",\n        _hideCountIf2: true,\n        _digitReplacements: [\"۰\", \"١\", \"٢\", \"٣\", \"٤\", \"٥\", \"٦\", \"٧\", \"٨\", \"٩\"]\n      }\n    ),\n    bg: language(\n      function (c) {\n        return [\"години\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеца\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"седмици\", \"седмица\", \"седмици\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дни\", \"ден\", \"дни\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часа\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минути\", \"минута\", \"минути\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    bn: language(\n      \"বছর\",\n      \"মাস\",\n      \"সপ্তাহ\",\n      \"দিন\",\n      \"ঘন্টা\",\n      \"মিনিট\",\n      \"সেকেন্ড\",\n      \"মিলিসেকেন্ড\"\n    ),\n    ca: language(\n      function (c) {\n        return \"any\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"os\");\n      },\n      function (c) {\n        return \"setman\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"di\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"hor\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segon\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegon\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ckb: language(\n      \"ساڵ\",\n      \"مانگ\",\n      \"هەفتە\",\n      \"ڕۆژ\",\n      \"کاژێر\",\n      \"خولەک\",\n      \"چرکە\",\n      \"میلی چرکە\",\n      \".\"\n    ),\n    cs: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"roky\", \"let\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"měsíc\", \"měsíce\", \"měsíce\", \"měsíců\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"týden\", \"týdne\", \"týdny\", \"týdnů\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"den\", \"dne\", \"dny\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodin\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    cy: language(\n      \"flwyddyn\",\n      \"mis\",\n      \"wythnos\",\n      \"diwrnod\",\n      \"awr\",\n      \"munud\",\n      \"eiliad\",\n      \"milieiliad\"\n    ),\n    da: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uge\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"ter\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    de: language(\n      function (c) {\n        return \"Jahr\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Monat\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Woche\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Tag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Stunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Minute\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Sekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Millisekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      \",\"\n    ),\n    el: GREEK,\n    en: language(\n      function (c) {\n        return \"year\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"month\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"week\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"day\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hour\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minute\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"\" : \"s\");\n      }\n    ),\n    eo: language(\n      function (c) {\n        return \"jaro\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"monato\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"semajno\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"tago\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"horo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"sekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"milisekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      \",\"\n    ),\n    es: language(\n      function (c) {\n        return \"año\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"es\");\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"día\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    et: language(\n      function (c) {\n        return \"aasta\" + (c === 1 ? \"\" : \"t\");\n      },\n      function (c) {\n        return \"kuu\" + (c === 1 ? \"\" : \"d\");\n      },\n      function (c) {\n        return \"nädal\" + (c === 1 ? \"\" : \"at\");\n      },\n      function (c) {\n        return \"päev\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"tund\" + (c === 1 ? \"\" : \"i\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      \",\"\n    ),\n    eu: language(\n      \"urte\",\n      \"hilabete\",\n      \"aste\",\n      \"egun\",\n      \"ordu\",\n      \"minutu\",\n      \"segundo\",\n      \"milisegundo\",\n      \",\"\n    ),\n    fa: language(\n      \"سال\",\n      \"ماه\",\n      \"هفته\",\n      \"روز\",\n      \"ساعت\",\n      \"دقیقه\",\n      \"ثانیه\",\n      \"میلی ثانیه\"\n    ),\n    fi: language(\n      function (c) {\n        return c === 1 ? \"vuosi\" : \"vuotta\";\n      },\n      function (c) {\n        return c === 1 ? \"kuukausi\" : \"kuukautta\";\n      },\n      function (c) {\n        return \"viikko\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"päivä\" + (c === 1 ? \"\" : \"ä\");\n      },\n      function (c) {\n        return \"tunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"minuutti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"sekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"millisekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      \",\"\n    ),\n    fo: language(\n      \"ár\",\n      function (c) {\n        return c === 1 ? \"mánaður\" : \"mánaðir\";\n      },\n      function (c) {\n        return c === 1 ? \"vika\" : \"vikur\";\n      },\n      function (c) {\n        return c === 1 ? \"dagur\" : \"dagar\";\n      },\n      function (c) {\n        return c === 1 ? \"tími\" : \"tímar\";\n      },\n      function (c) {\n        return c === 1 ? \"minuttur\" : \"minuttir\";\n      },\n      \"sekund\",\n      \"millisekund\",\n      \",\"\n    ),\n    fr: language(\n      function (c) {\n        return \"an\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \"mois\",\n      function (c) {\n        return \"semaine\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"jour\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"heure\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"minute\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"seconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"milliseconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \",\"\n    ),\n    gr: GREEK,\n    he: language(\n      function (c) {\n        return c === 1 ? \"שנה\" : \"שנים\";\n      },\n      function (c) {\n        return c === 1 ? \"חודש\" : \"חודשים\";\n      },\n      function (c) {\n        return c === 1 ? \"שבוע\" : \"שבועות\";\n      },\n      function (c) {\n        return c === 1 ? \"יום\" : \"ימים\";\n      },\n      function (c) {\n        return c === 1 ? \"שעה\" : \"שעות\";\n      },\n      function (c) {\n        return c === 1 ? \"דקה\" : \"דקות\";\n      },\n      function (c) {\n        return c === 1 ? \"שניה\" : \"שניות\";\n      },\n      function (c) {\n        return c === 1 ? \"מילישנייה\" : \"מילישניות\";\n      }\n    ),\n    hr: language(\n      function (c) {\n        if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"godine\";\n        }\n        return \"godina\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"mjesec\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"mjeseca\";\n        }\n        return \"mjeseci\";\n      },\n      function (c) {\n        if (c % 10 === 1 && c !== 11) {\n          return \"tjedan\";\n        }\n        return \"tjedna\";\n      },\n      function (c) {\n        return c === 1 ? \"dan\" : \"dana\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"sat\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"sata\";\n        }\n        return \"sati\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if ((mod10 === 2 || mod10 === 3 || mod10 === 4) && (c < 10 || c > 14)) {\n          return \"minute\";\n        }\n        return \"minuta\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if (mod10 === 5 || (Math.floor(c) === c && c >= 10 && c <= 19)) {\n          return \"sekundi\";\n        } else if (mod10 === 1) {\n          return \"sekunda\";\n        } else if (mod10 === 2 || mod10 === 3 || mod10 === 4) {\n          return \"sekunde\";\n        }\n        return \"sekundi\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"milisekunda\";\n        } else if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"milisekunde\";\n        }\n        return \"milisekundi\";\n      },\n      \",\"\n    ),\n    hi: language(\n      \"साल\",\n      function (c) {\n        return c === 1 ? \"महीना\" : \"महीने\";\n      },\n      function (c) {\n        return c === 1 ? \"हफ़्ता\" : \"हफ्ते\";\n      },\n      \"दिन\",\n      function (c) {\n        return c === 1 ? \"घंटा\" : \"घंटे\";\n      },\n      \"मिनट\",\n      \"सेकंड\",\n      \"मिलीसेकंड\"\n    ),\n    hu: language(\n      \"év\",\n      \"hónap\",\n      \"hét\",\n      \"nap\",\n      \"óra\",\n      \"perc\",\n      \"másodperc\",\n      \"ezredmásodperc\",\n      \",\"\n    ),\n    id: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"menit\",\n      \"detik\",\n      \"milidetik\"\n    ),\n    is: language(\n      \"ár\",\n      function (c) {\n        return \"mánuð\" + (c === 1 ? \"ur\" : \"ir\");\n      },\n      function (c) {\n        return \"vik\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"ur\" : \"ar\");\n      },\n      function (c) {\n        return \"klukkutím\" + (c === 1 ? \"i\" : \"ar\");\n      },\n      function (c) {\n        return \"mínút\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"sekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"millisekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      }\n    ),\n    it: language(\n      function (c) {\n        return \"ann\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"e\" : \"i\");\n      },\n      function (c) {\n        return \"settiman\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"giorn\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"or\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"o\" : \"i\");\n      },\n      \",\"\n    ),\n    ja: language(\"年\", \"ヶ月\", \"週\", \"日\", \"時間\", \"分\", \"秒\", \"ミリ秒\"),\n    km: language(\n      \"ឆ្នាំ\",\n      \"ខែ\",\n      \"សប្តាហ៍\",\n      \"ថ្ងៃ\",\n      \"ម៉ោង\",\n      \"នាទី\",\n      \"វិនាទី\",\n      \"មិល្លីវិនាទី\"\n    ),\n    kn: language(\n      function (c) {\n        return c === 1 ? \"ವರ್ಷ\" : \"ವರ್ಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ತಿಂಗಳು\" : \"ತಿಂಗಳುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ವಾರ\" : \"ವಾರಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ದಿನ\" : \"ದಿನಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಗಂಟೆ\" : \"ಗಂಟೆಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ನಿಮಿಷ\" : \"ನಿಮಿಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಸೆಕೆಂಡ್\" : \"ಸೆಕೆಂಡುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಮಿಲಿಸೆಕೆಂಡ್\" : \"ಮಿಲಿಸೆಕೆಂಡುಗಳು\";\n      }\n    ),\n    ko: language(\"년\", \"개월\", \"주일\", \"일\", \"시간\", \"분\", \"초\", \"밀리 초\"),\n    ku: language(\n      \"sal\",\n      \"meh\",\n      \"hefte\",\n      \"roj\",\n      \"seet\",\n      \"deqe\",\n      \"saniye\",\n      \"mîlîçirk\",\n      \",\"\n    ),\n    lo: language(\n      \"ປີ\",\n      \"ເດືອນ\",\n      \"ອາທິດ\",\n      \"ມື້\",\n      \"ຊົ່ວໂມງ\",\n      \"ນາທີ\",\n      \"ວິນາທີ\",\n      \"ມິນລິວິນາທີ\",\n      \",\"\n    ),\n    lt: language(\n      function (c) {\n        return c % 10 === 0 || (c % 100 >= 10 && c % 100 <= 20)\n          ? \"metų\"\n          : \"metai\";\n      },\n      function (c) {\n        return [\"mėnuo\", \"mėnesiai\", \"mėnesių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"savaitė\", \"savaitės\", \"savaičių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"diena\", \"dienos\", \"dienų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"valanda\", \"valandos\", \"valandų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"minutė\", \"minutės\", \"minučių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"sekundė\", \"sekundės\", \"sekundžių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"milisekundė\", \"milisekundės\", \"milisekundžių\"][\n          getLithuanianForm(c)\n        ];\n      },\n      \",\"\n    ),\n    lv: language(\n      function (c) {\n        return getLatvianForm(c) ? \"gads\" : \"gadi\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"mēnesis\" : \"mēneši\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"nedēļa\" : \"nedēļas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"diena\" : \"dienas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"stunda\" : \"stundas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"minūte\" : \"minūtes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"sekunde\" : \"sekundes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"milisekunde\" : \"milisekundes\";\n      },\n      \",\"\n    ),\n    mk: language(\n      function (c) {\n        return c === 1 ? \"година\" : \"години\";\n      },\n      function (c) {\n        return c === 1 ? \"месец\" : \"месеци\";\n      },\n      function (c) {\n        return c === 1 ? \"недела\" : \"недели\";\n      },\n      function (c) {\n        return c === 1 ? \"ден\" : \"дена\";\n      },\n      function (c) {\n        return c === 1 ? \"час\" : \"часа\";\n      },\n      function (c) {\n        return c === 1 ? \"минута\" : \"минути\";\n      },\n      function (c) {\n        return c === 1 ? \"секунда\" : \"секунди\";\n      },\n      function (c) {\n        return c === 1 ? \"милисекунда\" : \"милисекунди\";\n      },\n      \",\"\n    ),\n    mn: language(\n      \"жил\",\n      \"сар\",\n      \"долоо хоног\",\n      \"өдөр\",\n      \"цаг\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    mr: language(\n      function (c) {\n        return c === 1 ? \"वर्ष\" : \"वर्षे\";\n      },\n      function (c) {\n        return c === 1 ? \"महिना\" : \"महिने\";\n      },\n      function (c) {\n        return c === 1 ? \"आठवडा\" : \"आठवडे\";\n      },\n      \"दिवस\",\n      \"तास\",\n      function (c) {\n        return c === 1 ? \"मिनिट\" : \"मिनिटे\";\n      },\n      \"सेकंद\",\n      \"मिलिसेकंद\"\n    ),\n    ms: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"minit\",\n      \"saat\",\n      \"milisaat\"\n    ),\n    nl: language(\n      \"jaar\",\n      function (c) {\n        return c === 1 ? \"maand\" : \"maanden\";\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weken\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dagen\";\n      },\n      \"uur\",\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minuten\";\n      },\n      function (c) {\n        return c === 1 ? \"seconde\" : \"seconden\";\n      },\n      function (c) {\n        return c === 1 ? \"milliseconde\" : \"milliseconden\";\n      },\n      \",\"\n    ),\n    no: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uke\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minutt\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    pl: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"lata\", \"lat\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"miesiąc\", \"miesiąca\", \"miesiące\", \"miesięcy\"][\n          getPolishForm(c)\n        ];\n      },\n      function (c) {\n        return [\"tydzień\", \"tygodnia\", \"tygodnie\", \"tygodni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"dzień\", \"dnia\", \"dni\", \"dni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"godzina\", \"godziny\", \"godziny\", \"godzin\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getPolishForm(c)\n        ];\n      },\n      \",\"\n    ),\n    pt: language(\n      function (c) {\n        return \"ano\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return c === 1 ? \"mês\" : \"meses\";\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"dia\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milissegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ro: language(\n      function (c) {\n        return c === 1 ? \"an\" : \"ani\";\n      },\n      function (c) {\n        return c === 1 ? \"lună\" : \"luni\";\n      },\n      function (c) {\n        return c === 1 ? \"săptămână\" : \"săptămâni\";\n      },\n      function (c) {\n        return c === 1 ? \"zi\" : \"zile\";\n      },\n      function (c) {\n        return c === 1 ? \"oră\" : \"ore\";\n      },\n      function (c) {\n        return c === 1 ? \"minut\" : \"minute\";\n      },\n      function (c) {\n        return c === 1 ? \"secundă\" : \"secunde\";\n      },\n      function (c) {\n        return c === 1 ? \"milisecundă\" : \"milisecunde\";\n      },\n      \",\"\n    ),\n    ru: language(\n      function (c) {\n        return [\"лет\", \"год\", \"года\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месяцев\", \"месяц\", \"месяца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недель\", \"неделя\", \"недели\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дней\", \"день\", \"дня\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часов\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минут\", \"минута\", \"минуты\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунды\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"миллисекунд\", \"миллисекунда\", \"миллисекунды\"][\n          getSlavicForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sq: language(\n      function (c) {\n        return c === 1 ? \"vit\" : \"vjet\";\n      },\n      \"muaj\",\n      \"javë\",\n      \"ditë\",\n      \"orë\",\n      function (c) {\n        return \"minut\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"sekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"milisekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      \",\"\n    ),\n    sr: language(\n      function (c) {\n        return [\"години\", \"година\", \"године\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеци\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недељи\", \"недеља\", \"недеље\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дани\", \"дан\", \"дана\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"сати\", \"сат\", \"сата\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минута\", \"минут\", \"минута\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунде\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунде\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ta: language(\n      function (c) {\n        return c === 1 ? \"வருடம்\" : \"ஆண்டுகள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மாதம்\" : \"மாதங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"வாரம்\" : \"வாரங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"நாள்\" : \"நாட்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மணி\" : \"மணிநேரம்\";\n      },\n      function (c) {\n        return \"நிமிட\" + (c === 1 ? \"ம்\" : \"ங்கள்\");\n      },\n      function (c) {\n        return \"வினாடி\" + (c === 1 ? \"\" : \"கள்\");\n      },\n      function (c) {\n        return \"மில்லி விநாடி\" + (c === 1 ? \"\" : \"கள்\");\n      }\n    ),\n    te: language(\n      function (c) {\n        return \"సంవత్స\" + (c === 1 ? \"రం\" : \"రాల\");\n      },\n      function (c) {\n        return \"నెల\" + (c === 1 ? \"\" : \"ల\");\n      },\n      function (c) {\n        return c === 1 ? \"వారం\" : \"వారాలు\";\n      },\n      function (c) {\n        return \"రోజు\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return \"గంట\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return c === 1 ? \"నిమిషం\" : \"నిమిషాలు\";\n      },\n      function (c) {\n        return c === 1 ? \"సెకను\" : \"సెకన్లు\";\n      },\n      function (c) {\n        return c === 1 ? \"మిల్లీసెకన్\" : \"మిల్లీసెకన్లు\";\n      }\n    ),\n    uk: language(\n      function (c) {\n        return [\"років\", \"рік\", \"роки\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"місяців\", \"місяць\", \"місяці\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"тижнів\", \"тиждень\", \"тижні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"днів\", \"день\", \"дні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"годин\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"хвилин\", \"хвилина\", \"хвилини\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"мілісекунд\", \"мілісекунда\", \"мілісекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ur: language(\n      \"سال\",\n      function (c) {\n        return c === 1 ? \"مہینہ\" : \"مہینے\";\n      },\n      function (c) {\n        return c === 1 ? \"ہفتہ\" : \"ہفتے\";\n      },\n      \"دن\",\n      function (c) {\n        return c === 1 ? \"گھنٹہ\" : \"گھنٹے\";\n      },\n      \"منٹ\",\n      \"سیکنڈ\",\n      \"ملی سیکنڈ\"\n    ),\n    sk: language(\n      function (c) {\n        return [\"rok\", \"roky\", \"roky\", \"rokov\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"mesiac\", \"mesiace\", \"mesiace\", \"mesiacov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"týždeň\", \"týždne\", \"týždne\", \"týždňov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"deň\", \"dni\", \"dni\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodín\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minúta\", \"minúty\", \"minúty\", \"minút\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sl: language(\n      function (c) {\n        if (c % 10 === 1) {\n          return \"leto\";\n        } else if (c % 100 === 2) {\n          return \"leti\";\n        } else if (\n          c % 100 === 3 ||\n          c % 100 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 5)\n        ) {\n          return \"leta\";\n        } else {\n          return \"let\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"mesec\";\n        } else if (c % 100 === 2 || (Math.floor(c) !== c && c % 100 <= 5)) {\n          return \"meseca\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"mesece\";\n        } else {\n          return \"mesecev\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"teden\";\n        } else if (c % 10 === 2 || (Math.floor(c) !== c && c % 100 <= 4)) {\n          return \"tedna\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"tedne\";\n        } else {\n          return \"tednov\";\n        }\n      },\n      function (c) {\n        return c % 100 === 1 ? \"dan\" : \"dni\";\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"ura\";\n        } else if (c % 100 === 2) {\n          return \"uri\";\n        } else if (c % 10 === 3 || c % 10 === 4 || Math.floor(c) !== c) {\n          return \"ure\";\n        } else {\n          return \"ur\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"minuta\";\n        } else if (c % 10 === 2) {\n          return \"minuti\";\n        } else if (\n          c % 10 === 3 ||\n          c % 10 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 4)\n        ) {\n          return \"minute\";\n        } else {\n          return \"minut\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"sekunda\";\n        } else if (c % 100 === 2) {\n          return \"sekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"sekunde\";\n        } else {\n          return \"sekund\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"milisekunda\";\n        } else if (c % 100 === 2) {\n          return \"milisekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"milisekunde\";\n        } else {\n          return \"milisekund\";\n        }\n      },\n      \",\"\n    ),\n    sv: language(\n      \"år\",\n      function (c) {\n        return \"månad\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"veck\" + (c === 1 ? \"a\" : \"or\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"ar\");\n      },\n      function (c) {\n        return \"timm\" + (c === 1 ? \"e\" : \"ar\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    sw: assign(\n      language(\n        function (c) {\n          return c === 1 ? \"mwaka\" : \"miaka\";\n        },\n        function (c) {\n          return c === 1 ? \"mwezi\" : \"miezi\";\n        },\n        \"wiki\",\n        function (c) {\n          return c === 1 ? \"siku\" : \"masiku\";\n        },\n        function (c) {\n          return c === 1 ? \"saa\" : \"masaa\";\n        },\n        \"dakika\",\n        \"sekunde\",\n        \"milisekunde\"\n      ),\n      { _numberFirst: true }\n    ),\n    tr: language(\n      \"yıl\",\n      \"ay\",\n      \"hafta\",\n      \"gün\",\n      \"saat\",\n      \"dakika\",\n      \"saniye\",\n      \"milisaniye\",\n      \",\"\n    ),\n    th: language(\n      \"ปี\",\n      \"เดือน\",\n      \"สัปดาห์\",\n      \"วัน\",\n      \"ชั่วโมง\",\n      \"นาที\",\n      \"วินาที\",\n      \"มิลลิวินาที\"\n    ),\n    uz: language(\n      \"yil\",\n      \"oy\",\n      \"hafta\",\n      \"kun\",\n      \"soat\",\n      \"minut\",\n      \"sekund\",\n      \"millisekund\"\n    ),\n    uz_CYR: language(\n      \"йил\",\n      \"ой\",\n      \"ҳафта\",\n      \"кун\",\n      \"соат\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    vi: language(\n      \"năm\",\n      \"tháng\",\n      \"tuần\",\n      \"ngày\",\n      \"giờ\",\n      \"phút\",\n      \"giây\",\n      \"mili giây\",\n      \",\"\n    ),\n    zh_CN: language(\"年\", \"个月\", \"周\", \"天\", \"小时\", \"分钟\", \"秒\", \"毫秒\"),\n    zh_TW: language(\"年\", \"個月\", \"周\", \"天\", \"小時\", \"分鐘\", \"秒\", \"毫秒\")\n  };\n\n  /**\n   * Helper function for creating language definitions.\n   *\n   * @internal\n   * @param {Unit} y\n   * @param {Unit} mo\n   * @param {Unit} w\n   * @param {Unit} d\n   * @param {Unit} h\n   * @param {Unit} m\n   * @param {Unit} s\n   * @param {Unit} ms\n   * @param {string} [decimal]\n   * @returns {Language}\n   */\n  function language(y, mo, w, d, h, m, s, ms, decimal) {\n    /** @type {Language} */\n    var result = { y: y, mo: mo, w: w, d: d, h: h, m: m, s: s, ms: ms };\n    if (typeof decimal !== \"undefined\") {\n      result.decimal = decimal;\n    }\n    return result;\n  }\n\n  /**\n   * Helper function for Arabic.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getArabicForm(c) {\n    if (c === 2) {\n      return 1;\n    }\n    if (c > 2 && c < 11) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Polish.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getPolishForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && !(c % 100 > 10 && c % 100 < 20)) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Slavic languages.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getSlavicForm(c) {\n    if (Math.floor(c) !== c) {\n      return 2;\n    }\n    if (\n      (c % 100 >= 5 && c % 100 <= 20) ||\n      (c % 10 >= 5 && c % 10 <= 9) ||\n      c % 10 === 0\n    ) {\n      return 0;\n    }\n    if (c % 10 === 1) {\n      return 1;\n    }\n    if (c > 1) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Czech or Slovak.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getCzechOrSlovakForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && c % 100 < 10) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Lithuanian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getLithuanianForm(c) {\n    if (c === 1 || (c % 10 === 1 && c % 100 > 20)) {\n      return 0;\n    }\n    if (\n      Math.floor(c) !== c ||\n      (c % 10 >= 2 && c % 100 > 20) ||\n      (c % 10 >= 2 && c % 100 < 10)\n    ) {\n      return 1;\n    }\n    return 2;\n  }\n\n  /**\n   * Helper function for Latvian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {boolean}\n   */\n  function getLatvianForm(c) {\n    return c % 10 === 1 && c % 100 !== 11;\n  }\n\n  /**\n   * @internal\n   * @template T\n   * @param {T} obj\n   * @param {keyof T} key\n   * @returns {boolean}\n   */\n  function has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }\n\n  /**\n   * @internal\n   * @param {Pick<Required<Options>, \"language\" | \"fallbacks\" | \"languages\">} options\n   * @throws {Error} Throws an error if language is not found.\n   * @returns {Language}\n   */\n  function getLanguage(options) {\n    var possibleLanguages = [options.language];\n\n    if (has(options, \"fallbacks\")) {\n      if (isArray(options.fallbacks) && options.fallbacks.length) {\n        possibleLanguages = possibleLanguages.concat(options.fallbacks);\n      } else {\n        throw new Error(\"fallbacks must be an array with at least one element\");\n      }\n    }\n\n    for (var i = 0; i < possibleLanguages.length; i++) {\n      var languageToTry = possibleLanguages[i];\n      if (has(options.languages, languageToTry)) {\n        return options.languages[languageToTry];\n      }\n      if (has(LANGUAGES, languageToTry)) {\n        return LANGUAGES[languageToTry];\n      }\n    }\n\n    throw new Error(\"No language found.\");\n  }\n\n  /**\n   * @internal\n   * @param {Piece} piece\n   * @param {Language} language\n   * @param {Pick<Required<Options>, \"decimal\" | \"spacer\" | \"maxDecimalPoints\" | \"digitReplacements\">} options\n   */\n  function renderPiece(piece, language, options) {\n    var unitName = piece.unitName;\n    var unitCount = piece.unitCount;\n\n    var spacer = options.spacer;\n    var maxDecimalPoints = options.maxDecimalPoints;\n\n    /** @type {string} */\n    var decimal;\n    if (has(options, \"decimal\")) {\n      decimal = options.decimal;\n    } else if (has(language, \"decimal\")) {\n      decimal = language.decimal;\n    } else {\n      decimal = \".\";\n    }\n\n    /** @type {undefined | DigitReplacements} */\n    var digitReplacements;\n    if (\"digitReplacements\" in options) {\n      digitReplacements = options.digitReplacements;\n    } else if (\"_digitReplacements\" in language) {\n      digitReplacements = language._digitReplacements;\n    }\n\n    /** @type {string} */\n    var formattedCount;\n    var normalizedUnitCount =\n      maxDecimalPoints === void 0\n        ? unitCount\n        : Math.floor(unitCount * Math.pow(10, maxDecimalPoints)) /\n          Math.pow(10, maxDecimalPoints);\n    var countStr = normalizedUnitCount.toString();\n\n    if (language._hideCountIf2 && unitCount === 2) {\n      formattedCount = \"\";\n      spacer = \"\";\n    } else {\n      if (digitReplacements) {\n        formattedCount = \"\";\n        for (var i = 0; i < countStr.length; i++) {\n          var char = countStr[i];\n          if (char === \".\") {\n            formattedCount += decimal;\n          } else {\n            // @ts-ignore because `char` should always be 0-9 at this point.\n            formattedCount += digitReplacements[char];\n          }\n        }\n      } else {\n        formattedCount = countStr.replace(\".\", decimal);\n      }\n    }\n\n    var languageWord = language[unitName];\n    var word;\n    if (typeof languageWord === \"function\") {\n      word = languageWord(unitCount);\n    } else {\n      word = languageWord;\n    }\n\n    if (language._numberFirst) {\n      return word + spacer + formattedCount;\n    }\n    return formattedCount + spacer + word;\n  }\n\n  /**\n   * @internal\n   * @typedef {Object} Piece\n   * @prop {UnitName} unitName\n   * @prop {number} unitCount\n   */\n\n  /**\n   * @internal\n   * @param {number} ms\n   * @param {Pick<Required<Options>, \"units\" | \"unitMeasures\" | \"largest\" | \"round\">} options\n   * @returns {Piece[]}\n   */\n  function getPieces(ms, options) {\n    /** @type {UnitName} */\n    var unitName;\n\n    /** @type {number} */\n    var i;\n\n    /** @type {number} */\n    var unitCount;\n\n    /** @type {number} */\n    var msRemaining;\n\n    var units = options.units;\n    var unitMeasures = options.unitMeasures;\n    var largest = \"largest\" in options ? options.largest : Infinity;\n\n    if (!units.length) return [];\n\n    // Get the counts for each unit. Doesn't round or truncate anything.\n    // For example, might create an object like `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`.\n    /** @type {Partial<Record<UnitName, number>>} */\n    var unitCounts = {};\n    msRemaining = ms;\n    for (i = 0; i < units.length; i++) {\n      unitName = units[i];\n      var unitMs = unitMeasures[unitName];\n\n      var isLast = i === units.length - 1;\n      unitCount = isLast\n        ? msRemaining / unitMs\n        : Math.floor(msRemaining / unitMs);\n      unitCounts[unitName] = unitCount;\n\n      msRemaining -= unitCount * unitMs;\n    }\n\n    if (options.round) {\n      // Update counts based on the `largest` option.\n      // For example, if `largest === 2` and `unitCount` is `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`,\n      // updates to something like `{ y: 7, m: 6.2 }`.\n      var unitsRemainingBeforeRound = largest;\n      for (i = 0; i < units.length; i++) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        unitsRemainingBeforeRound--;\n\n        // \"Take\" the rest of the units into this one.\n        if (unitsRemainingBeforeRound === 0) {\n          for (var j = i + 1; j < units.length; j++) {\n            var smallerUnitName = units[j];\n            var smallerUnitCount = unitCounts[smallerUnitName];\n            unitCounts[unitName] +=\n              (smallerUnitCount * unitMeasures[smallerUnitName]) /\n              unitMeasures[unitName];\n            unitCounts[smallerUnitName] = 0;\n          }\n          break;\n        }\n      }\n\n      // Round the last piece (which should be the only non-integer).\n      //\n      // This can be a little tricky if the last piece \"bubbles up\" to a larger\n      // unit. For example, \"3 days, 23.99 hours\" should be rounded to \"4 days\".\n      // It can also require multiple passes. For example, \"6 days, 23.99 hours\"\n      // should become \"1 week\".\n      for (i = units.length - 1; i >= 0; i--) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        var rounded = Math.round(unitCount);\n        unitCounts[unitName] = rounded;\n\n        if (i === 0) break;\n\n        var previousUnitName = units[i - 1];\n        var previousUnitMs = unitMeasures[previousUnitName];\n        var amountOfPreviousUnit = Math.floor(\n          (rounded * unitMeasures[unitName]) / previousUnitMs\n        );\n        if (amountOfPreviousUnit) {\n          unitCounts[previousUnitName] += amountOfPreviousUnit;\n          unitCounts[unitName] = 0;\n        } else {\n          break;\n        }\n      }\n    }\n\n    /** @type {Piece[]} */\n    var result = [];\n    for (i = 0; i < units.length && result.length < largest; i++) {\n      unitName = units[i];\n      unitCount = unitCounts[unitName];\n      if (unitCount) {\n        result.push({ unitName: unitName, unitCount: unitCount });\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @internal\n   * @param {Piece[]} pieces\n   * @param {Pick<Required<Options>, \"units\" | \"language\" | \"languages\" | \"fallbacks\" | \"delimiter\" | \"spacer\" | \"decimal\" | \"conjunction\" | \"maxDecimalPoints\" | \"serialComma\" | \"digitReplacements\">} options\n   * @returns {string}\n   */\n  function formatPieces(pieces, options) {\n    var language = getLanguage(options);\n\n    if (!pieces.length) {\n      var units = options.units;\n      var smallestUnitName = units[units.length - 1];\n      return renderPiece(\n        { unitName: smallestUnitName, unitCount: 0 },\n        language,\n        options\n      );\n    }\n\n    var conjunction = options.conjunction;\n    var serialComma = options.serialComma;\n\n    var delimiter;\n    if (has(options, \"delimiter\")) {\n      delimiter = options.delimiter;\n    } else if (has(language, \"delimiter\")) {\n      delimiter = language.delimiter;\n    } else {\n      delimiter = \", \";\n    }\n\n    /** @type {string[]} */\n    var renderedPieces = [];\n    for (var i = 0; i < pieces.length; i++) {\n      renderedPieces.push(renderPiece(pieces[i], language, options));\n    }\n\n    if (!conjunction || pieces.length === 1) {\n      return renderedPieces.join(delimiter);\n    }\n\n    if (pieces.length === 2) {\n      return renderedPieces.join(conjunction);\n    }\n\n    return (\n      renderedPieces.slice(0, -1).join(delimiter) +\n      (serialComma ? \",\" : \"\") +\n      conjunction +\n      renderedPieces.slice(-1)\n    );\n  }\n\n  /**\n   * Create a humanizer, which lets you change the default options.\n   *\n   * @param {Options} [passedOptions]\n   */\n  function humanizer(passedOptions) {\n    /**\n     * @param {number} ms\n     * @param {Options} [humanizerOptions]\n     * @returns {string}\n     */\n    var result = function humanizer(ms, humanizerOptions) {\n      // Make sure we have a positive number.\n      //\n      // Has the nice side-effect of converting things to numbers. For example,\n      // converts `\"123\"` and `Number(123)` to `123`.\n      ms = Math.abs(ms);\n\n      var options = assign({}, result, humanizerOptions || {});\n\n      var pieces = getPieces(ms, options);\n\n      return formatPieces(pieces, options);\n    };\n\n    return assign(\n      result,\n      {\n        language: \"en\",\n        spacer: \" \",\n        conjunction: \"\",\n        serialComma: true,\n        units: [\"y\", \"mo\", \"w\", \"d\", \"h\", \"m\", \"s\"],\n        languages: {},\n        round: false,\n        unitMeasures: {\n          y: 31557600000,\n          mo: 2629800000,\n          w: 604800000,\n          d: 86400000,\n          h: 3600000,\n          m: 60000,\n          s: 1000,\n          ms: 1\n        }\n      },\n      passedOptions\n    );\n  }\n\n  /**\n   * Humanize a duration.\n   *\n   * This is a wrapper around the default humanizer.\n   */\n  var humanizeDuration = assign(humanizer({}), {\n    getSupportedLanguages: function getSupportedLanguages() {\n      var result = [];\n      for (var language in LANGUAGES) {\n        if (has(LANGUAGES, language) && language !== \"gr\") {\n          result.push(language);\n        }\n      }\n      return result;\n    },\n    humanizer: humanizer\n  });\n\n  // @ts-ignore\n  if (typeof define === \"function\" && define.amd) {\n    // @ts-ignore\n    define(function () {\n      return humanizeDuration;\n    });\n  } else if (typeof module !== \"undefined\" && module.exports) {\n    module.exports = humanizeDuration;\n  } else {\n    this.humanizeDuration = humanizeDuration;\n  }\n})();\n", "<template>\n  <span>{{ formatted }}</span>\n</template>\n\n<script lang=\"ts\">\nimport dayjs, { Dayjs } from \"dayjs\";\nimport { computed, defineComponent, PropType } from \"vue\";\nimport { merge } from \"lodash-es\";\nimport humanizeDuration, { HumanizerOptions } from \"humanize-duration\";\n\nconst defaultOptions = {\n  language: \"zh_CN\",\n  largest: 1,\n  maxDecimalPoints: 0\n};\n/**\n * 日期人性化格式展示组件\n * 例如几天前，几分钟前，几个小时前\n */\nexport default defineComponent({\n  name: \"FsTimeHumanize\",\n  props: {\n    /**\n     * 日期时间值，支持long,string,date等，由dayjs转化\n     */\n    modelValue: { required: false, default: undefined },\n    /**\n     *  输入格式化，不传则由dayjs自动转化\n     */\n    valueFormat: { type: String, default: undefined, required: false },\n    /**\n     *  日期输出格式化\n     */\n    format: { type: String, default: \"YYYY-MM-DD HH:mm:ss\", required: false },\n\n    /**\n     * 距离时间超过多少毫秒时，直接使用format格式，默认大于3天后\n     */\n    useFormatGreater: { type: Number, default: 1000 * 60 * 60 * 24 * 3, required: false },\n\n    /**\n     * HumanizeDuration参数\n     * https://github.com/EvanHahn/HumanizeDuration.js\n     */\n    options: {\n      type: Object as PropType<HumanizerOptions>,\n      default() {\n        return {};\n      }\n    },\n\n    /**\n     * 前后文本\n     */\n    text: {\n      type: Object as PropType<{ prev: string; after: string }>,\n      default() {\n        return {};\n      }\n    }\n  },\n  setup(props: any) {\n    const formatted = computed(() => {\n      if (props.modelValue == null || props.modelValue === \"\") {\n        return \"\";\n      }\n\n      let date: Dayjs;\n      if (props.valueFormat != null) {\n        date = dayjs(props.modelValue, props.valueFormat);\n      } else {\n        date = dayjs(props.modelValue);\n      }\n\n      let duration = dayjs().valueOf() - date.valueOf();\n      let suffix = props.text.ago ?? \"前\";\n      if (duration < 0) {\n        suffix = props.text.after ?? \"后\";\n        duration = -duration;\n      }\n      if (duration > props.useFormatGreater) {\n        //间隔时长超过3天，则直接显示格式化时间\n        return date.format(props.format);\n      }\n      return humanizeDuration(duration, merge({}, defaultOptions, props.options)) + suffix;\n    });\n\n    return {\n      formatted\n    };\n  }\n});\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,GAAC,WAAY;AAEX,QAAIA,IACF,OAAO;IAEP,SAAUC,GAAa;AAErB,eADIC,GACKC,IAAI,GAAGA,IAAI,UAAU,QAAQA,KAAK;AACzCD,YAAS,UAAUC,CAAC;AACpB,iBAASC,MAAQF;AACXG,YAAIH,GAAQE,EAAI,MAClBH,EAAYG,EAAI,IAAIF,EAAOE,EAAI;MAGpC;AACD,aAAOH;IACb,GAGMK,IACF,MAAM,WACN,SAAUC,GAAK;AACb,aAAO,OAAO,UAAU,SAAS,KAAKA,CAAG,MAAM;IACrD,GAMMC,IAAQC;MACV,SAAUC,GAAG;AACX,eAAOA,MAAM,IAAI,WAAW;MAC7B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,UAAU;MAC5B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,aAAa;MAC/B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,SAAS;MAC3B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,QAAQ;MAC1B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,UAAU;MAC5B;MACD,SAAUA,GAAG;AACX,eAAOA,MAAM,IAAI,iBAAiB;MACnC;MACD,SAAUA,GAAG;AACX,gBAAQA,MAAM,IAAI,aAAa,cAAc;MAC9C;MACD;IACJ,GAMMC,IAAY;MACd,IAAIF;QACF;QACA,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,kBAAkBA,MAAM,IAAI,KAAK;QACzC;QACD;MACD;MACD,IAAID,EAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,OAAO,QAAQ,QAAQ;MACtE,IAAIT;QACFS;UACE,SAAUC,GAAG;AACX,mBAAO,CAAC,OAAO,SAAS,OAAO,EAAEE,EAAcF,CAAC,CAAC;UAClD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,OAAO,SAAS,MAAM,EAAEE,EAAcF,CAAC,CAAC;UACjD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,SAAS,WAAW,QAAQ,EAAEE,EAAcF,CAAC,CAAC;UACvD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,OAAO,SAAS,MAAM,EAAEE,EAAcF,CAAC,CAAC;UACjD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,QAAQ,UAAU,OAAO,EAAEE,EAAcF,CAAC,CAAC;UACpD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,SAAS,WAAW,OAAO,EAAEE,EAAcF,CAAC,CAAC;UACtD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,SAAS,WAAW,OAAO,EAAEE,EAAcF,CAAC,CAAC;UACtD;UACD,SAAUA,GAAG;AACX,mBAAO,CAAC,kBAAkB,mBAAmB,kBAAkB,EAC7DE,EAAcF,CAAC,CAC3B;UACS;UACD;QACD;QACD;UACE,WAAW;UACX,eAAe;UACf,oBAAoB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;QACtE;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACtD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QAC1D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,OAAO,OAAO,KAAK,EAAEG,EAAcH,CAAC,CAAC;QAC9C;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QAChD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QAC1D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,eAAe,aAAa,EAAEG,EAAcH,CAAC,CAAC;QACtE;QACD;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,QAAQA,MAAM,IAAI,MAAM;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,MAAM;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,eAAeA,MAAM,IAAI,KAAK;QACtC;QACD;MACD;MACD,KAAKD;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAEI,EAAqBJ,CAAC,CAAC;QAC9D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,UAAU,UAAU,QAAQ,EAAEI,EAAqBJ,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,SAAS,SAAS,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QACpE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAEI,EAAqBJ,CAAC,CAAC;QAC5D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAC/CI,EAAqBJ,CAAC,CAChC;QACO;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/DI,EAAqBJ,CAAC,CAChC;QACO;QACD;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,kBAAkBA,MAAM,IAAI,KAAK;QACzC;QACD;MACD;MACD,IAAIF;MACJ,IAAIC;QACF,SAAUC,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,aAAa;QAC/B;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,cAAcA,MAAM,IAAI,KAAK;QACrC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,kBAAkBA,MAAM,IAAI,KAAK;QACzC;QACD;MACD;MACD,IAAID;QACF;QACA,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,YAAY;QAC9B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,aAAa;QAC/B;QACD;QACA;QACA;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,QAAQA,KAAK,IAAI,MAAM;QAC/B;QACD;QACA,SAAUA,GAAG;AACX,iBAAO,aAAaA,KAAK,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,KAAK,IAAI,MAAM;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,KAAK,IAAI,MAAM;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,KAAK,IAAI,MAAM;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,KAAK,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,kBAAkBA,KAAK,IAAI,MAAM;QACzC;QACD;MACD;MACD,IAAIF;MACJ,IAAIC;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,cAAc;QAChC;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAIA,IAAI,OAAO,KAAKA,IAAI,OAAO,KAAKA,IAAI,OAAO,IACtC,WAEF;QACR;QACD,SAAUA,GAAG;AACX,iBAAIA,MAAM,IACD,WACEA,MAAM,KAAKA,MAAM,KAAKA,MAAM,IAC9B,YAEF;QACR;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,KAAKA,MAAM,KACjB,WAEF;QACR;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAIA,MAAM,IACD,QACEA,MAAM,KAAKA,MAAM,KAAKA,MAAM,IAC9B,SAEF;QACR;QACD,SAAUA,GAAG;AACX,cAAIK,IAAQL,IAAI;AAChB,kBAAKK,MAAU,KAAKA,MAAU,KAAKA,MAAU,OAAOL,IAAI,MAAMA,IAAI,MACzD,WAEF;QACR;QACD,SAAUA,GAAG;AACX,cAAIK,IAAQL,IAAI;AAChB,iBAAIK,MAAU,KAAM,KAAK,MAAML,CAAC,MAAMA,KAAKA,KAAK,MAAMA,KAAK,KAClD,YACEK,MAAU,IACZ,YACEA,MAAU,KAAKA,MAAU,KAAKA,MAAU,IAC1C,YAEF;QACR;QACD,SAAUL,GAAG;AACX,iBAAIA,MAAM,IACD,gBACEA,IAAI,OAAO,KAAKA,IAAI,OAAO,KAAKA,IAAI,OAAO,IAC7C,gBAEF;QACR;QACD;MACD;MACD,IAAID;QACF;QACA,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD;QACA,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD;QACA;QACA;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,OAAO;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,MAAM;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,OAAO;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,eAAeA,MAAM,IAAI,MAAM;QACvC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,MAAM;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,MAAM;QACzC;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,MAAM;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,MAAM;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,cAAcA,MAAM,IAAI,MAAM;QACtC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,MAAM;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,QAAQA,MAAM,IAAI,MAAM;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,MAAM;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,MAAM;QACzC;QACD;MACD;MACD,IAAID,EAAS,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK;MACvD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,YAAY;QAC9B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,gBAAgB;QAClC;MACF;MACD,IAAID,EAAS,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,MAAM;MACzD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAOA,IAAI,OAAO,KAAMA,IAAI,OAAO,MAAMA,IAAI,OAAO,KAChD,SACA;QACL;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,YAAY,SAAS,EAAEM,EAAkBN,CAAC,CAAC;QAC7D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,YAAY,UAAU,EAAEM,EAAkBN,CAAC,CAAC;QAChE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,UAAU,OAAO,EAAEM,EAAkBN,CAAC,CAAC;QACzD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,YAAY,SAAS,EAAEM,EAAkBN,CAAC,CAAC;QAC/D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAEM,EAAkBN,CAAC,CAAC;QAC7D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,YAAY,WAAW,EAAEM,EAAkBN,CAAC,CAAC;QACjE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,gBAAgB,eAAe,EACpDM,EAAkBN,CAAC,CAC7B;QACO;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,SAAS;QACrC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,YAAY;QACxC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,WAAW;QACvC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,UAAU;QACtC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,WAAW;QACvC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,WAAW;QACvC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,YAAY;QACxC;QACD,SAAUA,GAAG;AACX,iBAAOO,EAAeP,CAAC,IAAI,gBAAgB;QAC5C;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,YAAY;QAC9B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,gBAAgB;QAClC;QACD;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD;QACA;QACA,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD;QACA;MACD;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD;QACA,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,YAAY;QAC9B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,iBAAiB;QACnC;QACD;MACD;MACD,IAAID;QACF;QACA,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAEQ,EAAcR,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,YAAY,YAAY,UAAU,EACnDQ,EAAcR,CAAC,CACzB;QACO;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,YAAY,YAAY,SAAS,EAAEQ,EAAcR,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,QAAQ,OAAO,KAAK,EAAEQ,EAAcR,CAAC,CAAC;QACxD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAEQ,EAAcR,CAAC,CAAC;QACpE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAEQ,EAAcR,CAAC,CAAC;QAChE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAEQ,EAAcR,CAAC,CAAC;QACpE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/DQ,EAAcR,CAAC,CACzB;QACO;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,aAAaA,MAAM,IAAI,KAAK;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,kBAAkBA,MAAM,IAAI,KAAK;QACzC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,OAAO;QACzB;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,cAAc;QAChC;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,OAAO;QACzB;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,YAAY;QAC9B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,gBAAgB;QAClC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,OAAO,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QAC/C;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,SAAS,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAEG,EAAcH,CAAC,CAAC;QAChD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QACjD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACtD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QACzD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,gBAAgB,cAAc,EACnDG,EAAcH,CAAC,CACzB;QACO;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD;QACA;QACA;QACA;QACA,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,MAAM;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,MAAM;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,gBAAgBA,MAAM,IAAI,MAAM;QACxC;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACtD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QAChD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QAChD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACtD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QAC1D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,eAAe,aAAa,EAAEG,EAAcH,CAAC,CAAC;QACtE;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,QAAQ;QAC1B;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,OAAO;QACpC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,mBAAmBA,MAAM,IAAI,KAAK;QAC1C;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,OAAO;QACrC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,KAAK;QACjC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,WAAW;QAC7B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,gBAAgB;QAClC;MACF;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,SAAS,OAAO,MAAM,EAAEG,EAAcH,CAAC,CAAC;QACjD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACxD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,OAAO,EAAEG,EAAcH,CAAC,CAAC;QACvD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAEG,EAAcH,CAAC,CAAC;QAChD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,SAAS,UAAU,QAAQ,EAAEG,EAAcH,CAAC,CAAC;QACtD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QACzD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAEG,EAAcH,CAAC,CAAC;QACzD;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,cAAc,eAAe,aAAa,EAAEG,EAAcH,CAAC,CAAC;QACrE;QACD;MACD;MACD,IAAID;QACF;QACA,SAAUC,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,SAAS;QAC3B;QACD;QACA,SAAUA,GAAG;AACX,iBAAOA,MAAM,IAAI,UAAU;QAC5B;QACD;QACA;QACA;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAO,CAAC,OAAO,QAAQ,QAAQ,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QAChE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,WAAW,WAAW,UAAU,EAChDI,EAAqBJ,CAAC,CAChC;QACO;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,SAAS,EAC7CI,EAAqBJ,CAAC,CAChC;QACO;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAEI,EAAqBJ,CAAC,CAAC;QAC5D;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAEI,EAAqBJ,CAAC,CAAC;QACvE;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAC/CI,EAAqBJ,CAAC,CAChC;QACO;QACD,SAAUA,GAAG;AACX,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/DI,EAAqBJ,CAAC,CAChC;QACO;QACD;MACD;MACD,IAAID;QACF,SAAUC,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,SACEA,IAAI,QAAQ,IACd,SAEPA,IAAI,QAAQ,KACZA,IAAI,QAAQ,KACX,KAAK,MAAMA,CAAC,MAAMA,KAAKA,IAAI,OAAO,IAE5B,SAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,UACEA,IAAI,QAAQ,KAAM,KAAK,MAAMA,CAAC,MAAMA,KAAKA,IAAI,OAAO,IACtD,WACEA,IAAI,OAAO,KAAKA,IAAI,OAAO,IAC7B,WAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,UACEA,IAAI,OAAO,KAAM,KAAK,MAAMA,CAAC,MAAMA,KAAKA,IAAI,OAAO,IACrD,UACEA,IAAI,OAAO,KAAKA,IAAI,OAAO,IAC7B,UAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAOA,IAAI,QAAQ,IAAI,QAAQ;QAChC;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,QACEA,IAAI,QAAQ,IACd,QACEA,IAAI,OAAO,KAAKA,IAAI,OAAO,KAAK,KAAK,MAAMA,CAAC,MAAMA,IACpD,QAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,WACEA,IAAI,OAAO,IACb,WAEPA,IAAI,OAAO,KACXA,IAAI,OAAO,KACV,KAAK,MAAMA,CAAC,MAAMA,KAAKA,IAAI,OAAO,IAE5B,WAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,YACEA,IAAI,QAAQ,IACd,YACEA,IAAI,QAAQ,KAAKA,IAAI,QAAQ,KAAK,KAAK,MAAMA,CAAC,MAAMA,IACtD,YAEA;QAEV;QACD,SAAUA,GAAG;AACX,iBAAIA,IAAI,OAAO,IACN,gBACEA,IAAI,QAAQ,IACd,gBACEA,IAAI,QAAQ,KAAKA,IAAI,QAAQ,KAAK,KAAK,MAAMA,CAAC,MAAMA,IACtD,gBAEA;QAEV;QACD;MACD;MACD,IAAID;QACF;QACA,SAAUC,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,MAAM;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,SAASA,MAAM,IAAI,KAAK;QAChC;QACD,SAAUA,GAAG;AACX,iBAAO,UAAUA,MAAM,IAAI,MAAM;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,WAAWA,MAAM,IAAI,KAAK;QAClC;QACD,SAAUA,GAAG;AACX,iBAAO,YAAYA,MAAM,IAAI,KAAK;QACnC;QACD,SAAUA,GAAG;AACX,iBAAO,iBAAiBA,MAAM,IAAI,KAAK;QACxC;QACD;MACD;MACD,IAAIV;QACFS;UACE,SAAUC,GAAG;AACX,mBAAOA,MAAM,IAAI,UAAU;UAC5B;UACD,SAAUA,GAAG;AACX,mBAAOA,MAAM,IAAI,UAAU;UAC5B;UACD;UACA,SAAUA,GAAG;AACX,mBAAOA,MAAM,IAAI,SAAS;UAC3B;UACD,SAAUA,GAAG;AACX,mBAAOA,MAAM,IAAI,QAAQ;UAC1B;UACD;UACA;UACA;QACD;QACD,EAAE,cAAc,KAAM;MACvB;MACD,IAAID;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,QAAQA;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,IAAIA;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACD;MACD,OAAOA,EAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;MAC1D,OAAOA,EAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;IAC9D;AAiBE,aAASA,EAASU,GAAGC,GAAIC,GAAGC,IAAGC,GAAGC,GAAGC,GAAGC,GAAIC,GAAS;AAEnD,UAAIC,IAAS,EAAE,GAAGT,GAAG,IAAIC,GAAI,GAAGC,GAAG,GAAGC,IAAG,GAAGC,GAAG,GAAGC,GAAG,GAAGC,GAAG,IAAIC,EAAAA;AAC/D,aAAI,OAAOC,IAAY,QACrBC,EAAO,UAAUD,IAEZC;IACR;AASD,aAAShB,EAAcF,GAAG;AACxB,aAAIA,MAAM,IACD,IAELA,IAAI,KAAKA,IAAI,KACR,IAEF;IACR;AASD,aAASQ,EAAcR,GAAG;AACxB,aAAIA,MAAM,IACD,IAEL,KAAK,MAAMA,CAAC,MAAMA,IACb,IAELA,IAAI,MAAM,KAAKA,IAAI,MAAM,KAAK,EAAEA,IAAI,MAAM,MAAMA,IAAI,MAAM,MACrD,IAEF;IACR;AASD,aAASG,EAAcH,GAAG;AACxB,aAAI,KAAK,MAAMA,CAAC,MAAMA,IACb,IAGNA,IAAI,OAAO,KAAKA,IAAI,OAAO,MAC3BA,IAAI,MAAM,KAAKA,IAAI,MAAM,KAC1BA,IAAI,OAAO,IAEJ,IAELA,IAAI,OAAO,IACN,IAELA,IAAI,IACC,IAEF;IACR;AASD,aAASI,EAAqBJ,GAAG;AAC/B,aAAIA,MAAM,IACD,IAEL,KAAK,MAAMA,CAAC,MAAMA,IACb,IAELA,IAAI,MAAM,KAAKA,IAAI,MAAM,KAAKA,IAAI,MAAM,KACnC,IAEF;IACR;AASD,aAASM,EAAkBN,GAAG;AAC5B,aAAIA,MAAM,KAAMA,IAAI,OAAO,KAAKA,IAAI,MAAM,KACjC,IAGP,KAAK,MAAMA,CAAC,MAAMA,KACjBA,IAAI,MAAM,KAAKA,IAAI,MAAM,MACzBA,IAAI,MAAM,KAAKA,IAAI,MAAM,KAEnB,IAEF;IACR;AASD,aAASO,EAAeP,GAAG;AACzB,aAAOA,IAAI,OAAO,KAAKA,IAAI,QAAQ;IACpC;AASD,aAASL,EAAIwB,GAAKC,GAAK;AACrB,aAAO,OAAO,UAAU,eAAe,KAAKD,GAAKC,CAAG;IACrD;AAQD,aAASC,EAAYC,GAAS;AAC5B,UAAIC,IAAoB,CAACD,EAAQ,QAAQ;AAEzC,UAAI3B,EAAI2B,GAAS,WAAW;AAC1B,YAAI1B,EAAQ0B,EAAQ,SAAS,KAAKA,EAAQ,UAAU;AAClDC,cAAoBA,EAAkB,OAAOD,EAAQ,SAAS;;AAE9D,gBAAM,IAAI,MAAM,sDAAsD;AAI1E,eAAS7B,IAAI,GAAGA,IAAI8B,EAAkB,QAAQ9B,KAAK;AACjD,YAAI+B,KAAgBD,EAAkB9B,CAAC;AACvC,YAAIE,EAAI2B,EAAQ,WAAWE,EAAa;AACtC,iBAAOF,EAAQ,UAAUE,EAAa;AAExC,YAAI7B,EAAIM,GAAWuB,EAAa;AAC9B,iBAAOvB,EAAUuB,EAAa;MAEjC;AAED,YAAM,IAAI,MAAM,oBAAoB;IACrC;AAQD,aAASC,EAAYC,GAAO3B,GAAUuB,GAAS;AAC7C,UAAIK,KAAWD,EAAM,UACjBE,IAAYF,EAAM,WAElBG,IAASP,EAAQ,QACjBQ,IAAmBR,EAAQ,kBAG3BL;AACAtB,QAAI2B,GAAS,SAAS,IACxBL,IAAUK,EAAQ,UACT3B,EAAII,GAAU,SAAS,IAChCkB,IAAUlB,EAAS,UAEnBkB,IAAU;AAIZ,UAAIc;AACA,6BAAuBT,IACzBS,IAAoBT,EAAQ,oBACnB,wBAAwBvB,MACjCgC,IAAoBhC,EAAS;AAI/B,UAAIiC,GACAC,IACFH,MAAqB,SACjBF,IACA,KAAK,MAAMA,IAAY,KAAK,IAAI,IAAIE,CAAgB,CAAC,IACrD,KAAK,IAAI,IAAIA,CAAgB,GAC/BI,IAAWD,EAAoB,SAAA;AAEnC,UAAIlC,EAAS,iBAAiB6B,MAAc;AAC1CI,YAAiB,IACjBH,IAAS;eAELE,GAAmB;AACrBC,YAAiB;AACjB,iBAASvC,IAAI,GAAGA,IAAIyC,EAAS,QAAQzC,KAAK;AACxC,cAAI0C,IAAOD,EAASzC,CAAC;AACjB0C,gBAAS,MACXH,KAAkBf,IAGlBe,KAAkBD,EAAkBI,CAAI;QAE3C;MACT;AACQH,YAAiBE,EAAS,QAAQ,KAAKjB,CAAO;AAIlD,UAAImB,IAAerC,EAAS4B,EAAQ,GAChCU;AAOJ,aANI,OAAOD,KAAiB,aAC1BC,IAAOD,EAAaR,CAAS,IAE7BS,IAAOD,GAGLrC,EAAS,eACJsC,IAAOR,IAASG,IAElBA,IAAiBH,IAASQ;IAClC;AAeD,aAASC,EAAUtB,GAAIM,GAAS;AAE9B,UAAIK,GAGAlC,IAGAmC,GAGAW,GAEAC,IAAQlB,EAAQ,OAChBmB,IAAenB,EAAQ,cACvBoB,IAAU,aAAapB,IAAUA,EAAQ,UAAU,IAAA;AAEvD,UAAI,CAACkB,EAAM;AAAQ,eAAO,CAAA;AAK1B,UAAIG,IAAa,CAAA;AAEjB,WADAJ,IAAcvB,GACTvB,KAAI,GAAGA,KAAI+C,EAAM,QAAQ/C,MAAK;AACjCkC,YAAWa,EAAM/C,EAAC;AAClB,YAAImD,IAASH,EAAad,CAAQ,GAE9BkB,IAASpD,OAAM+C,EAAM,SAAS;AAClCZ,YAAYiB,IACRN,IAAcK,IACd,KAAK,MAAML,IAAcK,CAAM,GACnCD,EAAWhB,CAAQ,IAAIC,GAEvBW,KAAeX,IAAYgB;MAC5B;AAED,UAAItB,EAAQ,OAAO;AAIjB,YAAIwB,IAA4BJ;AAChC,aAAKjD,KAAI,GAAGA,KAAI+C,EAAM,QAAQ/C;AAI5B,cAHAkC,IAAWa,EAAM/C,EAAC,GAClBmC,IAAYe,EAAWhB,CAAQ,GAE3BC,MAAc,MAElBkB,KAGIA,MAA8B,IAAG;AACnC,qBAASC,IAAItD,KAAI,GAAGsD,IAAIP,EAAM,QAAQO,KAAK;AACzC,kBAAIC,IAAkBR,EAAMO,CAAC,GACzBE,IAAmBN,EAAWK,CAAe;AACjDL,gBAAWhB,CAAQ,KAChBsB,IAAmBR,EAAaO,CAAe,IAChDP,EAAad,CAAQ,GACvBgB,EAAWK,CAAe,IAAI;YAC/B;AACD;UACD;AASH,aAAKvD,KAAI+C,EAAM,SAAS,GAAG/C,MAAK,GAAGA;AAIjC,cAHAkC,IAAWa,EAAM/C,EAAC,GAClBmC,IAAYe,EAAWhB,CAAQ,GAE3BC,MAAc,GAElB;AAAA,gBAAIsB,IAAU,KAAK,MAAMtB,CAAS;AAGlC,gBAFAe,EAAWhB,CAAQ,IAAIuB,GAEnBzD,OAAM;AAAG;AAEb,gBAAI0D,IAAmBX,EAAM/C,KAAI,CAAC,GAC9B2D,IAAiBX,EAAaU,CAAgB,GAC9CE,IAAuB,KAAK;cAC7BH,IAAUT,EAAad,CAAQ,IAAKyB;YAC/C;AACQ,gBAAIC;AACFV,gBAAWQ,CAAgB,KAAKE,GAChCV,EAAWhB,CAAQ,IAAI;;AAEvB;UAAA;MAGL;AAGD,UAAIT,IAAS,CAAA;AACb,WAAKzB,KAAI,GAAGA,KAAI+C,EAAM,UAAUtB,EAAO,SAASwB,GAASjD;AACvDkC,YAAWa,EAAM/C,EAAC,GAClBmC,IAAYe,EAAWhB,CAAQ,GAC3BC,KACFV,EAAO,KAAK,EAAE,UAAUS,GAAU,WAAWC,EAAS,CAAE;AAG5D,aAAOV;IACR;AAQD,aAASoC,EAAaC,GAAQjC,GAAS;AACrC,UAAIvB,IAAWsB,EAAYC,CAAO;AAElC,UAAI,CAACiC,EAAO,QAAQ;AAClB,YAAIf,KAAQlB,EAAQ,OAChBkC,IAAmBhB,GAAMA,GAAM,SAAS,CAAC;AAC7C,eAAOf;UACL,EAAE,UAAU+B,GAAkB,WAAW,EAAG;UAC5CzD;UACAuB;QACR;MACK;AAED,UAAImC,IAAcnC,EAAQ,aACtBoC,IAAcpC,EAAQ,aAEtBqC;AACAhE,QAAI2B,GAAS,WAAW,IAC1BqC,IAAYrC,EAAQ,YACX3B,EAAII,GAAU,WAAW,IAClC4D,IAAY5D,EAAS,YAErB4D,IAAY;AAKd,eADIC,IAAiB,CAAA,GACZnE,IAAI,GAAGA,IAAI8D,EAAO,QAAQ9D;AACjCmE,UAAe,KAAKnC,EAAY8B,EAAO9D,CAAC,GAAGM,GAAUuB,CAAO,CAAC;AAG/D,aAAI,CAACmC,KAAeF,EAAO,WAAW,IAC7BK,EAAe,KAAKD,CAAS,IAGlCJ,EAAO,WAAW,IACbK,EAAe,KAAKH,CAAW,IAItCG,EAAe,MAAM,GAAG,EAAE,EAAE,KAAKD,CAAS,KACzCD,IAAc,MAAM,MACrBD,IACAG,EAAe,MAAM,EAAE;IAE1B;AAOD,aAASC,EAAUC,GAAe;AAMhC,UAAI5C,IAAS,SAAmBF,IAAI+C,GAAkB;AAKpD/C,QAAAA,KAAK,KAAK,IAAIA,EAAE;AAEhB,YAAIM,IAAUhC,EAAO,CAAA,GAAI4B,GAAQ6C,KAAoB,CAAA,CAAE,GAEnDR,IAASjB,EAAUtB,IAAIM,CAAO;AAElC,eAAOgC,EAAaC,GAAQjC,CAAO;MACzC;AAEI,aAAOhC;QACL4B;QACA;UACE,UAAU;UACV,QAAQ;UACR,aAAa;UACb,aAAa;UACb,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;UAC1C,WAAW,CAAE;UACb,OAAO;UACP,cAAc;YACZ,GAAG;YACH,IAAI;YACJ,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,IAAI;UACL;QACF;QACD4C;MACN;IACG;AAOD,QAAIE,IAAmB1E,EAAOuE,EAAU,CAAE,CAAA,GAAG;MAC3C,uBAAuB,WAAiC;AACtD,YAAI3C,IAAS,CAAA;AACb,iBAASnB,KAAYE;AACfN,YAAIM,GAAWF,CAAQ,KAAKA,MAAa,QAC3CmB,EAAO,KAAKnB,CAAQ;AAGxB,eAAOmB;MACR;MACD,WAAW2C;IACf,CAAG;AAQ2CI,MAAO,UACjDA,EAAA,UAAiBD,IAEjB,KAAK,mBAAmBA;EAE5B,GAAA;;;;IC75DME,IAAiB;EACrB,UAAU;EACV,SAAS;EACT,kBAAkB;AACpB;IAKAC,IAAeC,gBAAgB;EAC7B,MAAM;EACN,OAAO;;;;IAIL,YAAY,EAAE,UAAU,OAAO,SAAS,OAAU;;;;IAIlD,aAAa,EAAE,MAAM,QAAQ,SAAS,QAAW,UAAU,MAAM;;;;IAIjE,QAAQ,EAAE,MAAM,QAAQ,SAAS,uBAAuB,UAAU,MAAM;;;;IAKxE,kBAAkB,EAAE,MAAM,QAAQ,SAAS,MAAO,KAAK,KAAK,KAAK,GAAG,UAAU,MAAM;;;;;IAMpF,SAAS;MACP,MAAM;MACN,UAAU;AACR,eAAO,CAAA;MACT;IACF;;;;IAKA,MAAM;MACJ,MAAM;MACN,UAAU;AACR,eAAO,CAAA;MACT;IACF;EACF;EACA,MAAMC,GAAY;AA0BT,WAAA;MACL,WA1BgBC,SAAS,MAAM;AAC/B,YAAID,EAAM,cAAc,QAAQA,EAAM,eAAe;AAC5C,iBAAA;AAGL,YAAAE;AACAF,UAAM,eAAe,OACvBE,QAAOC,aAAAA,SAAMH,EAAM,YAAYA,EAAM,WAAW,IAEzCE,QAAAC,aAAAA,SAAMH,EAAM,UAAU;AAG/B,YAAII,QAAWD,aAAAA,SAAM,EAAE,QAAQ,IAAID,EAAK,QAAA,GACpCG,IAASL,EAAM,KAAK,OAAO;AAK3B,eAJAI,IAAW,MACJC,IAAAL,EAAM,KAAK,SAAS,KAC7BI,IAAW,CAACA,IAEVA,IAAWJ,EAAM,mBAEZE,EAAK,OAAOF,EAAM,MAAM,IAE1BL,EAAiBS,GAAUE,cAAM,CAAA,GAAIT,GAAgBG,EAAM,OAAO,CAAC,IAAIK;MAAA,CAC/E;IAGC;EAEJ;AACF,CAAC;;;;;", "names": ["assign", "destination", "source", "i", "prop", "has", "isArray", "arg", "GREEK", "language", "c", "LANGUAGES", "getArabicForm", "getSlavicForm", "getCzechOrSlovakForm", "mod10", "getLithuanianForm", "getLatvianForm", "getPolishForm", "y", "mo", "w", "d", "h", "m", "s", "ms", "decimal", "result", "obj", "key", "getLanguage", "options", "possibleLanguages", "languageToTry", "renderPiece", "piece", "unitName", "unitCount", "spacer", "maxDecimalPoints", "digitReplacements", "formattedCount", "normalizedUnitCount", "countStr", "char", "languageWord", "word", "<PERSON><PERSON><PERSON><PERSON>", "msRemaining", "units", "unitMeasures", "largest", "unitCounts", "unitMs", "isLast", "unitsRemainingBeforeRound", "j", "smallerUnitName", "smallerUnitCount", "rounded", "previousUnitName", "previousUnitMs", "amountOfPreviousUnit", "formatPieces", "pieces", "smallestUnitName", "conjunction", "serialComma", "delimiter", "rendered<PERSON><PERSON>ces", "humanizer", "passedOptions", "humanizerOptions", "humanizeDuration", "module", "defaultOptions", "_sfc_main", "defineComponent", "props", "computed", "date", "dayjs", "duration", "suffix", "merge"]}