"""
自动关联文章模块

该模块实现了基于会话内容自动关联知识库文章的功能。
流程如下:
1. MQ消费者接收会话关闭消息 (@apps.mq_consumer.handlers.kcs_handlers)
2. 触发异步任务进行处理 (@apps.kcs.tasks.task__kcs_auto_link)
3. 获取会话内容并通过LLM获取归纳总结 get_qiyu_session_detail
4. 使用ES搜索相关文章 (@apps.es_search.actions.search_documents)
5. 通过LLM分析文章与会话的相关性 (@apps.kcs.utils.ai.get_ai_confidence_analysis)
6. 对置信度达标的文章进行关联 (@apps.kcs.models.Link)
"""
# import os
# import django
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
# django.setup()

import json
import logging
from json import JSONDecodeError
import re
from django.contrib.auth import get_user_model
from apps.common.LLM_api import LLM_api
# 移除此处的导入，避免循环引用
# from apps.kcs.tasks import task__ai_cs_performance_analysis # 导入新任务
logger = logging.getLogger(__name__)

def _get_qiyu_session_detail(session_id):
    from apps.common.qiyu_service import get_qiyu_service
    service = get_qiyu_service()
    # 第一步 获取全部的会话数据
    messages = service.get_session_messages(session_id)
    # 第二部 将会话数据格式化成Q&A格式
    qa_list = []
    for message in messages:
        if message.get("autoReply") == 1:
            continue
        msg_content = message.get("msg")
        if isinstance(msg_content, str) and msg_content.startswith("{"):
            try:
                msg_dict = json.loads(msg_content)
                # 跳过包含特定键的消息
                if any(key in msg_dict for key in ["cmd", "tagList"]):
                    continue
                if "url" in msg_dict:
                    msg_content = msg_dict["url"]
            except JSONDecodeError:
                # 如果解析失败，跳过当前消息
                continue
        if "人工" in msg_content or "转接" in msg_content:
            continue
        if "client?" in msg_content:
            continue
        if message.get("from") == 0:
            qa_list.append({"speaker":"a", "text": msg_content})
        else:
            qa_list.append({"speaker":"q", "text": msg_content})
    # 将qa转置排序
    qa_list.reverse()
    return qa_list

def _get_qiyu_session_game_id(session_id):
    """从会话详情中提取游戏ID
    
    Args:
        session_id: 会话ID
        
    Returns:
        Game.id 或 None (如果未找到或出错)
    """
    from apps.common.qiyu_service import get_qiyu_service
    from apps.common.utils.session_detail_get_game_id import get_game_id_from_session_detail
    
    service = get_qiyu_service()
    try:
        detail = service.get_session_detail(session_id)
        game = get_game_id_from_session_detail(detail)
        game_id = game.id if game else None
        return game_id
    except Exception as e:
        logger.error(f"[AutoLink] 会话 {session_id} 获取游戏ID失败: {e}")
        return None


def process_session_for_autolink(session_id: str or int, min_confidence: float = 0.7, max_links: int = 2) -> bool:
    """
    处理会话自动关联文章的完整流程 (已按最佳实践修改)

    :param session_id: 会话ID
    :param min_confidence: 置信度阈值
    :param max_links: 最多关联的文章数量
    :return: 处理是否成功 (是否有文章被关联)
    """
    # 确保导入路径正确
    from apps.kcs.models import Link, Article
    from apps.es_search.actions import search_documents

    try:
        logger.info(f"[AutoLink] 开始处理会话 {session_id}")

        # 1. 检查是否已存在手动或自动关联
        if Link.objects.filter(ticket_id=session_id).exists():
            logger.info(f"[AutoLink] 会话 {session_id} 已存在关联，跳过处理")
            return False

        # 2. 获取会话内容 & LLM分析会话 -> 获取结构化信息
        session_data = _get_qiyu_session_detail(session_id)
        game_id = _get_qiyu_session_game_id(session_id)
        # 如果游戏ID为空，则跳过处理
        if not game_id:
            logger.info(f"[AutoLink] 会话 {session_id} 没有游戏ID，跳过处理")
            return False
        if len(session_data) < 2: # 过滤掉过短对话
            logger.info(f"[AutoLink] 会话 {session_id} 对话轮次不足 (<2)，跳过处理")
            return False

        llm_api = LLM_api()
        # 获取LLM分析结果
        llm_result = llm_api.analysis_session_chat(session_data)

        # 尝试将LLM结果转换为JSON对象
        try:
            case_analysis_result = json.loads(llm_result)
        except JSONDecodeError:
            logger.info(f"[AutoLink] 会话 {session_id} LLM分析失败或未返回有效JSON")
            return False

        # 验证LLM结果
        if not case_analysis_result:
            logger.info(f"[AutoLink] 会话 {session_id} LLM分析结果为空")
            return False
            
        # 检查必要字段 (至少需要core_problem字段)
        if not case_analysis_result.get("core_problem"):
            logger.info(f"[AutoLink] 会话 {session_id} LLM分析未返回必要的字段")
            return False
        
        logger.info(f"[AutoLink] 会话 {session_id} LLM分析完成")

        # 触发LLM结果关键词分析任务 (对每篇候选文章)
        try:
            # 在函数内部导入，避免循环引用
            from apps.kcs.utils.kcs_alarm import task__analyze_llm_keywords
            # 不需要等待任务完成，异步执行即可
            task__analyze_llm_keywords.delay(llm_result=case_analysis_result, game_id=game_id, session_id=session_id)
            logger.info(f"[AutoLink] 已为会话 {session_id} 触发LLM关键词分析任务")
        except Exception as task_err:
            logger.error(f"[AutoLink] 触发LLM关键词分析任务失败: {task_err}", exc_info=True)
            # 继续执行，不影响后续流程

        # 3. 构建结构化查询数据
        structured_query_input = case_analysis_result
        
        # 设置过滤条件 - 按游戏ID筛选活跃文章
        filters = {'is_active': True}
        if game_id:
            filters['game_id'] = game_id

        # 4. 执行ES搜索
        search_result_list, total_count = search_documents(
            model=Article,
            query_string=case_analysis_result.get('core_problem', ''),
            filters=filters,
            page=1,
            page_size=5, # 获取 Top 5 候选文章
            include_scores=True, # 确保返回 _similarity
            structured_query_input=structured_query_input # 传递结构化查询数据
        )

        if not search_result_list:
            logger.info(f"[AutoLink] 会话 {session_id} ES未找到相关文章")
            return False

        logger.info(f"[AutoLink] 会话 {session_id} ES找到 {len(search_result_list)} 篇候选文章")

        # 5. 循环调用 LLM 进行置信度分析 (每次一篇)
        article_confidences = []
        for result_dict in search_result_list:
            try:
                # 5a. 准备单篇文章信息传递给 LLM
                es_similarity_info = result_dict.get('_similarity', {})
                es_score = es_similarity_info.get('final_score', 0) # 获取 ES 计算的总得分
                keyword_score = es_similarity_info.get('keyword_score', 0) 
                vector_similarity = es_similarity_info.get('vector_similarity', 0) 
                # 清理 content 中的 HTML 标签
                content_raw = result_dict.get('content', '')
                content_cleaned = re.sub(r'<.*?>', '', content_raw) if content_raw else ''
                # 截取片段避免过长，例如前 300 字符
                content_snippet = content_cleaned[:300] if content_cleaned else None

                article_info_for_llm = {
                    "article_id": result_dict.get('id'),
                    "title": result_dict.get('title'),
                    "summary": result_dict.get('summary'),
                    "tags": result_dict.get('tags'), # 假设 tags 是字符串或列表
                    "es_score": es_score, # 将 ES 分数传递给 LLM 参考
                    "es_keyword_score": keyword_score, 
                    "es_vector_similarity": vector_similarity, 
                    "content_snippet": content_snippet, # 可选传递内容片段
                    "game_name": result_dict.get('game_name'),
                }
                # 过滤掉 None 值，避免传递 null
                article_info_for_llm = {k: v for k, v in article_info_for_llm.items() if v is not None}


                # 5b. 调用置信度分析 LLM API
                # 传入结构化的 case 分析结果和单篇文章信息
                llm_prompt = {
                    "case_analysis": case_analysis_result,
                    "article_info": article_info_for_llm
                }
                confidence_result = llm_api.get_llm_confidence_analysis(
                    llm_prompt
                )
                confidence_result = json.loads(confidence_result)

                if confidence_result and 'confidence_score' in confidence_result:
                    article_id = article_info_for_llm.get('article_id')
                    confidence = confidence_result['confidence_score']
                    justification = confidence_result.get('justification', '') # 获取理由
                    if article_id:
                         article_confidences.append({
                             'article_id': article_id,
                             'confidence': confidence,
                             'justification': justification # 保存理由
                         })
                         logger.debug(f"[AutoLink] 会话 {session_id} 文章 {article_id} 置信度: {confidence:.2f}, ES Score: {es_score:.2f}, 理由: {justification}")
                else:
                     logger.warning(f"[AutoLink] 会话 {session_id} 文章 {article_info_for_llm.get('article_id')} 置信度分析失败或返回格式错误")

            except Exception as e:
                 logger.error(f"[AutoLink] 会话 {session_id} 处理文章 {result_dict.get('id')} 置信度分析时出错: {e}", exc_info=True)
                 continue # 继续处理下一篇文章

        if not article_confidences:
            logger.info(f"[AutoLink] 会话 {session_id} 所有候选文章置信度分析均失败或无结果")
            return False

        # 6. 筛选置信度达标的文章
        filtered_articles = [
            article for article in article_confidences
            if article.get('confidence', 0) >= min_confidence
        ]

        if not filtered_articles:
            logger.info(f"[AutoLink] 会话 {session_id} 没有文章达到最低置信度 {min_confidence}")
            return False

        # 按置信度排序并限制数量
        filtered_articles.sort(key=lambda x: x['confidence'], reverse=True)
        articles_to_link = filtered_articles[:max_links]

        logger.info(f"[AutoLink] 会话 {session_id} 筛选出 {len(articles_to_link)} 篇待关联文章: {[a['article_id'] for a in articles_to_link]}")

        # 7. 获取创建者用户 (系统用户)
        User = get_user_model()
        creator = None
        try:
            # 建议使用更可靠的方式获取系统用户，例如通过 username 或特定 role
            creator = User.objects.filter(is_superuser=True).order_by('id').first()
            if not creator:
                 creator = User.objects.get(id=1) # Fallback to ID 1 if no superuser
        except User.DoesNotExist:
            logger.error("[AutoLink] 未找到系统用户 (Superuser 或 ID=1)，自动关联将无法记录创建者")
            # 根据业务逻辑决定是否继续，这里选择继续但 creator 为 None

        # 8. 创建文章关联 Link
        created_links_count = 0
        created_link_ids = []

        for article_data in articles_to_link:
            article_id = article_data['article_id']
            try:
                # 使用 get_or_create 避免因并发等原因重复创建（虽然前面检查过一次）
                link, created = Link.objects.get_or_create(
                    ticket_id=session_id,
                    article_id=article_id,
                    defaults={
                        'creator': creator,
                        'link_type': 2, # 自动关联类型
                        'description': f"AI自动关联 (置信度: {article_data['confidence']:.2f}. 理由: {article_data.get('justification', 'N/A')})"
                    }
                )
                if created:
                    created_links_count += 1
                    created_link_ids.append(link.id)
                    logger.info(f"[AutoLink] 成功为会话 {session_id} 关联文章 {article_id}")
                
                if created_link_ids:
                    try:
                        # 在函数内部导入task__ai_cs_performance_analysis，避免循环导入
                        from apps.kcs.tasks import task__ai_cs_performance_analysis
                        task__ai_cs_performance_analysis.delay(
                            link_ids=created_link_ids,
                            session_id=str(session_id),  # 确保是字符串
                            session_data=session_data
                        )
                        logger.info(f"[AutoLink] 已为 Link ID: {link.id}, Session ID: {session_id} 触发客服表现分析任务")
                    except Exception as task_err:
                        logger.error(
                            f"[AutoLink] 触发客服表现分析任务失败 for Link ID: {link.id}, Session ID: {session_id}: {task_err}",
                            exc_info=True)

                else:
                    logger.warning(f"[AutoLink] 尝试关联文章 {article_id} 到会话 {session_id} 时发现已存在关联记录")

            except Article.DoesNotExist:
                 logger.error(f"[AutoLink] 尝试关联不存在的文章 ID: {article_id}")
            except Exception as e:
                logger.error(f"[AutoLink] 为会话 {session_id} 创建文章 {article_id} 关联时出错: {str(e)}", exc_info=True)

        logger.info(f"[AutoLink] 会话 {session_id} 自动关联处理完成，共成功创建 {created_links_count} 条关联")
        return created_links_count > 0

    except Exception as e:
        logger.error(f"[AutoLink] 处理会话 {session_id} 自动关联时发生顶层错误: {str(e)}", exc_info=True)
        return False

