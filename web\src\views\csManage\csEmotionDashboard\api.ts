import { request } from '/@/utils/service';

export const apiPrefix = '/api/cs_manage/cs_emotion_analysis_dashboard';

/**
 * 获取Dashboard数据
 * @param params 查询参数
 * @returns Promise
 */
export function getDashboardData(params?: {
  start_date?: string;
  end_date?: string;
  show_all?: boolean;
  async_mode?: boolean;
  task_id?: string;
}) {
  return request({
    url: apiPrefix + '/',
    method: 'get',
    params,
  });
}

/**
 * 获取客服详细数据
 * @param params 查询参数
 * @returns Promise
 */
export function getStaffDetail(params: {
  staff_id: string;
  start_date?: string;
  end_date?: string;
}) {
  return request({
    url: apiPrefix + '/detail/',
    method: 'get',
    params,
  });
}

/**
 * 刷新缓存数据
 * @param data 刷新参数
 * @returns Promise
 */
export function refreshCache(data: {
  start_date: string;
  end_date: string;
}) {
  return request({
    url: apiPrefix + '/refresh_cache/',
    method: 'post',
    data,
  });
}

/**
 * 获取任务状态
 * @param taskId 任务ID
 * @returns Promise
 */
export function getTaskStatus(taskId: string) {
  return request({
    url: apiPrefix + '/',
    method: 'get',
    params: { task_id: taskId },
  });
}

// 数据类型定义
export interface TeamSummary {
  total_staff: number;
  total_sessions: number;
  total_valid_sessions: number;
  avg_satisfaction: number;
  avg_reply_ratio: number;
  avg_one_off_ratio: number;
  avg_online_duration: number;
  avg_daily_online_duration: number;
  avg_login_duration: number;
  avg_rest_duration: number;
  avg_pend_duration: number;
  avg_resume_free_duration: number;
  avg_hang_session_duration: number;
  avg_total_non_work_duration: number;
  avg_emotion_contribution: number;
  avg_emotion_contribution_per_hour: number;
  avg_comprehensive_score: number;
  avg_hourly_efficiency: number;
  team_total_hourly_efficiency: number;
  total_emotion_analyses: number;
  // 团队概览增强数据
  total_sessions_overview?: number;
  total_valid_sessions_overview?: number;
  total_evaluations?: number;
  good_evaluations?: number;
  bad_evaluations?: number;
  avg_eva_ratio?: number;
  data_enhanced_by_overview?: boolean;
}

export interface StaffMetrics {
  staff_id: string;
  staff_name: string;
  staff_account: string;
  email?: string;
  dept_name?: string;
  // 质量指标
  avg_first_resp_time: number;
  avg_resp_time: number;
  reply_ratio: number;
  satisfaction_ratio: number;
  eva_ratio: number;
  invitation_ratio: number;
  one_off_ratio: number;
  user_resolved_ratio: number;
  // 时长指标
  online_duration: number;
  login_duration: number;
  rest_duration: number;
  pend_duration: number;
  resume_free_duration: number;
  hang_session_duration: number;
  total_non_work_duration: number;
  // 会话指标
  total_sessions: number;
  valid_sessions: number;
  effect_sessions?: number;
  online_ratio?: number;
  no_reply_ratio?: number;
  // 效率指标
  hourly_efficiency: number;
  // 情绪指标
  emotion_contribution: number;
  emotion_contribution_per_hour: number;
  emotion_analysis_count: number;
  avg_emotion_change: number;
  positive_emotion_changes: number;
  negative_emotion_changes: number;
  // 综合指标
  comprehensive_score: number;
  // 标识字段
  is_empty_record?: boolean;
}

export interface DashboardData {
  date_range: {
    start_date: string;
    end_date: string;
  };
  team_summary: TeamSummary;
  staff_list: StaffMetrics[];
  data_sources: {
    qiyu_reports_available: boolean;
    team_overview_available: boolean;
    emotion_analysis_available: boolean;
    total_staff_with_data: number;
    total_staff_all: number;
    cache_policy: {
      qiyu_data_simple_cached: boolean;
      emotion_data_realtime: boolean;
      result_not_cached: boolean;
    };
  };
}

export interface EmotionRecord {
  session_id: string;
  create_datetime: string;
  initial_emotion_score: number;
  final_emotion_score: number;
  emotion_change_score: number;
  conversation_summary: string;
  session_duration: number;
  session_duration_minutes: number;
  single_emotion_contribution: number;
  message_count: number;
  user_message_count: number;
  service_message_count: number;
}

export interface EmotionSummary {
  total_records: number;
  avg_emotion_change: number;
  total_emotion_contribution: number;
  positive_changes: number;
  negative_changes: number;
  neutral_changes: number;
}

export interface StaffDetailResponse {
  staff_info: StaffMetrics;
  emotion_records: EmotionRecord[];
  emotion_summary: EmotionSummary;
  department_averages: DepartmentAverages;
  date_range: {
    start_date: string;
    end_date: string;
  };
}

export interface DepartmentAverages {
  satisfaction_ratio: number;
  reply_ratio: number;
  one_off_ratio: number;
  eva_ratio: number;
  invitation_ratio: number;
  hourly_efficiency: number;
  dept_name: string;
  staff_count: number;
} 