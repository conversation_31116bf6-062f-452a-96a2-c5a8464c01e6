"""
指标计算工具

只计算七鱼API中没有的特定指标：
1. AI会话分类和统计
2. AI转人工率计算
3. 会话按组别分组
4. 工单按组别统计

其他指标（接入率、满意度、FCR等）直接使用七鱼API返回的数据
"""
import logging
from typing import List, Dict, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """指标计算器 - 只计算API中没有的特定指标"""

    def __init__(self, staff_groups: List[Dict]):
        """
        初始化指标计算器

        Args:
            staff_groups: 客服组列表
        """
        self.staff_groups = staff_groups
        self.group_mapping = {group['id']: group['name'] for group in staff_groups}
        
    def calculate_ai_session_metrics(self, sessions: List[Dict]) -> Dict:
        """
        计算AI会话相关指标（七鱼API中没有的数据）

        Args:
            sessions: 会话列表（已按组别过滤）

        Returns:
            AI会话指标字典
        """
        try:
            # 过滤有效会话
            valid_sessions = [s for s in sessions if s.get('isValid') == 1]

            # AI会话分类统计
            ai_sessions = []
            ai_transfer_sessions = []

            for session in valid_sessions:
                # 判断是否为AI转人工会话
                if self._is_ai_transfer_session(session):
                    ai_transfer_sessions.append(session)

                # 判断是否为AI会话（通过session_ext中的from_page参数）
                if self._is_ai_session(session):
                    ai_sessions.append(session)

            # 计算人工会话数（总会话 - AI会话）
            manual_sessions_count = len(valid_sessions) - len(ai_sessions)

            # 计算AI转人工率
            ai_transfer_rate = 0
            if len(ai_sessions) > 0:
                ai_transfer_rate = round(len(ai_transfer_sessions) / len(ai_sessions) * 100, 2)

            return {
                'ai_sessions': len(ai_sessions),
                'manual_sessions': manual_sessions_count,
                'ai_transfer': len(ai_transfer_sessions),
                'ai_transfer_rate': ai_transfer_rate
            }

        except Exception as e:
            logger.error(f"[指标计算] 计算AI会话指标失败: {str(e)}")
            return {
                'ai_sessions': 0,
                'manual_sessions': 0,
                'ai_transfer': 0,
                'ai_transfer_rate': 0
            }
    
    def extract_qiyu_metrics(self, overview_data: Optional[Dict]) -> Dict:
        """
        从七鱼API数据中提取指标（直接使用，无需计算）

        Args:
            overview_data: 七鱼统计概览数据

        Returns:
            提取的指标字典
        """
        try:
            if not overview_data:
                return self._get_empty_qiyu_metrics()

            # 直接从七鱼API提取数据，转换单位和格式
            return {
                # 会话量数据（直接使用）
                'total_sessions': overview_data.get('effectSessions', 0),

                # 接入率数据（直接使用，已是百分比）
                'online_ratio': round(overview_data.get('realSessionInRatio', 0) * 100, 2),

                # 满意度数据（转换为百分比）
                'manual_satisfaction': round(overview_data.get('satisfactionRatio', 0) * 100, 2),

                # FCR数据（转换为百分比）
                'fcr_ratio': round(overview_data.get('oneOffRatio', 0) * 100, 2),

                # 响应时间数据（毫秒转秒）
                'avg_first_resp': int(overview_data.get('avgFirstRespTime', 0) / 1000),

                # 30秒应答率（转换为百分比）
                'resp_30_ratio': round(overview_data.get('specialAnswerRatio', 0) * 100, 2),

                # 评价相关数据
                'eva_count': int(overview_data.get('effectSessions', 0) * overview_data.get('evaRatio', 0)),
                'eva_ratio': round(overview_data.get('evaRatio', 0) * 100, 2),
                'invite_count': overview_data.get('evaFromCount', 0),
            }

        except Exception as e:
            logger.error(f"[指标计算] 提取七鱼指标失败: {str(e)}")
            return self._get_empty_qiyu_metrics()

    def _get_empty_qiyu_metrics(self) -> Dict:
        """获取空的七鱼指标数据"""
        return {
            'total_sessions': 0,
            'online_ratio': 0,
            'manual_satisfaction': 0,
            'fcr_ratio': 0,
            'avg_first_resp': 0,
            'resp_30_ratio': 0,
            'eva_count': 0,
            'eva_ratio': 0,
            'invite_count': 0,
        }
    
    def calculate_worksheet_count(self, worksheets, group_id: int = 0) -> int:
        """
        计算工单数量

        Args:
            worksheets: 工单列表
            group_id: 组别ID，0表示全部

        Returns:
            工单数量
        """
        try:
            # 处理工单数据可能是字符串的情况
            if isinstance(worksheets, str):
                try:
                    import json
                    worksheets = json.loads(worksheets)
                except:
                    logger.warning(f"[指标计算] 工单数据格式异常: {type(worksheets)}")
                    return 0

            # 处理工单数据可能是包含'list'键的字典
            if isinstance(worksheets, dict) and 'list' in worksheets:
                worksheets = worksheets['list']

            if not isinstance(worksheets, list):
                logger.warning(f"[指标计算] 工单数据不是列表格式: {type(worksheets)}")
                return 0

            if group_id == 0:
                return len(worksheets)

            # 按组别过滤工单 - 优先使用groupId，fallback到groupName
            filtered_worksheets = []

            for w in worksheets:
                if not isinstance(w, dict):
                    continue

                # 方法1: 使用groupId进行精确匹配
                worksheet_group_id = w.get('groupId')
                if worksheet_group_id is not None:
                    # 处理可能的字符串类型groupId
                    try:
                        worksheet_group_id = int(worksheet_group_id)
                        if worksheet_group_id == group_id:
                            filtered_worksheets.append(w)
                            continue
                    except (ValueError, TypeError):
                        pass

                # 方法2: fallback到groupName匹配
                group_name = self.group_mapping.get(group_id, '')
                if group_name and w.get('groupName') == group_name:
                    filtered_worksheets.append(w)

            logger.debug(f"[指标计算] 组别 {group_id} 工单数: {len(filtered_worksheets)}")
            return len(filtered_worksheets)

        except Exception as e:
            logger.error(f"[指标计算] 计算工单数量失败: {str(e)}")
            return 0
    
    def _is_ai_group(self, route: str) -> bool:
        """
        判断是否为AI客服组
        
        Args:
            route: 路由名称
            
        Returns:
            是否为AI客服组
        """
        # 根据实际情况配置AI客服组名称
        ai_group_names = ['AI客服组', 'AI客服', '智能客服']
        return route in ai_group_names
    
    def _is_ai_transfer_session(self, session: Dict) -> bool:
        """
        判断是否为AI转人工会话
        
        Args:
            session: 会话数据
            
        Returns:
            是否为AI转人工会话
        """
        from ..services.qiyu_client import QiyuDataClient
        return QiyuDataClient.is_ai_transfer_session(session)
    
    def get_group_sessions_mapping(self, sessions: List[Dict]) -> Dict[int, List[Dict]]:
        """
        按组别分组会话数据（AB结合匹配策略）

        Args:
            sessions: 会话列表

        Returns:
            组别ID到会话列表的映射
        """
        try:
            group_sessions = defaultdict(list)
            unmatched_sessions = []

            # 过滤有效会话
            valid_sessions = [s for s in sessions if s.get('isValid') == 1]

            # 全部组别（包含所有有效会话）
            group_sessions[0] = valid_sessions

            # 按具体组别分组（AB结合策略）
            for session in valid_sessions:
                group_id = self._match_session_to_group(session)

                if group_id:
                    group_sessions[group_id].append(session)
                else:
                    unmatched_sessions.append(session)

            # 记录无法匹配的会话数量
            if unmatched_sessions:
                logger.info(f"[指标计算] {len(unmatched_sessions)} 个会话无法匹配到具体组别，归入全部统计")

            return dict(group_sessions)

        except Exception as e:
            logger.error(f"[指标计算] 按组别分组会话失败: {str(e)}")
            return {0: valid_sessions if 'valid_sessions' in locals() else []}

    def _match_session_to_group(self, session: Dict) -> Optional[int]:
        """
        匹配会话到组别（AB结合策略）

        Args:
            session: 会话数据

        Returns:
            匹配的组别ID，无法匹配返回None
        """
        # 策略A：优先使用categoryDescription
        category_desc = session.get('categoryDescription', '').strip()
        if category_desc:
            for group_id, group_name in self.group_mapping.items():
                if category_desc == group_name:
                    return group_id

        # 策略B：备选使用route
        route = session.get('route', '').strip()
        if route:
            for group_id, group_name in self.group_mapping.items():
                if route == group_name:
                    return group_id

        return None