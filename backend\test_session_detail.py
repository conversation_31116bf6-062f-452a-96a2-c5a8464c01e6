#!/usr/bin/env python3
"""
测试脚本：测试七鱼API的get_session_detail函数
使用单文件执行Django的方式
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
def setup_django():
    """设置Django环境"""
    # 获取项目根目录
    BASE_DIR = Path(__file__).resolve().parent
    
    # 添加项目路径到sys.path
    sys.path.insert(0, str(BASE_DIR))
    
    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
    
    # 初始化Django
    django.setup()
    
    print("✅ Django环境初始化成功")

def test_get_session_detail():
    """测试get_session_detail函数"""
    try:
        # 导入服务类
        from apps.cs_manage.utils.LLM_cs_emotion_analysis import process_session_for_emotion_analysis

        process_session_for_emotion_analysis(11937818237, False)

        # from apps.common.qiyu_service import get_qiyu_service
        # service = get_qiyu_service()
        # # 获取会话基本信息
        # session_detail = service.get_session_detail(11937818237)
        # print(session_detail)
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始执行测试脚本")
    print("=" * 50)
    
    # 设置Django环境
    setup_django()
    
    # 执行测试
    test_get_session_detail()
    
    print("=" * 50)
    print("🏁 测试脚本执行完成")

if __name__ == "__main__":
    main() 