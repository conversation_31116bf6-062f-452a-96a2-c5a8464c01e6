<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding"> </fs-crud>
    
    <!-- 情绪分析详情抽屉 -->
    <el-drawer 
      title="情绪分析详情" 
      v-model="drawerVisible" 
      size="80%"
      class="detail-drawer"
    >
      <EmotionAnalysisDetail :data="drawerData" v-if="drawerData" />
    </el-drawer>
    
    <!-- 会话消息抽屉 -->
    <el-drawer title="会话消息" v-model="sessionDrawerVisible" size="50%">
      <session-message :data="sessionDrawerData"></session-message>
    </el-drawer>
  </fs-page>
</template>

<script lang="ts" setup name="KCSEmotionAnalysis">
import SessionMessage from "/@/views/kcs/performanceAnalysis/components/sessionMessage.vue"; // 复用会话消息组件
import EmotionAnalysisDetail from "./components/EmotionAnalysisDetail.vue"; // 新的详情组件
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud'; // 引入 crud 配置
import * as api from './api';
import { ElMessage } from 'element-plus';

// 使用useFs创建CRUD
const { crudBinding, crudRef, crudExpose, context } = useFs({ createCrudOptions });
const { event } = context;

// 抽屉控制
const drawerVisible = ref(false);
const drawerData = ref(null);
const sessionDrawerVisible = ref(false);
const sessionDrawerData = ref([]);

// 处理事件
const handleEvent = (e, data) => {
  if (e === 'showDetails') {
    // 获取完整的分析详情数据
    api.GetObj(data.id).then((res) => {
      if (res.code === 2000) {
        drawerData.value = res.data;
        drawerVisible.value = true;
      }
    }).catch((error) => {
      ElMessage.warning('获取详情失败，使用基础数据展示');
      // 如果API调用失败，使用传入的基础数据
      drawerData.value = data;
      drawerVisible.value = true;
    });
  }
  if (e == 'showSession') {
    // 获取七鱼会话
    let sessionId = data.session_id;
    if (sessionId) {
      api.QiyuSessionMessage({ sessionId }).then((res) => {
        if (res.code == 2000) {
          sessionDrawerVisible.value = true;
          // 将resdata 倒置
          sessionDrawerData.value = res.data.reverse();
        }
      });
    }
  }
}

// 页面打开后获取列表数据
onMounted(() => {
  // 查看当前页面的query参数，将查询参数取出
  const query = useRouter().currentRoute.value.query;
  if (query && Object.keys(query).length > 0) {
    crudExpose.setSearchFormData({ form: query });
  }

  // 确保在 onMounted 后执行刷新
  crudExpose.doRefresh();
});

// 监听事件
watch(event, (val) => {
  if (val && val.e) {
    handleEvent(val.e, val.data);
  }
}, { deep: true });

// 暴露变量和方法给模板使用
defineExpose({ crudRef, crudBinding });
</script>

<style scoped>
.el-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.detail-drawer :deep(.el-drawer__body) {
  padding: 0;
  background-color: #f5f7fa;
}
</style>

