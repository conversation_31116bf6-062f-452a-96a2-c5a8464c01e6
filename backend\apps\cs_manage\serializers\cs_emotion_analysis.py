from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from apps.cs_manage.models import CsEmotionAnalysis


class CsEmotionAnalysisExportSerializer(CustomModelSerializer):
    """
    客服情绪分析导出序列化器
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_user_name = serializers.CharField(source='service_user.name', read_only=True, default='')
    service_user_username = serializers.CharField(source='service_user.username', read_only=True, default='')
    dept_name = serializers.SerializerMethodField()
    session_duration_minutes = serializers.SerializerMethodField()
    emotion_change_level = serializers.SerializerMethodField()
    session_start_time_str = serializers.SerializerMethodField()
    session_end_time_str = serializers.SerializerMethodField()
    create_datetime_str = serializers.SerializerMethod<PERSON>ield()
    emotion_keywords_text = serializers.SerializerMethodField()
    
    class Meta:
        model = CsEmotionAnalysis
        fields = [
            'session_id', 'service_name', 'service_user_name', 'service_user_username', 'dept_name',
            'game_name', 'initial_emotion_score', 'final_emotion_score', 'emotion_change_score',
            'emotion_change_level', 'session_duration_minutes', 'message_count', 
            'user_message_count', 'service_message_count', 'conversation_summary',
            'overall_assessment', 'session_start_time_str', 'session_end_time_str',
            'create_datetime_str', 'emotion_keywords_text', 'status'
        ]
    
    def get_dept_name(self, obj):
        """获取用户所属部门名称"""
        if obj.service_user and hasattr(obj.service_user, 'dept') and obj.service_user.dept:
            return obj.service_user.dept.name
        return ''
    
    def get_session_duration_minutes(self, obj):
        """获取会话时长（分钟）"""
        if obj.session_duration:
            return round(obj.session_duration / 60, 2)
        return 0
    
    def get_emotion_change_level(self, obj):
        """获取情绪变化等级"""
        if obj.emotion_change_score is None:
            return '未知'
        elif obj.emotion_change_score >= 5:
            return '大幅改善'
        elif obj.emotion_change_score >= 1:
            return '改善'
        elif obj.emotion_change_score >= -1:
            return '稳定'
        elif obj.emotion_change_score >= -5:
            return '恶化'
        else:
            return '大幅恶化'
    
    def get_session_start_time_str(self, obj):
        """格式化会话开始时间"""
        return obj.session_start_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_start_time else ''
    
    def get_session_end_time_str(self, obj):
        """格式化会话结束时间"""
        return obj.session_end_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_end_time else ''
    
    def get_create_datetime_str(self, obj):
        """格式化分析时间"""
        return obj.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_emotion_keywords_text(self, obj):
        """获取情绪关键词文本"""
        if obj.emotion_keywords:
            return ', '.join(obj.emotion_keywords)
        return ''