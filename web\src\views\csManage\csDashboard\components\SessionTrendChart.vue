<template>
  <div class="session-trend-chart">
    <div v-if="loading" class="flex items-center justify-center h-80">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
    
    <div v-else-if="!hasData" class="flex items-center justify-center h-80 text-gray-500">
      <div class="text-center">
        <i class="fas fa-chart-bar text-4xl mb-2"></i>
        <p>暂无数据</p>
      </div>
    </div>
    
    <div v-else ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import type { ChartData } from '../api'

interface Props {
  data?: ChartData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 计算是否有数据
const hasData = computed(() => {
  return props.data && props.data.dates && props.data.dates.length > 0
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !hasData.value) return
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        console.log(params)
        let result = `${params[0].axisValue}<br/>`
        let total = 0

        // 计算总计并显示各项数据
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
          total += param.value || 0
        })

        result = result +`● 总会话量: ${total}<br/>`

        // 得出人工会话量
        const humanSession = params.find((param: any) => param.seriesName === '人工会话')?.value || 0

        // 计算占比
        const humanRatio = humanSession / total * 100
        result = result + `● 人工占比: ${humanRatio.toFixed(2)}%<br/>`

        return result
      }
    },
    legend: {
      data: props.data?.series.map(s => s.name) || [],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data?.dates || [],
      axisLabel: {
        rotate: 0,
        interval: 0,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'k'
          }
          return value.toString()
        }
      }
    },
    series: props.data?.series.map((series, index) => ({
      name: series.name,
      type: series.type,
      data: series.data,
      stack: series.stack || '',
      emphasis: {
        focus: 'series'
      },
      itemStyle: {
        color: getSeriesColor(index)
      }
    })) || []
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeHandler = () => {
    chartInstance?.resize()
  }
  window.addEventListener('resize', resizeHandler)
  
  // 清理函数
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}

// 获取系列颜色
const getSeriesColor = (index: number) => {
  const colors = [
    '#3B82F6', // blue-500
    '#10B981', // green-500
    '#8B5CF6', // purple-500
    '#F59E0B', // orange-500
    '#EF4444', // red-500
    '#F59E0B'  // yellow-500
  ]
  return colors[index % colors.length]
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    nextTick(() => {
      initChart()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (newLoading) => {
    if (!newLoading) {
      nextTick(() => {
        initChart()
      })
    }
  }
)

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载时清理
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.session-trend-chart {
  width: 100%;
  height: 320px;
}
</style>
