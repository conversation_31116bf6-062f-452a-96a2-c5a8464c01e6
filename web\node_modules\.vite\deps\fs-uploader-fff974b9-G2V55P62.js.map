{"version": 3, "sources": ["../../@fast-crud/fast-extends/src/uploader/components/fs-uploader.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from \"vue\";\nimport { useUploader } from \"./utils\";\nimport { loadUploader } from \"./libs\";\n\nexport default defineComponent({\n  name: \"FsUploader\",\n  props: {\n    type: {}\n  },\n  setup(props) {\n    async function getUploaderRef() {\n      const { getDefaultType } = useUploader();\n      const type = props.type || getDefaultType();\n      return await loadUploader(type as string);\n    }\n\n    return {\n      getUploaderRef\n    };\n  }\n});\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;AAKA,IAAAA,IAAeC,gBAAgB;EAC7B,MAAM;EACN,OAAO;IACL,MAAM,CAAC;EACT;EACA,MAAMC,GAAO;AACX,mBAAeC,IAAiB;AACxB,YAAA,EAAE,gBAAAC,EAAAA,IAAmBC,GAAAA,GACrBC,IAAOJ,EAAM,QAAQE,EAAe;AACnC,aAAA,MAAMG,GAAaD,CAAc;IAC1C;AAEO,WAAA;MACL,gBAAAH;IAAA;EAEJ;AACF,CAAC;", "names": ["_sfc_main", "defineComponent", "props", "getUploaderRef", "getDefaultType", "useUploader", "type", "loadUploader"]}