import {
  Hl,
  Za,
  ot,
  rp,
  vt,
  vu
} from "./chunk-RUCDUSHK.js";
import {
  B,
  i
} from "./chunk-GQR6RJUV.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-6KFXODJP.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  guardReactiveProps,
  mergeProps,
  normalizeProps,
  openBlock,
  ref,
  renderList,
  resolveComponent,
  resolveDynamicComponent,
  unref,
  watch,
  withCtx
} from "./chunk-VL4YS5HC.js";

// node_modules/@fast-crud/fast-extends/dist/index-179c3581.mjs
var ge = defineComponent({
  name: "FsImagesFormat",
  inheritAttrs: false,
  props: {
    //包裹image的组件配置，antdv是preview-group，element是div
    wrapper: {
      type: Object,
      default: null
    },
    // 图片的url
    // 'value' 或 ['value','value']
    modelValue: {
      type: [String, Array, Object],
      require: true
    },
    /**
     * 构建好的图片链接，如果此处传值，则不走buildUrl方法
     * 'url' 或 ['url1','url2'] 或 {url,previewUrl} 或 [{url,previewUrl}]
     */
    urls: {
      type: [String, Object, Array]
    },
    /**
     * 加载错误时显示的图片
     */
    error: {
      default: 'data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="%23888" d="M5 21q-.825 0-1.413-.588T3 19v-6.6l3 3l4-4l4 4l4-4l3 3V19q0 .825-.588 1.413T19 21H5ZM5 3h14q.825 0 1.413.588T21 5v6.575l-3-3l-4 4l-4-4l-4 4l-3-3V5q0-.825.588-1.413T5 3Z"%2F%3E%3C%2Fsvg%3E'
    },
    /**
     * 从value构建图片下载url的方法
     * 支持异步
     */
    buildUrl: {
      type: Function,
      default: function(e) {
        return e;
      }
    },
    buildUrls: {
      type: Function,
      default: null
    },
    /**
     * 从value或url构建预览大图的方法
     * 支持异步
     */
    buildPreviewUrl: {
      type: Function,
      default: function({ url: e, value: t, index: n }) {
        return e;
      }
    },
    buildPreviewUrls: {
      type: Function,
      default: null
    }
  },
  setup(e, t) {
    const { ui: n } = B(), i2 = ref([]), r = computed(() => merge_default({ style: {} }, { style: t.attrs.style })), o = computed(() => {
      const s = [];
      if (e.modelValue == null || e.modelValue === "")
        return s;
      if (typeof e.modelValue == "string")
        s.push(e.modelValue);
      else if (Array.isArray(e.modelValue))
        for (const l of e.modelValue)
          l != null && (l.url != null ? s.push(l.url) : s.push(l));
      else
        e.modelValue.url != null ? s.push(e.modelValue.url) : s.push(e.modelValue);
      return s;
    });
    function a(s) {
      const l = [], p = [];
      for (let v = 0; v < s.length; v++) {
        const T = s[v];
        l.push(T.url), p.push(T.previewUrl);
      }
      const m = [];
      for (let v = 0; v < s.length; v++) {
        const T = s[v], k = T.url, S = T.url, O = n.image.buildPreviewBind({
          url: k,
          urls: l,
          previewUrl: S,
          previewUrls: p,
          index: v
        });
        m.push({
          fit: "contain",
          src: k,
          [n.image.fallback]: e.error,
          ...t.attrs,
          ...O
        });
      }
      return m;
    }
    async function u(s) {
      if (e.buildUrls) {
        const l = s.map((v) => v.value), p = await e.buildUrls(l);
        for (let v = 0; v < s.length; v++)
          s[v].url = p[v];
        let m = p;
        e.buildPreviewUrls && (m = await e.buildPreviewUrls(s));
        for (let v = 0; v < s.length; v++)
          s[v].previewUrl = m[v];
      } else if (e.buildUrl)
        for (let l of s)
          l.url = await e.buildUrl(l.value), l.previewUrl = l.url, e.buildPreviewUrl && (l.previewUrl = await e.buildPreviewUrl(l));
      else
        for (let l = 0; l < s.length; l++)
          s[l].url = s[l].value, s[l].previewUrl = s[l].value;
    }
    async function g(s) {
      const l = [];
      for (let p = 0; p < s.length; p++) {
        let m = s[p];
        l.push({
          value: m,
          index: p
        });
      }
      return await u(l), a(l);
    }
    async function w(s) {
      const l = [];
      if (typeof s == "string")
        l.push({
          value: s,
          url: s,
          index: 0,
          previewUrl: s
        });
      else if (s instanceof Array) {
        if (s.length > 0)
          if (typeof s[0] == "string")
            for (let p = 0; p < s.length; p++) {
              const m = s[p];
              l.push({
                value: m,
                url: m,
                previewUrl: m,
                index: p
              });
            }
          else
            for (let p = 0; p < s.length; p++) {
              const m = s[p];
              l.push({
                value: m.url,
                ...m,
                index: p
              });
            }
      } else
        l.push({
          value: s.url,
          ...s,
          index: 0
        });
      return a(l);
    }
    return watch(
      () => o.value,
      async (s) => {
        e.urls || (i2.value = await g(s));
      },
      {
        immediate: true
      }
    ), watch(
      () => e.urls,
      async (s) => {
        s && (i2.value = await w(s));
      },
      {
        immediate: true
      }
    ), { imageListRef: i2, ui: n, errorBinding: r };
  }
});
var he = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [i2, r] of t)
    n[i2] = r;
  return n;
};
var ve = { class: "fs-image-format" };
var be = { class: "fs-image-slot" };
var we = { class: "fs-image-slot" };
var Ce = ["src"];
function Ee(e, t, n, i2, r, o) {
  const a = resolveComponent("fs-loading");
  return openBlock(), createElementBlock("div", ve, [
    (openBlock(), createBlock(resolveDynamicComponent(e.ui.imageGroup.name), normalizeProps(guardReactiveProps(e.wrapper)), {
      default: withCtx(() => [
        (openBlock(true), createElementBlock(Fragment, null, renderList(e.imageListRef, (u) => (openBlock(), createBlock(resolveDynamicComponent(e.ui.image.name), mergeProps({
          key: u.src,
          class: "fs-image-item",
          ref_for: true
        }, u), {
          placeholder: withCtx(() => [
            createBaseVNode("div", be, [
              createVNode(a, mergeProps({
                loading: true,
                ref_for: true
              }, e.errorBinding), null, 16)
            ])
          ]),
          error: withCtx(() => [
            createBaseVNode("div", we, [
              createBaseVNode("img", mergeProps({
                src: e.error,
                ref_for: true
              }, e.errorBinding), null, 16, Ce)
            ])
          ]),
          _: 2
        }, 1040))), 128))
      ]),
      _: 1
    }, 16))
  ]);
}
var Te = he(ge, [["render", Ee]]);
var Q = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Te
}, Symbol.toStringTag, { value: "Module" }));
var _e = (e) => async (t, n) => {
  const i2 = await e(t.fullField, true);
  if (i2 && i2.hasUploading())
    throw new Error("还有未上传完成的文件");
  return true;
};
var F = () => Hl(({ getComponentRef: e }) => _e(e));
var gt = (e, t) => (e == null && (e = []), e.push({
  //@ts-ignore
  validator: F(),
  message: t || "还有文件正在上传，请稍候",
  trigger: "blur"
  // <-------注意使用blur事件，否则会闪现
}), e);
function xe() {
  const {
    t: e
  } = ot(), t = i.get();
  return {
    "image-uploader": {
      form: {
        component: {
          name: "fs-file-uploader",
          listType: t.upload.typeImageCard,
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.webp,.svg"
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true,
          // @ts-ignore
          previewMask: () => createVNode("div", {
            class: "ant-mask-info"
          }, [createVNode(resolveComponent("fs-icon"), {
            icon: t.icons.eye
          }, null)])
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      }
    },
    "avatar-uploader": {
      form: {
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }],
        component: {
          name: "fs-file-uploader",
          limit: 1,
          listType: t.upload.typeImageCard,
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.webp,.svg"
        }
      },
      column: {
        align: "center",
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      },
      valueResolve({
        row: n,
        key: i2
      }) {
        const r = n[i2];
        r != null && r instanceof Array && (r.length >= 0 ? n[i2] = r[0].url : n[i2] = null);
      }
    },
    "file-uploader": {
      form: {
        component: {
          name: "fs-file-uploader",
          listType: "text"
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        component: {
          name: "fs-files-format"
        }
      }
    },
    "cropper-uploader": {
      form: {
        component: {
          name: "fs-cropper-uploader",
          accept: ".png,.jpeg,.jpg,.ico,.bmp,.gif,.svg,.webp",
          cropper: {
            aspectRatio: 1,
            autoCropArea: 1,
            viewMode: 0
          }
        },
        [t.formItem.rules]: [{
          validator: F(),
          message: e("fs.extends.fileUploader.hasUploading"),
          trigger: "blur"
        }]
      },
      column: {
        align: "center",
        component: {
          name: "fs-images-format",
          style: "width:30px",
          previewTeleported: true
        }
      },
      viewForm: {
        component: {
          height: 100,
          width: 100
        }
      }
    }
  };
}
var ke = {
  defaultType: "cos",
  cos: {
    // 腾讯云 cos 的配置
    domain: "https://d2p-demo-1251260344.cos.ap-guangzhou.myqcloud.com",
    bucket: "d2p-demo-1251260344",
    region: "",
    secretId: "",
    secretKey: "",
    async getAuthorization(e) {
      throw new Error("请配置config.cos.getAuthorization 或 uploader.getAuthorization");
    }
  },
  alioss: {
    domain: "https://d2p-demo.oss-cn-shenzhen.aliyuncs.com",
    bucket: "d2p-demo",
    region: "oss-cn-shenzhen",
    accessKeyId: "",
    accessKeySecret: "",
    getAuthorization(e) {
      return new Promise((t, n) => {
        n(new Error("请实现config.alioss.getAuthorization，返回Promise获取临时授权token"));
      });
    },
    keepName: false,
    sdkOpts: {
      // sdk配置
      // secure: false // 默认为非https上传,为了安全，你可以设置为true
    }
  },
  qiniu: {
    bucket: "d2p-demo",
    async getToken(e) {
      throw new Error("请实现config.qiniu.getToken方法，返回Promise获取七牛的授权token{token:xxx,expires:xxx}");
    },
    domain: "http://pzrsldiu3.bkt.clouddn.com"
  },
  s3: {
    bucket: "fast-crud",
    sdkOpts: {
      region: "us-east-1",
      forcePathStyle: true,
      endpoint: "https://play.min.io",
      credentials: {
        accessKeyId: "",
        secretAccessKey: ""
        //访问密码
      }
    }
  },
  form: {
    successHandle(e) {
      return e;
    },
    action: void 0,
    name: "file",
    headers: {},
    data: {}
    // async uploadRequest({ file, action }) {
    //   自定义文件上传请求
    //   return await axios.request();
    // }
  },
  async buildKey(e) {
    const { fileName: t } = e, n = /* @__PURE__ */ new Date(), i2 = e.fileType ?? "file", r = e.keepName ?? false;
    let o = "";
    return r ? o = "/" + t : t.lastIndexOf(".") >= 0 && (o = t.substring(t.lastIndexOf("."))), i2 + "/" + n.getFullYear() + "/" + (n.getMonth() + 1) + "/" + n.getDate() + "/" + Math.floor(Math.random() * 1e14) + o;
  }
};
var P = cloneDeep_default(ke);
function Se(e, t) {
  merge_default(P, t);
}
var ht = F;
var Ae = {
  install(e, t) {
    const n = xe(), { addTypes: i2 } = rp();
    i2(n), Se(e, t);
  }
};
async function Fe(e) {
  let t = null;
  return e === "alioss" ? t = await import("./uploader-alioss-2fad37eb-SBY5WKQB.js") : e === "cos" ? t = await import("./uploader-cos-e34a1567-SJQN54SL.js") : e === "form" ? t = await import("./uploader-form-95c3d72e-4DRLK7FV.js") : e === "qiniu" ? t = await import("./uploader-qiniu-b8d0dde5-YYP3D4NY.js") : e === "s3" ? t = await import("./uploader-s3-5e18a75c-44MFHTJG.js") : console.error(`未找到${e}的上传实现`), t;
}
async function vt2(e, t, n) {
  return n.buildKey({
    fileName: t,
    file: e,
    ...n
  });
}
function bt() {
  function e() {
    const i2 = P;
    return i2 == null ? void 0 : i2.defaultType;
  }
  function t(i2) {
    i2 == null && (i2 = e());
    const r = P, o = P[i2];
    return o.buildKey == null && (o.buildKey = r.buildKey), o;
  }
  async function n(i2) {
    return await Fe(i2 || e());
  }
  return {
    getConfig: t,
    getDefaultType: e,
    getUploaderImpl: n
  };
}
var Ne = Object.assign({ "./components/fs-cropper-uploader.vue": () => import("./fs-cropper-uploader-e61fc78a-T57VU6JN.js"), "./components/fs-cropper.vue": () => import("./fs-cropper-261d1ed8-KOYLQKMI.js"), "./components/fs-file-uploader.vue": () => import("./fs-file-uploader-c6a29428-ILHY4FU7.js"), "./components/fs-files-format.vue": () => import("./fs-files-format-4dc328b0-TVDZVKIU.js"), "./components/fs-images-format.vue": () => Promise.resolve().then(() => Q), "./components/fs-uploader.vue": () => import("./fs-uploader-fff974b9-NNPPYBWQ.js") });
var Ve = Object.assign({ "./components/fs-images-format.vue": Q });
var { registerMergeColumnPlugin: Oe } = Za();
Oe({
  name: "uploader-merge-plugin",
  order: 5,
  handle: (e = {}, t = {}) => {
    if (typeof e.type == "string" && e.type.endsWith("uploader")) {
      const n = e.buildUrl, i2 = e.buildUrls;
      merge_default(e, {
        form: {
          component: {
            buildUrl: n,
            buildUrls: i2
          }
        },
        column: {
          component: {
            buildUrl: n,
            buildUrls: i2
          }
        }
      });
    }
    return e;
  }
});
var je = {
  install(e) {
    vt.vite.installAsyncComponents(e, Ne, ["FsImagesFormat"], null, null), vt.vite.installSyncComponents(e, Ve, null, null, null);
  }
};
var wt = {
  install(e, t) {
    e.use(Ae, t), e.use(je);
  }
};
function Ue() {
  return {
    "editor-wang": {
      form: { component: { name: "fs-editor-wang" } }
    },
    "editor-wang5": {
      form: { component: { name: "fs-editor-wang5", style: { zIndex: 800 }, toolbarAttrs: { style: { zIndex: 800 } } } }
    },
    "editor-code": {
      form: { component: { name: "fs-editor-code" }, col: { span: 24 } }
    }
  };
}
var Me = {
  wangEditor: {},
  wangEditor5: {
    editorConfig: {},
    toolbarConfig: {}
  }
};
function Ie(e, t) {
  e.config.globalProperties.$fs_editor_config = merge_default(Me, t);
}
var Le = {
  install(e, t) {
    const n = Ue(), { addTypes: i2 } = rp();
    i2(n), Ie(e, t);
  }
};
var ee = {};
function Ct(e, t) {
  ee[e] = t;
}
async function Et() {
  if (window.MonacoEnvironment)
    return;
  const e = await import("./editor.worker-b4d37356-S5BGXJ25.js"), t = await import("./json.worker-cb4384f6-6V2H2RLJ.js"), n = await import("./css.worker-3f8909b5-HWW5DZFF.js"), i2 = await import("./html.worker-7f10fd35-PRP2TYFF.js"), r = await import("./ts.worker-68019fa9-XYOG4SH4.js"), o = await import("./yaml.worker-651c702d-SXEW5TB4.js");
  window.MonacoEnvironment = {
    //@ts-ignore
    getWorker(a, u) {
      const g = ee[u];
      return g ? new g() : u === "json" ? new t.default() : u === "css" || u === "scss" || u === "less" ? new n.default() : u === "html" || u === "handlebars" || u === "razor" ? new i2.default() : u === "typescript" || u === "javascript" ? new r.default() : u === "yaml" || u === "yml" ? new o.default() : new e.default();
    }
  };
}
var Pe = {
  validator: async (e, t) => {
    if (t)
      try {
        JSON.parse(t);
      } catch (n) {
        throw console.error(n), new Error("json格式错误:" + n.message);
      }
  },
  message: "json格式错误"
};
var $e = {
  validator: async (e, t) => {
    if (t)
      try {
        const n = await import("./js-yaml-2USE2BHP.js");
        n.load(t, { schema: n.JSON_SCHEMA });
      } catch (n) {
        throw console.error(n), new Error("yaml格式错误:" + n.message);
      }
  },
  message: "yaml格式错误"
};
var Tt = {
  jsonRule: Pe,
  yamlRule: $e
};
var Be = Object.assign({ "./components/fs-editor-code/index.vue": () => import("./index-b1b23590-GSEKZGSQ.js"), "./components/fs-editor-wang/index.vue": () => import("./index-47ee27c0-AK66HCW2.js"), "./components/fs-editor-wang5/index.vue": () => import("./index-60c8d191-6WPV5ISL.js") });
var Re = {
  install(e) {
    vt.vite.installAsyncComponents(e, Be, null, /^.*\/([^\/]+)\/.*.vue$/, null);
  }
};
var _t = {
  install(e, t) {
    e.use(Le, t), e.use(Re);
  }
};
function ze() {
  return {
    json: {
      form: {
        component: {
          name: "fs-json-editor"
        }
      }
    }
  };
}
var He = {
  install(e) {
    const t = ze(), { addTypes: n } = rp();
    n(t);
  }
};
var qe = Object.assign({ "./components/fs-json-editor.vue": () => import("./fs-json-editor-893f6d75-LSHPXKWN.js") });
var De = {
  install(e) {
    vt.vite.installAsyncComponents(e, qe, [], null, null);
  }
};
var xt = {
  install(e) {
    e.use(He), e.use(De);
  }
};
function Ke() {
  return {
    copyable: {
      column: {
        component: {
          name: "fs-copyable",
          vModel: "modelValue"
        }
      }
    }
  };
}
var We = {
  install(e) {
    const t = Ke(), { addTypes: n } = rp();
    n(t);
  }
};
var Je = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function Ye(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
function Ge(e, t, n) {
  return n = {
    path: t,
    exports: {},
    require: function(i2, r) {
      return Xe(i2, r ?? n.path);
    }
  }, e(n, n.exports), n.exports;
}
function Xe() {
  throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs");
}
var Ze = Ge(function(e, t) {
  (function(i2, r) {
    e.exports = r();
  })(Je, function() {
    return (
      /******/
      function(n) {
        var i2 = {};
        function r(o) {
          if (i2[o])
            return i2[o].exports;
          var a = i2[o] = {
            /******/
            i: o,
            /******/
            l: false,
            /******/
            exports: {}
            /******/
          };
          return n[o].call(a.exports, a, a.exports, r), a.l = true, a.exports;
        }
        return r.m = n, r.c = i2, r.d = function(o, a, u) {
          r.o(o, a) || Object.defineProperty(o, a, { enumerable: true, get: u });
        }, r.r = function(o) {
          typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(o, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(o, "__esModule", { value: true });
        }, r.t = function(o, a) {
          if (a & 1 && (o = r(o)), a & 8 || a & 4 && typeof o == "object" && o && o.__esModule)
            return o;
          var u = /* @__PURE__ */ Object.create(null);
          if (r.r(u), Object.defineProperty(u, "default", { enumerable: true, value: o }), a & 2 && typeof o != "string")
            for (var g in o)
              r.d(u, g, (function(w) {
                return o[w];
              }).bind(null, g));
          return u;
        }, r.n = function(o) {
          var a = o && o.__esModule ? (
            /******/
            function() {
              return o.default;
            }
          ) : (
            /******/
            function() {
              return o;
            }
          );
          return r.d(a, "a", a), a;
        }, r.o = function(o, a) {
          return Object.prototype.hasOwnProperty.call(o, a);
        }, r.p = "", r(r.s = 6);
      }([
        /* 0 */
        /***/
        function(n, i2) {
          function r(o) {
            var a;
            if (o.nodeName === "SELECT")
              o.focus(), a = o.value;
            else if (o.nodeName === "INPUT" || o.nodeName === "TEXTAREA") {
              var u = o.hasAttribute("readonly");
              u || o.setAttribute("readonly", ""), o.select(), o.setSelectionRange(0, o.value.length), u || o.removeAttribute("readonly"), a = o.value;
            } else {
              o.hasAttribute("contenteditable") && o.focus();
              var g = window.getSelection(), w = document.createRange();
              w.selectNodeContents(o), g.removeAllRanges(), g.addRange(w), a = g.toString();
            }
            return a;
          }
          n.exports = r;
        },
        /* 1 */
        /***/
        function(n, i2) {
          function r() {
          }
          r.prototype = {
            on: function(o, a, u) {
              var g = this.e || (this.e = {});
              return (g[o] || (g[o] = [])).push({
                fn: a,
                ctx: u
              }), this;
            },
            once: function(o, a, u) {
              var g = this;
              function w() {
                g.off(o, w), a.apply(u, arguments);
              }
              return w._ = a, this.on(o, w, u);
            },
            emit: function(o) {
              var a = [].slice.call(arguments, 1), u = ((this.e || (this.e = {}))[o] || []).slice(), g = 0, w = u.length;
              for (g; g < w; g++)
                u[g].fn.apply(u[g].ctx, a);
              return this;
            },
            off: function(o, a) {
              var u = this.e || (this.e = {}), g = u[o], w = [];
              if (g && a)
                for (var s = 0, l = g.length; s < l; s++)
                  g[s].fn !== a && g[s].fn._ !== a && w.push(g[s]);
              return w.length ? u[o] = w : delete u[o], this;
            }
          }, n.exports = r, n.exports.TinyEmitter = r;
        },
        /* 2 */
        /***/
        function(n, i2, r) {
          var o = r(3), a = r(4);
          function u(l, p, m) {
            if (!l && !p && !m)
              throw new Error("Missing required arguments");
            if (!o.string(p))
              throw new TypeError("Second argument must be a String");
            if (!o.fn(m))
              throw new TypeError("Third argument must be a Function");
            if (o.node(l))
              return g(l, p, m);
            if (o.nodeList(l))
              return w(l, p, m);
            if (o.string(l))
              return s(l, p, m);
            throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList");
          }
          function g(l, p, m) {
            return l.addEventListener(p, m), {
              destroy: function() {
                l.removeEventListener(p, m);
              }
            };
          }
          function w(l, p, m) {
            return Array.prototype.forEach.call(l, function(v) {
              v.addEventListener(p, m);
            }), {
              destroy: function() {
                Array.prototype.forEach.call(l, function(v) {
                  v.removeEventListener(p, m);
                });
              }
            };
          }
          function s(l, p, m) {
            return a(document.body, l, p, m);
          }
          n.exports = u;
        },
        /* 3 */
        /***/
        function(n, i2) {
          i2.node = function(r) {
            return r !== void 0 && r instanceof HTMLElement && r.nodeType === 1;
          }, i2.nodeList = function(r) {
            var o = Object.prototype.toString.call(r);
            return r !== void 0 && (o === "[object NodeList]" || o === "[object HTMLCollection]") && "length" in r && (r.length === 0 || i2.node(r[0]));
          }, i2.string = function(r) {
            return typeof r == "string" || r instanceof String;
          }, i2.fn = function(r) {
            var o = Object.prototype.toString.call(r);
            return o === "[object Function]";
          };
        },
        /* 4 */
        /***/
        function(n, i2, r) {
          var o = r(5);
          function a(w, s, l, p, m) {
            var v = g.apply(this, arguments);
            return w.addEventListener(l, v, m), {
              destroy: function() {
                w.removeEventListener(l, v, m);
              }
            };
          }
          function u(w, s, l, p, m) {
            return typeof w.addEventListener == "function" ? a.apply(null, arguments) : typeof l == "function" ? a.bind(null, document).apply(null, arguments) : (typeof w == "string" && (w = document.querySelectorAll(w)), Array.prototype.map.call(w, function(v) {
              return a(v, s, l, p, m);
            }));
          }
          function g(w, s, l, p) {
            return function(m) {
              m.delegateTarget = o(m.target, s), m.delegateTarget && p.call(w, m);
            };
          }
          n.exports = u;
        },
        /* 5 */
        /***/
        function(n, i2) {
          var r = 9;
          if (typeof Element < "u" && !Element.prototype.matches) {
            var o = Element.prototype;
            o.matches = o.matchesSelector || o.mozMatchesSelector || o.msMatchesSelector || o.oMatchesSelector || o.webkitMatchesSelector;
          }
          function a(u, g) {
            for (; u && u.nodeType !== r; ) {
              if (typeof u.matches == "function" && u.matches(g))
                return u;
              u = u.parentNode;
            }
          }
          n.exports = a;
        },
        /* 6 */
        /***/
        function(n, i2, r) {
          r.r(i2);
          var o = r(0), a = r.n(o), u = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(y) {
            return typeof y;
          } : function(y) {
            return y && typeof Symbol == "function" && y.constructor === Symbol && y !== Symbol.prototype ? "symbol" : typeof y;
          }, g = function() {
            function y(c, d) {
              for (var f = 0; f < d.length; f++) {
                var C = d[f];
                C.enumerable = C.enumerable || false, C.configurable = true, "value" in C && (C.writable = true), Object.defineProperty(c, C.key, C);
              }
            }
            return function(c, d, f) {
              return d && y(c.prototype, d), f && y(c, f), c;
            };
          }();
          function w(y, c) {
            if (!(y instanceof c))
              throw new TypeError("Cannot call a class as a function");
          }
          var s = function() {
            function y(c) {
              w(this, y), this.resolveOptions(c), this.initSelection();
            }
            return g(y, [{
              key: "resolveOptions",
              value: function() {
                var d = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                this.action = d.action, this.container = d.container, this.emitter = d.emitter, this.target = d.target, this.text = d.text, this.trigger = d.trigger, this.selectedText = "";
              }
              /**
               * Decides which selection strategy is going to be applied based
               * on the existence of `text` and `target` properties.
               */
            }, {
              key: "initSelection",
              value: function() {
                this.text ? this.selectFake() : this.target && this.selectTarget();
              }
              /**
               * Creates a fake textarea element, sets its value from `text` property,
               * and makes a selection on it.
               */
            }, {
              key: "selectFake",
              value: function() {
                var d = this, f = document.documentElement.getAttribute("dir") == "rtl";
                this.removeFake(), this.fakeHandlerCallback = function() {
                  return d.removeFake();
                }, this.fakeHandler = this.container.addEventListener("click", this.fakeHandlerCallback) || true, this.fakeElem = document.createElement("textarea"), this.fakeElem.style.fontSize = "12pt", this.fakeElem.style.border = "0", this.fakeElem.style.padding = "0", this.fakeElem.style.margin = "0", this.fakeElem.style.position = "absolute", this.fakeElem.style[f ? "right" : "left"] = "-9999px";
                var C = window.pageYOffset || document.documentElement.scrollTop;
                this.fakeElem.style.top = C + "px", this.fakeElem.setAttribute("readonly", ""), this.fakeElem.value = this.text, this.container.appendChild(this.fakeElem), this.selectedText = a()(this.fakeElem), this.copyText();
              }
              /**
               * Only removes the fake element after another click event, that way
               * a user can hit `Ctrl+C` to copy because selection still exists.
               */
            }, {
              key: "removeFake",
              value: function() {
                this.fakeHandler && (this.container.removeEventListener("click", this.fakeHandlerCallback), this.fakeHandler = null, this.fakeHandlerCallback = null), this.fakeElem && (this.container.removeChild(this.fakeElem), this.fakeElem = null);
              }
              /**
               * Selects the content from element passed on `target` property.
               */
            }, {
              key: "selectTarget",
              value: function() {
                this.selectedText = a()(this.target), this.copyText();
              }
              /**
               * Executes the copy operation based on the current selection.
               */
            }, {
              key: "copyText",
              value: function() {
                var d = void 0;
                try {
                  d = document.execCommand(this.action);
                } catch {
                  d = false;
                }
                this.handleResult(d);
              }
              /**
               * Fires an event based on the copy operation result.
               * @param {Boolean} succeeded
               */
            }, {
              key: "handleResult",
              value: function(d) {
                this.emitter.emit(d ? "success" : "error", {
                  action: this.action,
                  text: this.selectedText,
                  trigger: this.trigger,
                  clearSelection: this.clearSelection.bind(this)
                });
              }
              /**
               * Moves focus away from `target` and back to the trigger, removes current selection.
               */
            }, {
              key: "clearSelection",
              value: function() {
                this.trigger && this.trigger.focus(), document.activeElement.blur(), window.getSelection().removeAllRanges();
              }
              /**
               * Sets the `action` to be performed which can be either 'copy' or 'cut'.
               * @param {String} action
               */
            }, {
              key: "destroy",
              /**
               * Destroy lifecycle.
               */
              value: function() {
                this.removeFake();
              }
            }, {
              key: "action",
              set: function() {
                var d = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "copy";
                if (this._action = d, this._action !== "copy" && this._action !== "cut")
                  throw new Error('Invalid "action" value, use either "copy" or "cut"');
              },
              get: function() {
                return this._action;
              }
              /**
               * Sets the `target` property using an element
               * that will be have its content copied.
               * @param {Element} target
               */
            }, {
              key: "target",
              set: function(d) {
                if (d !== void 0)
                  if (d && (typeof d > "u" ? "undefined" : u(d)) === "object" && d.nodeType === 1) {
                    if (this.action === "copy" && d.hasAttribute("disabled"))
                      throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');
                    if (this.action === "cut" && (d.hasAttribute("readonly") || d.hasAttribute("disabled")))
                      throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`);
                    this._target = d;
                  } else
                    throw new Error('Invalid "target" value, use a valid Element');
              },
              get: function() {
                return this._target;
              }
            }]), y;
          }(), l = s, p = r(1), m = r.n(p), v = r(2), T = r.n(v), k = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(y) {
            return typeof y;
          } : function(y) {
            return y && typeof Symbol == "function" && y.constructor === Symbol && y !== Symbol.prototype ? "symbol" : typeof y;
          }, S = function() {
            function y(c, d) {
              for (var f = 0; f < d.length; f++) {
                var C = d[f];
                C.enumerable = C.enumerable || false, C.configurable = true, "value" in C && (C.writable = true), Object.defineProperty(c, C.key, C);
              }
            }
            return function(c, d, f) {
              return d && y(c.prototype, d), f && y(c, f), c;
            };
          }();
          function O(y, c) {
            if (!(y instanceof c))
              throw new TypeError("Cannot call a class as a function");
          }
          function h(y, c) {
            if (!y)
              throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            return c && (typeof c == "object" || typeof c == "function") ? c : y;
          }
          function b(y, c) {
            if (typeof c != "function" && c !== null)
              throw new TypeError("Super expression must either be null or a function, not " + typeof c);
            y.prototype = Object.create(c && c.prototype, { constructor: { value: y, enumerable: false, writable: true, configurable: true } }), c && (Object.setPrototypeOf ? Object.setPrototypeOf(y, c) : y.__proto__ = c);
          }
          var E = function(y) {
            b(c, y);
            function c(d, f) {
              O(this, c);
              var C = h(this, (c.__proto__ || Object.getPrototypeOf(c)).call(this));
              return C.resolveOptions(f), C.listenClick(d), C;
            }
            return S(c, [{
              key: "resolveOptions",
              value: function() {
                var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                this.action = typeof f.action == "function" ? f.action : this.defaultAction, this.target = typeof f.target == "function" ? f.target : this.defaultTarget, this.text = typeof f.text == "function" ? f.text : this.defaultText, this.container = k(f.container) === "object" ? f.container : document.body;
              }
              /**
               * Adds a click event listener to the passed trigger.
               * @param {String|HTMLElement|HTMLCollection|NodeList} trigger
               */
            }, {
              key: "listenClick",
              value: function(f) {
                var C = this;
                this.listener = T()(f, "click", function(j) {
                  return C.onClick(j);
                });
              }
              /**
               * Defines a new `ClipboardAction` on each click event.
               * @param {Event} e
               */
            }, {
              key: "onClick",
              value: function(f) {
                var C = f.delegateTarget || f.currentTarget;
                this.clipboardAction && (this.clipboardAction = null), this.clipboardAction = new l({
                  action: this.action(C),
                  target: this.target(C),
                  text: this.text(C),
                  container: this.container,
                  trigger: C,
                  emitter: this
                });
              }
              /**
               * Default `action` lookup function.
               * @param {Element} trigger
               */
            }, {
              key: "defaultAction",
              value: function(f) {
                return _("action", f);
              }
              /**
               * Default `target` lookup function.
               * @param {Element} trigger
               */
            }, {
              key: "defaultTarget",
              value: function(f) {
                var C = _("target", f);
                if (C)
                  return document.querySelector(C);
              }
              /**
               * Returns the support of the given action, or all actions if no action is
               * given.
               * @param {String} [action]
               */
            }, {
              key: "defaultText",
              /**
               * Default `text` lookup function.
               * @param {Element} trigger
               */
              value: function(f) {
                return _("text", f);
              }
              /**
               * Destroy lifecycle.
               */
            }, {
              key: "destroy",
              value: function() {
                this.listener.destroy(), this.clipboardAction && (this.clipboardAction.destroy(), this.clipboardAction = null);
              }
            }], [{
              key: "isSupported",
              value: function() {
                var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : ["copy", "cut"], C = typeof f == "string" ? [f] : f, j = !!document.queryCommandSupported;
                return C.forEach(function(ie) {
                  j = j && !!document.queryCommandSupported(ie);
                }), j;
              }
            }]), c;
          }(m.a);
          function _(y, c) {
            var d = "data-clipboard-" + y;
            if (c.hasAttribute(d))
              return c.getAttribute(d);
          }
          i2.default = E;
        }
        /******/
      ]).default
    );
  });
});
var te = Ye(Ze);
var I = {
  autoSetContainer: false,
  appendToBody: true
};
var Qe = {
  config: (e) => {
    const { autoSetContainer: t, appendToBody: n } = e;
    I.autoSetContainer = t || false, I.appendToBody = n || true;
  },
  install: (e) => {
    e.config.globalProperties.$vclipboard = G, e.directive("clipboard", {
      beforeMount(t, n) {
        if (n.arg === "success")
          t._vClipboard_success = n.value;
        else if (n.arg === "error")
          t._vClipboard_error = n.value;
        else {
          const i2 = new te(t, {
            text: () => n.value,
            action: () => n.arg === "cut" ? "cut" : "copy",
            container: I.autoSetContainer ? t : void 0
          });
          i2.on("success", (r) => {
            const o = t._vClipboard_success;
            o && o(r);
          }), i2.on("error", (r) => {
            const o = t._vClipboard_error;
            o && o(r);
          }), t._vClipboard = i2;
        }
      },
      updated(t, n) {
        n.arg === "success" ? t._vClipboard_success = n.value : n.arg === "error" ? t._vClipboard_error = n.value : (t._vClipboard.text = () => n.value, t._vClipboard.action = () => n.arg === "cut" ? "cut" : "copy");
      },
      unmounted(t, n) {
        n.arg === "success" ? delete t._vClipboard_success : n.arg === "error" ? delete t._vClipboard_error : (t._vClipboard.destroy(), delete t._vClipboard);
      }
    });
  },
  toClipboard: (e, t) => G(e, t)
};
var G = (e, t = "copy") => new Promise((n, i2) => {
  const r = document.createElement("button"), o = new te(r, {
    text: () => e,
    action: () => t
  });
  o.on("success", (a) => {
    o.destroy(), n(a);
  }), o.on("error", (a) => {
    o.destroy(), i2(a);
  }), I.appendToBody && document.body.appendChild(r), r.click(), I.appendToBody && document.body.removeChild(r);
});
var et = Object.assign({ "./components/fs-copyable.vue": () => import("./fs-copyable-52723316-Z6F7PEYV.js") });
var tt = {
  install(e) {
    vt.vite.installAsyncComponents(e, et, [], null, null);
  }
};
var kt = {
  install(e) {
    e.use(We), e.use(tt), e.use(Qe);
  }
};
function nt() {
  return {
    "time-humanize": {
      column: {
        component: {
          name: "fs-time-humanize",
          vModel: "modelValue"
        }
      }
    }
  };
}
var ot2 = {
  install(e) {
    const t = nt(), { addTypes: n } = rp();
    n(t);
  }
};
var rt = Object.assign({ "./components/fs-time-humanize.vue": () => import("./fs-time-humanize-a219f65a-QA34GVWN.js") });
var it = {
  install(e) {
    vt.vite.installAsyncComponents(e, rt, [], null, null);
  }
};
var St = {
  install(e) {
    e.use(ot2), e.use(it);
  }
};
async function W() {
  const e = Object.assign({ "./phoneCodeCountries.ts": () => import("./phoneCodeCountries-923a4b31-OO7PN5C2.js") }), { countries: t } = await e["./phoneCodeCountries.ts"]();
  return t;
}
async function ne(e) {
  const t = await W();
  let n = null;
  return e != null && (e.countryCode != null ? n = t.find((i2) => i2.iso2 === e.countryCode) : e.callingCode != null && (n = t.find((i2) => i2.dialCode === e.callingCode))), n != null && (n = {
    callingCode: n.dialCode,
    countryCode: n.iso2
  }), n;
}
var at = Object.freeze(Object.defineProperty({
  __proto__: null,
  getCountries: W,
  getCountryByValue: ne
}, Symbol.toStringTag, { value: "Module" }));
var st = { class: "fs-phone-input" };
var At = defineComponent({
  __name: "fs-phone-input",
  props: {
    select: {},
    input: {},
    modelValue: {},
    onlyCountries: {},
    ignoredCountries: {},
    priorityCountries: {},
    clearable: { type: Boolean },
    filterable: { type: Boolean },
    defaultCountry: { default: "CN" },
    disabled: { type: Boolean },
    readonly: { type: Boolean }
  },
  emits: ["change", "input", "update:modelValue"],
  setup(e, { emit: t }) {
    const { ui: n } = B(), i2 = n.formItem.injectFormItemContext(), r = e, o = t, a = ref(
      r.modelValue || {
        callingCode: void 0,
        // 电话区号
        countryCode: void 0,
        // 国家代码
        phoneNumber: void 0
        // 电话号码
      }
    ), u = vu({
      value: "iso2",
      label: "label"
    }), g = ref([]);
    async function w() {
      g.value = await W();
    }
    w();
    const s = computed(() => {
      const h = g.value;
      let b = [];
      if (r.onlyCountries != null && r.onlyCountries.length > 0)
        for (let E of h)
          r.onlyCountries.find((_) => _.toLowerCase() === E.iso2.toLowerCase()) && b.push(E);
      else {
        const E = r.priorityCountries || [], _ = r.ignoredCountries || [], y = [], c = [];
        for (let d of h)
          E.find((f) => f.toLowerCase() === d.iso2.toLowerCase()) && y.push(d), _.find((f) => f.toLowerCase() === d.iso2.toLowerCase()) || c.push(d);
        b = y.concat(c);
      }
      return b = b.map((E) => ({
        ...E,
        label: E.name + "(" + E.dialCode + ")"
      })), b;
    }), l = computed(() => {
      const h = {
        placeholder: "请选择",
        [n.select.filterable]: true,
        [n.select.clearable]: true,
        [n.select.modelValue]: a.value.countryCode,
        ["onUpdate:" + n.select.modelValue]: T
      };
      return merge_default(h, r.select);
    }), p = computed(() => {
      const h = {
        placeholder: "请输入",
        [n.select.clearable]: true,
        [n.input.modelValue]: a.value.phoneNumber,
        [`onUpdate:${n.input.modelValue}`]: k
      };
      return merge_default(h, r.input);
    });
    async function m(h) {
      a.value = { callingCode: void 0, countryCode: void 0, phoneNumber: void 0 };
      const b = await v(h);
      b != null && (a.value.callingCode = b.callingCode, a.value.countryCode = b.countryCode), h && h.phoneNumber ? a.value.phoneNumber = h.phoneNumber : a.value.phoneNumber = void 0;
    }
    async function v(h) {
      let b = null;
      return h != null && (h.countryCode != null ? b = s.value.find((E) => E.iso2 === h.countryCode) : h.callingCode != null && (b = s.value.find((E) => E.dialCode === h.callingCode))), b != null && (b = {
        callingCode: b.dialCode,
        countryCode: b.iso2
      }), b == null && (b = await ne({ countryCode: r.defaultCountry })), b;
    }
    async function T(h) {
      await O(h);
      let b = S();
      o("update:modelValue", b), o("input", b), o("change", b), await i2.onChange(), await i2.onBlur();
    }
    async function k(h) {
      if (a.value.phoneNumber = h, a.value.callingCode == null && a.value.countryCode == null) {
        a.value.countryCode = r.defaultCountry;
        const E = await v(a.value);
        E && (a.value.callingCode = E.callingCode);
      }
      let b = S();
      o("update:modelValue", b), o("input", b), o("change", b), await i2.onChange(), await i2.onBlur();
    }
    function S() {
      return {
        countryCode: a.value.countryCode,
        callingCode: a.value.callingCode,
        phoneNumber: a.value.phoneNumber
      };
    }
    async function O(h) {
      h || (a.value.callingCode = void 0), a.value.countryCode = h;
      let b = await v(a.value);
      b && (a.value.callingCode = b.callingCode);
    }
    return watch(
      () => r.modelValue,
      async (h, b) => {
        await m(h), o("change", a.value);
      },
      {
        immediate: true
      }
    ), (h, b) => {
      const E = resolveComponent("fs-dict-select");
      return openBlock(), createElementBlock("div", st, [
        createVNode(E, mergeProps({
          disabled: h.disabled,
          readonly: h.readonly,
          filterable: h.filterable,
          clearable: h.clearable,
          options: s.value,
          dict: unref(u),
          "show-search": true,
          "allow-clear": true
        }, l.value), null, 16, ["disabled", "readonly", "filterable", "clearable", "options", "dict"]),
        (openBlock(), createBlock(resolveDynamicComponent(unref(n).input.name), mergeProps({
          type: "text",
          clearable: h.clearable,
          disabled: h.disabled,
          readonly: h.readonly,
          "allow-clear": true
        }, p.value), null, 16, ["clearable", "disabled", "readonly"]))
      ]);
    };
  }
});
async function oe({ phoneNumber: e, countryCode: t }) {
  const i2 = (await import("./index-1f520acb-ZXEK6AZA.js")).parsePhoneNumberFromString, r = e && t ? i2(e, t) : null;
  return {
    phoneNumber: e || null,
    countryCode: t,
    isValid: false,
    ...r ? {
      formattedNumber: r.number,
      nationalNumber: r.nationalNumber,
      isValid: r.isValid(),
      type: r.getType(),
      formatInternational: r.formatInternational(),
      formatNational: r.formatNational(),
      uri: r.getURI(),
      e164: r.format("E.164")
    } : null
  };
}
async function re(e) {
  const t = Object.assign({ "./utils.ts": () => Promise.resolve().then(() => at) }), { getCountryByValue: n } = await t["./utils.ts"]();
  return n(e);
}
async function Ft(e, t) {
  if (!t || t.phoneNumber == null || t.phoneNumber === "")
    return true;
  if (!t.countryCode && t.callingCode) {
    const i2 = await re(t);
    i2 && (t.countryCode = i2.countryCode);
  }
  const n = await oe({
    phoneNumber: t.phoneNumber,
    countryCode: t.countryCode
  });
  if (!n.isValid)
    throw console.warn("parse:", n), new Error("电话号码错误");
  return true;
}
async function lt(e, t, n) {
  if (!t || t.phoneNumber == null || t.phoneNumber === "" || t.countryCode == null || t.countryCode === "")
    return true;
  if (!t.countryCode && t.callingCode) {
    const r = await re(t);
    r && (t.countryCode = r.countryCode);
  }
  const i2 = await oe({
    phoneNumber: t.phoneNumber,
    countryCode: t.countryCode
  });
  if (!i2.isValid || i2.type !== "MOBILE" && i2.type !== "FIXED_LINE_OR_MOBILE")
    throw console.warn("parse:", i2), new Error("手机号错误");
  return true;
}
async function Nt(e, t) {
  const n = t.countryCode == null || t.countryCode === "", i2 = t.callingCode == null || t.callingCode === "", r = t.phoneNumber == null || t.phoneNumber === "";
  if (!t || r || n && i2)
    throw new Error("该项必填");
  return true;
}
function ut() {
  return {
    phone: {
      column: {
        cellRender({
          value: e
        }) {
          return !e || !e.phoneNumber ? "" : createVNode("div", null, [createTextVNode("("), e.callingCode || "86", createTextVNode(")"), e.phoneNumber]);
        }
      },
      form: {
        component: {
          name: "fs-phone-input",
          vModel: "modelValue"
        },
        rules: [{
          validator: lt,
          message: "请填写正确的手机号码"
        }]
      }
    }
  };
}
var ct = {
  install(e) {
    const t = ut(), { addTypes: n } = rp();
    n(t);
  }
};
var dt = Object.assign({ "./components/fs-phone-input/fs-phone-input.vue": () => import("./fs-phone-input-a751befb-YWECT3UL.js") });
var ft = {
  install(e) {
    vt.vite.installAsyncComponents(e, dt, [], null, null);
  }
};
var Vt = {
  install(e) {
    e.use(ct), e.use(ft);
  }
};

export {
  he,
  _e,
  F,
  gt,
  ht,
  Fe,
  vt2 as vt,
  bt,
  wt,
  Me,
  Ct,
  Et,
  Tt,
  _t,
  xt,
  kt,
  St,
  At,
  oe,
  Ft,
  lt,
  Nt,
  Vt
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/index-179c3581.mjs:
  (*!
    * @soerenmartius/vue3-clipboard v0.1.2
    * (c) 2021 Soeren Martius
    * @license MIT
    *)
  (*!
   * clipboard.js v2.0.6
   * https://clipboardjs.com/
   * 
   * Licensed MIT © Zeno Rocha
   *)
*/
//# sourceMappingURL=chunk-OFUQC3BW.js.map
