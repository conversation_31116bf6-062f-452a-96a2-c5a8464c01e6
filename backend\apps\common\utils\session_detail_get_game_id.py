import logging
from typing import Dict, Optional, List, Callable
from urllib.parse import urlparse, parse_qs
import re
import json
from apps.kcs.models import Game

logger = logging.getLogger(__name__)

def get_game_id_from_session_detail(session_detail: Dict) -> Optional[int]:
    """
    从会话详情中提取游戏ID（非必要字段，获取失败不影响主流程）
    该函数按优先级顺序尝试多种提取规则。

    Args:
        session_detail: 会话详情数据

    Returns:
        Optional[int]: 数据库中的游戏主键ID (game_obj.id)，如果获取失败返回None
    """
    if not session_detail:
        logger.debug("[情绪分析] 会话详情为空，跳过游戏ID提取")
        return None

    # --- 定义具体的提取策略函数 ---
    def _from_foreign_id(detail: Dict) -> Optional[str]:
        """规则1: 从 foreignId 字段的'#'后提取"""
        if value := detail.get("foreignId"):
            if '#' in value:
                return value.split('#')[-1]
        return None

    def _from_frompage_path(detail: Dict) -> Optional[str]:
        """规则2: 从 fromPage 的 URL 路径末尾提取 (e.g., /contactCs/5289)"""
        if value := detail.get("fromPage"):
            try:
                parsed_url = urlparse(value)
                match = re.search(r'/(\d+)(?:[?#]|$)', parsed_url.path)
                if match:
                    return match.group(1)
            except Exception:
                pass
        return None

    def _from_frompage_nonstandard_u_fragment(detail: Dict) -> Optional[str]:
        """
        规则3 (新增): 处理非标准URL, 从 fromPage 的原始字符串中用正则提取 u=...#game_id 模式
        """
        if value := detail.get("fromPage"):
            # 正则解释:
            # [?&]     - 匹配 '?' 或 '&', 即参数的开始
            # u=        - 匹配 'u='
            # [^&#]* - 匹配任意非'&'和'#'的字符0次或多次 (即u的值)
            # #         - 匹配 '#'
            # (\d+)     - 捕获一个或多个数字 (这是我们想要的game_id)
            match = re.search(r'[?&]u=[^&#]*#(\d+)', value)
            if match:
                return match.group(1)
        return None
    
    def _from_frompage_query_u(detail: Dict) -> Optional[str]:
        """规则4 (原规则): 从 fromPage 的标准查询参数 'u' 的值中'#'后提取"""
        if value := detail.get("fromPage"):
            try:
                parsed_url = urlparse(value)
                query_params = parse_qs(parsed_url.query)
                if 'u' in query_params and query_params['u']:
                    u_value = query_params['u'][0]
                    if '#' in u_value:
                        return u_value.split('#')[-1]
            except Exception:
                pass
        return None
    
    # --- 按优先级组合提取策略 ---
    extraction_strategies: List[Callable[[Dict], Optional[str]]] = [
        _from_foreign_id,
        _from_frompage_path,
        _from_frompage_nonstandard_u_fragment, 
        _from_frompage_query_u,
    ]

    # --- 依次执行提取策略 ---
    for strategy in extraction_strategies:
        try:
            game_id_str = strategy(session_detail)
            if game_id_str and game_id_str.isdigit():
                game_id = int(game_id_str)
                if game_obj := Game.objects.filter(game_id=game_id).first():
                    logger.debug(f"[情绪分析] 通过策略 {strategy.__name__} 提取到游戏ID: {game_id}")
                    return game_obj
        except (ValueError, IndexError) as e:
            logger.debug(f"[情绪分析] 策略 {strategy.__name__} 执行出错: {e}")
            continue

    # --- 尝试从CRM信息提取 (作为补充) ---
    if user_crm_info := session_detail.get("userCrmInfo"):
        # ... (此部分逻辑不变) ...
        try:
            crm_data = json.loads(user_crm_info) if isinstance(user_crm_info, str) else user_crm_info
            for item in crm_data:
                if item.get("key") == "game_id" and str(item.get("value")).isdigit():
                    game_id = int(item["value"])
                    if game_obj := Game.objects.filter(game_id=game_id):
                        logger.debug(f"[情绪分析] 从CRM信息提取到游戏ID: {game_id}")
                        return game_obj.id
        except (json.JSONDecodeError, ValueError, TypeError, KeyError):
            pass

    logger.debug("[情绪分析] 未能从会话详情提取到有效的游戏ID")
    return None