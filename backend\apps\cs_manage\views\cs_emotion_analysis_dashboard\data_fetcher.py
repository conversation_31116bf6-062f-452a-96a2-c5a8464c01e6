"""
数据获取器

负责从各种数据源获取原始数据，包括七鱼API和本地数据库
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

from apps.common.qiyu_service import get_qiyu_service, QiyuRateLimitError
from apps.cs_manage.models import CsEmotionAnalysis
from dvadmin.system.models import Users
from .cache_manager import cache_manager

logger = logging.getLogger(__name__)


class DataFetcher:
    """
    数据获取器
    
    职责：
    1. 从七鱼API获取报表数据
    2. 从数据库获取情绪分析数据 
    3. 获取系统用户信息
    4. 处理数据获取异常
    """
    
    def __init__(self):
        self.qiyu_service = get_qiyu_service()
    
    def fetch_qiyu_reports(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """
        获取七鱼报表数据（带缓存）
        
        Args:
            start_time: 开始时间戳（毫秒）
            end_time: 结束时间戳（毫秒）
            
        Returns:
            Dict: 包含quality、attendance、workload等报表数据
        """
        def _fetch():
            return self._fetch_qiyu_reports_raw(start_time, end_time)
        
        return cache_manager.get_or_fetch(
            data_type="qiyu_reports",
            fetch_func=_fetch,
            start_time=start_time,
            end_time=end_time
        )
    
    def _fetch_qiyu_reports_raw(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """
        原始七鱼数据获取（无缓存）
        
        Args:
            start_time: 开始时间戳（毫秒）
            end_time: 结束时间戳（毫秒）
            
        Returns:
            Dict: 七鱼报表数据
        """
        reports_data = {
            'quality': [],
            'attendance': [],
            'workload': [],
            'workload_summary': {}
        }
        
        try:
            # 1. 获取客服质量报表
            quality_report = self.qiyu_service.get_staff_quality_report_web(
                start_time=start_time,
                end_time=end_time,
                use_cache=True
            )
            reports_data['quality'] = quality_report or []
            
            # 避免频率限制
            time.sleep(1)
            
            # 2. 获取考勤报表
            attendance_detail = self.qiyu_service.get_staff_attendance_detail_web(
                start_time=start_time,
                end_time=end_time,
                use_cache=True
            )
            reports_data['attendance'] = attendance_detail or []
            
            # 避免频率限制
            time.sleep(1)
            
            # 3. 获取工作量报表
            staff_work_data = self.qiyu_service.get_staff_work_list(
                start_time=start_time,
                end_time=end_time
            )
            
            if staff_work_data and 'staffworkload' in staff_work_data:
                reports_data['workload'] = staff_work_data['staffworkload']
                reports_data['workload_summary'] = staff_work_data.get('sum', {})
            
        except QiyuRateLimitError as e:
            logger.warning(f"Qiyu rate limit reached: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to fetch qiyu reports: {str(e)}")
        
        return reports_data
    

    
    def fetch_emotion_analysis(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        获取情绪分析数据（实时查询，不缓存）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 按客服ID组织的情绪分析统计数据
        """
        try:
            # 实时查询数据库
            emotion_queryset = CsEmotionAnalysis.objects.filter(
                create_datetime__gte=start_date,
                create_datetime__lte=end_date,
                status='completed'
            ).select_related('service_user')
            
            emotion_count = emotion_queryset.count()
            
            if emotion_count == 0:
                return {}
            
            # 按service_id分组统计
            emotion_stats = {}
            for analysis in emotion_queryset:
                service_id = str(analysis.service_id)
                
                if service_id not in emotion_stats:
                    emotion_stats[service_id] = {
                        'service_name': analysis.service_name,
                        'analysis_count': 0,
                        'total_emotion_change': 0,
                        'positive_changes': 0,
                        'negative_changes': 0,
                        'emotion_scores': {
                            'initial': [],
                            'final': [],
                            'changes': []
                        }
                    }
                
                stats = emotion_stats[service_id]
                stats['analysis_count'] += 1
                
                # 收集分数用于计算平均值
                if analysis.initial_emotion_score is not None:
                    stats['emotion_scores']['initial'].append(analysis.initial_emotion_score)
                
                if analysis.final_emotion_score is not None:
                    stats['emotion_scores']['final'].append(analysis.final_emotion_score)
                
                if analysis.emotion_change_score is not None:
                    stats['emotion_scores']['changes'].append(analysis.emotion_change_score)
                    stats['total_emotion_change'] += analysis.emotion_change_score
                    
                    if analysis.emotion_change_score > 0:
                        stats['positive_changes'] += 1
                    elif analysis.emotion_change_score < 0:
                        stats['negative_changes'] += 1
            
            # 计算平均值
            for service_id, stats in emotion_stats.items():
                scores = stats['emotion_scores']
                
                stats['avg_initial_emotion'] = (
                    sum(scores['initial']) / len(scores['initial']) 
                    if scores['initial'] else 0
                )
                stats['avg_final_emotion'] = (
                    sum(scores['final']) / len(scores['final']) 
                    if scores['final'] else 0
                )
                stats['avg_emotion_change'] = (
                    sum(scores['changes']) / len(scores['changes']) 
                    if scores['changes'] else 0
                )
                
                # 清理临时数据
                del stats['emotion_scores']
            
            return emotion_stats
            
        except Exception as e:
            logger.error(f"Failed to fetch emotion analysis: {str(e)}")
            return {}
    
    def fetch_staff_detail_emotions(
        self, 
        staff_id: str, 
        start_date: datetime, 
        end_date: datetime, 
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        获取单个客服的详细情绪分析记录
        
        Args:
            staff_id: 客服ID
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回记录数限制
            
        Returns:
            List: 情绪分析记录列表
        """
        try:
            emotion_analyses = CsEmotionAnalysis.objects.filter(
                service_id=staff_id,
                create_datetime__gte=start_date,
                create_datetime__lte=end_date,
                status='completed'
            ).order_by('-create_datetime')[:limit]
            
            emotion_records = []
            for analysis in emotion_analyses:
                emotion_records.append({
                    'session_id': analysis.session_id,
                    'create_datetime': analysis.create_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                    'initial_emotion_score': analysis.initial_emotion_score,
                    'final_emotion_score': analysis.final_emotion_score,
                    'emotion_change_score': analysis.emotion_change_score,
                    'conversation_summary': analysis.conversation_summary,
                    'session_duration': analysis.session_duration,
                    'session_duration_minutes': round(analysis.session_duration / 60, 1) if analysis.session_duration else 0,
                    'single_emotion_contribution': round(analysis.emotion_change_score or 0, 4),
                    'message_count': analysis.message_count,
                    'user_message_count': analysis.user_message_count,
                    'service_message_count': analysis.service_message_count
                })
            
            return emotion_records
            
        except Exception as e:
            logger.error(f"Failed to fetch emotion details for staff {staff_id}: {str(e)}")
            return []
    
    def fetch_all_staff_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有客服的基础信息
        
        Returns:
            Dict: 客服ID -> 基础信息的字典
        """
        def _fetch():
            return self._fetch_all_staff_info_raw()
        
        return cache_manager.get_or_fetch(
            data_type="all_staff_info",
            fetch_func=_fetch,
            timeout=3600 * 6  # 6小时缓存
        )
    
    def _fetch_all_staff_info_raw(self) -> Dict[str, Dict[str, Any]]:
        """
        原始获取所有客服基础信息
        
        Returns:
            Dict: 客服信息字典
        """
        staff_info = {}
        
        try:
            # 从七鱼历史数据中获取客服信息（最近90天）
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=90)).timestamp() * 1000)
            
            historical_reports = self._fetch_qiyu_reports_raw(start_time, end_time)
            
            # 从质量报表中收集客服信息
            for quality_item in historical_reports.get('quality', []):
                staff_id = str(quality_item.get('id', ''))
                if staff_id and staff_id not in staff_info:
                    staff_info[staff_id] = {
                        'staff_id': staff_id,
                        'staff_name': quality_item.get('name', f'客服{staff_id}'),
                        'staff_account': quality_item.get('namePinyin', staff_id),
                        'email': '',
                        'dept_name': ''
                    }
            
            # 从工作量报表中收集客服信息
            for workload_item in historical_reports.get('workload', []):
                staff_id = str(workload_item.get('id', ''))
                if staff_id and staff_id not in staff_info:
                    staff_info[staff_id] = {
                        'staff_id': staff_id,
                        'staff_name': workload_item.get('name', f'客服{staff_id}'),
                        'staff_account': workload_item.get('namePinyin', staff_id),
                        'email': '',
                        'dept_name': ''
                    }
            

            
        except Exception as e:
            logger.warning(f"Failed to get staff info from qiyu: {str(e)}")
        
        # 备用方案：从系统用户表获取
        if not staff_info:
            try:
                staff_users = Users.objects.filter(
                    is_active=True,
                ).values('id', 'username', 'name', 'email', 'dept__name')[:50]
                
                for user in staff_users:
                    staff_id = str(user['id'])
                    staff_info[staff_id] = {
                        'staff_id': staff_id,
                        'staff_name': user['name'] or user['username'],
                        'staff_account': user['username'],
                        'email': user['email'] or '',
                        'dept_name': user['dept__name'] or ''
                    }
                
            except Exception as e:
                logger.error(f"Failed to get staff info from system users: {str(e)}")
        
        return staff_info


# 全局数据获取器实例
data_fetcher = DataFetcher() 