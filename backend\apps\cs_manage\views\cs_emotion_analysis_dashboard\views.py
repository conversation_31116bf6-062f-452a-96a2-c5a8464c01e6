"""
客服数据分析Dashboard API视图

提供简洁清晰的API接口，整合数据获取、计算和缓存功能
支持异步任务管理和进度追踪
"""

import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, Any

from django.core.exceptions import ValidationError
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from dvadmin.utils.json_response import SuccessResponse, ErrorResponse
from .data_fetcher import data_fetcher
from .metrics_calculator import metrics_calculator
from .async_manager import async_task_manager

logger = logging.getLogger(__name__)


class DashboardAPI(APIView):
    """
    Dashboard数据API
    
    提供客服团队数据分析的主要接口
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        获取Dashboard数据（支持异步模式）
        
        Query参数:
        - start_date: 开始日期 (YYYY-MM-DD，可选，默认30天前)
        - end_date: 结束日期 (YYYY-MM-DD，可选，默认昨天)
        - show_all: 是否显示所有客服 (true/false，可选，默认false)
        - async_mode: 是否使用异步模式 (true/false，可选，默认true)
        - task_id: 任务ID，用于查询任务状态（异步模式下使用）
        """
        try:
            # 获取查询参数
            task_id = request.query_params.get('task_id')
            async_mode = request.query_params.get('async_mode', 'true').lower() == 'true'
            
            # 如果是查询任务状态
            if task_id:
                task_status = async_task_manager.get_task_status(task_id)
                return SuccessResponse(data=task_status, msg="任务状态查询成功")
            
            # 获取并验证查询参数
            params = self._get_and_validate_params(request)
            
            # 生成请求参数键
            params_key = f"{params['start_date_str']}_{params['end_date_str']}_{params['show_all']}"
            
            # 如果启用异步模式
            if async_mode:
                user_id = str(request.user.id)
                task_result = async_task_manager.create_task(
                    api_type="dashboard",
                    params=params_key,
                    user_id=user_id
                )
                
                if task_result['status'] in ['running', 'queued', 'waiting']:
                    # 启动后台任务（仅在新任务时）
                    if task_result['status'] == 'running':
                        threading.Thread(
                            target=self._execute_dashboard_task,
                            args=(task_result['task_id'], params),
                            daemon=True
                        ).start()
                
                return SuccessResponse(data=task_result, msg="异步任务已启动")
            
            # 同步模式：直接获取数据
            dashboard_data = self._get_dashboard_data_sync(params)
            return SuccessResponse(data=dashboard_data, msg="Dashboard数据获取成功")
            
        except ValidationError as e:
            logger.warning(f"Dashboard validation error: {str(e)}")
            return ErrorResponse(msg=f"参数错误: {str(e)}")
        except Exception as e:
            logger.error(f"Dashboard error: {str(e)}")
            return ErrorResponse(msg=f"获取数据失败: {str(e)}")
    
    def _get_and_validate_params(self, request) -> Dict[str, Any]:
        """获取并验证请求参数"""
        # 获取查询参数
        end_date_str = request.query_params.get('end_date')
        start_date_str = request.query_params.get('start_date')
        show_all = request.query_params.get('show_all', 'false').lower() == 'true'
        
        # 确保参数是字符串类型
        if end_date_str and not isinstance(end_date_str, str):
            logger.warning(f"end_date_str is not string: {type(end_date_str)}")
            end_date_str = str(end_date_str)
        
        if start_date_str and not isinstance(start_date_str, str):
            logger.warning(f"start_date_str is not string: {type(start_date_str)}")
            start_date_str = str(start_date_str)
        
        # 设置默认时间范围（最近30天）
        if not end_date_str:
            yesterday = datetime.now() - timedelta(days=1)
            end_date_str = yesterday.strftime('%Y-%m-%d')
        
        if not start_date_str:
            try:
                start_date = datetime.strptime(end_date_str, '%Y-%m-%d') - timedelta(days=30)
                start_date_str = start_date.strftime('%Y-%m-%d')
            except (ValueError, TypeError) as e:
                logger.error(f"Error parsing end_date_str '{end_date_str}': {str(e)}")
                raise ValidationError(f"日期格式错误: {str(e)}")
        
        # 验证日期范围
        start_timestamp, end_timestamp = self._validate_date_range(start_date_str, end_date_str)
        
        # 计算日期范围天数
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            date_range_days = (end_date - start_date).days + 1
        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing dates '{start_date_str}', '{end_date_str}': {str(e)}")
            raise ValidationError(f"日期格式错误: {str(e)}")
        
        return {
            'start_date_str': start_date_str,
            'end_date_str': end_date_str,
            'start_date': start_date,
            'end_date': end_date.replace(hour=23, minute=59, second=59),
            'start_timestamp': start_timestamp,
            'end_timestamp': end_timestamp,
            'show_all': show_all,
            'date_range_days': date_range_days
        }
    
    def _validate_date_range(self, start_date: str, end_date: str) -> tuple:
        """
        验证日期范围
        
        Args:
            start_date: 开始日期字符串 (YYYY-MM-DD)
            end_date: 结束日期字符串 (YYYY-MM-DD)
            
        Returns:
            tuple: (start_timestamp, end_timestamp) 毫秒级时间戳
            
        Raises:
            ValidationError: 日期范围无效时抛出
        """
        try:
            # 确保参数是字符串类型
            if not isinstance(start_date, str):
                logger.error(f"start_date is not string: {type(start_date)}, value: {start_date}")
                raise ValidationError(f"开始日期参数类型错误: 期望字符串，得到 {type(start_date)}")
            
            if not isinstance(end_date, str):
                logger.error(f"end_date is not string: {type(end_date)}, value: {end_date}")
                raise ValidationError(f"结束日期参数类型错误: 期望字符串，得到 {type(end_date)}")
            
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            # 结束时间设置为当天23:59:59
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            
            # 验证时间范围
            if start_dt >= end_dt:
                raise ValidationError("开始时间必须早于结束时间")
            
            # 验证不能超过60天
            if (end_dt - start_dt).days > 60:
                raise ValidationError("查询时间范围不能超过60天")
            
            # 验证结束时间不能超过今天零点
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            if end_dt.date() >= today_start.date():
                # 如果查询今天，结束时间设置为昨天23:59:59
                yesterday = today_start - timedelta(days=1)
                end_dt = yesterday.replace(hour=23, minute=59, second=59)
            
            # 转换为毫秒级时间戳
            start_timestamp = int(start_dt.timestamp() * 1000)
            end_timestamp = int(end_dt.timestamp() * 1000)
            
            return start_timestamp, end_timestamp
            
        except ValueError as e:
            raise ValidationError(f"日期格式错误: {str(e)}")
    
    def _execute_dashboard_task(self, task_id: str, params: Dict[str, Any]):
        """
        执行Dashboard数据获取任务（后台线程）
        """
        try:
            # 更新进度：开始获取数据
            async_task_manager.update_progress(task_id, 10, "开始获取数据...")
            
            result = self._get_dashboard_data_sync(params, task_id)
            
            # 完成任务
            async_task_manager.complete_task(task_id, result)
            
        except Exception as e:
            logger.error(f"Dashboard task {task_id} failed: {str(e)}")
            async_task_manager.fail_task(task_id, str(e))
    
    def _get_dashboard_data_sync(self, params: Dict[str, Any], task_id: str = None) -> Dict[str, Any]:
        """
        同步获取Dashboard数据
        
        Args:
            params: 包含所有必要参数的字典
            task_id: 可选的任务ID，用于更新进度
            
        Returns:
            Dict: Dashboard数据
        """
        if task_id:
            async_task_manager.update_progress(task_id, 20, "获取七鱼报表数据...")
        
        # 1. 获取七鱼报表数据（带缓存）
        reports_data = data_fetcher.fetch_qiyu_reports(params['start_timestamp'], params['end_timestamp'])
        
        if task_id:
            async_task_manager.update_progress(task_id, 60, "获取情绪分析数据...")
        
        # 3. 获取情绪分析数据（实时查询）
        emotion_data = data_fetcher.fetch_emotion_analysis(params['start_date'], params['end_date'])
        
        if task_id:
            async_task_manager.update_progress(task_id, 80, "计算客服指标...")
        
        # 4. 计算客服指标
        staff_metrics = metrics_calculator.calculate_staff_metrics(reports_data, emotion_data)
        
        # 5. 如果需要显示全部客服，补充空数据
        if params['show_all']:
            all_staff_info = data_fetcher.fetch_all_staff_info()
            staff_metrics = metrics_calculator.supplement_empty_staff(staff_metrics, all_staff_info)
        
        if task_id:
            async_task_manager.update_progress(task_id, 90, "计算团队汇总数据...")
        
        # 6. 计算团队汇总数据（不再使用团队概览数据）
        team_summary = metrics_calculator.calculate_team_summary(
            staff_metrics, {}, params['date_range_days']
        )
        
        if task_id:
            async_task_manager.update_progress(task_id, 95, "准备返回数据...")
        
        # 7. 准备返回数据
        result = {
            'date_range': {
                'start_date': params['start_date_str'],
                'end_date': params['end_date_str']
            },
            'team_summary': team_summary,
            'staff_list': list(staff_metrics.values()),
            'data_sources': {
                'qiyu_reports_available': bool(reports_data.get('quality') or reports_data.get('workload')),
                'team_overview_available': False,  # 已移除团队概览功能
                'emotion_analysis_available': bool(emotion_data),
                'total_staff_with_data': len([
                    s for s in staff_metrics.values() 
                    if s.get('total_sessions', 0) > 0 or s.get('online_duration', 0) > 0
                ]),
                'total_staff_all': len(staff_metrics),
                'cache_policy': {
                    'qiyu_data_cached': True,
                    'emotion_data_realtime': True,
                    'result_not_cached': True
                }
            }
        }
        
        return result


class StaffDetailAPI(APIView):
    """
    客服详情API
    
    提供单个客服的详细分析数据
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        获取单个客服的详细分析数据
        
        Query参数:
        - staff_id: 客服ID (必须)
        - start_date: 开始日期 (YYYY-MM-DD，可选，默认30天前)
        - end_date: 结束日期 (YYYY-MM-DD，可选，默认昨天)
        """
        try:
            # 验证staff_id参数
            staff_id = request.query_params.get('staff_id')
            if not staff_id:
                return ErrorResponse(msg="缺少staff_id参数")
            
            # 获取并验证其他参数
            dashboard = DashboardAPI()
            params = dashboard._get_and_validate_params(request)
            
            # 获取详细数据
            detail_data = self._get_staff_detail_data(staff_id, params)
            
            return SuccessResponse(data=detail_data, msg="客服详情获取成功")
            
        except ValidationError as e:
            logger.warning(f"Staff detail validation error: {str(e)}")
            return ErrorResponse(msg=f"参数错误: {str(e)}")
        except Exception as e:
            logger.error(f"Staff detail error: {str(e)}")
            return ErrorResponse(msg=f"获取详情失败: {str(e)}")
    
    def _get_staff_detail_data(self, staff_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取客服详细数据
        
        Args:
            staff_id: 客服ID
            params: 验证后的参数字典
            
        Returns:
            Dict: 客服详情数据
        """
        # 1. 获取基础数据（复用Dashboard逻辑）
        reports_data = data_fetcher.fetch_qiyu_reports(params['start_timestamp'], params['end_timestamp'])
        emotion_data = data_fetcher.fetch_emotion_analysis(params['start_date'], params['end_date'])
        staff_metrics = metrics_calculator.calculate_staff_metrics(reports_data, emotion_data)
        
        # 2. 获取指定客服的数据
        staff_detail = staff_metrics.get(staff_id)
        if not staff_detail:
            raise ValidationError("未找到该客服的数据")
        
        # 3. 计算部门平均值
        dept_averages = metrics_calculator.calculate_department_averages(
            staff_metrics, staff_detail.get('dept_name', '')
        )
        
        # 4. 获取详细情绪分析记录
        emotion_records = data_fetcher.fetch_staff_detail_emotions(
            staff_id, params['start_date'], params['end_date']
        )
        
        # 5. 计算情绪摘要
        emotion_summary = self._calculate_emotion_summary(emotion_records, staff_detail)
        
        # 6. 准备返回数据
        result = {
            'staff_info': staff_detail,
            'emotion_records': emotion_records,
            'emotion_summary': emotion_summary,
            'department_averages': dept_averages,
            'date_range': {
                'start_date': params['start_date_str'],
                'end_date': params['end_date_str']
            }
        }
        
        return result
    
    def _calculate_emotion_summary(
        self, 
        emotion_records: list, 
        staff_detail: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        计算情绪分析摘要
        
        Args:
            emotion_records: 情绪分析记录列表
            staff_detail: 客服详细信息
            
        Returns:
            Dict: 情绪摘要数据
        """
        if not emotion_records:
            return {
                'total_records': 0,
                'avg_emotion_change': 0,
                'total_emotion_contribution': 0,
                'positive_changes': 0,
                'negative_changes': 0,
                'neutral_changes': 0
            }
        
        # 统计各类变化
        positive_changes = len([r for r in emotion_records if r['emotion_change_score'] and r['emotion_change_score'] > 0])
        negative_changes = len([r for r in emotion_records if r['emotion_change_score'] and r['emotion_change_score'] < 0])
        neutral_changes = len([r for r in emotion_records if not r['emotion_change_score'] or r['emotion_change_score'] == 0])
        
        # 计算总贡献度
        total_emotion_contribution = sum(r['single_emotion_contribution'] for r in emotion_records)
        
        return {
            'total_records': len(emotion_records),
            'avg_emotion_change': staff_detail.get('avg_emotion_change', 0),
            'total_emotion_contribution': round(total_emotion_contribution, 4),
            'positive_changes': positive_changes,
            'negative_changes': negative_changes,
            'neutral_changes': neutral_changes
        } 