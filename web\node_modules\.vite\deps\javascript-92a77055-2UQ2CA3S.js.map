{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js", "../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/javascript/javascript.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/typescript/typescript.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  onEnterRules: [\n    {\n      // e.g. /** | */\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      afterText: /^\\s*\\*\\/$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g. /** ...|\n      beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \" * \"\n      }\n    },\n    {\n      // e.g.  * ...|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*(\\ ([^\\*]|\\*(?!\\/))*)?$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        appendText: \"* \"\n      }\n    },\n    {\n      // e.g.  */|\n      beforeText: /^(\\t|(\\ \\ ))*\\ \\*\\/\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.None,\n        removeText: 1\n      }\n    }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*#?region\\\\b\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*#?endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".ts\",\n  keywords: [\n    // Should match the keys of textToKeywordObj in\n    // https://github.com/microsoft/TypeScript/blob/master/src/compiler/scanner.ts\n    \"abstract\",\n    \"any\",\n    \"as\",\n    \"asserts\",\n    \"bigint\",\n    \"boolean\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"const\",\n    \"constructor\",\n    \"debugger\",\n    \"declare\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"function\",\n    \"get\",\n    \"if\",\n    \"implements\",\n    \"import\",\n    \"in\",\n    \"infer\",\n    \"instanceof\",\n    \"interface\",\n    \"is\",\n    \"keyof\",\n    \"let\",\n    \"module\",\n    \"namespace\",\n    \"never\",\n    \"new\",\n    \"null\",\n    \"number\",\n    \"object\",\n    \"out\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"override\",\n    \"readonly\",\n    \"require\",\n    \"global\",\n    \"return\",\n    \"satisfies\",\n    \"set\",\n    \"static\",\n    \"string\",\n    \"super\",\n    \"switch\",\n    \"symbol\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"typeof\",\n    \"undefined\",\n    \"unique\",\n    \"unknown\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"async\",\n    \"await\",\n    \"of\"\n  ],\n  operators: [\n    \"<=\",\n    \">=\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"=>\",\n    \"+\",\n    \"-\",\n    \"**\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"<<\",\n    \"</\",\n    \">>\",\n    \">>>\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"!\",\n    \"~\",\n    \"&&\",\n    \"||\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"@\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /#?[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w\\$]*/, \"type.identifier\"],\n      // to show class names nicely\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([dgimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"],\n      [/`/, \"string\", \"@string_backtick\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([dgimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    string_single: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    string_backtick: [\n      [/\\$\\{/, { token: \"delimiter.bracket\", next: \"@bracketCounting\" }],\n      [/[^\\\\`$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/`/, \"string\", \"@pop\"]\n    ],\n    bracketCounting: [\n      [/\\{/, \"delimiter.bracket\", \"@bracketCounting\"],\n      [/\\}/, \"delimiter.bracket\", \"@pop\"],\n      { include: \"common\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n", "/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/javascript/javascript.ts\nimport { conf as tsConf, language as tsLanguage } from \"../typescript/typescript.js\";\nvar conf = tsConf;\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".js\",\n  keywords: [\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"const\",\n    \"constructor\",\n    \"debugger\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"function\",\n    \"get\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"instanceof\",\n    \"let\",\n    \"new\",\n    \"null\",\n    \"return\",\n    \"set\",\n    \"static\",\n    \"super\",\n    \"switch\",\n    \"symbol\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"undefined\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"async\",\n    \"await\",\n    \"of\"\n  ],\n  typeKeywords: [],\n  operators: tsLanguage.operators,\n  symbols: tsLanguage.symbols,\n  escapes: tsLanguage.escapes,\n  digits: tsLanguage.digits,\n  octaldigits: tsLanguage.octaldigits,\n  binarydigits: tsLanguage.binarydigits,\n  hexdigits: tsLanguage.hexdigits,\n  regexpctl: tsLanguage.regexpctl,\n  regexpesc: tsLanguage.regexpesc,\n  tokenizer: tsLanguage.tokenizer\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAIA,IAAY,OAAO;AAAvB,IACIC,IAAmB,OAAO;AAD9B,IAEIC,IAAoB,OAAO;AAF/B,IAGIC,IAAe,OAAO,UAAU;AAHpC,IAIIC,IAAc,CAACC,GAAIC,GAAMC,GAAQC,MAAS;AAC5C,MAAIF,KAAQ,OAAOA,KAAS,YAAY,OAAOA,KAAS;AACtD,aAASG,KAAOP,EAAkBI,CAAI;AAChC,OAACH,EAAa,KAAKE,GAAII,CAAG,KAAKA,MAAQF,KACzCP,EAAUK,GAAII,GAAK,EAAE,KAAK,MAAMH,EAAKG,CAAG,GAAG,YAAY,EAAED,IAAOP,EAAiBK,GAAMG,CAAG,MAAMD,EAAK,WAAU,CAAE;AAEvH,SAAOH;AACT;AAXA,IAYIK,IAAa,CAACC,GAAQC,GAAKC,OAAkBT,EAAYO,GAAQC,GAAK,SAAS,GAAGC,KAAgBT,EAAYS,GAAcD,GAAK,SAAS;AAZ9I,IAeIE,IAA6B,CAAA;AACjCJ,EAAWI,GAA4BC,GAAuB;AAI9D,IAAIC,IAAO;EACT,aAAa;EACb,UAAU;IACR,aAAa;IACb,cAAc,CAAC,MAAM,IAAI;EAC1B;EACD,UAAU;IACR,CAAC,KAAK,GAAG;IACT,CAAC,KAAK,GAAG;IACT,CAAC,KAAK,GAAG;EACV;EACD,cAAc;IACZ;;MAEE,YAAY;MACZ,WAAW;MACX,QAAQ;QACN,cAAcF,EAA2B,UAAU,aAAa;QAChE,YAAY;MACb;IACF;IACD;;MAEE,YAAY;MACZ,QAAQ;QACN,cAAcA,EAA2B,UAAU,aAAa;QAChE,YAAY;MACb;IACF;IACD;;MAEE,YAAY;MACZ,QAAQ;QACN,cAAcA,EAA2B,UAAU,aAAa;QAChE,YAAY;MACb;IACF;IACD;;MAEE,YAAY;MACZ,QAAQ;QACN,cAAcA,EAA2B,UAAU,aAAa;QAChE,YAAY;MACb;IACF;EACF;EACD,kBAAkB;IAChB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAG;IAC5C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAG;IACvD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAG;IACvD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAG;EACjD;EACD,SAAS;IACP,SAAS;MACP,OAAO,IAAI,OAAO,wBAAwB;MAC1C,KAAK,IAAI,OAAO,2BAA2B;IAC5C;EACF;AACH;AA7DA,IA8DIG,IAAW;;EAEb,cAAc;EACd,cAAc;EACd,UAAU;;;IAGR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;EACD,WAAW;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;;EAED,SAAS;EACT,SAAS;EACT,QAAQ;EACR,aAAa;EACb,cAAc;EACd,WAAW;EACX,WAAW;EACX,WAAW;;EAEX,WAAW;IACT,MAAM,CAAC,CAAC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,SAAA,CAAU;IAC3D,QAAQ;;MAEN;QACE;QACA;UACE,OAAO;YACL,aAAa;YACb,YAAY;UACb;QACF;MACF;MACD,CAAC,gBAAgB,iBAAiB;;;;MAIlC,EAAE,SAAS,cAAe;;MAE1B;QACE;QACA,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,UAAW;MACvD;;MAED,CAAC,YAAY,WAAW;MACxB,CAAC,oBAAoB,WAAW;MAChC,CAAC,iBAAiB,WAAW;MAC7B;QACE;QACA;UACE,OAAO;YACL,cAAc;YACd,YAAY;UACb;QACF;MACF;;MAED,CAAC,mCAAmC,cAAc;MAClD,CAAC,8CAA8C,cAAc;MAC7D,CAAC,uBAAuB,YAAY;MACpC,CAAC,0BAA0B,cAAc;MACzC,CAAC,0BAA0B,eAAe;MAC1C,CAAC,eAAe,QAAQ;;MAExB,CAAC,SAAS,WAAW;;MAErB,CAAC,mBAAmB,gBAAgB;;MAEpC,CAAC,mBAAmB,gBAAgB;;MAEpC,CAAC,KAAK,UAAU,gBAAgB;MAChC,CAAC,KAAK,UAAU,gBAAgB;MAChC,CAAC,KAAK,UAAU,kBAAkB;IACnC;IACD,YAAY;MACV,CAAC,cAAc,EAAE;MACjB,CAAC,gBAAgB,eAAe,QAAQ;MACxC,CAAC,QAAQ,WAAW,UAAU;MAC9B,CAAC,WAAW,SAAS;IACtB;IACD,SAAS;MACP,CAAC,WAAW,SAAS;MACrB,CAAC,QAAQ,WAAW,MAAM;MAC1B,CAAC,SAAS,SAAS;IACpB;IACD,OAAO;MACL,CAAC,WAAW,aAAa;MACzB,CAAC,QAAQ,eAAe,MAAM;MAC9B,CAAC,SAAS,aAAa;IACxB;;IAED,QAAQ;MACN;QACE;QACA,CAAC,yBAAyB,yBAAyB,uBAAuB;MAC3E;MACD;QACE;QACA,CAAC,yBAAyB,EAAE,OAAO,yBAAyB,MAAM,cAAa,CAAE;MAClF;MACD,CAAC,qBAAqB,CAAC,yBAAyB,uBAAuB,CAAC;MACxE,CAAC,QAAQ,uBAAuB;MAChC,CAAC,cAAc,uBAAuB;MACtC,CAAC,WAAW,QAAQ;MACpB,CAAC,cAAc,eAAe;MAC9B,CAAC,QAAQ,gBAAgB;MACzB,CAAC,oBAAoB,CAAC,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAA,GAAU,eAAe,CAAC;IAC7F;IACD,YAAY;MACV,CAAC,KAAK,uBAAuB;MAC7B,CAAC,MAAM,gBAAgB;MACvB,CAAC,cAAc,eAAe;MAC9B,CAAC,SAAS,QAAQ;MAClB;QACE;QACA;UACE,OAAO;UACP,MAAM;UACN,SAAS;QACV;MACF;IACF;IACD,eAAe;MACb,CAAC,WAAW,QAAQ;MACpB,CAAC,YAAY,eAAe;MAC5B,CAAC,OAAO,uBAAuB;MAC/B,CAAC,KAAK,UAAU,MAAM;IACvB;IACD,eAAe;MACb,CAAC,WAAW,QAAQ;MACpB,CAAC,YAAY,eAAe;MAC5B,CAAC,OAAO,uBAAuB;MAC/B,CAAC,KAAK,UAAU,MAAM;IACvB;IACD,iBAAiB;MACf,CAAC,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAkB,CAAE;MACjE,CAAC,YAAY,QAAQ;MACrB,CAAC,YAAY,eAAe;MAC5B,CAAC,OAAO,uBAAuB;MAC/B,CAAC,KAAK,UAAU,MAAM;IACvB;IACD,iBAAiB;MACf,CAAC,MAAM,qBAAqB,kBAAkB;MAC9C,CAAC,MAAM,qBAAqB,MAAM;MAClC,EAAE,SAAS,SAAU;IACtB;EACF;AACH;AC5VG,IAACD,IAAOE;AAAR,IACCD,IAAW;;EAEb,cAAc;EACd,cAAc;EACd,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;EACD,cAAc,CAAE;EAChB,WAAWE,EAAW;EACtB,SAASA,EAAW;EACpB,SAASA,EAAW;EACpB,QAAQA,EAAW;EACnB,aAAaA,EAAW;EACxB,cAAcA,EAAW;EACzB,WAAWA,EAAW;EACtB,WAAWA,EAAW;EACtB,WAAWA,EAAW;EACtB,WAAWA,EAAW;AACxB;", "names": ["__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "__reExport", "target", "mod", "second<PERSON><PERSON><PERSON>", "monaco_editor_core_exports", "monaco_editor_core_star", "conf", "language", "tsConf", "tsLanguage"]}