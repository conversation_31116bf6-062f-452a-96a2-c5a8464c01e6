from datetime import datetime, timedelta

from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix
from dvadmin.system.models import Users

table_prefix = table_prefix + "kcs_"


class ArticleCategory(CoreModel):
    name = models.CharField(max_length=255, verbose_name="分类名称", help_text="分类名称")

    class Meta:
        db_table = table_prefix + "article_category"
        verbose_name = "文章分类表"
        verbose_name_plural = verbose_name


class LinkCategory(CoreModel):
    name = models.CharField(max_length=255, verbose_name="分类名称", help_text="分类名称")

    class Meta:
        db_table = table_prefix + "link_category"
        verbose_name = "link分类表"
        verbose_name_plural = verbose_name


class Game(CoreModel):
    name = models.CharField(max_length=255, verbose_name="游戏名称", help_text="游戏名称")
    game_id = models.Char<PERSON>ield(max_length=255, verbose_name="游戏ID", help_text="游戏ID", default="")
    img = models.CharField(max_length=255, verbose_name="图标", help_text="图标", null=True, blank=True)

    class Meta:
        db_table = table_prefix + "game"
        verbose_name = "游戏表"
        verbose_name_plural = verbose_name


class Article(CoreModel):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, verbose_name="游戏ID", help_text="游戏ID", default=None)
    title = models.CharField(max_length=255, verbose_name="标题", help_text="标题")
    summary = models.CharField(max_length=255, verbose_name="摘要", help_text="摘要")
    content = models.TextField(verbose_name="内容", help_text="内容")
    tags = models.JSONField(default=list, blank=True, null=True, verbose_name="文章标签", help_text="由LLM生成的文章标签列表")
    category = models.ForeignKey(ArticleCategory, on_delete=models.CASCADE, verbose_name="分类", help_text="分类",
                                 default=None)
    problem_type = models.CharField(max_length=255, verbose_name="问题类型", help_text="问题类型", blank=True)
    is_top = models.BooleanField(default=False, verbose_name="是否置顶", help_text="是否置顶")
    is_review = models.BooleanField(default=False, verbose_name="是否审核", help_text="是否审核")
    is_active = models.BooleanField(default=True, verbose_name="文章活跃", help_text="文章活跃")
    review_time = models.DateTimeField(null=True, blank=True, verbose_name="审核时间", help_text="审核时间")
    status = models.CharField(max_length=255, verbose_name="状态", help_text="状态")
    alarm = models.DateTimeField(null=True, blank=True, verbose_name="告警时间", help_text="告警时间")
    sort = models.IntegerField(default=0, verbose_name="排序", help_text="排序")
    reward_points = models.IntegerField(default=0, verbose_name="奖励积分", help_text="奖励积分")

    # 设置一个功能，当更新is_review字段状态时，自动更新review_time字段
    def save(self, *args, **kwargs):
        # 如果是审核状态，更新审核时间
        if self.is_review:
            self.review_time = self.update_datetime

        # 调用原始的save方法
        super().save(*args, **kwargs)

    class Meta:
        db_table = table_prefix + "article"
        verbose_name = "文章表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_top']),
            models.Index(fields=['category']),
            models.Index(fields=['game']),
            models.Index(fields=['sort']),
            models.Index(fields=['create_datetime']),
        ]


class Link(CoreModel):
    article = models.ForeignKey(Article, on_delete=models.CASCADE, verbose_name="文章ID", help_text="文章ID",
                                error_messages={'unique': '无法重复关联'}, default=None)
    ticket_id = models.CharField(max_length=512, verbose_name="ticketId", help_text="ticketId", default="")
    link_type = models.CharField(max_length=255, verbose_name="链接类型", help_text="链接类型", default=1)

    class Meta:
        db_table = table_prefix + "link"
        verbose_name = "link表"
        verbose_name_plural = verbose_name
        unique_together = ('article', 'ticket_id')
        indexes = [
            models.Index(fields=['article', 'ticket_id', 'creator']),
        ]


class ArticleComments(CoreModel):
    article = models.ForeignKey(Article, on_delete=models.CASCADE, verbose_name="文章ID", help_text="文章ID",
                                default=None)
    content = models.TextField(verbose_name="内容", help_text="内容")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="父评论ID",
                               help_text="父评论ID", default=None)
    is_active = models.BooleanField(default=True, verbose_name="是否活跃", help_text="是否活跃")
    likes = models.IntegerField(default=0, verbose_name="点赞数", help_text="点赞数")

    class Meta:
        db_table = table_prefix + "article_comments"
        verbose_name = "文章评论表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['article']),
            models.Index(fields=['parent']),
            models.Index(fields=['is_active']),
        ]


class ArticleCommentsLikes(CoreModel):
    comment = models.ForeignKey(ArticleComments, on_delete=models.SET_NULL, verbose_name="评论ID", help_text="评论ID",
                                null=True, blank=True, default=None)

    class Meta:
        db_table = table_prefix + "article_comment_likes"
        verbose_name = "文章评论点赞表"
        verbose_name_plural = verbose_name
        unique_together = ('comment', 'creator')
        indexes = [
            models.Index(fields=['comment']),
        ]


class UserPointsLogs(CoreModel):
    user = models.ForeignKey(Users, on_delete=models.SET_NULL, related_name='points_logs', verbose_name="用户ID",
                             help_text="用户ID", null=True, blank=True, default=None)
    points = models.IntegerField(default=0, verbose_name="积分", help_text="积分")
    type = models.CharField(max_length=255, verbose_name="类型", help_text="类型", default=1,
                            choices=[(0, "remove"), (1, "add")])
    comment = models.ForeignKey(ArticleComments, on_delete=models.SET_NULL, null=True, blank=True,
                                verbose_name="评论ID", help_text="评论ID", default=None)
    reason = models.CharField(max_length=255, verbose_name="原因", help_text="原因", default="")

    class Meta:
        db_table = table_prefix + "user_points_logs"
        verbose_name = "用户积分日志表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['type']),
        ]

class AICsPerformanceAnalysis(CoreModel):
    link = models.OneToOneField(Link, on_delete=models.CASCADE, verbose_name="关联Link", help_text="触发分析的Link记录", related_name='performance_analysis')
    session_id = models.CharField(max_length=512, verbose_name="会话ID", help_text="七鱼会话ID", db_index=True)
    initial_sentiment_score = models.FloatField(null=True, blank=True, verbose_name="初始情绪得分", help_text="LLM评估的对话开始时用户情绪分 (-10 到 10)")
    final_sentiment_score = models.FloatField(null=True, blank=True, verbose_name="最终情绪得分", help_text="LLM评估的对话结束时用户情绪分 (-10 到 10)")
    sentiment_change_score = models.FloatField(null=True, blank=True, verbose_name="情绪变化得分", help_text="最终情绪得分 - 初始情绪得分")
    analysis_details = models.JSONField(null=True, blank=True, verbose_name="LLM分析详情", help_text="LLM返回的完整分析结果或理由")

    class Meta:
        db_table = table_prefix + "cs_performance_analysis"
        verbose_name = "客服表现分析"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['create_datetime']),
        ]