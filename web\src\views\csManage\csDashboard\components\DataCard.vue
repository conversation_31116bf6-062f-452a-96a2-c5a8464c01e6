<template>
  <div class="data-card bg-white rounded-lg shadow-sm p-4 border-l-4 transition-all duration-200 hover:shadow-md" :class="borderColorClass">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-600 mb-1">{{ title }}</p>
        <div class="flex items-baseline">
          <p v-if="!loading" class="text-xl font-bold" :class="textColorClass">
            {{ formattedValue }}
            <span v-if="suffix" class="text-sm font-normal text-gray-500 ml-1">{{ suffix }}</span>
          </p>
          <div v-else class="animate-pulse">
            <div class="h-6 bg-gray-200 rounded w-16"></div>
          </div>
        </div>

        <!-- 趋势指示器 -->
        <div v-if="trend && !loading" class="flex items-center mt-2">
          <el-icon
            :class="[
              'text-xs mr-1',
              trend > 0 ? 'text-green-500' :
              trend < 0 ? 'text-red-500' :
              'text-gray-400'
            ]"
          >
            <component :is="trend > 0 ? 'ArrowUp' : trend < 0 ? 'ArrowDown' : 'Remove'" />
          </el-icon>
          <span
            :class="[
              'text-xs',
              trend > 0 ? 'text-green-600' :
              trend < 0 ? 'text-red-600' :
              'text-gray-500'
            ]"
          >
            {{ Math.abs(trend) }}%
          </span>
          <span class="text-xs text-gray-500 ml-1">vs 昨日</span>
        </div>
      </div>

      <div class="flex-shrink-0 ml-4">
        <div
          class="w-10 h-10 rounded-full flex items-center justify-center"
          :class="backgroundColorClass"
        >
          <el-icon :size="18" color="#ffffff">
            <component :is="icon" />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Remove } from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number | string
  suffix?: string
  icon: string
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'yellow' | 'teal' | 'indigo' | 'pink' | 'cyan' | 'lime' | 'rose'
  loading?: boolean
  trend?: number // 趋势百分比，正数表示上升，负数表示下降
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  suffix: '',
  trend: undefined
})

// 格式化数值显示 - 直接显示完整数值，不使用k,w缩写
const formattedValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }

  // 数字格式化 - 添加千位分隔符
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  } else {
    return props.value.toString()
  }
})

// 颜色类映射
const colorClasses = {
  blue: {
    border: 'border-l-blue-500',
    text: 'text-blue-600',
    background: 'bg-blue-500'
  },
  green: {
    border: 'border-l-green-500',
    text: 'text-green-600',
    background: 'bg-green-500'
  },
  purple: {
    border: 'border-l-purple-500',
    text: 'text-purple-600',
    background: 'bg-purple-500'
  },
  orange: {
    border: 'border-l-orange-500',
    text: 'text-orange-600',
    background: 'bg-orange-500'
  },
  red: {
    border: 'border-l-red-500',
    text: 'text-red-600',
    background: 'bg-red-500'
  },
  yellow: {
    border: 'border-l-yellow-500',
    text: 'text-yellow-600',
    background: 'bg-yellow-500'
  },
  teal: {
    border: 'border-l-teal-500',
    text: 'text-teal-600',
    background: 'bg-teal-500'
  },
  indigo: {
    border: 'border-l-indigo-500',
    text: 'text-indigo-600',
    background: 'bg-indigo-500'
  },
  pink: {
    border: 'border-l-pink-500',
    text: 'text-pink-600',
    background: 'bg-pink-500'
  },
  cyan: {
    border: 'border-l-cyan-500',
    text: 'text-cyan-600',
    background: 'bg-cyan-500'
  },
  lime: {
    border: 'border-l-lime-500',
    text: 'text-lime-600',
    background: 'bg-lime-500'
  },
  rose: {
    border: 'border-l-rose-500',
    text: 'text-rose-600',
    background: 'bg-rose-500'
  }
}

const borderColorClass = computed(() => colorClasses[props.color].border)
const textColorClass = computed(() => colorClasses[props.color].text)
const backgroundColorClass = computed(() => colorClasses[props.color].background)
</script>

<style scoped>
.data-card {
  min-height: 100px;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
