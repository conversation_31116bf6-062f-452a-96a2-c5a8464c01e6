"""
异步任务管理器

负责处理Dashboard数据获取的异步任务管理、进度追踪和并发控制
"""

import logging
import time
import uuid
import threading
from typing import Dict, List, Optional, Any

from django.core.cache import cache

logger = logging.getLogger(__name__)


class AsyncTaskManager:
    """
    异步任务管理器
    
    功能：
    1. 并发控制 - 限制同时执行的任务数量
    2. 任务队列 - 超出并发限制时排队等待
    3. 进度追踪 - 实时更新任务执行进度
    4. 状态查询 - 支持前端轮询任务状态
    """
    
    def __init__(self):
        self.QUEUE_PREFIX = "api_queue"
        self.PROGRESS_PREFIX = "api_progress"
        self.LOCK_PREFIX = "api_lock"
        self.RESULT_PREFIX = "api_result"
        
        # 配置参数
        self.MAX_CONCURRENT_REQUESTS = 1  # 最大并发请求数
        self.QUEUE_TIMEOUT = 300  # 队列超时时间（5分钟）
        self.PROGRESS_TIMEOUT = 600  # 进度超时时间（10分钟）
        self.RESULT_CACHE_TIME = 3600 * 1  # 结果缓存时间（1小时）
    
    def _get_cache_key(self, prefix: str, key: str) -> str:
        """生成缓存键"""
        return f"{prefix}:{key}"
    
    def _get_request_key(self, api_type: str, params: str) -> str:
        """生成请求唯一键"""
        return f"{api_type}:{params}"
    
    def create_task(self, api_type: str, params: str, user_id: str) -> Dict[str, Any]:
        """
        创建并发任务
        
        Args:
            api_type: API类型 (如 "dashboard")
            params: 请求参数字符串
            user_id: 用户ID
        
        Returns:
            Dict: 包含task_id和状态的字典
        """
        request_key = self._get_request_key(api_type, params)
        
        # 检查是否有相同参数的任务正在执行
        existing_task = self._get_running_task(request_key)
        if existing_task:
            return {
                'task_id': existing_task['task_id'],
                'status': 'waiting',
                'message': '检测到相同参数的查询正在执行中，已自动等待其完成',
                'progress': 0
            }
        
        # 检查当前并发数
        running_count = self._get_running_count()
        task_id = str(uuid.uuid4())
        
        if running_count < self.MAX_CONCURRENT_REQUESTS:
            # 可以立即执行
            self._start_task(task_id, request_key, user_id)
            return {
                'task_id': task_id,
                'status': 'running',
                'message': '任务已开始执行',
                'progress': 0
            }
        else:
            # 加入队列等待
            self._add_to_queue(task_id, request_key, user_id)
            return {
                'task_id': task_id,
                'status': 'queued',
                'message': '任务已加入队列，等待执行',
                'queue_position': self._get_queue_position(task_id),
                'estimated_wait_time': self._estimate_wait_time(task_id)
            }
    
    def _get_running_task(self, request_key: str) -> Optional[Dict]:
        """获取正在运行的相同参数任务"""
        lock_key = self._get_cache_key(self.LOCK_PREFIX, request_key)
        task_info = cache.get(lock_key)
        return task_info
    
    def _get_running_count(self) -> int:
        """获取当前运行的任务数量"""
        running_count_key = self._get_cache_key("running", "count")
        return cache.get(running_count_key, 0)
    
    def _start_task(self, task_id: str, request_key: str, user_id: str):
        """开始执行任务"""
        task_info = {
            'task_id': task_id,
            'request_key': request_key,
            'user_id': user_id,
            'start_time': time.time(),
            'status': 'running'
        }
        
        # 设置锁
        lock_key = self._get_cache_key(self.LOCK_PREFIX, request_key)
        cache.set(lock_key, task_info, self.PROGRESS_TIMEOUT)
        
        # 更新运行计数
        running_count_key = self._get_cache_key("running", "count")
        current_count = cache.get(running_count_key, 0)
        cache.set(running_count_key, current_count + 1, self.PROGRESS_TIMEOUT)
        
        # 初始化进度
        self.update_progress(task_id, 0, "开始获取数据...")
    
    def _add_to_queue(self, task_id: str, request_key: str, user_id: str):
        """添加任务到队列"""
        queue_key = self._get_cache_key(self.QUEUE_PREFIX, "main")
        queue_item = {
            'task_id': task_id,
            'request_key': request_key,
            'user_id': user_id,
            'queue_time': time.time()
        }
        
        # 获取当前队列
        queue = cache.get(queue_key, [])
        queue.append(queue_item)
        cache.set(queue_key, queue, self.QUEUE_TIMEOUT)
        
        # 设置任务状态
        self.update_progress(task_id, 0, "等待队列中...")
    
    def _get_queue_position(self, task_id: str) -> int:
        """获取任务在队列中的位置"""
        queue_key = self._get_cache_key(self.QUEUE_PREFIX, "main")
        queue = cache.get(queue_key, [])
        
        for i, item in enumerate(queue):
            if item['task_id'] == task_id:
                return i + 1
        return 0
    
    def _estimate_wait_time(self, task_id: str) -> int:
        """估算等待时间（秒）"""
        position = self._get_queue_position(task_id)
        if position == 0:
            return 0
        
        # 假设每个任务平均需要60秒
        avg_task_time = 60
        return position * avg_task_time
    
    def update_progress(self, task_id: str, progress: int, message: str = ""):
        """更新任务进度"""
        progress_key = self._get_cache_key(self.PROGRESS_PREFIX, task_id)
        progress_info = {
            'task_id': task_id,
            'progress': progress,
            'message': message,
            'update_time': time.time()
        }
        cache.set(progress_key, progress_info, self.PROGRESS_TIMEOUT)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        progress_key = self._get_cache_key(self.PROGRESS_PREFIX, task_id)
        progress_info = cache.get(progress_key)
        
        if not progress_info:
            return {
                'status': 'not_found',
                'message': '任务不存在或已过期'
            }
        
        # 检查是否已完成
        result_key = self._get_cache_key(self.RESULT_PREFIX, task_id)
        result = cache.get(result_key)
        
        if result:
            return {
                'status': 'completed',
                'progress': 100,
                'message': '任务已完成',
                'result': result
            }
        
        # 检查是否在等待其他相同任务完成
        if progress_info.get('message', '').startswith('检测到相同参数'):
            return {
                'status': 'waiting',
                'progress': progress_info.get('progress', 0),
                'message': '等待相同查询任务完成中...'
            }
        
        # 检查是否在队列中
        if progress_info['progress'] == 0 and "等待" in progress_info.get('message', ''):
            return {
                'status': 'queued',
                'progress': progress_info['progress'],
                'message': progress_info['message'],
                'queue_position': self._get_queue_position(task_id),
                'estimated_wait_time': self._estimate_wait_time(task_id)
            }
        
        return {
            'status': 'running',
            'progress': progress_info['progress'],
            'message': progress_info['message']
        }
    
    def complete_task(self, task_id: str, result: Any):
        """完成任务"""
        # 保存结果
        result_key = self._get_cache_key(self.RESULT_PREFIX, task_id)
        cache.set(result_key, result, self.RESULT_CACHE_TIME)
        
        # 更新进度为100%
        self.update_progress(task_id, 100, "任务完成")
        
        # 释放锁和更新计数
        self._release_task_resources(task_id)
        
        # 处理队列中的下一个任务
        self._process_queue()
    
    def fail_task(self, task_id: str, error_message: str):
        """任务失败"""
        self.update_progress(task_id, -1, f"任务失败: {error_message}")
        self._release_task_resources(task_id)
        self._process_queue()
    
    def _release_task_resources(self, task_id: str):
        """释放任务资源"""
        # 减少运行计数
        running_count_key = self._get_cache_key("running", "count")
        current_count = cache.get(running_count_key, 0)
        cache.set(running_count_key, max(0, current_count - 1), self.PROGRESS_TIMEOUT)
    
    def _process_queue(self):
        """处理队列中的下一个任务"""
        queue_key = self._get_cache_key(self.QUEUE_PREFIX, "main")
        queue = cache.get(queue_key, [])
        
        if not queue:
            return
        
        running_count = self._get_running_count()
        if running_count >= self.MAX_CONCURRENT_REQUESTS:
            return
        
        # 取出队列中的第一个任务
        next_task = queue.pop(0)
        cache.set(queue_key, queue, self.QUEUE_TIMEOUT)
        
        # 开始执行
        self._start_task(
            next_task['task_id'],
            next_task['request_key'],
            next_task['user_id']
        )


# 全局异步任务管理器实例
async_task_manager = AsyncTaskManager() 