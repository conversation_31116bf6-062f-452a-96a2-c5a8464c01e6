"""
客服会话情绪分析模块

该模块实现了基于会话内容对客服服务效果进行情绪分析的功能。
流程如下:
1. MQ消费者接收会话关闭消息
2. 触发异步任务进行情绪分析
3. 获取会话内容并格式化
4. 通过LLM进行情绪分析
5. 将分析结果保存到数据库
6. 支持情绪分析结果的查询和展示
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist
from apps.common.utils.session_detail_get_game_id import get_game_id_from_session_detail

logger = logging.getLogger(__name__)


def _is_robot_service(service_info: dict) -> bool:
    """
    判断是否为机器人客服
    
    Args:
        service_info: 客服信息字典
        
    Returns:
        bool: 是否为机器人客服
    """
    service_id = service_info.get('service_id', '')
    service_name = service_info.get('service_name', '')
    
    # 检查service_id是否为负数（机器人标识）
    try:
        if service_id and int(service_id) < 0:
            return True
    except (ValueError, TypeError):
        pass
    
    # 检查service_name是否包含机器人关键词
    robot_keywords = ['机器人', 'robot', 'bot', '自动', 'auto']
    service_name_lower = service_name.lower()
    
    for keyword in robot_keywords:
        if keyword in service_name_lower:
            return True
    
    return False


def _get_qiyu_session_data_for_emotion_analysis(session_id: str) -> Optional[Dict]:
    """
    获取会话数据并格式化为情绪分析专用格式
    
    Args:
        session_id: 会话ID
        
    Returns:
        dict: 包含会话信息和格式化消息的字典
    """
    from apps.common.qiyu_service import get_qiyu_service
    
    service = get_qiyu_service()
    
    # 获取会话基本信息
    session_detail = service.get_session_detail(session_id)
    if not session_detail:
        logger.warning(f"[情绪分析] 会话 {session_id} 详情获取失败")
        return None
    
    # 获取会话消息
    messages = service.get_session_messages(session_id)
    if not messages:
        logger.warning(f"[情绪分析] 会话 {session_id} 消息获取失败")
        return None
    
    # 提取客服信息
    service_info = {
        'service_id': session_detail.get('staffId'),
        'service_name': session_detail.get('staffName', '未知客服'),
        'service_account': session_detail.get('staffAccount', ''),
        'session_start_time': session_detail.get('startTime'),
        'session_end_time': session_detail.get('endTime'),
        'session_duration': _parse_duration_safely(session_detail.get('staffReceptionDuration', '00:00:00'))
    }
    
    # 格式化消息为时间序列对话
    formatted_messages = _format_messages_for_emotion_analysis(messages)
    
    if not formatted_messages:
        logger.warning(f"[情绪分析] 会话 {session_id} 无有效消息")
        return None
    
    return {
        'service_info': service_info,
        'formatted_messages': formatted_messages,
        'total_messages': len(formatted_messages),
        'session_evaluation': session_detail.get('evaluation', ''),
        'session_detail': session_detail
    }


def _parse_duration_safely(duration_str: str) -> int:
    """
    安全解析时长字符串为秒数
    """
    try:
        if not duration_str or duration_str.strip() == '':
            return 0
        
        time_parts = [part.strip() for part in duration_str.split(':') if part.strip()]
        while len(time_parts) < 3:
            time_parts.insert(0, '0')
        time_parts = time_parts[:3]
        
        return sum(x * int(t) for x, t in zip([3600, 60, 1], time_parts))
        
    except (ValueError, AttributeError) as e:
        logger.warning(f"[情绪分析] 时长解析失败: {duration_str}, 错误: {e}")
        return 0


def _format_messages_for_emotion_analysis(messages: List[Dict]) -> List[Dict]:
    """
    将消息格式化为情绪分析专用格式
    
    按时间顺序排列，标记发送者，提取有效内容
    """
    formatted_messages = []
    
    for message in messages:
        # 跳过自动回复和系统消息
        if message.get("autoReply") == 1:
            continue
            
        msg_content = message.get("msg", "")
        msg_time = message.get("time", 0)
        
        # 处理JSON格式的消息内容
        if isinstance(msg_content, str) and msg_content.startswith("{"):
            try:
                msg_dict = json.loads(msg_content)
                # 跳过系统命令消息
                if any(key in msg_dict for key in ["cmd", "tagList"]):
                    continue
                # 提取文本内容
                if "text" in msg_dict:
                    msg_content = msg_dict["text"]
                elif "url" in msg_dict:
                    msg_content = f"[分享链接] {msg_dict['url']}"
                else:
                    continue
            except json.JSONDecodeError:
                continue
        
        # 过滤无效消息
        if not msg_content or len(msg_content.strip()) == 0:
            continue
        if "人工" in msg_content or "转接" in msg_content:
            continue
        if "client?" in msg_content:
            continue
            
        # 确定发送者和消息类型
        sender_type = "service" if message.get("from") == 0 else "user"
        
        formatted_messages.append({
            "sender": sender_type,
            "content": msg_content.strip(),
            "timestamp": msg_time,
            "message_id": message.get("id"),
            "index": len(formatted_messages)
        })
    
    # 按时间排序
    formatted_messages.sort(key=lambda x: x.get('timestamp', 0))
    
    # 重新分配索引
    for i, msg in enumerate(formatted_messages):
        msg['index'] = i
    
    return formatted_messages


def _should_skip_session(session_detail: Dict) -> bool:
    """
    判断会话是否应该跳过处理
    
    Args:
        session_detail: 会话详情数据
        
    Returns:
        bool: 是否应该跳过处理
    """
    # 检查fromGroup字段，如果是"超自然-VIP服务组"则跳过
    from_group = session_detail.get('fromGroup', '')
    if from_group == '超自然-VIP服务组':
        logger.info(f"[情绪分析] 会话 fromGroup为'{from_group}'，跳过处理")
        return True
    
    return False


def _get_optional_associations(session_id: str) -> Tuple[Optional[int], Optional[int]]:
    """
    获取可选的关联信息（非必要字段，获取失败不影响主流程）
    
    Returns:
        Tuple[link_id,]: 返回相关的Link ID
    """
    try:
        from apps.kcs.models import Link
        from apps.cs_manage.models import CsQualityCheck

        from django.core.cache import cache
        
        # 使用缓存避免重复查询（短时间缓存，因为可能有新的关联创建）
        cache_key = f"session_relations:{session_id}"
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            logger.debug(f"[情绪分析] 从缓存获取关联信息: {session_id}")
            return cached_result
        
        link_id = None
        
        # 查找相关的Link（可能不存在，不影响主流程）
        try:
            link = Link.objects.filter(ticket_id=session_id).first()
            if link:
                link_id = link.id
                logger.debug(f"[情绪分析] 找到关联Link: {session_id} -> {link_id}")
        except Exception as e:
            logger.debug(f"[情绪分析] 查找Link时出错: {e}")

            
        result = link_id
        
        # 缓存结果3分钟（较短时间，便于新创建的关联能被及时发现）
        cache.set(cache_key, result, 180)
        
        return result
            
    except Exception as e:
        logger.debug(f"[情绪分析] 获取关联信息时出错: {e}")
        return None, None


def _get_optional_system_user(service_name: str) -> Optional[int]:
    """
    通过客服姓名查找系统用户（非必要字段，获取失败不影响主流程）
    """
    if not service_name or service_name.strip() == '':
        return None
        
    try:
        from dvadmin.system.models import Users
        from django.core.cache import cache
        
        # 使用缓存避免重复查询
        cache_key = f"user_name_mapping:{service_name}"
        user_id = cache.get(cache_key)
        
        if user_id is not None:
            result = user_id if user_id > 0 else None
            if result:
                logger.debug(f"[情绪分析] 从缓存获取用户映射: {service_name} -> {result}")
            return result
        
        # 按优先级匹配: 姓名 -> 用户名
        user = (Users.objects.filter(name=service_name).first() or 
                Users.objects.filter(username=service_name).first())
        
        result = user.id if user else None
        
        # 缓存结果（包括null结果，避免重复查询）
        cache.set(cache_key, result or -1, 300)  # 缓存5分钟
        
        if result:
            logger.debug(f"[情绪分析] 找到系统用户映射: {service_name} -> {result}")
        else:
            logger.debug(f"[情绪分析] 未找到系统用户映射: {service_name}")
        
        return result
        
    except Exception as e:
        logger.debug(f"[情绪分析] 查找系统用户时出错: {service_name}, 错误: {e}")
        return None


def _extract_conversation_phases(messages: List[Dict]) -> Dict[str, List[Dict]]:
    """
    将对话分为初始、中间、最终三个阶段
    """
    total_messages = len(messages)
    if total_messages < 3:
        return {
            'initial': messages,
            'middle': [],
            'final': messages
        }
    
    # 分阶段：前30%为初始，中间40%为中间，后30%为最终
    initial_end = max(1, int(total_messages * 0.3))
    final_start = min(total_messages - 1, int(total_messages * 0.7))
    
    return {
        'initial': messages[:initial_end],
        'middle': messages[initial_end:final_start],
        'final': messages[final_start:]
    }


def process_session_for_emotion_analysis(session_id: str, force_reprocess: bool = False) -> bool:
    """
    处理会话进行情绪分析的完整流程
    
    Args:
        session_id: 会话ID
        force_reprocess: 是否强制重新处理已处理过的会话
        
    Returns:
        bool: 处理是否成功
    """
    from apps.cs_manage.models import CsEmotionAnalysis
    
    try:
        logger.info(f"[情绪分析] 开始处理会话 {session_id} 的情绪分析")
        
        #  检查是否已处理过
        # if not force_reprocess and CsEmotionAnalysis.objects.filter(session_id=session_id).exists():
        #     logger.info(f"[情绪分析] 会话 {session_id} 已处理过情绪分析，跳过")
        #     return False
        
        #  获取会话详情
        from apps.common.qiyu_service import get_qiyu_service
        service = get_qiyu_service()
        session_detail = service.get_session_detail(session_id)
        group_name = session_detail.get('fromGroup', '')

        if not session_detail:
            logger.warning(f"[情绪分析] 会话 {session_id} 详情获取失败")
            return False
        
        #  检查是否应该跳过此会话
        if _should_skip_session(session_detail):
            logger.info(f"[情绪分析] 会话 {session_id} 符合跳过条件，跳过处理")
            return False
        
        #  获取会话数据
        session_data = _get_qiyu_session_data_for_emotion_analysis(session_id)
        if not session_data:
            logger.warning(f"[情绪分析] 会话 {session_id} 数据获取失败")
            return False
        
        service_info = session_data['service_info']
        formatted_messages = session_data['formatted_messages']
        
        #  数据验证
        if not service_info.get('service_id'):
            logger.warning(f"[情绪分析] 会话 {session_id} 缺少客服信息")
            return False
        
        #  检查是否为机器人客服，机器人不进行情绪分析
        if _is_robot_service(service_info):
            logger.info(f"[情绪分析] 会话 {session_id} 为机器人客服 {service_info.get('service_name')}，跳过情绪分析")
            return False
            
        # 检查是否有足够的对话进行分析
        if len(formatted_messages) < 3:
            logger.info(f"[情绪分析] 会话 {session_id} 对话轮次不足，跳过分析")
            return False
        
        #  获取可选的关联信息（这些字段非必要，获取失败不影响分析）
        logger.debug(f"[情绪分析] 开始获取会话 {session_id} 的可选关联信息")
        
        game = get_game_id_from_session_detail(session_detail)
        game_id = game.id if game else None
        link_id = _get_optional_associations(session_id)
        system_user_id = _get_optional_system_user(service_info.get('service_name', ''))
        
        logger.info(f"[情绪分析] 会话 {session_id} 关联信息获取完成 - 游戏ID: {game_id}, Link ID: {link_id}, 系统用户ID: {system_user_id}")
        
        #  调用LLM进行情绪分析
        from apps.common.LLM_api import LLM_api
        llm_api = LLM_api()
        
        # 直接传递会话数据给LLM
        llm_response = llm_api.analyze_cs_emotion(formatted_messages)
        
        if not llm_response:
            logger.warning(f"[情绪分析] 会话 {session_id} LLM情绪分析失败")
            return False
        
        # 解析LLM返回的结果
        try:
            if isinstance(llm_response, str):
                emotion_result = json.loads(llm_response)
            else:
                emotion_result = llm_response
                
            if 'error' in emotion_result:
                logger.warning(f"[情绪分析] 会话 {session_id} LLM返回错误: {emotion_result['error']}")
                return False
                
        except json.JSONDecodeError as e:
            logger.error(f"[情绪分析] 会话 {session_id} LLM返回格式错误: {e}")
            return False
        
        # 9. 保存分析结果
        success = _save_emotion_analysis_result(
            session_id=session_id,
            service_info=service_info,
            emotion_result=emotion_result,
            formatted_messages=formatted_messages,
            game_id=game_id,
            link_id=link_id,
            system_user_id=system_user_id,
            group_name=group_name
        )
        
        if success:
            logger.info(f"[情绪分析] 会话 {session_id} 情绪分析处理完成")
            return True
        else:
            logger.error(f"[情绪分析] 会话 {session_id} 情绪分析结果保存失败")
            return False
            
    except Exception as e:
        logger.error(f"[情绪分析] 处理会话 {session_id} 情绪分析时发生错误: {str(e)}", exc_info=True)
        return False


def _save_emotion_analysis_result(
    session_id: str,
    service_info: Dict, 
    emotion_result: Dict,
    formatted_messages: List[Dict],
    game_id: Optional[int] = None,
    link_id: Optional[int] = None,
    system_user_id: Optional[int] = None,
    group_name: Optional[str] = None
) -> bool:
    """
    保存情绪分析结果到数据库
    """
    from apps.cs_manage.models import CsEmotionAnalysis, CsEmotionAnalysisDetail
    
    try:
        with transaction.atomic():
            # 计算情绪变化分数
            initial_score = emotion_result.get('initial_emotion_score', 0)
            final_score = emotion_result.get('final_emotion_score', 0)
            emotion_change = final_score - initial_score if initial_score is not None and final_score is not None else None
            
            # 统计消息数量
            user_message_count = len([m for m in formatted_messages if m.get('sender') == 'user'])
            service_message_count = len([m for m in formatted_messages if m.get('sender') == 'service'])
            
            # 创建主分析记录
            emotion_analysis = CsEmotionAnalysis.objects.create(
                session_id=session_id,
                service_id=service_info.get('service_id'),
                service_name=service_info.get('service_name'),
                service_account=service_info.get('service_account'),
                service_user_id=system_user_id,
                game_id=game_id,
                link_id=link_id,
                
                # 情绪分析结果
                initial_emotion_score=initial_score,
                initial_emotion_justification=emotion_result.get('initial_emotion_justification', ''),
                final_emotion_score=final_score,
                final_emotion_justification=emotion_result.get('final_emotion_justification', ''),
                emotion_change_score=emotion_change,
                overall_assessment=emotion_result.get('overall_assessment', ''),
                
                # 会话统计
                session_start_time=datetime.fromtimestamp(service_info.get('session_start_time', 0) / 1000) if service_info.get('session_start_time') else None,
                session_end_time=datetime.fromtimestamp(service_info.get('session_end_time', 0) / 1000) if service_info.get('session_end_time') else None,
                session_duration=service_info.get('session_duration', 0),
                message_count=len(formatted_messages),
                user_message_count=user_message_count,
                service_message_count=service_message_count,
                
                # 元数据
                analysis_details=emotion_result,
                conversation_summary=emotion_result.get('conversation_summary', ''),
                emotion_keywords=emotion_result.get('emotion_keywords', []),
                structured_conversation=emotion_result.get('structured_conversation', {}),
                status='completed',
                group_name=group_name
            )
            
            # 创建分阶段详情记录
            phase_details = emotion_result.get('phase_details', {})
            conversation_phases = _extract_conversation_phases(formatted_messages)
            
            for phase_name, phase_messages in conversation_phases.items():
                if not phase_messages:
                    continue
                    
                phase_data = phase_details.get(phase_name, {})
                
                CsEmotionAnalysisDetail.objects.create(
                    emotion_analysis=emotion_analysis,
                    phase=phase_name,
                    start_message_index=phase_messages[0]['index'],
                    end_message_index=phase_messages[-1]['index'],
                    emotion_score=phase_data.get('emotion_score', 0),
                    emotion_description=phase_data.get('emotion_description', ''),
                    confidence_level=phase_data.get('confidence_level', 0.5),
                    key_messages=phase_data.get('key_messages', []),
                    emotion_triggers=phase_data.get('emotion_triggers', [])
                )
            
            logger.info(f"[情绪分析] 会话 {session_id} 情绪分析保存成功，情绪变化: {emotion_change}")
            return True
            
    except Exception as e:
        logger.error(f"[情绪分析] 保存会话 {session_id} 情绪分析结果时出错: {str(e)}", exc_info=True)
        return False


def get_emotion_analysis_summary(
    service_id: str = None, 
    game_id: int = None, 
    days: int = 30
) -> Dict:
    """
    获取情绪分析汇总统计
    """
    from apps.cs_manage.models import CsEmotionAnalysis
    from django.utils import timezone
    from django.db.models import Avg, Count, Sum, Q
    from datetime import timedelta
    
    # 构建查询条件
    queryset = CsEmotionAnalysis.objects.filter(
        created_time__gte=timezone.now() - timedelta(days=days),
        status='completed'
    )
    
    if service_id:
        queryset = queryset.filter(service_id=service_id)
    if game_id:
        queryset = queryset.filter(game_id=game_id)
    
    # 基础统计
    summary = queryset.aggregate(
        total_sessions=Count('id'),
        avg_initial_emotion=Avg('initial_emotion_score'),
        avg_final_emotion=Avg('final_emotion_score'),
        avg_emotion_change=Avg('emotion_change_score'),
        avg_session_duration=Avg('session_duration')
    )
    
    # 情绪改善统计
    improvement_stats = {
        'improved_sessions': queryset.filter(emotion_change_score__gt=0).count(),
        'worsened_sessions': queryset.filter(emotion_change_score__lt=0).count(),
        'stable_sessions': queryset.filter(emotion_change_score=0).count(),
    }
    
    # 按客服分组统计
    service_stats = queryset.values('service_id', 'service_name').annotate(
        session_count=Count('id'),
        avg_initial_emotion=Avg('initial_emotion_score'),
        avg_final_emotion=Avg('final_emotion_score'),
        avg_emotion_change=Avg('emotion_change_score'),
        improvement_rate=Count('id', filter=Q(emotion_change_score__gt=0)) * 100.0 / Count('id')
    ).order_by('-avg_emotion_change')
    
    return {
        'summary': summary,
        'improvement_stats': improvement_stats,
        'service_stats': list(service_stats),
        'period_days': days
    }


def get_service_emotion_details(service_id: str, page: int = 1, page_size: int = 20) -> Dict:
    """
    获取特定客服的详细情绪分析记录
    """
    from apps.cs_manage.models import CsEmotionAnalysis
    from django.core.paginator import Paginator
    
    queryset = CsEmotionAnalysis.objects.filter(
        service_id=service_id,
        status='completed'
    ).select_related('game', 'link', 'quality_check', 'service_user').prefetch_related('details').order_by('-create_datetime')
    
    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)
    
    records = []
    for analysis in page_obj:
        records.append({
            'id': analysis.id,
            'session_id': analysis.session_id,
            'initial_emotion_score': analysis.initial_emotion_score,
            'final_emotion_score': analysis.final_emotion_score,
            'emotion_change_score': analysis.emotion_change_score,
            'overall_assessment': analysis.overall_assessment,
            'session_duration': analysis.session_duration,
            'message_count': analysis.message_count,
            'create_datetime': analysis.create_datetime,
            'game_name': analysis.game.name if analysis.game else None,
            'conversation_summary': analysis.conversation_summary,
            'emotion_keywords': analysis.emotion_keywords,
            'phase_details': [
                {
                    'phase': detail.phase,
                    'emotion_score': detail.emotion_score,
                    'emotion_description': detail.emotion_description,
                    'confidence_level': detail.confidence_level
                } for detail in analysis.details.all()
            ]
        })
    
    return {
        'records': records,
        'total_count': paginator.count,
        'page_count': paginator.num_pages,
        'current_page': page,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous()
    } 