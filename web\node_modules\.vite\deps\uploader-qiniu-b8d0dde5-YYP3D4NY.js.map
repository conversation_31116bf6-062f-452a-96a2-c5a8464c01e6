{"version": 3, "sources": ["../../spark-md5/spark-md5.js", "../../querystring/decode.js", "../../querystring/encode.js", "../../querystring/index.js", "../../qiniu-js/src/errors/index.ts", "../../qiniu-js/src/utils/pool.ts", "../../qiniu-js/src/utils/observable.ts", "../../qiniu-js/src/utils/base64.ts", "../../qiniu-js/src/utils/helper.ts", "../../qiniu-js/src/config/region.ts", "../../qiniu-js/src/api/index.ts", "../../qiniu-js/src/upload/base.ts", "../../qiniu-js/src/upload/resume.ts", "../../qiniu-js/src/utils/crc32.ts", "../../qiniu-js/src/upload/direct.ts", "../../qiniu-js/src/logger/report-v3.ts", "../../qiniu-js/src/logger/index.ts", "../../qiniu-js/src/upload/hosts.ts", "../../qiniu-js/src/upload/index.ts", "../../qiniu-js/src/utils/config.ts", "../../qiniu-js/src/utils/compress.ts", "../../@fast-crud/src/uploader/components/libs/uploader-qiniu.ts"], "sourcesContent": ["(function (factory) {\n    if (typeof exports === 'object') {\n        // Node/CommonJS\n        module.exports = factory();\n    } else if (typeof define === 'function' && define.amd) {\n        // AMD\n        define(factory);\n    } else {\n        // Browser globals (with support for web workers)\n        var glob;\n\n        try {\n            glob = window;\n        } catch (e) {\n            glob = self;\n        }\n\n        glob.SparkMD5 = factory();\n    }\n}(function (undefined) {\n\n    'use strict';\n\n    /*\n     * Fastest md5 implementation around (JKM md5).\n     * Credits: <PERSON>\n     *\n     * @see http://www.myersdaily.org/joseph/javascript/md5-text.html\n     * @see http://jsperf.com/md5-shootout/7\n     */\n\n    /* this function is much faster,\n      so if possible we use it. Some IEs\n      are the only ones I know of that\n      need the idiotic second function,\n      generated by an if clause.  */\n    var add32 = function (a, b) {\n        return (a + b) & 0xFFFFFFFF;\n    },\n        hex_chr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'];\n\n\n    function cmn(q, a, b, x, s, t) {\n        a = add32(add32(a, q), add32(x, t));\n        return add32((a << s) | (a >>> (32 - s)), b);\n    }\n\n    function md5cycle(x, k) {\n        var a = x[0],\n            b = x[1],\n            c = x[2],\n            d = x[3];\n\n        a += (b & c | ~b & d) + k[0] - 680876936 | 0;\n        a  = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[1] - 389564586 | 0;\n        d  = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[2] + 606105819 | 0;\n        c  = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[3] - 1044525330 | 0;\n        b  = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[4] - 176418897 | 0;\n        a  = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[5] + 1200080426 | 0;\n        d  = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[6] - 1473231341 | 0;\n        c  = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[7] - 45705983 | 0;\n        b  = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[8] + 1770035416 | 0;\n        a  = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[9] - 1958414417 | 0;\n        d  = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[10] - 42063 | 0;\n        c  = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[11] - 1990404162 | 0;\n        b  = (b << 22 | b >>> 10) + c | 0;\n        a += (b & c | ~b & d) + k[12] + 1804603682 | 0;\n        a  = (a << 7 | a >>> 25) + b | 0;\n        d += (a & b | ~a & c) + k[13] - 40341101 | 0;\n        d  = (d << 12 | d >>> 20) + a | 0;\n        c += (d & a | ~d & b) + k[14] - 1502002290 | 0;\n        c  = (c << 17 | c >>> 15) + d | 0;\n        b += (c & d | ~c & a) + k[15] + 1236535329 | 0;\n        b  = (b << 22 | b >>> 10) + c | 0;\n\n        a += (b & d | c & ~d) + k[1] - 165796510 | 0;\n        a  = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[6] - 1069501632 | 0;\n        d  = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[11] + 643717713 | 0;\n        c  = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[0] - 373897302 | 0;\n        b  = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[5] - 701558691 | 0;\n        a  = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[10] + 38016083 | 0;\n        d  = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[15] - 660478335 | 0;\n        c  = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[4] - 405537848 | 0;\n        b  = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[9] + 568446438 | 0;\n        a  = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[14] - 1019803690 | 0;\n        d  = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[3] - 187363961 | 0;\n        c  = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[8] + 1163531501 | 0;\n        b  = (b << 20 | b >>> 12) + c | 0;\n        a += (b & d | c & ~d) + k[13] - 1444681467 | 0;\n        a  = (a << 5 | a >>> 27) + b | 0;\n        d += (a & c | b & ~c) + k[2] - 51403784 | 0;\n        d  = (d << 9 | d >>> 23) + a | 0;\n        c += (d & b | a & ~b) + k[7] + 1735328473 | 0;\n        c  = (c << 14 | c >>> 18) + d | 0;\n        b += (c & a | d & ~a) + k[12] - 1926607734 | 0;\n        b  = (b << 20 | b >>> 12) + c | 0;\n\n        a += (b ^ c ^ d) + k[5] - 378558 | 0;\n        a  = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[8] - 2022574463 | 0;\n        d  = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[11] + 1839030562 | 0;\n        c  = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[14] - 35309556 | 0;\n        b  = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[1] - 1530992060 | 0;\n        a  = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[4] + 1272893353 | 0;\n        d  = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[7] - 155497632 | 0;\n        c  = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[10] - 1094730640 | 0;\n        b  = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[13] + 681279174 | 0;\n        a  = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[0] - 358537222 | 0;\n        d  = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[3] - 722521979 | 0;\n        c  = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[6] + 76029189 | 0;\n        b  = (b << 23 | b >>> 9) + c | 0;\n        a += (b ^ c ^ d) + k[9] - 640364487 | 0;\n        a  = (a << 4 | a >>> 28) + b | 0;\n        d += (a ^ b ^ c) + k[12] - 421815835 | 0;\n        d  = (d << 11 | d >>> 21) + a | 0;\n        c += (d ^ a ^ b) + k[15] + 530742520 | 0;\n        c  = (c << 16 | c >>> 16) + d | 0;\n        b += (c ^ d ^ a) + k[2] - 995338651 | 0;\n        b  = (b << 23 | b >>> 9) + c | 0;\n\n        a += (c ^ (b | ~d)) + k[0] - 198630844 | 0;\n        a  = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[7] + 1126891415 | 0;\n        d  = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[14] - 1416354905 | 0;\n        c  = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[5] - 57434055 | 0;\n        b  = (b << 21 |b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[12] + 1700485571 | 0;\n        a  = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[3] - 1894986606 | 0;\n        d  = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[10] - 1051523 | 0;\n        c  = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[1] - 2054922799 | 0;\n        b  = (b << 21 |b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[8] + 1873313359 | 0;\n        a  = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[15] - 30611744 | 0;\n        d  = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[6] - 1560198380 | 0;\n        c  = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[13] + 1309151649 | 0;\n        b  = (b << 21 |b >>> 11) + c | 0;\n        a += (c ^ (b | ~d)) + k[4] - 145523070 | 0;\n        a  = (a << 6 | a >>> 26) + b | 0;\n        d += (b ^ (a | ~c)) + k[11] - 1120210379 | 0;\n        d  = (d << 10 | d >>> 22) + a | 0;\n        c += (a ^ (d | ~b)) + k[2] + 718787259 | 0;\n        c  = (c << 15 | c >>> 17) + d | 0;\n        b += (d ^ (c | ~a)) + k[9] - 343485551 | 0;\n        b  = (b << 21 | b >>> 11) + c | 0;\n\n        x[0] = a + x[0] | 0;\n        x[1] = b + x[1] | 0;\n        x[2] = c + x[2] | 0;\n        x[3] = d + x[3] | 0;\n    }\n\n    function md5blk(s) {\n        var md5blks = [],\n            i; /* Andy King said do it this way. */\n\n        for (i = 0; i < 64; i += 4) {\n            md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);\n        }\n        return md5blks;\n    }\n\n    function md5blk_array(a) {\n        var md5blks = [],\n            i; /* Andy King said do it this way. */\n\n        for (i = 0; i < 64; i += 4) {\n            md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);\n        }\n        return md5blks;\n    }\n\n    function md51(s) {\n        var n = s.length,\n            state = [1732584193, -271733879, -1732584194, 271733878],\n            i,\n            length,\n            tail,\n            tmp,\n            lo,\n            hi;\n\n        for (i = 64; i <= n; i += 64) {\n            md5cycle(state, md5blk(s.substring(i - 64, i)));\n        }\n        s = s.substring(i - 64);\n        length = s.length;\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n        for (i = 0; i < length; i += 1) {\n            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);\n        }\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\n        if (i > 55) {\n            md5cycle(state, tail);\n            for (i = 0; i < 16; i += 1) {\n                tail[i] = 0;\n            }\n        }\n\n        // Beware that the final length might not fit in 32 bits so we take care of that\n        tmp = n * 8;\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\n        lo = parseInt(tmp[2], 16);\n        hi = parseInt(tmp[1], 16) || 0;\n\n        tail[14] = lo;\n        tail[15] = hi;\n\n        md5cycle(state, tail);\n        return state;\n    }\n\n    function md51_array(a) {\n        var n = a.length,\n            state = [1732584193, -271733879, -1732584194, 271733878],\n            i,\n            length,\n            tail,\n            tmp,\n            lo,\n            hi;\n\n        for (i = 64; i <= n; i += 64) {\n            md5cycle(state, md5blk_array(a.subarray(i - 64, i)));\n        }\n\n        // Not sure if it is a bug, however IE10 will always produce a sub array of length 1\n        // containing the last element of the parent array if the sub array specified starts\n        // beyond the length of the parent array - weird.\n        // https://connect.microsoft.com/IE/feedback/details/771452/typed-array-subarray-issue\n        a = (i - 64) < n ? a.subarray(i - 64) : new Uint8Array(0);\n\n        length = a.length;\n        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n        for (i = 0; i < length; i += 1) {\n            tail[i >> 2] |= a[i] << ((i % 4) << 3);\n        }\n\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\n        if (i > 55) {\n            md5cycle(state, tail);\n            for (i = 0; i < 16; i += 1) {\n                tail[i] = 0;\n            }\n        }\n\n        // Beware that the final length might not fit in 32 bits so we take care of that\n        tmp = n * 8;\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\n        lo = parseInt(tmp[2], 16);\n        hi = parseInt(tmp[1], 16) || 0;\n\n        tail[14] = lo;\n        tail[15] = hi;\n\n        md5cycle(state, tail);\n\n        return state;\n    }\n\n    function rhex(n) {\n        var s = '',\n            j;\n        for (j = 0; j < 4; j += 1) {\n            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];\n        }\n        return s;\n    }\n\n    function hex(x) {\n        var i;\n        for (i = 0; i < x.length; i += 1) {\n            x[i] = rhex(x[i]);\n        }\n        return x.join('');\n    }\n\n    // In some cases the fast add32 function cannot be used..\n    if (hex(md51('hello')) !== '5d41402abc4b2a76b9719d911017c592') {\n        add32 = function (x, y) {\n            var lsw = (x & 0xFFFF) + (y & 0xFFFF),\n                msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n            return (msw << 16) | (lsw & 0xFFFF);\n        };\n    }\n\n    // ---------------------------------------------------\n\n    /**\n     * ArrayBuffer slice polyfill.\n     *\n     * @see https://github.com/ttaubert/node-arraybuffer-slice\n     */\n\n    if (typeof ArrayBuffer !== 'undefined' && !ArrayBuffer.prototype.slice) {\n        (function () {\n            function clamp(val, length) {\n                val = (val | 0) || 0;\n\n                if (val < 0) {\n                    return Math.max(val + length, 0);\n                }\n\n                return Math.min(val, length);\n            }\n\n            ArrayBuffer.prototype.slice = function (from, to) {\n                var length = this.byteLength,\n                    begin = clamp(from, length),\n                    end = length,\n                    num,\n                    target,\n                    targetArray,\n                    sourceArray;\n\n                if (to !== undefined) {\n                    end = clamp(to, length);\n                }\n\n                if (begin > end) {\n                    return new ArrayBuffer(0);\n                }\n\n                num = end - begin;\n                target = new ArrayBuffer(num);\n                targetArray = new Uint8Array(target);\n\n                sourceArray = new Uint8Array(this, begin, num);\n                targetArray.set(sourceArray);\n\n                return target;\n            };\n        })();\n    }\n\n    // ---------------------------------------------------\n\n    /**\n     * Helpers.\n     */\n\n    function toUtf8(str) {\n        if (/[\\u0080-\\uFFFF]/.test(str)) {\n            str = unescape(encodeURIComponent(str));\n        }\n\n        return str;\n    }\n\n    function utf8Str2ArrayBuffer(str, returnUInt8Array) {\n        var length = str.length,\n           buff = new ArrayBuffer(length),\n           arr = new Uint8Array(buff),\n           i;\n\n        for (i = 0; i < length; i += 1) {\n            arr[i] = str.charCodeAt(i);\n        }\n\n        return returnUInt8Array ? arr : buff;\n    }\n\n    function arrayBuffer2Utf8Str(buff) {\n        return String.fromCharCode.apply(null, new Uint8Array(buff));\n    }\n\n    function concatenateArrayBuffers(first, second, returnUInt8Array) {\n        var result = new Uint8Array(first.byteLength + second.byteLength);\n\n        result.set(new Uint8Array(first));\n        result.set(new Uint8Array(second), first.byteLength);\n\n        return returnUInt8Array ? result : result.buffer;\n    }\n\n    function hexToBinaryString(hex) {\n        var bytes = [],\n            length = hex.length,\n            x;\n\n        for (x = 0; x < length - 1; x += 2) {\n            bytes.push(parseInt(hex.substr(x, 2), 16));\n        }\n\n        return String.fromCharCode.apply(String, bytes);\n    }\n\n    // ---------------------------------------------------\n\n    /**\n     * SparkMD5 OOP implementation.\n     *\n     * Use this class to perform an incremental md5, otherwise use the\n     * static methods instead.\n     */\n\n    function SparkMD5() {\n        // call reset to init the instance\n        this.reset();\n    }\n\n    /**\n     * Appends a string.\n     * A conversion will be applied if an utf8 string is detected.\n     *\n     * @param {String} str The string to be appended\n     *\n     * @return {SparkMD5} The instance itself\n     */\n    SparkMD5.prototype.append = function (str) {\n        // Converts the string to utf8 bytes if necessary\n        // Then append as binary\n        this.appendBinary(toUtf8(str));\n\n        return this;\n    };\n\n    /**\n     * Appends a binary string.\n     *\n     * @param {String} contents The binary string to be appended\n     *\n     * @return {SparkMD5} The instance itself\n     */\n    SparkMD5.prototype.appendBinary = function (contents) {\n        this._buff += contents;\n        this._length += contents.length;\n\n        var length = this._buff.length,\n            i;\n\n        for (i = 64; i <= length; i += 64) {\n            md5cycle(this._hash, md5blk(this._buff.substring(i - 64, i)));\n        }\n\n        this._buff = this._buff.substring(i - 64);\n\n        return this;\n    };\n\n    /**\n     * Finishes the incremental computation, reseting the internal state and\n     * returning the result.\n     *\n     * @param {Boolean} raw True to get the raw string, false to get the hex string\n     *\n     * @return {String} The result\n     */\n    SparkMD5.prototype.end = function (raw) {\n        var buff = this._buff,\n            length = buff.length,\n            i,\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n            ret;\n\n        for (i = 0; i < length; i += 1) {\n            tail[i >> 2] |= buff.charCodeAt(i) << ((i % 4) << 3);\n        }\n\n        this._finish(tail, length);\n        ret = hex(this._hash);\n\n        if (raw) {\n            ret = hexToBinaryString(ret);\n        }\n\n        this.reset();\n\n        return ret;\n    };\n\n    /**\n     * Resets the internal state of the computation.\n     *\n     * @return {SparkMD5} The instance itself\n     */\n    SparkMD5.prototype.reset = function () {\n        this._buff = '';\n        this._length = 0;\n        this._hash = [1732584193, -271733879, -1732584194, 271733878];\n\n        return this;\n    };\n\n    /**\n     * Gets the internal state of the computation.\n     *\n     * @return {Object} The state\n     */\n    SparkMD5.prototype.getState = function () {\n        return {\n            buff: this._buff,\n            length: this._length,\n            hash: this._hash.slice()\n        };\n    };\n\n    /**\n     * Gets the internal state of the computation.\n     *\n     * @param {Object} state The state\n     *\n     * @return {SparkMD5} The instance itself\n     */\n    SparkMD5.prototype.setState = function (state) {\n        this._buff = state.buff;\n        this._length = state.length;\n        this._hash = state.hash;\n\n        return this;\n    };\n\n    /**\n     * Releases memory used by the incremental buffer and other additional\n     * resources. If you plan to use the instance again, use reset instead.\n     */\n    SparkMD5.prototype.destroy = function () {\n        delete this._hash;\n        delete this._buff;\n        delete this._length;\n    };\n\n    /**\n     * Finish the final calculation based on the tail.\n     *\n     * @param {Array}  tail   The tail (will be modified)\n     * @param {Number} length The length of the remaining buffer\n     */\n    SparkMD5.prototype._finish = function (tail, length) {\n        var i = length,\n            tmp,\n            lo,\n            hi;\n\n        tail[i >> 2] |= 0x80 << ((i % 4) << 3);\n        if (i > 55) {\n            md5cycle(this._hash, tail);\n            for (i = 0; i < 16; i += 1) {\n                tail[i] = 0;\n            }\n        }\n\n        // Do the final computation based on the tail and length\n        // Beware that the final length may not fit in 32 bits so we take care of that\n        tmp = this._length * 8;\n        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);\n        lo = parseInt(tmp[2], 16);\n        hi = parseInt(tmp[1], 16) || 0;\n\n        tail[14] = lo;\n        tail[15] = hi;\n        md5cycle(this._hash, tail);\n    };\n\n    /**\n     * Performs the md5 hash on a string.\n     * A conversion will be applied if utf8 string is detected.\n     *\n     * @param {String}  str The string\n     * @param {Boolean} [raw] True to get the raw string, false to get the hex string\n     *\n     * @return {String} The result\n     */\n    SparkMD5.hash = function (str, raw) {\n        // Converts the string to utf8 bytes if necessary\n        // Then compute it using the binary function\n        return SparkMD5.hashBinary(toUtf8(str), raw);\n    };\n\n    /**\n     * Performs the md5 hash on a binary string.\n     *\n     * @param {String}  content The binary string\n     * @param {Boolean} [raw]     True to get the raw string, false to get the hex string\n     *\n     * @return {String} The result\n     */\n    SparkMD5.hashBinary = function (content, raw) {\n        var hash = md51(content),\n            ret = hex(hash);\n\n        return raw ? hexToBinaryString(ret) : ret;\n    };\n\n    // ---------------------------------------------------\n\n    /**\n     * SparkMD5 OOP implementation for array buffers.\n     *\n     * Use this class to perform an incremental md5 ONLY for array buffers.\n     */\n    SparkMD5.ArrayBuffer = function () {\n        // call reset to init the instance\n        this.reset();\n    };\n\n    /**\n     * Appends an array buffer.\n     *\n     * @param {ArrayBuffer} arr The array to be appended\n     *\n     * @return {SparkMD5.ArrayBuffer} The instance itself\n     */\n    SparkMD5.ArrayBuffer.prototype.append = function (arr) {\n        var buff = concatenateArrayBuffers(this._buff.buffer, arr, true),\n            length = buff.length,\n            i;\n\n        this._length += arr.byteLength;\n\n        for (i = 64; i <= length; i += 64) {\n            md5cycle(this._hash, md5blk_array(buff.subarray(i - 64, i)));\n        }\n\n        this._buff = (i - 64) < length ? new Uint8Array(buff.buffer.slice(i - 64)) : new Uint8Array(0);\n\n        return this;\n    };\n\n    /**\n     * Finishes the incremental computation, reseting the internal state and\n     * returning the result.\n     *\n     * @param {Boolean} raw True to get the raw string, false to get the hex string\n     *\n     * @return {String} The result\n     */\n    SparkMD5.ArrayBuffer.prototype.end = function (raw) {\n        var buff = this._buff,\n            length = buff.length,\n            tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n            i,\n            ret;\n\n        for (i = 0; i < length; i += 1) {\n            tail[i >> 2] |= buff[i] << ((i % 4) << 3);\n        }\n\n        this._finish(tail, length);\n        ret = hex(this._hash);\n\n        if (raw) {\n            ret = hexToBinaryString(ret);\n        }\n\n        this.reset();\n\n        return ret;\n    };\n\n    /**\n     * Resets the internal state of the computation.\n     *\n     * @return {SparkMD5.ArrayBuffer} The instance itself\n     */\n    SparkMD5.ArrayBuffer.prototype.reset = function () {\n        this._buff = new Uint8Array(0);\n        this._length = 0;\n        this._hash = [1732584193, -271733879, -1732584194, 271733878];\n\n        return this;\n    };\n\n    /**\n     * Gets the internal state of the computation.\n     *\n     * @return {Object} The state\n     */\n    SparkMD5.ArrayBuffer.prototype.getState = function () {\n        var state = SparkMD5.prototype.getState.call(this);\n\n        // Convert buffer to a string\n        state.buff = arrayBuffer2Utf8Str(state.buff);\n\n        return state;\n    };\n\n    /**\n     * Gets the internal state of the computation.\n     *\n     * @param {Object} state The state\n     *\n     * @return {SparkMD5.ArrayBuffer} The instance itself\n     */\n    SparkMD5.ArrayBuffer.prototype.setState = function (state) {\n        // Convert string to buffer\n        state.buff = utf8Str2ArrayBuffer(state.buff, true);\n\n        return SparkMD5.prototype.setState.call(this, state);\n    };\n\n    SparkMD5.ArrayBuffer.prototype.destroy = SparkMD5.prototype.destroy;\n\n    SparkMD5.ArrayBuffer.prototype._finish = SparkMD5.prototype._finish;\n\n    /**\n     * Performs the md5 hash on an array buffer.\n     *\n     * @param {ArrayBuffer} arr The array buffer\n     * @param {Boolean}     [raw] True to get the raw string, false to get the hex one\n     *\n     * @return {String} The result\n     */\n    SparkMD5.ArrayBuffer.hash = function (arr, raw) {\n        var hash = md51_array(new Uint8Array(arr)),\n            ret = hex(hash);\n\n        return raw ? hexToBinaryString(ret) : ret;\n    };\n\n    return SparkMD5;\n}));\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n// If obj.hasOwnProperty has been overridden, then calling\n// obj.hasOwnProperty(prop) will break.\n// See: https://github.com/joyent/node/issues/1707\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nmodule.exports = function(qs, sep, eq, options) {\n  sep = sep || '&';\n  eq = eq || '=';\n  var obj = {};\n\n  if (typeof qs !== 'string' || qs.length === 0) {\n    return obj;\n  }\n\n  var regexp = /\\+/g;\n  qs = qs.split(sep);\n\n  var maxKeys = 1000;\n  if (options && typeof options.maxKeys === 'number') {\n    maxKeys = options.maxKeys;\n  }\n\n  var len = qs.length;\n  // maxKeys <= 0 means that we should not limit keys count\n  if (maxKeys > 0 && len > maxKeys) {\n    len = maxKeys;\n  }\n\n  for (var i = 0; i < len; ++i) {\n    var x = qs[i].replace(regexp, '%20'),\n        idx = x.indexOf(eq),\n        kstr, vstr, k, v;\n\n    if (idx >= 0) {\n      kstr = x.substr(0, idx);\n      vstr = x.substr(idx + 1);\n    } else {\n      kstr = x;\n      vstr = '';\n    }\n\n    k = decodeURIComponent(kstr);\n    v = decodeURIComponent(vstr);\n\n    if (!hasOwnProperty(obj, k)) {\n      obj[k] = v;\n    } else if (Array.isArray(obj[k])) {\n      obj[k].push(v);\n    } else {\n      obj[k] = [obj[k], v];\n    }\n  }\n\n  return obj;\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar stringifyPrimitive = function(v) {\n  switch (typeof v) {\n    case 'string':\n      return v;\n\n    case 'boolean':\n      return v ? 'true' : 'false';\n\n    case 'number':\n      return isFinite(v) ? v : '';\n\n    default:\n      return '';\n  }\n};\n\nmodule.exports = function(obj, sep, eq, name) {\n  sep = sep || '&';\n  eq = eq || '=';\n  if (obj === null) {\n    obj = undefined;\n  }\n\n  if (typeof obj === 'object') {\n    return Object.keys(obj).map(function(k) {\n      var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;\n      if (Array.isArray(obj[k])) {\n        return obj[k].map(function(v) {\n          return ks + encodeURIComponent(stringifyPrimitive(v));\n        }).join(sep);\n      } else {\n        return ks + encodeURIComponent(stringifyPrimitive(obj[k]));\n      }\n    }).filter(Boolean).join(sep);\n\n  }\n\n  if (!name) return '';\n  return encodeURIComponent(stringifyPrimitive(name)) + eq +\n         encodeURIComponent(stringifyPrimitive(obj));\n};\n", "'use strict';\n\nexports.decode = exports.parse = require('./decode');\nexports.encode = exports.stringify = require('./encode');\n", "export enum QiniuErrorName {\n  // 输入错误\n  InvalidFile = 'InvalidFile',\n  InvalidToken = 'InvalidToken',\n  InvalidMetadata = 'InvalidMetadata',\n  InvalidChunkSize = 'InvalidChunkSize',\n  InvalidCustomVars = 'InvalidCustomVars',\n  NotAvailableUploadHost = 'NotAvailableUploadHost',\n\n  // 缓存相关\n  ReadCacheFailed = 'ReadCacheFailed',\n  InvalidCacheData = 'InvalidCacheData',\n  WriteCacheFailed = 'WriteCacheFailed',\n  RemoveCacheFailed = 'RemoveCacheFailed',\n\n  // 图片压缩模块相关\n  GetCanvasContextFailed = 'GetCanvasContextFailed',\n  UnsupportedFileType = 'UnsupportedFileType',\n\n  // 运行环境相关\n  FileReaderReadFailed = 'FileReaderReadFailed',\n  NotAvailableXMLHttpRequest = 'NotAvailableXMLHttpRequest',\n  InvalidProgressEventTarget = 'InvalidProgressEventTarget',\n\n  // 请求错误\n  RequestError = 'RequestError'\n}\n\nexport class QiniuError implements Error {\n  public stack: string | undefined\n  constructor(public name: QiniuErrorName, public message: string) {\n    this.stack = new Error().stack\n  }\n}\n\nexport class QiniuRequestError extends QiniuError {\n\n  /**\n   * @description 标记当前的 error 类型是一个 QiniuRequestError\n   * @deprecated 下一个大版本将会移除，不推荐使用，推荐直接使用 instanceof 进行判断\n   */\n  public isRequestError = true\n\n  /**\n   * @description 发生错误时服务端返回的错误信息，如果返回不是一个合法的 json、则该字段为 undefined\n   */\n  public data?: any\n\n  constructor(public code: number, public reqId: string, message: string, data?: any) {\n    super(QiniuErrorName.RequestError, message)\n    this.data = data\n  }\n}\n\n/**\n * @description 由于跨域、证书错误、断网、host 解析失败、系统拦截等原因导致的错误\n */\nexport class QiniuNetworkError extends QiniuRequestError {\n  constructor(message: string, reqId = '') {\n    super(0, reqId, message)\n  }\n}\n", "export type RunTask<T> = (...args: T[]) => Promise<void>\n\nexport interface QueueContent<T> {\n  task: T\n  resolve: () => void\n  reject: (err?: any) => void\n}\n\nexport class Pool<T> {\n  aborted = false\n  queue: Array<QueueContent<T>> = []\n  processing: Array<QueueContent<T>> = []\n\n  constructor(private runTask: RunTask<T>, private limit: number) {}\n\n  enqueue(task: T) {\n    return new Promise<void>((resolve, reject) => {\n      this.queue.push({\n        task,\n        resolve,\n        reject\n      })\n      this.check()\n    })\n  }\n\n  private run(item: QueueContent<T>) {\n    this.queue = this.queue.filter(v => v !== item)\n    this.processing.push(item)\n    this.runTask(item.task).then(\n      () => {\n        this.processing = this.processing.filter(v => v !== item)\n        item.resolve()\n        this.check()\n      },\n      err => item.reject(err)\n    )\n  }\n\n  private check() {\n    if (this.aborted) return\n    const processingNum = this.processing.length\n    const availableNum = this.limit - processingNum\n    this.queue.slice(0, availableNum).forEach(item => {\n      this.run(item)\n    })\n  }\n\n  abort() {\n    this.queue = []\n    this.aborted = true\n  }\n}\n", "/** 消费者接口 */\nexport interface IObserver<T, E, C> {\n  /** 用来接收 Observable 中的 next 类型通知 */\n  next: (value: T) => void\n  /** 用来接收 Observable 中的 error 类型通知 */\n  error: (err: E) => void\n  /** 用来接收 Observable 中的 complete 类型通知 */\n  complete: (res: C) => void\n}\n\nexport interface NextObserver<T, E, C> {\n  next: (value: T) => void\n  error?: (err: E) => void\n  complete?: (res: C) => void\n}\n\nexport interface ErrorObserver<T, E, C> {\n  next?: (value: T) => void\n  error: (err: E) => void\n  complete?: (res: C) => void\n}\n\nexport interface CompletionObserver<T, E, C> {\n  next?: (value: T) => void\n  error?: (err: E) => void\n  complete: (res: C) => void\n}\n\nexport type PartialObserver<T, E, C> = NextObserver<T, E, C> | ErrorObserver<T, E, C> | CompletionObserver<T, E, C>\n\nexport interface IUnsubscribable {\n  /** 取消 observer 的订阅 */\n  unsubscribe(): void\n}\n\n/** Subscription 的接口 */\nexport interface ISubscriptionLike extends IUnsubscribable {\n  readonly closed: boolean\n}\n\nexport type TeardownLogic = () => void\n\nexport interface ISubscribable<T, E, C> {\n  subscribe(\n    observer?: PartialObserver<T, E, C> | ((value: T) => void),\n    error?: (error: any) => void,\n    complete?: () => void\n  ): IUnsubscribable\n}\n\n/** 表示可清理的资源，比如 Observable 的执行 */\nclass Subscription implements ISubscriptionLike {\n  /** 用来标示该 Subscription 是否被取消订阅的标示位 */\n  public closed = false\n\n  /** 清理 subscription 持有的资源 */\n  private _unsubscribe: TeardownLogic | undefined\n\n  /** 取消 observer 的订阅 */\n  unsubscribe() {\n    if (this.closed) {\n      return\n    }\n\n    this.closed = true\n    if (this._unsubscribe) {\n      this._unsubscribe()\n    }\n  }\n\n  /** 添加一个 tear down 在该 Subscription 的 unsubscribe() 期间调用 */\n  add(teardown: TeardownLogic) {\n    this._unsubscribe = teardown\n  }\n}\n\n/**\n * 实现 Observer 接口并且继承 Subscription 类，Observer 是消费 Observable 值的公有 API\n * 所有 Observers 都转化成了 Subscriber，以便提供类似 Subscription 的能力，比如 unsubscribe\n*/\nexport class Subscriber<T, E, C> extends Subscription implements IObserver<T, E, C> {\n  protected isStopped = false\n  protected destination: Partial<IObserver<T, E, C>>\n\n  constructor(\n    observerOrNext?: PartialObserver<T, E, C> | ((value: T) => void) | null,\n    error?: ((err: E) => void) | null,\n    complete?: ((res: C) => void) | null\n  ) {\n    super()\n\n    if (observerOrNext && typeof observerOrNext === 'object') {\n      this.destination = observerOrNext\n    } else {\n      this.destination = {\n        ...observerOrNext && { next: observerOrNext },\n        ...error && { error },\n        ...complete && { complete }\n      }\n    }\n  }\n\n  unsubscribe(): void {\n    if (this.closed) {\n      return\n    }\n\n    this.isStopped = true\n    super.unsubscribe()\n  }\n\n  next(value: T) {\n    if (!this.isStopped && this.destination.next) {\n      this.destination.next(value)\n    }\n  }\n\n  error(err: E) {\n    if (!this.isStopped && this.destination.error) {\n      this.isStopped = true\n      this.destination.error(err)\n    }\n  }\n\n  complete(result: C) {\n    if (!this.isStopped && this.destination.complete) {\n      this.isStopped = true\n      this.destination.complete(result)\n    }\n  }\n}\n\n/** 可观察对象，当前的上传事件的集合 */\nexport class Observable<T, E, C> implements ISubscribable<T, E, C> {\n\n  constructor(private _subscribe: (subscriber: Subscriber<T, E, C>) => TeardownLogic) {}\n\n  subscribe(observer: PartialObserver<T, E, C>): Subscription\n  subscribe(next: null | undefined, error: null | undefined, complete: (res: C) => void): Subscription\n  subscribe(next: null | undefined, error: (error: E) => void, complete?: (res: C) => void): Subscription\n  subscribe(next: (value: T) => void, error: null | undefined, complete: (res: C) => void): Subscription\n  subscribe(\n    observerOrNext?: PartialObserver<T, E, C> | ((value: T) => void) | null,\n    error?: ((err: E) => void) | null,\n    complete?: ((res: C) => void) | null\n  ): Subscription {\n    const sink = new Subscriber(observerOrNext, error, complete)\n    sink.add(this._subscribe(sink))\n    return sink\n  }\n}\n", "/* eslint-disable */\n\n// https://github.com/locutusjs/locutus/blob/master/src/php/xml/utf8_encode.js\nfunction utf8Encode(argString: string) {\n  // http://kevin.vanzonneveld.net\n  // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)\n  // +   improved by: <PERSON> (http://kevin.vanzonneveld.net)\n  // +   improved by: sowberry\n  // +    tweaked by: Jack\n  // +   bugfixed by: <PERSON><PERSON>\n  // +   improved by: <PERSON>\n  // +   bugfixed by: <PERSON><PERSON>\n  // +   bugfixed by: <PERSON>\n  // +   bugfixed by: <PERSON><PERSON><PERSON>\n  // +   improved by: kirilloid\n  // +   bugfixed by: kirilloid\n  // *     example 1: this.utf8Encode('<PERSON>')\n  // *     returns 1: '<PERSON>'\n\n  if (argString === null || typeof argString === 'undefined') {\n    return ''\n  }\n\n  let string = argString + '' // .replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n')\n  let utftext = '',\n    start,\n    end,\n    stringl = 0\n\n  start = end = 0\n  stringl = string.length\n  for (let n = 0; n < stringl; n++) {\n    let c1 = string.charCodeAt(n)\n    let enc = null\n\n    if (c1 < 128) {\n      end++\n    } else if (c1 > 127 && c1 < 2048) {\n      enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128)\n    } else if ((c1 & 0xf800 ^ 0xd800) > 0) {\n      enc = String.fromCharCode(\n        (c1 >> 12) | 224,\n        ((c1 >> 6) & 63) | 128,\n        (c1 & 63) | 128\n      )\n    } else {\n      // surrogate pairs\n      if ((c1 & 0xfc00 ^ 0xd800) > 0) {\n        throw new RangeError('Unmatched trail surrogate at ' + n)\n      }\n      let c2 = string.charCodeAt(++n)\n      if ((c2 & 0xfc00 ^ 0xdc00) > 0) {\n        throw new RangeError('Unmatched lead surrogate at ' + (n - 1))\n      }\n      c1 = ((c1 & 0x3ff) << 10) + (c2 & 0x3ff) + 0x10000\n      enc = String.fromCharCode(\n        (c1 >> 18) | 240,\n        ((c1 >> 12) & 63) | 128,\n        ((c1 >> 6) & 63) | 128,\n        (c1 & 63) | 128\n      )\n    }\n    if (enc !== null) {\n      if (end > start) {\n        utftext += string.slice(start, end)\n      }\n      utftext += enc\n      start = end = n + 1\n    }\n  }\n\n  if (end > start) {\n    utftext += string.slice(start, stringl)\n  }\n\n  return utftext\n}\n\n// https://github.com/locutusjs/locutus/blob/master/src/php/xml/utf8_decode.js\nfunction utf8Decode(strData: string) {\n  // eslint-disable-line camelcase\n  //  discuss at: https://locutus.io/php/utf8_decode/\n  // original by: Webtoolkit.info (https://www.webtoolkit.info/)\n  //    input by: Aman Gupta\n  //    input by: Brett Zamir (https://brett-zamir.me)\n  // improved by: Kevin van Zonneveld (https://kvz.io)\n  // improved by: Norman \"zEh\" Fuchs\n  // bugfixed by: hitwork\n  // bugfixed by: Onno Marsman (https://twitter.com/onnomarsman)\n  // bugfixed by: Kevin van Zonneveld (https://kvz.io)\n  // bugfixed by: kirilloid\n  // bugfixed by: w35l3y (https://www.wesley.eti.br)\n  //   example 1: utf8_decode('Kevin van Zonneveld')\n  //   returns 1: 'Kevin van Zonneveld'\n\n  const tmpArr = []\n  let i = 0\n  let c1 = 0\n  let seqlen = 0\n\n  strData += ''\n\n  while (i < strData.length) {\n    c1 = strData.charCodeAt(i) & 0xFF\n    seqlen = 0\n\n    // https://en.wikipedia.org/wiki/UTF-8#Codepage_layout\n    if (c1 <= 0xBF) {\n      c1 = (c1 & 0x7F)\n      seqlen = 1\n    } else if (c1 <= 0xDF) {\n      c1 = (c1 & 0x1F)\n      seqlen = 2\n    } else if (c1 <= 0xEF) {\n      c1 = (c1 & 0x0F)\n      seqlen = 3\n    } else {\n      c1 = (c1 & 0x07)\n      seqlen = 4\n    }\n\n    for (let ai = 1; ai < seqlen; ++ai) {\n      c1 = ((c1 << 0x06) | (strData.charCodeAt(ai + i) & 0x3F))\n    }\n\n    if (seqlen === 4) {\n      c1 -= 0x10000\n      tmpArr.push(String.fromCharCode(0xD800 | ((c1 >> 10) & 0x3FF)))\n      tmpArr.push(String.fromCharCode(0xDC00 | (c1 & 0x3FF)))\n    } else {\n      tmpArr.push(String.fromCharCode(c1))\n    }\n\n    i += seqlen\n  }\n\n  return tmpArr.join('')\n}\n\nfunction base64Encode(data: any) {\n  // http://kevin.vanzonneveld.net\n  // +   original by: Tyler Akins (http://rumkin.com)\n  // +   improved by: Bayron Guevara\n  // +   improved by: Thunder.m\n  // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)\n  // +   bugfixed by: Pellentesque Malesuada\n  // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)\n  // -    depends on: this.utf8Encode\n  // *     example 1: this.base64Encode('Kevin van Zonneveld')\n  // *     returns 1: 'S2V2aW4gdmFuIFpvbm5ldmVsZA=='\n  // mozilla has this native\n  // - but breaks in 2.0.0.12!\n  // if (typeof this.window['atob'] == 'function') {\n  //    return atob(data)\n  // }\n  let b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\n  let o1,\n    o2,\n    o3,\n    h1,\n    h2,\n    h3,\n    h4,\n    bits,\n    i = 0,\n    ac = 0,\n    enc = '',\n    tmp_arr = []\n\n  if (!data) {\n    return data\n  }\n\n  data = utf8Encode(data + '')\n\n  do {\n    // pack three octets into four hexets\n    o1 = data.charCodeAt(i++)\n    o2 = data.charCodeAt(i++)\n    o3 = data.charCodeAt(i++)\n\n    bits = (o1 << 16) | (o2 << 8) | o3\n\n    h1 = (bits >> 18) & 0x3f\n    h2 = (bits >> 12) & 0x3f\n    h3 = (bits >> 6) & 0x3f\n    h4 = bits & 0x3f\n\n    // use hexets to index into b64, and append result to encoded string\n    tmp_arr[ac++] =\n      b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4)\n  } while (i < data.length)\n\n  enc = tmp_arr.join('')\n\n  switch (data.length % 3) {\n    case 1:\n      enc = enc.slice(0, -2) + '=='\n      break\n    case 2:\n      enc = enc.slice(0, -1) + '='\n      break\n  }\n\n  return enc\n}\n\nfunction base64Decode(data: string) {\n  // http://kevin.vanzonneveld.net\n  // +   original by: Tyler Akins (http://rumkin.com)\n  // +   improved by: Thunder.m\n  // +      input by: Aman Gupta\n  // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)\n  // +   bugfixed by: Onno Marsman\n  // +   bugfixed by: Pellentesque Malesuada\n  // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)\n  // +      input by: Brett Zamir (http://brett-zamir.me)\n  // +   bugfixed by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)\n  // *     example 1: base64_decode('S2V2aW4gdmFuIFpvbm5ldmVsZA==')\n  // *     returns 1: 'Kevin van Zonneveld'\n  // mozilla has this native\n  // - but breaks in 2.0.0.12!\n  // if (typeof this.window['atob'] == 'function') {\n  //    return atob(data)\n  // }\n  let b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\n  let o1, o2, o3, h1, h2, h3, h4, bits, i = 0,\n    ac = 0,\n    dec = '',\n    tmp_arr = []\n\n  if (!data) {\n    return data\n  }\n\n  data += ''\n\n  do { // unpack four hexets into three octets using index points in b64\n    h1 = b64.indexOf(data.charAt(i++))\n    h2 = b64.indexOf(data.charAt(i++))\n    h3 = b64.indexOf(data.charAt(i++))\n    h4 = b64.indexOf(data.charAt(i++))\n\n    bits = h1 << 18 | h2 << 12 | h3 << 6 | h4\n\n    o1 = bits >> 16 & 0xff\n    o2 = bits >> 8 & 0xff\n    o3 = bits & 0xff\n\n    if (h3 === 64) {\n      tmp_arr[ac++] = String.fromCharCode(o1)\n    } else if (h4 === 64) {\n      tmp_arr[ac++] = String.fromCharCode(o1, o2)\n    } else {\n      tmp_arr[ac++] = String.fromCharCode(o1, o2, o3)\n    }\n  } while (i < data.length)\n\n  dec = tmp_arr.join('')\n\n  return utf8Decode(dec)\n}\n\nexport function urlSafeBase64Encode(v: any) {\n  v = base64Encode(v)\n\n  // 参考 https://tools.ietf.org/html/rfc4648#section-5\n  return v.replace(/\\//g, '_').replace(/\\+/g, '-')\n}\n\nexport function urlSafeBase64Decode(v: any) {\n  v = v.replace(/_/g, '/').replace(/-/g, '+')\n  return base64Decode(v)\n}\n", "import SparkMD5 from 'spark-md5'\n\nimport { QiniuErrorName, QiniuError, QiniuRequestError, QiniuNetworkError } from '../errors'\nimport { Progress, LocalInfo } from '../upload'\nimport Logger from '../logger'\n\nimport { urlSafeBase64Decode } from './base64'\n\nexport const MB = 1024 ** 2\n\n// 文件分块\nexport function getChunks(file: File, blockSize: number): Blob[] {\n\n  let chunkByteSize = blockSize * MB // 转换为字节\n  // 如果 chunkByteSize 比文件大，则直接取文件的大小\n  if (chunkByteSize > file.size) {\n    chunkByteSize = file.size\n  } else {\n    // 因为最多 10000 chunk，所以如果 chunkSize 不符合则把每片 chunk 大小扩大两倍\n    while (file.size > chunkByteSize * 10000) {\n      chunkByteSize *= 2\n    }\n  }\n\n  const chunks: Blob[] = []\n  const count = Math.ceil(file.size / chunkByteSize)\n  for (let i = 0; i < count; i++) {\n    const chunk = file.slice(\n      chunkByteSize * i,\n      i === count - 1 ? file.size : chunkByteSize * (i + 1)\n    )\n    chunks.push(chunk)\n  }\n  return chunks\n}\n\nexport function isMetaDataValid(params: { [key: string]: string }) {\n  return Object.keys(params).every(key => key.indexOf('x-qn-meta-') === 0)\n}\n\nexport function isCustomVarsValid(params: { [key: string]: string }) {\n  return Object.keys(params).every(key => key.indexOf('x:') === 0)\n}\n\nexport function sum(list: number[]) {\n  return list.reduce((data, loaded) => data + loaded, 0)\n}\n\nexport function setLocalFileInfo(localKey: string, info: LocalInfo, logger: Logger) {\n  try {\n    localStorage.setItem(localKey, JSON.stringify(info))\n  } catch (err) {\n    logger.warn(new QiniuError(\n      QiniuErrorName.WriteCacheFailed,\n      `setLocalFileInfo failed: ${localKey}`\n    ))\n  }\n}\n\nexport function createLocalKey(name: string, key: string | null | undefined, size: number): string {\n  const localKey = key == null ? '_' : `_key_${key}_`\n  return `qiniu_js_sdk_upload_file_name_${name}${localKey}size_${size}`\n}\n\nexport function removeLocalFileInfo(localKey: string, logger: Logger) {\n  try {\n    localStorage.removeItem(localKey)\n  } catch (err) {\n    logger.warn(new QiniuError(\n      QiniuErrorName.RemoveCacheFailed,\n      `removeLocalFileInfo failed. key: ${localKey}`\n    ))\n  }\n}\n\nexport function getLocalFileInfo(localKey: string, logger: Logger): LocalInfo | null {\n  let localInfoString: string | null = null\n  try {\n    localInfoString = localStorage.getItem(localKey)\n  } catch {\n    logger.warn(new QiniuError(\n      QiniuErrorName.ReadCacheFailed,\n      `getLocalFileInfo failed. key: ${localKey}`\n    ))\n  }\n\n  if (localInfoString == null) {\n    return null\n  }\n\n  let localInfo: LocalInfo | null = null\n  try {\n    localInfo = JSON.parse(localInfoString)\n  } catch {\n    // 本地信息已被破坏，直接删除\n    removeLocalFileInfo(localKey, logger)\n    logger.warn(new QiniuError(\n      QiniuErrorName.InvalidCacheData,\n      `getLocalFileInfo failed to parse. key: ${localKey}`\n    ))\n  }\n\n  return localInfo\n}\n\nexport function getAuthHeaders(token: string) {\n  const auth = 'UpToken ' + token\n  return { Authorization: auth }\n}\n\nexport function getHeadersForChunkUpload(token: string) {\n  const header = getAuthHeaders(token)\n  return {\n    'content-type': 'application/octet-stream',\n    ...header\n  }\n}\n\nexport function getHeadersForMkFile(token: string) {\n  const header = getAuthHeaders(token)\n  return {\n    'content-type': 'application/json',\n    ...header\n  }\n}\n\nexport function createXHR(): XMLHttpRequest {\n  if (window.XMLHttpRequest) {\n    return new XMLHttpRequest()\n  }\n\n  if (window.ActiveXObject) {\n    return new window.ActiveXObject('Microsoft.XMLHTTP')\n  }\n\n  throw new QiniuError(\n    QiniuErrorName.NotAvailableXMLHttpRequest,\n    'the current environment does not support.'\n  )\n}\n\nexport async function computeMd5(data: Blob): Promise<string> {\n  const buffer = await readAsArrayBuffer(data)\n  const spark = new SparkMD5.ArrayBuffer()\n  spark.append(buffer)\n  return spark.end()\n}\n\nexport function readAsArrayBuffer(data: Blob): Promise<ArrayBuffer> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    // evt 类型目前存在问题 https://github.com/Microsoft/TypeScript/issues/4163\n    reader.onload = (evt: ProgressEvent<FileReader>) => {\n      if (evt.target) {\n        const body = evt.target.result\n        resolve(body as ArrayBuffer)\n      } else {\n        reject(new QiniuError(\n          QiniuErrorName.InvalidProgressEventTarget,\n          'progress event target is undefined'\n        ))\n      }\n    }\n\n    reader.onerror = () => {\n      reject(new QiniuError(\n        QiniuErrorName.FileReaderReadFailed,\n        'fileReader read failed'\n      ))\n    }\n\n    reader.readAsArrayBuffer(data)\n  })\n}\n\nexport interface ResponseSuccess<T> {\n  data: T\n  reqId: string\n}\n\nexport type XHRHandler = (xhr: XMLHttpRequest) => void\n\nexport interface RequestOptions {\n  method: string\n  onProgress?: (data: Progress) => void\n  onCreate?: XHRHandler\n  body?: BodyInit | null\n  headers?: { [key: string]: string }\n}\n\nexport type Response<T> = Promise<ResponseSuccess<T>>\n\nexport function request<T>(url: string, options: RequestOptions): Response<T> {\n  return new Promise((resolve, reject) => {\n    const xhr = createXHR()\n    xhr.open(options.method, url)\n\n    if (options.onCreate) {\n      options.onCreate(xhr)\n    }\n\n    if (options.headers) {\n      const headers = options.headers\n      Object.keys(headers).forEach(k => {\n        xhr.setRequestHeader(k, headers[k])\n      })\n    }\n\n    xhr.upload.addEventListener('progress', (evt: ProgressEvent) => {\n      if (evt.lengthComputable && options.onProgress) {\n        options.onProgress({\n          loaded: evt.loaded,\n          total: evt.total\n        })\n      }\n    })\n\n    xhr.onreadystatechange = () => {\n      const responseText = xhr.responseText\n      if (xhr.readyState !== 4) {\n        return\n      }\n\n      const reqId = xhr.getResponseHeader('x-reqId') || ''\n\n      if (xhr.status === 0) {\n        // 发生 0 基本都是网络错误，常见的比如跨域、断网、host 解析失败、系统拦截等等\n        reject(new QiniuNetworkError('network error.', reqId))\n        return\n      }\n\n      if (xhr.status !== 200) {\n        let message = `xhr request failed, code: ${xhr.status}`\n        if (responseText) {\n          message += ` response: ${responseText}`\n        }\n\n        let data\n        try {\n          data = JSON.parse(responseText)\n        } catch {\n          // 无需处理该错误、可能拿到非 json 格式的响应是预期的\n        }\n\n        reject(new QiniuRequestError(xhr.status, reqId, message, data))\n        return\n      }\n\n      try {\n        resolve({\n          data: JSON.parse(responseText),\n          reqId\n        })\n      } catch (err) {\n        reject(err)\n      }\n    }\n\n    xhr.send(options.body)\n  })\n}\n\nexport function getPortFromUrl(url: string | undefined) {\n  if (url && url.match) {\n    let groups = url.match(/(^https?)/)\n\n    if (!groups) {\n      return ''\n    }\n\n    const type = groups[1]\n    groups = url.match(/^https?:\\/\\/([^:^/]*):(\\d*)/)\n\n    if (groups) {\n      return groups[2]\n    }\n\n    if (type === 'http') {\n      return '80'\n    }\n\n    return '443'\n  }\n\n  return ''\n}\n\nexport function getDomainFromUrl(url: string | undefined): string {\n  if (url && url.match) {\n    const groups = url.match(/^https?:\\/\\/([^:^/]*)/)\n    return groups ? groups[1] : ''\n  }\n\n  return ''\n}\n\n// 非标准的 PutPolicy\ninterface PutPolicy {\n  assessKey: string\n  bucketName: string\n  scope: string\n}\n\nexport function getPutPolicy(token: string): PutPolicy {\n  if (!token) throw new QiniuError(QiniuErrorName.InvalidToken, 'invalid token.')\n\n  const segments = token.split(':')\n  if (segments.length === 1) throw new QiniuError(QiniuErrorName.InvalidToken, 'invalid token segments.')\n\n  // token 构造的差异参考：https://github.com/qbox/product/blob/master/kodo/auths/UpToken.md#admin-uptoken-authorization\n  const assessKey = segments.length > 3 ? segments[1] : segments[0]\n  if (!assessKey) throw new QiniuError(QiniuErrorName.InvalidToken, 'missing assess key field.')\n\n  let putPolicy: PutPolicy | null = null\n\n  try {\n    putPolicy = JSON.parse(urlSafeBase64Decode(segments[segments.length - 1]))\n  } catch (error) {\n    throw new QiniuError(QiniuErrorName.InvalidToken, 'token parse failed.')\n  }\n\n  if (putPolicy == null) {\n    throw new QiniuError(QiniuErrorName.InvalidToken, 'putPolicy is null.')\n  }\n\n  if (putPolicy.scope == null) {\n    throw new QiniuError(QiniuErrorName.InvalidToken, 'scope field is null.')\n  }\n\n  const bucketName = putPolicy.scope.split(':')[0]\n  if (!bucketName) {\n    throw new QiniuError(QiniuErrorName.InvalidToken, 'resolve bucketName failed.')\n  }\n\n  return { assessKey, bucketName, scope: putPolicy.scope }\n}\n\nexport function createObjectURL(file: File) {\n  const URL = window.URL || window.webkitURL || window.mozURL\n  // FIXME:  需要 revokeObjectURL\n  return URL.createObjectURL(file)\n}\n", "/** 上传区域 */\nexport const region = {\n  z0: 'z0',\n  z1: 'z1',\n  z2: 'z2',\n  na0: 'na0',\n  as0: 'as0',\n  cnEast2: 'cn-east-2'\n} as const\n\n/** 上传区域对应的 host */\nexport const regionUphostMap = {\n  [region.z0]: {\n    srcUphost: ['up.qiniup.com'],\n    cdnUphost: ['upload.qiniup.com']\n  },\n  [region.z1]: {\n    srcUphost: ['up-z1.qiniup.com'],\n    cdnUphost: ['upload-z1.qiniup.com']\n  },\n  [region.z2]: {\n    srcUphost: ['up-z2.qiniup.com'],\n    cdnUphost: ['upload-z2.qiniup.com']\n  },\n  [region.na0]: {\n    srcUphost: ['up-na0.qiniup.com'],\n    cdnUphost: ['upload-na0.qiniup.com']\n  },\n  [region.as0]: {\n    srcUphost: ['up-as0.qiniup.com'],\n    cdnUphost: ['upload-as0.qiniup.com']\n  },\n  [region.cnEast2]: {\n    srcUphost: ['up-cn-east-2.qiniup.com'],\n    cdnUphost: ['upload-cn-east-2.qiniup.com']\n  }\n} as const\n", "import { stringify } from 'querystring'\n\nimport { normalizeUploadConfig } from '../utils'\nimport { Config, InternalConfig, UploadInfo } from '../upload'\nimport * as utils from '../utils'\n\ninterface UpHosts {\n  data: {\n    up: {\n      acc: {\n        main: string[]\n        backup: string[]\n      }\n    }\n  }\n}\n\nexport async function getUpHosts(accessKey: string, bucketName: string, protocol: InternalConfig['upprotocol']): Promise<UpHosts> {\n  const params = stringify({ ak: accessKey, bucket: bucketName })\n  const url = `${protocol}://api.qiniu.com/v2/query?${params}`\n  return utils.request(url, { method: 'GET' })\n}\n\n/**\n * @param bucket 空间名\n * @param key 目标文件名\n * @param uploadInfo 上传信息\n */\nfunction getBaseUrl(bucket: string, key: string | null | undefined, uploadInfo: UploadInfo) {\n  const { url, id } = uploadInfo\n  return `${url}/buckets/${bucket}/objects/${key != null ? utils.urlSafeBase64Encode(key) : '~'}/uploads/${id}`\n}\n\nexport interface InitPartsData {\n  /** 该文件的上传 id， 后续该文件其他各个块的上传，已上传块的废弃，已上传块的合成文件，都需要该 id */\n  uploadId: string\n  /** uploadId 的过期时间 */\n  expireAt: number\n}\n\n/**\n * @param token 上传鉴权凭证\n * @param bucket 上传空间\n * @param key 目标文件名\n * @param uploadUrl 上传地址\n */\nexport function initUploadParts(\n  token: string,\n  bucket: string,\n  key: string | null | undefined,\n  uploadUrl: string\n): utils.Response<InitPartsData> {\n  const url = `${uploadUrl}/buckets/${bucket}/objects/${key != null ? utils.urlSafeBase64Encode(key) : '~'}/uploads`\n  return utils.request<InitPartsData>(\n    url,\n    {\n      method: 'POST',\n      headers: utils.getAuthHeaders(token)\n    }\n  )\n}\n\nexport interface UploadChunkData {\n  etag: string\n  md5: string\n}\n\n/**\n * @param token 上传鉴权凭证\n * @param index 当前 chunk 的索引\n * @param uploadInfo 上传信息\n * @param options 请求参数\n */\nexport function uploadChunk(\n  token: string,\n  key: string | null | undefined,\n  index: number,\n  uploadInfo: UploadInfo,\n  options: Partial<utils.RequestOptions & { md5: string }>\n): utils.Response<UploadChunkData> {\n  const bucket = utils.getPutPolicy(token).bucketName\n  const url = getBaseUrl(bucket, key, uploadInfo) + `/${index}`\n  const headers = utils.getHeadersForChunkUpload(token)\n  if (options.md5) headers['Content-MD5'] = options.md5\n\n  return utils.request<UploadChunkData>(url, {\n    ...options,\n    method: 'PUT',\n    headers\n  })\n}\n\nexport type UploadCompleteData = any\n\n/**\n * @param token 上传鉴权凭证\n * @param key 目标文件名\n * @param uploadInfo 上传信息\n * @param options 请求参数\n */\nexport function uploadComplete(\n  token: string,\n  key: string | null | undefined,\n  uploadInfo: UploadInfo,\n  options: Partial<utils.RequestOptions>\n): utils.Response<UploadCompleteData> {\n  const bucket = utils.getPutPolicy(token).bucketName\n  const url = getBaseUrl(bucket, key, uploadInfo)\n  return utils.request<UploadCompleteData>(url, {\n    ...options,\n    method: 'POST',\n    headers: utils.getHeadersForMkFile(token)\n  })\n}\n\n/**\n * @param token 上传鉴权凭证\n * @param key 目标文件名\n * @param uploadInfo 上传信息\n */\nexport function deleteUploadedChunks(\n  token: string,\n  key: string | null | undefined,\n  uploadinfo: UploadInfo\n): utils.Response<void> {\n  const bucket = utils.getPutPolicy(token).bucketName\n  const url = getBaseUrl(bucket, key, uploadinfo)\n  return utils.request(\n    url,\n    {\n      method: 'DELETE',\n      headers: utils.getAuthHeaders(token)\n    }\n  )\n}\n\n/**\n * @param  {string} url\n * @param  {FormData} data\n * @param  {Partial<utils.RequestOptions>} options\n * @returns Promise\n * @description 直传接口\n */\nexport function direct(\n  url: string,\n  data: FormData,\n  options: Partial<utils.RequestOptions>\n): Promise<UploadCompleteData> {\n  return utils.request<UploadCompleteData>(url, {\n    method: 'POST',\n    body: data,\n    ...options\n  })\n}\n\nexport type UploadUrlConfig = Partial<Pick<Config, 'upprotocol' | 'uphost' | 'region' | 'useCdnDomain'>>\n\n/**\n * @param  {UploadUrlConfig} config\n * @param  {string} token\n * @returns Promise\n * @description 获取上传 url\n */\nexport async function getUploadUrl(_config: UploadUrlConfig, token: string): Promise<string> {\n  const config = normalizeUploadConfig(_config)\n  const protocol = config.upprotocol\n\n  if (config.uphost.length > 0) {\n    return `${protocol}://${config.uphost[0]}`\n  }\n  const putPolicy = utils.getPutPolicy(token)\n  const res = await getUpHosts(putPolicy.assessKey, putPolicy.bucketName, protocol)\n  const hosts = res.data.up.acc.main\n  return `${protocol}://${hosts[0]}`\n}\n", "import { <PERSON><PERSON><PERSON>rror<PERSON>ame, <PERSON><PERSON><PERSON><PERSON><PERSON>, QiniuRequestError } from '../errors'\nimport Logger, { LogLevel } from '../logger'\nimport { region } from '../config'\nimport * as utils from '../utils'\n\nimport { Host, HostPool } from './hosts'\n\nexport const DEFAULT_CHUNK_SIZE = 4 // 单位 MB\n\n// code 信息地址 https://developer.qiniu.com/kodo/3928/error-responses\nexport const FREEZE_CODE_LIST = [0, 502, 503, 504, 599] // 将会冻结当前 host 的 code\nexport const RETRY_CODE_LIST = [...FREEZE_CODE_LIST, 612] // 会进行重试的 code\n\n/** 上传文件的资源信息配置 */\nexport interface Extra {\n  /** 文件原文件名 */\n  fname: string\n  /** 用来放置自定义变量 */\n  customVars?: { [key: string]: string }\n  /** 自定义元信息 */\n  metadata?: { [key: string]: string }\n  /** 文件类型设置 */\n  mimeType?: string //\n}\n\nexport interface InternalConfig {\n  /** 是否开启 cdn 加速 */\n  useCdnDomain: boolean\n  /** 是否开启服务端校验 */\n  checkByServer: boolean\n  /** 是否对分片进行 md5校验 */\n  checkByMD5: boolean\n  /** 强制直传 */\n  forceDirect: boolean\n  /** 上传失败后重试次数 */\n  retryCount: number\n  /** 自定义上传域名 */\n  uphost: string[]\n  /** 自定义分片上传并发请求量 */\n  concurrentRequestLimit: number\n  /** 分片大小，单位为 MB */\n  chunkSize: number\n  /** 上传域名协议 */\n  upprotocol: 'https' | 'http'\n  /** 上传区域 */\n  region?: typeof region[keyof typeof region]\n  /** 是否禁止统计日志上报 */\n  disableStatisticsReport: boolean\n  /** 设置调试日志输出模式，默认 `OFF`，不输出任何日志 */\n  debugLogLevel?: LogLevel\n}\n\n/** 上传任务的配置信息 */\nexport interface Config extends Partial<Omit<InternalConfig, 'upprotocol' | 'uphost'>> {\n  /** 上传域名协议 */\n  upprotocol?: InternalConfig['upprotocol'] | 'https:' | 'http:'\n  /** 自定义上传域名 */\n  uphost?: InternalConfig['uphost'] | string\n}\n\nexport interface UploadOptions {\n  file: File\n  key: string | null | undefined\n  token: string\n  config: InternalConfig\n  putExtra?: Partial<Extra>\n}\n\nexport interface UploadInfo {\n  id: string\n  url: string\n}\n\n/** 传递给外部的上传进度信息 */\nexport interface UploadProgress {\n  total: ProgressCompose\n  uploadInfo?: UploadInfo\n  chunks?: ProgressCompose[]\n}\n\nexport interface UploadHandlers {\n  onData: (data: UploadProgress) => void\n  onError: (err: QiniuError) => void\n  onComplete: (res: any) => void\n}\n\nexport interface Progress {\n  total: number\n  loaded: number\n}\n\nexport interface ProgressCompose {\n  size: number\n  loaded: number\n  percent: number\n  fromCache?: boolean\n}\n\nexport type XHRHandler = (xhr: XMLHttpRequest) => void\n\nconst GB = 1024 ** 3\n\nexport default abstract class Base {\n  protected config: InternalConfig\n  protected putExtra: Extra\n\n  protected aborted = false\n  protected retryCount = 0\n\n  protected uploadHost?: Host\n  protected xhrList: XMLHttpRequest[] = []\n\n  protected file: File\n  protected key: string | null | undefined\n\n  protected token: string\n  protected assessKey: string\n  protected bucketName: string\n\n  protected uploadAt: number\n  protected progress: UploadProgress\n\n  protected onData: (data: UploadProgress) => void\n  protected onError: (err: QiniuError) => void\n  protected onComplete: (res: any) => void\n\n  /**\n   * @returns utils.Response<any>\n   * @description 子类通过该方法实现具体的任务处理\n   */\n  protected abstract run(): utils.Response<any>\n\n  constructor(\n    options: UploadOptions,\n    handlers: UploadHandlers,\n    protected hostPool: HostPool,\n    protected logger: Logger\n  ) {\n\n    this.config = options.config\n    logger.info('config inited.', this.config)\n\n    this.putExtra = {\n      fname: '',\n      ...options.putExtra\n    }\n\n    logger.info('putExtra inited.', this.putExtra)\n\n    this.key = options.key\n    this.file = options.file\n    this.token = options.token\n\n    this.onData = handlers.onData\n    this.onError = handlers.onError\n    this.onComplete = handlers.onComplete\n\n    try {\n      const putPolicy = utils.getPutPolicy(this.token)\n      this.bucketName = putPolicy.bucketName\n      this.assessKey = putPolicy.assessKey\n    } catch (error) {\n      logger.error('get putPolicy from token failed.', error)\n      this.onError(error)\n    }\n  }\n\n  // 检查并更新 upload host\n  protected async checkAndUpdateUploadHost() {\n    // 从 hostPool 中获取一个可用的 host 挂载在 this\n    this.logger.info('get available upload host.')\n    const newHost = await this.hostPool.getUp(\n      this.assessKey,\n      this.bucketName,\n      this.config.upprotocol\n    )\n\n    if (newHost == null) {\n      throw new QiniuError(\n        QiniuErrorName.NotAvailableUploadHost,\n        'no available upload host.'\n      )\n    }\n\n    if (this.uploadHost != null && this.uploadHost.host !== newHost.host) {\n      this.logger.warn(`host switches from ${this.uploadHost.host} to ${newHost.host}.`)\n    } else {\n      this.logger.info(`use host ${newHost.host}.`)\n    }\n\n    this.uploadHost = newHost\n  }\n\n  // 检查并解冻当前的 host\n  protected checkAndUnfreezeHost() {\n    this.logger.info('check unfreeze host.')\n    if (this.uploadHost != null && this.uploadHost.isFrozen()) {\n      this.logger.warn(`${this.uploadHost.host} will be unfrozen.`)\n      this.uploadHost.unfreeze()\n    }\n  }\n\n  // 检查并更新冻结当前的 host\n  private checkAndFreezeHost(error: QiniuError) {\n    this.logger.info('check freeze host.')\n    if (error instanceof QiniuRequestError && this.uploadHost != null) {\n      if (FREEZE_CODE_LIST.includes(error.code)) {\n        this.logger.warn(`${this.uploadHost.host} will be temporarily frozen.`)\n        this.uploadHost.freeze()\n      }\n    }\n  }\n\n  private handleError(error: QiniuError) {\n    this.logger.error(error.message)\n    this.onError(error)\n  }\n\n  /**\n   * @returns Promise 返回结果与上传最终状态无关，状态信息请通过 [Subscriber] 获取。\n   * @description 上传文件，状态信息请通过 [Subscriber] 获取。\n   */\n  public async putFile(): Promise<void> {\n    this.aborted = false\n    if (!this.putExtra.fname) {\n      this.logger.info('use file.name as fname.')\n      this.putExtra.fname = this.file.name\n    }\n\n    if (this.file.size > 10000 * GB) {\n      this.handleError(new QiniuError(\n        QiniuErrorName.InvalidFile,\n        'file size exceed maximum value 10000G'\n      ))\n      return\n    }\n\n    if (this.putExtra.customVars) {\n      if (!utils.isCustomVarsValid(this.putExtra.customVars)) {\n        this.handleError(new QiniuError(\n          QiniuErrorName.InvalidCustomVars,\n          // FIXME: width => with\n          'customVars key should start width x:'\n        ))\n        return\n      }\n    }\n\n    if (this.putExtra.metadata) {\n      if (!utils.isMetaDataValid(this.putExtra.metadata)) {\n        this.handleError(new QiniuError(\n          QiniuErrorName.InvalidMetadata,\n          'metadata key should start with x-qn-meta-'\n        ))\n        return\n      }\n    }\n\n    try {\n      this.uploadAt = new Date().getTime()\n      await this.checkAndUpdateUploadHost()\n      const result = await this.run()\n      this.onComplete(result.data)\n      this.checkAndUnfreezeHost()\n      this.sendLog(result.reqId, 200)\n      return\n    } catch (err) {\n      if (this.aborted) {\n        this.logger.warn('upload is aborted.')\n        this.sendLog('', -2)\n        return\n      }\n\n      this.clear()\n      this.logger.error(err)\n      if (err instanceof QiniuRequestError) {\n        this.sendLog(err.reqId, err.code)\n\n        // 检查并冻结当前的 host\n        this.checkAndFreezeHost(err)\n\n        const notReachRetryCount = ++this.retryCount <= this.config.retryCount\n        const needRetry = RETRY_CODE_LIST.includes(err.code)\n\n        // 以下条件满足其中之一则会进行重新上传：\n        // 1. 满足 needRetry 的条件且 retryCount 不为 0\n        // 2. uploadId 无效时在 resume 里会清除本地数据，并且这里触发重新上传\n        if (needRetry && notReachRetryCount) {\n          this.logger.warn(`error auto retry: ${this.retryCount}/${this.config.retryCount}.`)\n          this.putFile()\n          return\n        }\n      }\n\n      this.onError(err)\n    }\n  }\n\n  private clear() {\n    this.xhrList.forEach(xhr => {\n      xhr.onreadystatechange = null\n      xhr.abort()\n    })\n    this.xhrList = []\n    this.logger.info('cleanup uploading xhr.')\n  }\n\n  public stop() {\n    this.logger.info('aborted.')\n    this.clear()\n    this.aborted = true\n  }\n\n  public addXhr(xhr: XMLHttpRequest) {\n    this.xhrList.push(xhr)\n  }\n\n  private sendLog(reqId: string, code: number) {\n    this.logger.report({\n      code,\n      reqId,\n      remoteIp: '',\n      upType: 'jssdk-h5',\n      size: this.file.size,\n      time: Math.floor(this.uploadAt / 1000),\n      port: utils.getPortFromUrl(this.uploadHost?.getUrl()),\n      host: utils.getDomainFromUrl(this.uploadHost?.getUrl()),\n      bytesSent: this.progress ? this.progress.total.loaded : 0,\n      duration: Math.floor((new Date().getTime() - this.uploadAt) / 1000)\n    })\n  }\n\n  public getProgressInfoItem(loaded: number, size: number, fromCache?: boolean): ProgressCompose {\n    return {\n      size,\n      loaded,\n      percent: loaded / size * 100,\n      ...(fromCache == null ? {} : { fromCache })\n    }\n  }\n}\n", "import { uploadChunk, uploadComplete, initUploadParts, UploadChunkData } from '../api'\nimport { QiniuError, QiniuErrorName, QiniuRequestError } from '../errors'\nimport * as utils from '../utils'\n\nimport Base, { Progress, UploadInfo, Extra } from './base'\n\nexport interface UploadedChunkStorage extends UploadChunkData {\n  size: number\n}\n\nexport interface ChunkLoaded {\n  mkFileProgress: 0 | 1\n  chunks: number[]\n}\n\nexport interface ChunkInfo {\n  chunk: Blob\n  index: number\n}\n\nexport interface LocalInfo {\n  data: UploadedChunkStorage[]\n  id: string\n}\n\nexport interface ChunkPart {\n  etag: string\n  partNumber: number\n}\n\nexport interface UploadChunkBody extends Extra {\n  parts: ChunkPart[]\n}\n\n/** 是否为正整数 */\nfunction isPositiveInteger(n: number) {\n  const re = /^[1-9]\\d*$/\n  return re.test(String(n))\n}\n\nexport default class Resume extends Base {\n  /**\n   * @description 文件的分片 chunks\n   */\n  private chunks: Blob[]\n\n  /**\n   * @description 使用缓存的 chunks\n   */\n  private usedCacheList: boolean[]\n\n  /**\n   * @description 来自缓存的上传信息\n   */\n  private cachedUploadedList: UploadedChunkStorage[]\n\n  /**\n   * @description 当前上传过程中已完成的上传信息\n   */\n  private uploadedList: UploadedChunkStorage[]\n\n  /**\n   * @description 当前上传片进度信息\n   */\n  private loaded: ChunkLoaded\n\n  /**\n   * @description 当前上传任务的 id\n   */\n  private uploadId: string\n\n  /**\n   * @returns  {Promise<ResponseSuccess<any>>}\n   * @description 实现了 Base 的 run 接口，处理具体的分片上传事务，并抛出过程中的异常。\n   */\n  protected async run() {\n    this.logger.info('start run Resume.')\n    if (!this.config.chunkSize || !isPositiveInteger(this.config.chunkSize)) {\n      throw new QiniuError(\n        QiniuErrorName.InvalidChunkSize,\n        'chunkSize must be a positive integer'\n      )\n    }\n\n    if (this.config.chunkSize > 1024) {\n      throw new QiniuError(\n        QiniuErrorName.InvalidChunkSize,\n        'chunkSize maximum value is 1024'\n      )\n    }\n\n    await this.initBeforeUploadChunks()\n\n    const pool = new utils.Pool(\n      async (chunkInfo: ChunkInfo) => {\n        if (this.aborted) {\n          pool.abort()\n          throw new Error('pool is aborted')\n        }\n\n        await this.uploadChunk(chunkInfo)\n      },\n      this.config.concurrentRequestLimit\n    )\n\n    let mkFileResponse = null\n    const localKey = this.getLocalKey()\n    const uploadChunks = this.chunks.map((chunk, index) => pool.enqueue({ chunk, index }))\n\n    try {\n      await Promise.all(uploadChunks)\n      mkFileResponse = await this.mkFileReq()\n    } catch (error) {\n      // uploadId 无效，上传参数有误（多由于本地存储信息的 uploadId 失效）\n      if (error instanceof QiniuRequestError && (error.code === 612 || error.code === 400)) {\n        utils.removeLocalFileInfo(localKey, this.logger)\n      }\n\n      throw error\n    }\n\n    // 上传成功，清理本地缓存数据\n    utils.removeLocalFileInfo(localKey, this.logger)\n    return mkFileResponse\n  }\n\n  private async uploadChunk(chunkInfo: ChunkInfo) {\n    const { index, chunk } = chunkInfo\n    const cachedInfo = this.cachedUploadedList[index]\n    this.logger.info(`upload part ${index}, cache:`, cachedInfo)\n\n    const shouldCheckMD5 = this.config.checkByMD5\n    const reuseSaved = () => {\n      this.usedCacheList[index] = true\n      this.updateChunkProgress(chunk.size, index)\n      this.uploadedList[index] = cachedInfo\n      this.updateLocalCache()\n    }\n\n    // FIXME: 至少判断一下 size\n    if (cachedInfo && !shouldCheckMD5) {\n      reuseSaved()\n      return\n    }\n\n    const md5 = await utils.computeMd5(chunk)\n    this.logger.info('computed part md5.', md5)\n\n    if (cachedInfo && md5 === cachedInfo.md5) {\n      reuseSaved()\n      return\n    }\n\n    // 没有使用缓存设置标记为 false\n    this.usedCacheList[index] = false\n\n    const onProgress = (data: Progress) => {\n      this.updateChunkProgress(data.loaded, index)\n    }\n\n    const requestOptions = {\n      body: chunk,\n      md5: this.config.checkByServer ? md5 : undefined,\n      onProgress,\n      onCreate: (xhr: XMLHttpRequest) => this.addXhr(xhr)\n    }\n\n    this.logger.info(`part ${index} start uploading.`)\n    const response = await uploadChunk(\n      this.token,\n      this.key,\n      chunkInfo.index + 1,\n      this.getUploadInfo(),\n      requestOptions\n    )\n    this.logger.info(`part ${index} upload completed.`)\n\n    // 在某些浏览器环境下，xhr 的 progress 事件无法被触发，progress 为 null，这里在每次分片上传完成后都手动更新下 progress\n    onProgress({\n      loaded: chunk.size,\n      total: chunk.size\n    })\n\n    this.uploadedList[index] = {\n      etag: response.data.etag,\n      md5: response.data.md5,\n      size: chunk.size\n    }\n\n    this.updateLocalCache()\n  }\n\n  private async mkFileReq() {\n    const data: UploadChunkBody = {\n      parts: this.uploadedList.map((value, index) => ({\n        etag: value.etag,\n        // 接口要求 index 需要从 1 开始，所以需要整体 + 1\n        partNumber: index + 1\n      })),\n      fname: this.putExtra.fname,\n      ...this.putExtra.mimeType && { mimeType: this.putExtra.mimeType },\n      ...this.putExtra.customVars && { customVars: this.putExtra.customVars },\n      ...this.putExtra.metadata && { metadata: this.putExtra.metadata }\n    }\n\n    this.logger.info('parts upload completed, make file.', data)\n    const result = await uploadComplete(\n      this.token,\n      this.key,\n      this.getUploadInfo(),\n      {\n        onCreate: xhr => this.addXhr(xhr),\n        body: JSON.stringify(data)\n      }\n    )\n\n    this.logger.info('finish Resume Progress.')\n    this.updateMkFileProgress(1)\n    return result\n  }\n\n  private async initBeforeUploadChunks() {\n    this.uploadedList = []\n    this.usedCacheList = []\n    const cachedInfo = utils.getLocalFileInfo(this.getLocalKey(), this.logger)\n\n    // 分片必须和当时使用的 uploadId 配套，所以断点续传需要把本地存储的 uploadId 拿出来\n    // 假如没有 cachedInfo 本地信息并重新获取 uploadId\n    if (!cachedInfo) {\n      this.logger.info('init upload parts from api.')\n      const res = await initUploadParts(\n        this.token,\n        this.bucketName,\n        this.key,\n        this.uploadHost!.getUrl()\n      )\n      this.logger.info(`initd upload parts of id: ${res.data.uploadId}.`)\n      this.uploadId = res.data.uploadId\n      this.cachedUploadedList = []\n    } else {\n      const infoMessage = [\n        'resume upload parts from local cache,',\n        `total ${cachedInfo.data.length} part,`,\n        `id is ${cachedInfo.id}.`\n      ]\n\n      this.logger.info(infoMessage.join(' '))\n      this.cachedUploadedList = cachedInfo.data\n      this.uploadId = cachedInfo.id\n    }\n\n    this.chunks = utils.getChunks(this.file, this.config.chunkSize)\n    this.loaded = {\n      mkFileProgress: 0,\n      chunks: this.chunks.map(_ => 0)\n    }\n    this.notifyResumeProgress()\n  }\n\n  private getUploadInfo(): UploadInfo {\n    return {\n      id: this.uploadId,\n      url: this.uploadHost!.getUrl()\n    }\n  }\n\n  private getLocalKey() {\n    return utils.createLocalKey(this.file.name, this.key, this.file.size)\n  }\n\n  private updateLocalCache() {\n    utils.setLocalFileInfo(this.getLocalKey(), {\n      id: this.uploadId,\n      data: this.uploadedList\n    }, this.logger)\n  }\n\n  private updateChunkProgress(loaded: number, index: number) {\n    this.loaded.chunks[index] = loaded\n    this.notifyResumeProgress()\n  }\n\n  private updateMkFileProgress(progress: 0 | 1) {\n    this.loaded.mkFileProgress = progress\n    this.notifyResumeProgress()\n  }\n\n  private notifyResumeProgress() {\n    this.progress = {\n      total: this.getProgressInfoItem(\n        utils.sum(this.loaded.chunks) + this.loaded.mkFileProgress,\n        // FIXME: 不准确的 fileSize\n        this.file.size + 1 // 防止在 complete 未调用的时候进度显示 100%\n      ),\n      chunks: this.chunks.map((chunk, index) => {\n        const fromCache = this.usedCacheList[index]\n        return this.getProgressInfoItem(this.loaded.chunks[index], chunk.size, fromCache)\n      }),\n      uploadInfo: {\n        id: this.uploadId,\n        url: this.uploadHost!.getUrl()\n      }\n    }\n    this.onData(this.progress)\n  }\n}\n", "/* eslint-disable no-bitwise */\n\nimport { MB } from './helper'\n\n/**\n * 以下 class 实现参考\n * https://github.com/Stuk/jszip/blob/d4702a70834bd953d4c2d0bc155fad795076631a/lib/crc32.js\n * 该实现主要针对大文件优化、对计算的值进行了 `>>> 0` 运算（为与服务端保持一致）\n */\nexport class CRC32 {\n  private crc = -1\n  private table = this.makeTable()\n\n  private makeTable() {\n    const table = new Array<number>()\n    for (let i = 0; i < 256; i++) {\n      let t = i\n      for (let j = 0; j < 8; j++) {\n        if (t & 1) {\n          // IEEE 标准\n          t = (t >>> 1) ^ 0xEDB88320\n        } else {\n          t >>>= 1\n        }\n      }\n      table[i] = t\n    }\n\n    return table\n  }\n\n  private append(data: Uint8Array) {\n    let crc = this.crc\n    for (let offset = 0; offset < data.byteLength; offset++) {\n      crc = (crc >>> 8) ^ this.table[(crc ^ data[offset]) & 0xFF]\n    }\n    this.crc = crc\n  }\n\n  private compute() {\n    return (this.crc ^ -1) >>> 0\n  }\n\n  private async readAsUint8Array(file: File | Blob): Promise<Uint8Array> {\n    if (typeof file.arrayBuffer === 'function') {\n      return new Uint8Array(await file.arrayBuffer())\n    }\n\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      reader.onload = () => {\n        if (reader.result == null) {\n          reject()\n          return\n        }\n\n        if (typeof reader.result === 'string') {\n          reject()\n          return\n        }\n\n        resolve(new Uint8Array(reader.result))\n      }\n      reader.readAsArrayBuffer(file)\n    })\n  }\n\n  async file(file: File): Promise<number> {\n    if (file.size <= MB) {\n      this.append(await this.readAsUint8Array(file))\n      return this.compute()\n    }\n\n    const count = Math.ceil(file.size / MB)\n    for (let index = 0; index < count; index++) {\n      const start = index * MB\n      const end = index === (count - 1) ? file.size : start + MB\n      // eslint-disable-next-line no-await-in-loop\n      const chuck = await this.readAsUint8Array(file.slice(start, end))\n      this.append(new Uint8Array(chuck))\n    }\n\n    return this.compute()\n  }\n\n  static file(file: File): Promise<number> {\n    const crc = new CRC32()\n    return crc.file(file)\n  }\n}\n", "import { CRC32 } from '../utils/crc32'\n\nimport { direct } from '../api'\n\nimport Base from './base'\n\nexport default class Direct extends Base {\n\n  protected async run() {\n    this.logger.info('start run Direct.')\n\n    const formData = new FormData()\n    formData.append('file', this.file)\n    formData.append('token', this.token)\n    if (this.key != null) {\n      formData.append('key', this.key)\n    }\n    formData.append('fname', this.putExtra.fname)\n\n    if (this.config.checkByServer) {\n      const crcSign = await CRC32.file(this.file)\n      formData.append('crc32', crcSign.toString())\n    }\n\n    if (this.putExtra.customVars) {\n      this.logger.info('init customVars.')\n      const { customVars } = this.putExtra\n      Object.keys(customVars).forEach(key => formData.append(key, customVars[key].toString()))\n      this.logger.info('customVars inited.')\n    }\n\n    this.logger.info('formData inited.')\n    const result = await direct(this.uploadHost!.getUrl(), formData, {\n      onProgress: data => {\n        this.updateDirectProgress(data.loaded, data.total)\n      },\n      onCreate: xhr => this.addXhr(xhr)\n    })\n\n    this.logger.info('Direct progress finish.')\n    this.finishDirectProgress()\n    return result\n  }\n\n  private updateDirectProgress(loaded: number, total: number) {\n    // 当请求未完成时可能进度会达到100，所以total + 1来防止这种情况出现\n    this.progress = { total: this.getProgressInfoItem(loaded, total + 1) }\n    this.onData(this.progress)\n  }\n\n  private finishDirectProgress() {\n    // 在某些浏览器环境下，xhr 的 progress 事件无法被触发，progress 为 null，这里 fake 下\n    if (!this.progress) {\n      this.logger.warn('progress is null.')\n      this.progress = { total: this.getProgressInfoItem(this.file.size, this.file.size) }\n      this.onData(this.progress)\n      return\n    }\n\n    const { total } = this.progress\n    this.progress = { total: this.getProgressInfoItem(total.loaded + 1, total.size) }\n    this.onData(this.progress)\n  }\n}\n", "import { createXHR, getAuthHeaders } from '../utils'\n\nexport interface V3LogInfo {\n  code: number\n  reqId: string\n  host: string\n  remoteIp: string\n  port: string\n  duration: number\n  time: number\n  bytesSent: number\n  upType: 'jssdk-h5'\n  size: number\n}\n\n/**\n * @param  {string} token 上传使用的 token\n * @param  {V3LogInfo} data 上报的统计数据\n * @param  {number} retry 重试的次数，默认值 3\n * @description v3 版本的日志上传接口，参考文档 https://github.com/qbox/product/blob/master/kodo/uplog.md#%E7%89%88%E6%9C%AC-3。\n */\nexport function reportV3(token: string, data: V3LogInfo, retry = 3) {\n  const xhr = createXHR()\n  xhr.open('POST', 'https://uplog.qbox.me/log/3')\n  xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded')\n  xhr.setRequestHeader('Authorization', getAuthHeaders(token).Authorization)\n  xhr.onreadystatechange = () => {\n    if (xhr.readyState === 4 && xhr.status !== 200 && retry > 0) {\n      reportV3(token, data, retry - 1)\n    }\n  }\n\n  // 顺序参考：https://github.com/qbox/product/blob/master/kodo/uplog.md#%E7%89%88%E6%9C%AC-3\n  const stringifyData = [\n    data.code || '',\n    data.reqId || '',\n    data.host || '',\n    data.remoteIp || '',\n    data.port || '',\n    data.duration || '',\n    data.time || '',\n    data.bytesSent || '',\n    data.upType || '',\n    data.size || ''\n  ].join(',')\n\n  xhr.send(stringifyData)\n}\n", "import { reportV3, V3LogInfo } from './report-v3'\n\nexport type LogLevel = 'INFO' | 'WARN' | 'ERROR' | 'OFF'\n\nexport default class Logger {\n  private static id = 0\n\n  // 为每个类分配一个 id\n  // 用以区分不同的上传任务\n  private id = ++Logger.id\n\n  constructor(\n    private token: string,\n    private disableReport = true,\n    private level: LogLevel = 'OFF',\n    private prefix = 'UPLOAD'\n  ) { }\n\n  private getPrintPrefix(level: LogLevel) {\n    return `Qiniu-JS-SDK [${level}][${this.prefix}#${this.id}]:`\n  }\n\n  /**\n   * @param  {V3LogInfo} data 上报的数据。\n   * @param  {boolean} retry 重试次数，可选，默认为 3。\n   * @description 向服务端上报统计信息。\n   */\n  report(data: V3LogInfo, retry?: number) {\n    if (this.disableReport) return\n    try {\n      reportV3(this.token, data, retry)\n    } catch (error) {\n      this.warn(error)\n    }\n  }\n\n  /**\n   * @param  {unknown[]} ...args\n   * @description 输出 info 级别的调试信息。\n   */\n  info(...args: unknown[]) {\n    const allowLevel: LogLevel[] = ['INFO']\n    if (allowLevel.includes(this.level)) {\n      // eslint-disable-next-line no-console\n      console.log(this.getPrintPrefix('INFO'), ...args)\n    }\n  }\n\n  /**\n   * @param  {unknown[]} ...args\n   * @description 输出 warn 级别的调试信息。\n   */\n  warn(...args: unknown[]) {\n    const allowLevel: LogLevel[] = ['INFO', 'WARN']\n    if (allowLevel.includes(this.level)) {\n      // eslint-disable-next-line no-console\n      console.warn(this.getPrintPrefix('WARN'), ...args)\n    }\n  }\n\n  /**\n   * @param  {unknown[]} ...args\n   * @description 输出 error 级别的调试信息。\n   */\n  error(...args: unknown[]) {\n    const allowLevel: LogLevel[] = ['INFO', 'WARN', 'ERROR']\n    if (allowLevel.includes(this.level)) {\n      // eslint-disable-next-line no-console\n      console.error(this.getPrintPrefix('ERROR'), ...args)\n    }\n  }\n}\n", "import { getUpHosts } from '../api'\nimport { InternalConfig } from './base'\n\n/**\n  * @description 解冻时间，key 是 host，value 为解冻时间\n  */\nconst unfreezeTimeMap = new Map<string, number>()\n\nexport class Host {\n  constructor(public host: string, public protocol: InternalConfig['upprotocol']) { }\n\n  /**\n   * @description 当前 host 是否为冻结状态\n   */\n  isFrozen() {\n    const currentTime = new Date().getTime()\n    const unfreezeTime = unfreezeTimeMap.get(this.host)\n    return unfreezeTime != null && unfreezeTime >= currentTime\n  }\n\n  /**\n   * @param  {number} time 单位秒，默认 20s\n   * @description 冻结该 host 对象，该 host 将在指定时间内不可用\n   */\n  freeze(time = 20) {\n    const unfreezeTime = new Date().getTime() + (time * 1000)\n    unfreezeTimeMap.set(this.host, unfreezeTime)\n  }\n\n  /**\n   * @description 解冻该 host\n   */\n  unfreeze() {\n    unfreezeTimeMap.delete(this.host)\n  }\n\n  /**\n   * @description 获取当前 host 的完整 url\n   */\n  getUrl() {\n    return `${this.protocol}://${this.host}`\n  }\n\n  /**\n   * @description 获取解冻时间\n   */\n  getUnfreezeTime() {\n    return unfreezeTimeMap.get(this.host)\n  }\n}\nexport class HostPool {\n  /**\n   * @description 缓存的 host 表，以 bucket 和 accessKey 作为 key\n   */\n  private cachedHostsMap = new Map<string, Host[]>()\n\n  /**\n   * @param  {string[]} initHosts\n   * @description 如果在构造时传入 initHosts，则该 host 池始终使用传入的 initHosts 做为可用的数据\n   */\n  constructor(private initHosts: string[] = []) { }\n\n  /**\n   * @param  {string} accessKey\n   * @param  {string} bucketName\n   * @param  {string[]} hosts\n   * @param  {InternalConfig['upprotocol']} protocol\n   * @returns  {void}\n   * @description 注册可用 host\n   */\n  private register(accessKey: string, bucketName: string, hosts: string[], protocol: InternalConfig['upprotocol']): void {\n    this.cachedHostsMap.set(\n      `${accessKey}@${bucketName}`,\n      hosts.map(host => new Host(host, protocol))\n    )\n  }\n\n  /**\n   * @param  {string} accessKey\n   * @param  {string} bucketName\n   * @param  {InternalConfig['upprotocol']} protocol\n   * @returns  {Promise<void>}\n   * @description 刷新最新的 host 数据，如果用户在构造时该类时传入了 host 或者已经存在缓存则不会发起请求\n   */\n  private async refresh(accessKey: string, bucketName: string, protocol: InternalConfig['upprotocol']): Promise<void> {\n    const cachedHostList = this.cachedHostsMap.get(`${accessKey}@${bucketName}`) || []\n    if (cachedHostList.length > 0) return\n\n    if (this.initHosts.length > 0) {\n      this.register(accessKey, bucketName, this.initHosts, protocol)\n      return\n    }\n\n    const response = await getUpHosts(accessKey, bucketName, protocol)\n    if (response?.data != null) {\n      const stashHosts: string[] = [\n        ...(response.data.up?.acc?.main || []),\n        ...(response.data.up?.acc?.backup || [])\n      ]\n      this.register(accessKey, bucketName, stashHosts, protocol)\n    }\n  }\n\n  /**\n   * @param  {string} accessKey\n   * @param  {string} bucketName\n   * @param  {InternalConfig['upprotocol']} protocol\n   * @returns  {Promise<Host | null>}\n   * @description 获取一个可用的上传 Host，排除已冻结的\n   */\n  public async getUp(accessKey: string, bucketName: string, protocol: InternalConfig['upprotocol']): Promise<Host | null> {\n    await this.refresh(accessKey, bucketName, protocol)\n    const cachedHostList = this.cachedHostsMap.get(`${accessKey}@${bucketName}`) || []\n\n    if (cachedHostList.length === 0) return null\n    const availableHostList = cachedHostList.filter(host => !host.isFrozen())\n    if (availableHostList.length > 0) return availableHostList[0]\n\n    // 无可用的，去取离解冻最近的 host\n    const priorityQueue = cachedHostList\n      .slice().sort(\n        (hostA, hostB) => (hostA.getUnfreezeTime() || 0) - (hostB.getUnfreezeTime() || 0)\n      )\n\n    return priorityQueue[0]\n  }\n}\n", "import Resume from './resume'\nimport Direct from './direct'\nimport Logger from '../logger'\nimport { UploadCompleteData } from '../api'\nimport { Observable, IObserver, MB, normalizeUploadConfig } from '../utils'\nimport { QiniuError, Qin<PERSON>NetworkError, QiniuRequestError } from '../errors'\nimport { Extra, UploadOptions, UploadHandlers, UploadProgress, Config } from './base'\nimport { HostPool } from './hosts'\n\nexport * from './base'\nexport * from './resume'\n\nexport function createUploadManager(\n  options: UploadOptions,\n  handlers: UploadHandlers,\n  hostPool: HostPool,\n  logger: Logger\n) {\n  if (options.config && options.config.forceDirect) {\n    logger.info('ues forceDirect mode.')\n    return new Direct(options, handlers, hostPool, logger)\n  }\n\n  if (options.file.size > 4 * MB) {\n    logger.info('file size over 4M, use Resume.')\n    return new Resume(options, handlers, hostPool, logger)\n  }\n\n  logger.info('file size less or equal than 4M, use Direct.')\n  return new Direct(options, handlers, hostPool, logger)\n}\n\n/**\n * @param file 上传文件\n * @param key 目标文件名\n * @param token 上传凭证\n * @param putExtra 上传文件的相关资源信息配置\n * @param config 上传任务的配置\n * @returns 返回用于上传任务的可观察对象\n */\nexport default function upload(\n  file: File,\n  key: string | null | undefined,\n  token: string,\n  putExtra?: Partial<Extra>,\n  config?: Config\n): Observable<UploadProgress, QiniuError | QiniuRequestError | QiniuNetworkError, UploadCompleteData> {\n\n  // 为每个任务创建单独的 Logger\n  const logger = new Logger(token, config?.disableStatisticsReport, config?.debugLogLevel, file.name)\n\n  const options: UploadOptions = {\n    file,\n    key,\n    token,\n    putExtra,\n    config: normalizeUploadConfig(config, logger)\n  }\n\n  // 创建 host 池\n  const hostPool = new HostPool(options.config.uphost)\n\n  return new Observable((observer: IObserver<\n    UploadProgress,\n    QiniuError | QiniuRequestError | QiniuNetworkError,\n    UploadCompleteData\n  >) => {\n    const manager = createUploadManager(options, {\n      onData: (data: UploadProgress) => observer.next(data),\n      onError: (err: QiniuError) => observer.error(err),\n      onComplete: (res: any) => observer.complete(res)\n    }, hostPool, logger)\n    manager.putFile()\n    return manager.stop.bind(manager)\n  })\n}\n", "import Logger from '../logger'\nimport { regionUphostMap } from '../config'\nimport { Config, DEFAULT_CHUNK_SIZE, InternalConfig } from '../upload'\n\nexport function normalizeUploadConfig(config?: Partial<Config>, logger?: Logger): InternalConfig {\n  const { upprotocol, uphost, ...otherConfig } = { ...config }\n\n  const normalizeConfig: InternalConfig = {\n    uphost: [],\n    retryCount: 3,\n\n    checkByMD5: false,\n    forceDirect: false,\n    useCdnDomain: true,\n    checkByServer: false,\n    concurrentRequestLimit: 3,\n    chunkSize: DEFAULT_CHUNK_SIZE,\n\n    upprotocol: 'https',\n\n    debugLogLevel: 'OFF',\n    disableStatisticsReport: false,\n\n    ...otherConfig\n  }\n\n  // 兼容原来的 http: https: 的写法\n  if (upprotocol) {\n    normalizeConfig.upprotocol = upprotocol\n      .replace(/:$/, '') as InternalConfig['upprotocol']\n  }\n\n  const hostList: string[] = []\n\n  if (logger && config?.uphost != null && config?.region != null) {\n    logger.warn('do not use both the uphost and region config.')\n  }\n\n  // 如果同时指定了 uphost 参数，添加到可用 host 列表\n  if (uphost) {\n    if (Array.isArray(uphost)) {\n      hostList.push(...uphost)\n    } else {\n      hostList.push(uphost)\n    }\n\n    // 否则如果用户传了 region，添加指定 region 的 host 到可用 host 列表\n  } else if (normalizeConfig?.region) {\n    const hostMap = regionUphostMap[normalizeConfig?.region]\n    if (normalizeConfig.useCdnDomain) {\n      hostList.push(...hostMap.cdnUphost)\n    } else {\n      hostList.push(...hostMap.srcUphost)\n    }\n  }\n\n  return {\n    ...normalizeConfig,\n    uphost: hostList.filter(Boolean)\n  }\n}\n", "import { QiniuErrorName, QiniuError } from '../errors'\n\nimport { createObjectURL } from './helper'\n\nexport interface CompressOptions {\n  quality?: number\n  noCompressIfLarger?: boolean\n  maxWidth?: number\n  maxHeight?: number\n}\n\nexport interface Dimension {\n  width?: number\n  height?: number\n}\n\nexport interface CompressResult {\n  dist: Blob | File\n  width: number\n  height: number\n}\n\nconst mimeTypes = {\n  PNG: 'image/png',\n  JPEG: 'image/jpeg',\n  WEBP: 'image/webp',\n  BMP: 'image/bmp'\n} as const\n\nconst maxSteps = 4\nconst scaleFactor = Math.log(2)\nconst supportMimeTypes = Object.keys(mimeTypes).map(type => mimeTypes[type])\nconst defaultType = mimeTypes.JPEG\n\ntype MimeKey = keyof typeof mimeTypes\n\nfunction isSupportedType(type: string): type is typeof mimeTypes[MimeKey] {\n  return supportMimeTypes.includes(type)\n}\n\nclass Compress {\n  private outputType: string\n\n  constructor(private file: File, private config: CompressOptions) {\n    this.config = {\n      quality: 0.92,\n      noCompressIfLarger: false,\n      ...this.config\n    }\n  }\n\n  async process(): Promise<CompressResult> {\n    this.outputType = this.file.type\n    const srcDimension: Dimension = {}\n    if (!isSupportedType(this.file.type)) {\n      throw new QiniuError(\n        QiniuErrorName.UnsupportedFileType,\n        `unsupported file type: ${this.file.type}`\n      )\n    }\n\n    const originImage = await this.getOriginImage()\n    const canvas = await this.getCanvas(originImage)\n    let scale = 1\n    if (this.config.maxWidth) {\n      scale = Math.min(1, this.config.maxWidth / canvas.width)\n    }\n    if (this.config.maxHeight) {\n      scale = Math.min(1, scale, this.config.maxHeight / canvas.height)\n    }\n    srcDimension.width = canvas.width\n    srcDimension.height = canvas.height\n\n    const scaleCanvas = await this.doScale(canvas, scale)\n    const distBlob = this.toBlob(scaleCanvas)\n    if (distBlob.size > this.file.size && this.config.noCompressIfLarger) {\n      return {\n        dist: this.file,\n        width: srcDimension.width,\n        height: srcDimension.height\n      }\n    }\n\n    return {\n      dist: distBlob,\n      width: scaleCanvas.width,\n      height: scaleCanvas.height\n    }\n  }\n\n  clear(ctx: CanvasRenderingContext2D, width: number, height: number) {\n    // jpeg 没有 alpha 通道，透明区间会被填充成黑色，这里把透明区间填充为白色\n    if (this.outputType === defaultType) {\n      ctx.fillStyle = '#fff'\n      ctx.fillRect(0, 0, width, height)\n    } else {\n      ctx.clearRect(0, 0, width, height)\n    }\n  }\n\n  /** 通过 file 初始化 image 对象 */\n  getOriginImage(): Promise<HTMLImageElement> {\n    return new Promise((resolve, reject) => {\n      const url = createObjectURL(this.file)\n      const img = new Image()\n      img.onload = () => {\n        resolve(img)\n      }\n      img.onerror = () => {\n        reject('image load error')\n      }\n      img.src = url\n    })\n  }\n\n  getCanvas(img: HTMLImageElement): Promise<HTMLCanvasElement> {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas')\n      const context = canvas.getContext('2d')\n\n      if (!context) {\n        reject(new QiniuError(\n          QiniuErrorName.GetCanvasContextFailed,\n          'context is null'\n        ))\n        return\n      }\n\n      const { width, height } = img\n      canvas.height = height\n      canvas.width = width\n\n      this.clear(context, width, height)\n      context.drawImage(img, 0, 0)\n      resolve(canvas)\n    })\n  }\n\n  async doScale(source: HTMLCanvasElement, scale: number) {\n    if (scale === 1) {\n      return source\n    }\n    // 不要一次性画图，通过设定的 step 次数，渐进式的画图，这样可以增加图片的清晰度，防止一次性画图导致的像素丢失严重\n    const sctx = source.getContext('2d')\n    const steps = Math.min(maxSteps, Math.ceil((1 / scale) / scaleFactor))\n\n    const factor = scale ** (1 / steps)\n\n    const mirror = document.createElement('canvas')\n    const mctx = mirror.getContext('2d')\n\n    let { width, height } = source\n    const originWidth = width\n    const originHeight = height\n    mirror.width = width\n    mirror.height = height\n    if (!mctx || !sctx) {\n      throw new QiniuError(\n        QiniuErrorName.GetCanvasContextFailed,\n        \"mctx or sctx can't be null\"\n      )\n    }\n\n    let src!: CanvasImageSource\n    let context!: CanvasRenderingContext2D\n    for (let i = 0; i < steps; i++) {\n\n      let dw = width * factor | 0 // eslint-disable-line no-bitwise\n      let dh = height * factor | 0 // eslint-disable-line no-bitwise\n      // 到最后一步的时候 dw, dh 用目标缩放尺寸，否则会出现最后尺寸偏小的情况\n      if (i === steps - 1) {\n        dw = originWidth * scale\n        dh = originHeight * scale\n      }\n\n      if (i % 2 === 0) {\n        src = source\n        context = mctx\n      } else {\n        src = mirror\n        context = sctx\n      }\n      // 每次画前都清空，避免图像重叠\n      this.clear(context, width, height)\n      context.drawImage(src, 0, 0, width, height, 0, 0, dw, dh)\n      width = dw\n      height = dh\n    }\n\n    const canvas = src === source ? mirror : source\n    // save data\n    const data = context.getImageData(0, 0, width, height)\n\n    // resize\n    canvas.width = width\n    canvas.height = height\n\n    // store image data\n    context.putImageData(data, 0, 0)\n\n    return canvas\n  }\n\n  /** 这里把 base64 字符串转为 blob 对象 */\n  toBlob(result: HTMLCanvasElement) {\n    const dataURL = result.toDataURL(this.outputType, this.config.quality)\n    const buffer = atob(dataURL.split(',')[1]).split('').map(char => char.charCodeAt(0))\n    const blob = new Blob([new Uint8Array(buffer)], { type: this.outputType })\n    return blob\n  }\n}\n\nconst compressImage = (file: File, options: CompressOptions) => new Compress(file, options).process()\n\nexport default compressImage\n", null], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAU,SAAS;AAChB,UAAI,OAAO,YAAY,UAAU;AAE7B,eAAO,UAAU,QAAQ;AAAA,MAC7B,WAAW,OAAO,WAAW,cAAc,OAAO,KAAK;AAEnD,eAAO,OAAO;AAAA,MAClB,OAAO;AAEH,YAAI;AAEJ,YAAI;AACA,iBAAO;AAAA,QACX,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AAEA,aAAK,WAAW,QAAQ;AAAA,MAC5B;AAAA,IACJ,GAAE,SAAUA,YAAW;AAEnB;AAeA,UAAI,QAAQ,SAAU,GAAG,GAAG;AACxB,eAAQ,IAAI,IAAK;AAAA,MACrB,GACI,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAG7F,eAAS,IAAIC,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC3B,YAAI,MAAM,MAAM,GAAGA,EAAC,GAAG,MAAM,GAAG,CAAC,CAAC;AAClC,eAAO,MAAO,KAAK,IAAM,MAAO,KAAK,GAAK,CAAC;AAAA,MAC/C;AAEA,eAAS,SAAS,GAAG,GAAG;AACpB,YAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AAEX,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,WAAW;AAC1C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,QAAQ;AACxC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,WAAW;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAEhC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,YAAY;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,WAAW;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,YAAY;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,YAAY;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,WAAW;AAC1C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,aAAa;AAC5C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,aAAa;AAC7C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAEhC,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,SAAS;AACnC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AACxC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,WAAW;AACtC,aAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,aAAa;AACvC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,aAAa;AACxC,aAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,WAAW;AACrC,aAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY;AACvC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,YAAY;AACtC,aAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AAE/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW;AACxC,aAAM,KAAK,KAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,UAAU;AACxC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,aAAM,KAAK,KAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,WAAW;AACzC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa;AAC1C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,aAAM,KAAK,KAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,aAAM,KAAK,IAAI,MAAM,MAAM,IAAI;AAC/B,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,aAAa;AAC3C,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAChC,cAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY;AACzC,aAAM,KAAK,KAAK,MAAM,MAAM,IAAI;AAEhC,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAClB,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,MACtB;AAEA,eAAS,OAAO,GAAG;AACf,YAAI,UAAU,CAAC,GACX;AAEJ,aAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AACxB,kBAAQ,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC,KAAK,MAAM,EAAE,WAAW,IAAI,CAAC,KAAK,OAAO,EAAE,WAAW,IAAI,CAAC,KAAK;AAAA,QAC3H;AACA,eAAO;AAAA,MACX;AAEA,eAAS,aAAa,GAAG;AACrB,YAAI,UAAU,CAAC,GACX;AAEJ,aAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AACxB,kBAAQ,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,MAAM,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE,IAAI,CAAC,KAAK;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AAEA,eAAS,KAAK,GAAG;AACb,YAAI,IAAI,EAAE,QACN,QAAQ,CAAC,YAAY,YAAY,aAAa,SAAS,GACvD,GACA,QACA,MACA,KACA,IACA;AAEJ,aAAK,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI;AAC1B,mBAAS,OAAO,OAAO,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC;AAAA,QAClD;AACA,YAAI,EAAE,UAAU,IAAI,EAAE;AACtB,iBAAS,EAAE;AACX,eAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACtD,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,eAAK,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,MAAO,IAAI,KAAM;AAAA,QACnD;AACA,aAAK,KAAK,CAAC,KAAK,QAAU,IAAI,KAAM;AACpC,YAAI,IAAI,IAAI;AACR,mBAAS,OAAO,IAAI;AACpB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AACxB,iBAAK,CAAC,IAAI;AAAA,UACd;AAAA,QACJ;AAGA,cAAM,IAAI;AACV,cAAM,IAAI,SAAS,EAAE,EAAE,MAAM,gBAAgB;AAC7C,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE;AACxB,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK;AAE7B,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AAEX,iBAAS,OAAO,IAAI;AACpB,eAAO;AAAA,MACX;AAEA,eAAS,WAAW,GAAG;AACnB,YAAI,IAAI,EAAE,QACN,QAAQ,CAAC,YAAY,YAAY,aAAa,SAAS,GACvD,GACA,QACA,MACA,KACA,IACA;AAEJ,aAAK,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI;AAC1B,mBAAS,OAAO,aAAa,EAAE,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;AAAA,QACvD;AAMA,YAAK,IAAI,KAAM,IAAI,EAAE,SAAS,IAAI,EAAE,IAAI,IAAI,WAAW,CAAC;AAExD,iBAAS,EAAE;AACX,eAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACtD,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,eAAK,KAAK,CAAC,KAAK,EAAE,CAAC,MAAO,IAAI,KAAM;AAAA,QACxC;AAEA,aAAK,KAAK,CAAC,KAAK,QAAU,IAAI,KAAM;AACpC,YAAI,IAAI,IAAI;AACR,mBAAS,OAAO,IAAI;AACpB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AACxB,iBAAK,CAAC,IAAI;AAAA,UACd;AAAA,QACJ;AAGA,cAAM,IAAI;AACV,cAAM,IAAI,SAAS,EAAE,EAAE,MAAM,gBAAgB;AAC7C,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE;AACxB,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK;AAE7B,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AAEX,iBAAS,OAAO,IAAI;AAEpB,eAAO;AAAA,MACX;AAEA,eAAS,KAAK,GAAG;AACb,YAAI,IAAI,IACJ;AACJ,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACvB,eAAK,QAAS,KAAM,IAAI,IAAI,IAAM,EAAI,IAAI,QAAS,KAAM,IAAI,IAAM,EAAI;AAAA,QAC3E;AACA,eAAO;AAAA,MACX;AAEA,eAAS,IAAI,GAAG;AACZ,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AAC9B,YAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;AAAA,QACpB;AACA,eAAO,EAAE,KAAK,EAAE;AAAA,MACpB;AAGA,UAAI,IAAI,KAAK,OAAO,CAAC,MAAM,oCAAoC;AAC3D,gBAAQ,SAAU,GAAG,GAAG;AACpB,cAAI,OAAO,IAAI,UAAW,IAAI,QAC1B,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC1C,iBAAQ,OAAO,KAAO,MAAM;AAAA,QAChC;AAAA,MACJ;AAUA,UAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,UAAU,OAAO;AACpE,SAAC,WAAY;AACT,mBAAS,MAAM,KAAK,QAAQ;AACxB,kBAAO,MAAM,KAAM;AAEnB,gBAAI,MAAM,GAAG;AACT,qBAAO,KAAK,IAAI,MAAM,QAAQ,CAAC;AAAA,YACnC;AAEA,mBAAO,KAAK,IAAI,KAAK,MAAM;AAAA,UAC/B;AAEA,sBAAY,UAAU,QAAQ,SAAU,MAAM,IAAI;AAC9C,gBAAI,SAAS,KAAK,YACd,QAAQ,MAAM,MAAM,MAAM,GAC1B,MAAM,QACN,KACA,QACA,aACA;AAEJ,gBAAI,OAAOD,YAAW;AAClB,oBAAM,MAAM,IAAI,MAAM;AAAA,YAC1B;AAEA,gBAAI,QAAQ,KAAK;AACb,qBAAO,IAAI,YAAY,CAAC;AAAA,YAC5B;AAEA,kBAAM,MAAM;AACZ,qBAAS,IAAI,YAAY,GAAG;AAC5B,0BAAc,IAAI,WAAW,MAAM;AAEnC,0BAAc,IAAI,WAAW,MAAM,OAAO,GAAG;AAC7C,wBAAY,IAAI,WAAW;AAE3B,mBAAO;AAAA,UACX;AAAA,QACJ,GAAG;AAAA,MACP;AAQA,eAAS,OAAO,KAAK;AACjB,YAAI,kBAAkB,KAAK,GAAG,GAAG;AAC7B,gBAAM,SAAS,mBAAmB,GAAG,CAAC;AAAA,QAC1C;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,oBAAoB,KAAK,kBAAkB;AAChD,YAAI,SAAS,IAAI,QACd,OAAO,IAAI,YAAY,MAAM,GAC7B,MAAM,IAAI,WAAW,IAAI,GACzB;AAEH,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,cAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,QAC7B;AAEA,eAAO,mBAAmB,MAAM;AAAA,MACpC;AAEA,eAAS,oBAAoB,MAAM;AAC/B,eAAO,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,IAAI,CAAC;AAAA,MAC/D;AAEA,eAAS,wBAAwB,OAAO,QAAQ,kBAAkB;AAC9D,YAAI,SAAS,IAAI,WAAW,MAAM,aAAa,OAAO,UAAU;AAEhE,eAAO,IAAI,IAAI,WAAW,KAAK,CAAC;AAChC,eAAO,IAAI,IAAI,WAAW,MAAM,GAAG,MAAM,UAAU;AAEnD,eAAO,mBAAmB,SAAS,OAAO;AAAA,MAC9C;AAEA,eAAS,kBAAkBE,MAAK;AAC5B,YAAI,QAAQ,CAAC,GACT,SAASA,KAAI,QACb;AAEJ,aAAK,IAAI,GAAG,IAAI,SAAS,GAAG,KAAK,GAAG;AAChC,gBAAM,KAAK,SAASA,KAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AAAA,QAC7C;AAEA,eAAO,OAAO,aAAa,MAAM,QAAQ,KAAK;AAAA,MAClD;AAWA,eAASC,YAAW;AAEhB,aAAK,MAAM;AAAA,MACf;AAUA,MAAAA,UAAS,UAAU,SAAS,SAAU,KAAK;AAGvC,aAAK,aAAa,OAAO,GAAG,CAAC;AAE7B,eAAO;AAAA,MACX;AASA,MAAAA,UAAS,UAAU,eAAe,SAAU,UAAU;AAClD,aAAK,SAAS;AACd,aAAK,WAAW,SAAS;AAEzB,YAAI,SAAS,KAAK,MAAM,QACpB;AAEJ,aAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC/B,mBAAS,KAAK,OAAO,OAAO,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC;AAAA,QAChE;AAEA,aAAK,QAAQ,KAAK,MAAM,UAAU,IAAI,EAAE;AAExC,eAAO;AAAA,MACX;AAUA,MAAAA,UAAS,UAAU,MAAM,SAAU,KAAK;AACpC,YAAI,OAAO,KAAK,OACZ,SAAS,KAAK,QACd,GACA,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GACtD;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,eAAK,KAAK,CAAC,KAAK,KAAK,WAAW,CAAC,MAAO,IAAI,KAAM;AAAA,QACtD;AAEA,aAAK,QAAQ,MAAM,MAAM;AACzB,cAAM,IAAI,KAAK,KAAK;AAEpB,YAAI,KAAK;AACL,gBAAM,kBAAkB,GAAG;AAAA,QAC/B;AAEA,aAAK,MAAM;AAEX,eAAO;AAAA,MACX;AAOA,MAAAA,UAAS,UAAU,QAAQ,WAAY;AACnC,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,aAAK,QAAQ,CAAC,YAAY,YAAY,aAAa,SAAS;AAE5D,eAAO;AAAA,MACX;AAOA,MAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,eAAO;AAAA,UACH,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK,MAAM,MAAM;AAAA,QAC3B;AAAA,MACJ;AASA,MAAAA,UAAS,UAAU,WAAW,SAAU,OAAO;AAC3C,aAAK,QAAQ,MAAM;AACnB,aAAK,UAAU,MAAM;AACrB,aAAK,QAAQ,MAAM;AAEnB,eAAO;AAAA,MACX;AAMA,MAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,eAAO,KAAK;AACZ,eAAO,KAAK;AACZ,eAAO,KAAK;AAAA,MAChB;AAQA,MAAAA,UAAS,UAAU,UAAU,SAAU,MAAM,QAAQ;AACjD,YAAI,IAAI,QACJ,KACA,IACA;AAEJ,aAAK,KAAK,CAAC,KAAK,QAAU,IAAI,KAAM;AACpC,YAAI,IAAI,IAAI;AACR,mBAAS,KAAK,OAAO,IAAI;AACzB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AACxB,iBAAK,CAAC,IAAI;AAAA,UACd;AAAA,QACJ;AAIA,cAAM,KAAK,UAAU;AACrB,cAAM,IAAI,SAAS,EAAE,EAAE,MAAM,gBAAgB;AAC7C,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE;AACxB,aAAK,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK;AAE7B,aAAK,EAAE,IAAI;AACX,aAAK,EAAE,IAAI;AACX,iBAAS,KAAK,OAAO,IAAI;AAAA,MAC7B;AAWA,MAAAA,UAAS,OAAO,SAAU,KAAK,KAAK;AAGhC,eAAOA,UAAS,WAAW,OAAO,GAAG,GAAG,GAAG;AAAA,MAC/C;AAUA,MAAAA,UAAS,aAAa,SAAU,SAAS,KAAK;AAC1C,YAAI,OAAO,KAAK,OAAO,GACnB,MAAM,IAAI,IAAI;AAElB,eAAO,MAAM,kBAAkB,GAAG,IAAI;AAAA,MAC1C;AASA,MAAAA,UAAS,cAAc,WAAY;AAE/B,aAAK,MAAM;AAAA,MACf;AASA,MAAAA,UAAS,YAAY,UAAU,SAAS,SAAU,KAAK;AACnD,YAAI,OAAO,wBAAwB,KAAK,MAAM,QAAQ,KAAK,IAAI,GAC3D,SAAS,KAAK,QACd;AAEJ,aAAK,WAAW,IAAI;AAEpB,aAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC/B,mBAAS,KAAK,OAAO,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;AAAA,QAC/D;AAEA,aAAK,QAAS,IAAI,KAAM,SAAS,IAAI,WAAW,KAAK,OAAO,MAAM,IAAI,EAAE,CAAC,IAAI,IAAI,WAAW,CAAC;AAE7F,eAAO;AAAA,MACX;AAUA,MAAAA,UAAS,YAAY,UAAU,MAAM,SAAU,KAAK;AAChD,YAAI,OAAO,KAAK,OACZ,SAAS,KAAK,QACd,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GACtD,GACA;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,eAAK,KAAK,CAAC,KAAK,KAAK,CAAC,MAAO,IAAI,KAAM;AAAA,QAC3C;AAEA,aAAK,QAAQ,MAAM,MAAM;AACzB,cAAM,IAAI,KAAK,KAAK;AAEpB,YAAI,KAAK;AACL,gBAAM,kBAAkB,GAAG;AAAA,QAC/B;AAEA,aAAK,MAAM;AAEX,eAAO;AAAA,MACX;AAOA,MAAAA,UAAS,YAAY,UAAU,QAAQ,WAAY;AAC/C,aAAK,QAAQ,IAAI,WAAW,CAAC;AAC7B,aAAK,UAAU;AACf,aAAK,QAAQ,CAAC,YAAY,YAAY,aAAa,SAAS;AAE5D,eAAO;AAAA,MACX;AAOA,MAAAA,UAAS,YAAY,UAAU,WAAW,WAAY;AAClD,YAAI,QAAQA,UAAS,UAAU,SAAS,KAAK,IAAI;AAGjD,cAAM,OAAO,oBAAoB,MAAM,IAAI;AAE3C,eAAO;AAAA,MACX;AASA,MAAAA,UAAS,YAAY,UAAU,WAAW,SAAU,OAAO;AAEvD,cAAM,OAAO,oBAAoB,MAAM,MAAM,IAAI;AAEjD,eAAOA,UAAS,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,MACvD;AAEA,MAAAA,UAAS,YAAY,UAAU,UAAUA,UAAS,UAAU;AAE5D,MAAAA,UAAS,YAAY,UAAU,UAAUA,UAAS,UAAU;AAU5D,MAAAA,UAAS,YAAY,OAAO,SAAU,KAAK,KAAK;AAC5C,YAAI,OAAO,WAAW,IAAI,WAAW,GAAG,CAAC,GACrC,MAAM,IAAI,IAAI;AAElB,eAAO,MAAM,kBAAkB,GAAG,IAAI;AAAA,MAC1C;AAEA,aAAOA;AAAA,IACX,CAAC;AAAA;AAAA;;;AC9uBD;AAAA;AAAA;AA0BA,aAAS,eAAe,KAAK,MAAM;AACjC,aAAO,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI;AAAA,IACvD;AAEA,WAAO,UAAU,SAAS,IAAI,KAAK,IAAI,SAAS;AAC9C,YAAM,OAAO;AACb,WAAK,MAAM;AACX,UAAI,MAAM,CAAC;AAEX,UAAI,OAAO,OAAO,YAAY,GAAG,WAAW,GAAG;AAC7C,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AACb,WAAK,GAAG,MAAM,GAAG;AAEjB,UAAI,UAAU;AACd,UAAI,WAAW,OAAO,QAAQ,YAAY,UAAU;AAClD,kBAAU,QAAQ;AAAA,MACpB;AAEA,UAAI,MAAM,GAAG;AAEb,UAAI,UAAU,KAAK,MAAM,SAAS;AAChC,cAAM;AAAA,MACR;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAI,IAAI,GAAG,CAAC,EAAE,QAAQ,QAAQ,KAAK,GAC/B,MAAM,EAAE,QAAQ,EAAE,GAClB,MAAM,MAAM,GAAG;AAEnB,YAAI,OAAO,GAAG;AACZ,iBAAO,EAAE,OAAO,GAAG,GAAG;AACtB,iBAAO,EAAE,OAAO,MAAM,CAAC;AAAA,QACzB,OAAO;AACL,iBAAO;AACP,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,IAAI;AAC3B,YAAI,mBAAmB,IAAI;AAE3B,YAAI,CAAC,eAAe,KAAK,CAAC,GAAG;AAC3B,cAAI,CAAC,IAAI;AAAA,QACX,WAAW,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AAChC,cAAI,CAAC,EAAE,KAAK,CAAC;AAAA,QACf,OAAO;AACL,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AAAA,QACrB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/EA;AAAA;AAAA;AAuBA,QAAI,qBAAqB,SAAS,GAAG;AACnC,cAAQ,OAAO,GAAG;AAAA,QAChB,KAAK;AACH,iBAAO;AAAA,QAET,KAAK;AACH,iBAAO,IAAI,SAAS;AAAA,QAEtB,KAAK;AACH,iBAAO,SAAS,CAAC,IAAI,IAAI;AAAA,QAE3B;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,KAAK,KAAK,IAAI,MAAM;AAC5C,YAAM,OAAO;AACb,WAAK,MAAM;AACX,UAAI,QAAQ,MAAM;AAChB,cAAM;AAAA,MACR;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,OAAO,KAAK,GAAG,EAAE,IAAI,SAAS,GAAG;AACtC,cAAI,KAAK,mBAAmB,mBAAmB,CAAC,CAAC,IAAI;AACrD,cAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AACzB,mBAAO,IAAI,CAAC,EAAE,IAAI,SAAS,GAAG;AAC5B,qBAAO,KAAK,mBAAmB,mBAAmB,CAAC,CAAC;AAAA,YACtD,CAAC,EAAE,KAAK,GAAG;AAAA,UACb,OAAO;AACL,mBAAO,KAAK,mBAAmB,mBAAmB,IAAI,CAAC,CAAC,CAAC;AAAA,UAC3D;AAAA,QACF,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,MAE7B;AAEA,UAAI,CAAC;AAAM,eAAO;AAClB,aAAO,mBAAmB,mBAAmB,IAAI,CAAC,IAAI,KAC/C,mBAAmB,mBAAmB,GAAG,CAAC;AAAA,IACnD;AAAA;AAAA;;;AC/DA;AAAA;AAAA;AAEA,YAAQ,SAAS,QAAQ,QAAQ;AACjC,YAAQ,SAAS,QAAQ,YAAY;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACHrC,IAAY;CAAZ,SAAYC,iBAAc;AAExB,EAAAA,gBAAA,aAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACA,EAAAA,gBAAA,iBAAA,IAAA;AACA,EAAAA,gBAAA,kBAAA,IAAA;AACA,EAAAA,gBAAA,mBAAA,IAAA;AACA,EAAAA,gBAAA,wBAAA,IAAA;AAGA,EAAAA,gBAAA,iBAAA,IAAA;AACA,EAAAA,gBAAA,kBAAA,IAAA;AACA,EAAAA,gBAAA,kBAAA,IAAA;AACA,EAAAA,gBAAA,mBAAA,IAAA;AAGA,EAAAA,gBAAA,wBAAA,IAAA;AACA,EAAAA,gBAAA,qBAAA,IAAA;AAGA,EAAAA,gBAAA,sBAAA,IAAA;AACA,EAAAA,gBAAA,4BAAA,IAAA;AACA,EAAAA,gBAAA,4BAAA,IAAA;AAGA,EAAAA,gBAAA,cAAA,IAAA;AACF,GA1BY,mBAAA,iBAAc,CAAA,EAAA;AA4B1B,IAAA;;EAAA,WAAA;AAEE,aAAAC,YAAmB,MAA6B,SAAe;AAA5C,WAAA,OAAA;AAA6B,WAAA,UAAA;AAC9C,WAAK,QAAQ,IAAI,MAAK,EAAG;IAC3B;AACF,WAAAA;EAAA,EALA;;AAOA,IAAA;;EAAA,SAAA,QAAA;AAAuC,cAAAC,oBAAA,MAAA;AAarC,aAAAA,mBAAmB,MAAqB,OAAe,SAAiB,MAAU;AAAlF,UAAA,QACE,OAAA,KAAA,MAAM,eAAe,cAAc,OAAO,KAAC;AAD1B,YAAA,OAAA;AAAqB,YAAA,QAAA;AAPjC,YAAA,iBAAiB;AAStB,YAAK,OAAO;;IACd;AACF,WAAAA;EAAA,EAjBuC,UAAU;;AAsBjD,IAAA;;EAAA,SAAA,QAAA;AAAuC,cAAAC,oBAAA,MAAA;AACrC,aAAAA,mBAAY,SAAiB,OAAU;AAAV,UAAA,UAAA,QAAA;AAAA,gBAAA;MAAU;aACrC,OAAA,KAAA,MAAM,GAAG,OAAO,OAAO,KAAC;IAC1B;AACF,WAAAA;EAAA,EAJuC,iBAAiB;;;;ACjDxD,IAAA;;EAAA,WAAA;AAKE,aAAAC,MAAoB,SAA6B,OAAa;AAA1C,WAAA,UAAA;AAA6B,WAAA,QAAA;AAJjD,WAAA,UAAU;AACV,WAAA,QAAgC,CAAA;AAChC,WAAA,aAAqC,CAAA;IAE4B;AAEjE,IAAAA,MAAA,UAAA,UAAA,SAAQ,MAAO;AAAf,UAAA,QAAA;AACE,aAAO,IAAI,QAAc,SAAC,SAAS,QAAM;AACvC,cAAK,MAAM,KAAK;UACd;UACA;UACA;SACD;AACD,cAAK,MAAK;MACZ,CAAC;IACH;AAEQ,IAAAA,MAAA,UAAA,MAAR,SAAY,MAAqB;AAAjC,UAAA,QAAA;AACE,WAAK,QAAQ,KAAK,MAAM,OAAO,SAAA,GAAC;AAAI,eAAA,MAAM;MAAN,CAAU;AAC9C,WAAK,WAAW,KAAK,IAAI;AACzB,WAAK,QAAQ,KAAK,IAAI,EAAE,KACtB,WAAA;AACE,cAAK,aAAa,MAAK,WAAW,OAAO,SAAA,GAAC;AAAI,iBAAA,MAAM;QAAN,CAAU;AACxD,aAAK,QAAO;AACZ,cAAK,MAAK;MACZ,GACA,SAAA,KAAG;AAAI,eAAA,KAAK,OAAO,GAAG;MAAf,CAAgB;IAE3B;AAEQ,IAAAA,MAAA,UAAA,QAAR,WAAA;AAAA,UAAA,QAAA;AACE,UAAI,KAAK;AAAS;AAClB,UAAM,gBAAgB,KAAK,WAAW;AACtC,UAAM,eAAe,KAAK,QAAQ;AAClC,WAAK,MAAM,MAAM,GAAG,YAAY,EAAE,QAAQ,SAAA,MAAI;AAC5C,cAAK,IAAI,IAAI;MACf,CAAC;IACH;AAEA,IAAAA,MAAA,UAAA,QAAA,WAAA;AACE,WAAK,QAAQ,CAAA;AACb,WAAK,UAAU;IACjB;AACF,WAAAA;EAAA,EA5CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2CA,IAAA;;EAAA,WAAA;AAAA,aAAAC,gBAAA;AAES,WAAA,SAAS;IAqBlB;AAfE,IAAAA,cAAA,UAAA,cAAA,WAAA;AACE,UAAI,KAAK,QAAQ;AACf;;AAGF,WAAK,SAAS;AACd,UAAI,KAAK,cAAc;AACrB,aAAK,aAAY;;IAErB;AAGA,IAAAA,cAAA,UAAA,MAAA,SAAI,UAAuB;AACzB,WAAK,eAAe;IACtB;AACF,WAAAA;EAAA,EAvBA;;AA6BA,IAAA;;EAAA,SAAA,QAAA;AAAyC,IAAAC,WAAAC,aAAA,MAAA;AAIvC,aAAAA,YACE,gBACA,OACA,UAAoC;AAHtC,UAAA,QAKE,OAAA,KAAA,IAAA,KAAO;AARC,YAAA,YAAY;AAUpB,UAAI,kBAAkB,OAAO,mBAAmB,UAAU;AACxD,cAAK,cAAc;aACd;AACL,cAAK,cAAW,SAAA,SAAA,SAAA,CAAA,GACX,kBAAkB,EAAE,MAAM,eAAc,CAAE,GAC1C,SAAS,EAAE,MAAK,CAAE,GAClB,YAAY,EAAE,SAAQ,CAAE;;;IAGjC;AAEA,IAAAA,YAAA,UAAA,cAAA,WAAA;AACE,UAAI,KAAK,QAAQ;AACf;;AAGF,WAAK,YAAY;AACjB,aAAA,UAAM,YAAW,KAAA,IAAA;IACnB;AAEA,IAAAA,YAAA,UAAA,OAAA,SAAK,OAAQ;AACX,UAAI,CAAC,KAAK,aAAa,KAAK,YAAY,MAAM;AAC5C,aAAK,YAAY,KAAK,KAAK;;IAE/B;AAEA,IAAAA,YAAA,UAAA,QAAA,SAAM,KAAM;AACV,UAAI,CAAC,KAAK,aAAa,KAAK,YAAY,OAAO;AAC7C,aAAK,YAAY;AACjB,aAAK,YAAY,MAAM,GAAG;;IAE9B;AAEA,IAAAA,YAAA,UAAA,WAAA,SAAS,QAAS;AAChB,UAAI,CAAC,KAAK,aAAa,KAAK,YAAY,UAAU;AAChD,aAAK,YAAY;AACjB,aAAK,YAAY,SAAS,MAAM;;IAEpC;AACF,WAAAA;EAAA,EAlDyC,YAAY;;AAqDrD,IAAA;;EAAA,WAAA;AAEE,aAAAC,YAAoB,YAA8D;AAA9D,WAAA,aAAA;IAAiE;AAMrF,IAAAA,YAAA,UAAA,YAAA,SACE,gBACA,OACA,UAAoC;AAEpC,UAAM,OAAO,IAAI,WAAW,gBAAgB,OAAO,QAAQ;AAC3D,WAAK,IAAI,KAAK,WAAW,IAAI,CAAC;AAC9B,aAAO;IACT;AACF,WAAAA;EAAA,EAjBA;;;;AClIA,SAAS,WAAW,WAAiB;AAgBnC,MAAI,cAAc,QAAQ,OAAO,cAAc,aAAa;AAC1D,WAAO;;AAGT,MAAI,SAAS,YAAY;AACzB,MAAI,UAAU,IACZ,OACA,KACA,UAAU;AAEZ,UAAQ,MAAM;AACd,YAAU,OAAO;AACjB,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,QAAI,KAAK,OAAO,WAAW,CAAC;AAC5B,QAAI,MAAM;AAEV,QAAI,KAAK,KAAK;AACZ;eACS,KAAK,OAAO,KAAK,MAAM;AAChC,YAAM,OAAO,aAAc,MAAM,IAAK,KAAM,KAAK,KAAM,GAAG;gBAChD,KAAK,QAAS,SAAU,GAAG;AACrC,YAAM,OAAO,aACV,MAAM,KAAM,KACX,MAAM,IAAK,KAAM,KAClB,KAAK,KAAM,GAAG;WAEZ;AAEL,WAAK,KAAK,QAAS,SAAU,GAAG;AAC9B,cAAM,IAAI,WAAW,kCAAkC,CAAC;;AAE1D,UAAI,KAAK,OAAO,WAAW,EAAE,CAAC;AAC9B,WAAK,KAAK,QAAS,SAAU,GAAG;AAC9B,cAAM,IAAI,WAAW,kCAAkC,IAAI,EAAE;;AAE/D,aAAO,KAAK,SAAU,OAAO,KAAK,QAAS;AAC3C,YAAM,OAAO,aACV,MAAM,KAAM,KACX,MAAM,KAAM,KAAM,KAClB,MAAM,IAAK,KAAM,KAClB,KAAK,KAAM,GAAG;;AAGnB,QAAI,QAAQ,MAAM;AAChB,UAAI,MAAM,OAAO;AACf,mBAAW,OAAO,MAAM,OAAO,GAAG;;AAEpC,iBAAW;AACX,cAAQ,MAAM,IAAI;;;AAItB,MAAI,MAAM,OAAO;AACf,eAAW,OAAO,MAAM,OAAO,OAAO;;AAGxC,SAAO;AACT;AAGA,SAAS,WAAW,SAAe;AAgBjC,MAAM,SAAS,CAAA;AACf,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AAEb,aAAW;AAEX,SAAO,IAAI,QAAQ,QAAQ;AACzB,SAAK,QAAQ,WAAW,CAAC,IAAI;AAC7B,aAAS;AAGT,QAAI,MAAM,KAAM;AACd,WAAM,KAAK;AACX,eAAS;eACA,MAAM,KAAM;AACrB,WAAM,KAAK;AACX,eAAS;eACA,MAAM,KAAM;AACrB,WAAM,KAAK;AACX,eAAS;WACJ;AACL,WAAM,KAAK;AACX,eAAS;;AAGX,aAAS,KAAK,GAAG,KAAK,QAAQ,EAAE,IAAI;AAClC,WAAO,MAAM,IAAS,QAAQ,WAAW,KAAK,CAAC,IAAI;;AAGrD,QAAI,WAAW,GAAG;AAChB,YAAM;AACN,aAAO,KAAK,OAAO,aAAa,QAAW,MAAM,KAAM,IAAM,CAAC;AAC9D,aAAO,KAAK,OAAO,aAAa,QAAU,KAAK,IAAM,CAAC;WACjD;AACL,aAAO,KAAK,OAAO,aAAa,EAAE,CAAC;;AAGrC,SAAK;;AAGP,SAAO,OAAO,KAAK,EAAE;AACvB;AAEA,SAAS,aAAa,MAAS;AAgB7B,MAAI,MAAM;AACV,MAAI,IACF,IACA,IACA,IACA,IACA,IACA,IACA,MACA,IAAI,GACJ,KAAK,GACL,MAAM,IACN,UAAU,CAAA;AAEZ,MAAI,CAAC,MAAM;AACT,WAAO;;AAGT,SAAO,WAAW,OAAO,EAAE;AAE3B,KAAG;AAED,SAAK,KAAK,WAAW,GAAG;AACxB,SAAK,KAAK,WAAW,GAAG;AACxB,SAAK,KAAK,WAAW,GAAG;AAExB,WAAQ,MAAM,KAAO,MAAM,IAAK;AAEhC,SAAM,QAAQ,KAAM;AACpB,SAAM,QAAQ,KAAM;AACpB,SAAM,QAAQ,IAAK;AACnB,SAAK,OAAO;AAGZ,YAAQ,IAAI,IACV,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE;WAC3D,IAAI,KAAK;AAElB,QAAM,QAAQ,KAAK,EAAE;AAErB,UAAQ,KAAK,SAAS,GAAG;IACvB,KAAK;AACH,YAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AACzB;IACF,KAAK;AACH,YAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AACzB;;AAGJ,SAAO;AACT;AAEA,SAAS,aAAa,MAAY;AAkBhC,MAAI,MAAM;AACV,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,GACxC,KAAK,GACL,MAAM,IACN,UAAU,CAAA;AAEZ,MAAI,CAAC,MAAM;AACT,WAAO;;AAGT,UAAQ;AAER,KAAG;AACD,SAAK,IAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AACjC,SAAK,IAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AACjC,SAAK,IAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AACjC,SAAK,IAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AAEjC,WAAO,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI;AAEvC,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,IAAI;AACjB,SAAK,OAAO;AAEZ,QAAI,OAAO,IAAI;AACb,cAAQ,IAAI,IAAI,OAAO,aAAa,EAAE;eAC7B,OAAO,IAAI;AACpB,cAAQ,IAAI,IAAI,OAAO,aAAa,IAAI,EAAE;WACrC;AACL,cAAQ,IAAI,IAAI,OAAO,aAAa,IAAI,IAAI,EAAE;;WAEzC,IAAI,KAAK;AAElB,QAAM,QAAQ,KAAK,EAAE;AAErB,SAAO,WAAW,GAAG;AACvB;AAEM,SAAU,oBAAoB,GAAM;AACxC,MAAI,aAAa,CAAC;AAGlB,SAAO,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACjD;AAEM,SAAU,oBAAoB,GAAM;AACxC,MAAI,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC1C,SAAO,aAAa,CAAC;AACvB;;;ACjRA,uBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQd,IAAM,KAAK,KAAA,IAAA,MAAQ,CAAC;AAGrB,SAAU,UAAU,MAAY,WAAiB;AAErD,MAAI,gBAAgB,YAAY;AAEhC,MAAI,gBAAgB,KAAK,MAAM;AAC7B,oBAAgB,KAAK;SAChB;AAEL,WAAO,KAAK,OAAO,gBAAgB,KAAO;AACxC,uBAAiB;;;AAIrB,MAAM,SAAiB,CAAA;AACvB,MAAM,QAAQ,KAAK,KAAK,KAAK,OAAO,aAAa;AACjD,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,QAAM,QAAQ,KAAK,MACjB,gBAAgB,GAChB,MAAM,QAAQ,IAAI,KAAK,OAAO,iBAAiB,IAAI,EAAE;AAEvD,WAAO,KAAK,KAAK;;AAEnB,SAAO;AACT;AAEM,SAAU,gBAAgB,QAAiC;AAC/D,SAAO,OAAO,KAAK,MAAM,EAAE,MAAM,SAAA,KAAG;AAAI,WAAA,IAAI,QAAQ,YAAY,MAAM;EAA9B,CAA+B;AACzE;AAEM,SAAU,kBAAkB,QAAiC;AACjE,SAAO,OAAO,KAAK,MAAM,EAAE,MAAM,SAAA,KAAG;AAAI,WAAA,IAAI,QAAQ,IAAI,MAAM;EAAtB,CAAuB;AACjE;AAEM,SAAU,IAAI,MAAc;AAChC,SAAO,KAAK,OAAO,SAAC,MAAM,QAAM;AAAK,WAAA,OAAO;EAAP,GAAe,CAAC;AACvD;AAEM,SAAU,iBAAiB,UAAkB,MAAiB,QAAc;AAChF,MAAI;AACF,iBAAa,QAAQ,UAAU,KAAK,UAAU,IAAI,CAAC;WAC5C,KAAK;AACZ,WAAO,KAAK,IAAI,WACd,eAAe,kBACf,8BAA4B,QAAU,CACvC;;AAEL;AAEM,SAAU,eAAe,MAAc,KAAgC,MAAY;AACvF,MAAM,WAAW,OAAO,OAAO,MAAM,UAAQ,MAAG;AAChD,SAAO,mCAAiC,OAAO,WAAQ,UAAQ;AACjE;AAEM,SAAU,oBAAoB,UAAkB,QAAc;AAClE,MAAI;AACF,iBAAa,WAAW,QAAQ;WACzB,KAAK;AACZ,WAAO,KAAK,IAAI,WACd,eAAe,mBACf,sCAAoC,QAAU,CAC/C;;AAEL;AAEM,SAAU,iBAAiB,UAAkB,QAAc;AAC/D,MAAI,kBAAiC;AACrC,MAAI;AACF,sBAAkB,aAAa,QAAQ,QAAQ;WAC/CC,KAAM;AACN,WAAO,KAAK,IAAI,WACd,eAAe,iBACf,mCAAiC,QAAU,CAC5C;;AAGH,MAAI,mBAAmB,MAAM;AAC3B,WAAO;;AAGT,MAAI,YAA8B;AAClC,MAAI;AACF,gBAAY,KAAK,MAAM,eAAe;WACtC,IAAM;AAEN,wBAAoB,UAAU,MAAM;AACpC,WAAO,KAAK,IAAI,WACd,eAAe,kBACf,4CAA0C,QAAU,CACrD;;AAGH,SAAO;AACT;AAEM,SAAU,eAAe,OAAa;AAC1C,MAAM,OAAO,aAAa;AAC1B,SAAO,EAAE,eAAe,KAAI;AAC9B;AAEM,SAAU,yBAAyB,OAAa;AACpD,MAAM,SAAS,eAAe,KAAK;AACnC,SAAAC,UAAA,EACE,gBAAgB,2BAA0B,GACvC,MAAM;AAEb;AAEM,SAAU,oBAAoB,OAAa;AAC/C,MAAM,SAAS,eAAe,KAAK;AACnC,SAAAA,UAAA,EACE,gBAAgB,mBAAkB,GAC/B,MAAM;AAEb;AAEM,SAAU,YAAS;AACvB,MAAI,OAAO,gBAAgB;AACzB,WAAO,IAAI,eAAc;;AAG3B,MAAI,OAAO,eAAe;AACxB,WAAO,IAAI,OAAO,cAAc,mBAAmB;;AAGrD,QAAM,IAAI,WACR,eAAe,4BACf,2CAA2C;AAE/C;AAEM,SAAgB,WAAW,MAAU;;;;;;AAC1B,iBAAA,CAAA,GAAM,kBAAkB,IAAI,CAAC;;AAAtC,mBAASD,IAAA,KAAA;AACT,kBAAQ,IAAI,iBAAAE,QAAS,YAAW;AACtC,gBAAM,OAAO,MAAM;AACnB,iBAAA,CAAA,GAAO,MAAM,IAAG,CAAE;;;;;AAGd,SAAU,kBAAkB,MAAU;AAC1C,SAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,QAAM,SAAS,IAAI,WAAU;AAE7B,WAAO,SAAS,SAAC,KAA8B;AAC7C,UAAI,IAAI,QAAQ;AACd,YAAM,OAAO,IAAI,OAAO;AACxB,gBAAQ,IAAmB;aACtB;AACL,eAAO,IAAI,WACT,eAAe,4BACf,oCAAoC,CACrC;;IAEL;AAEA,WAAO,UAAU,WAAA;AACf,aAAO,IAAI,WACT,eAAe,sBACf,wBAAwB,CACzB;IACH;AAEA,WAAO,kBAAkB,IAAI;EAC/B,CAAC;AACH;AAmBM,SAAU,QAAW,KAAa,SAAuB;AAC7D,SAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,QAAM,MAAM,UAAS;AACrB,QAAI,KAAK,QAAQ,QAAQ,GAAG;AAE5B,QAAI,QAAQ,UAAU;AACpB,cAAQ,SAAS,GAAG;;AAGtB,QAAI,QAAQ,SAAS;AACnB,UAAM,YAAU,QAAQ;AACxB,aAAO,KAAK,SAAO,EAAE,QAAQ,SAAA,GAAC;AAC5B,YAAI,iBAAiB,GAAG,UAAQ,CAAC,CAAC;MACpC,CAAC;;AAGH,QAAI,OAAO,iBAAiB,YAAY,SAAC,KAAkB;AACzD,UAAI,IAAI,oBAAoB,QAAQ,YAAY;AAC9C,gBAAQ,WAAW;UACjB,QAAQ,IAAI;UACZ,OAAO,IAAI;SACZ;;IAEL,CAAC;AAED,QAAI,qBAAqB,WAAA;AACvB,UAAM,eAAe,IAAI;AACzB,UAAI,IAAI,eAAe,GAAG;AACxB;;AAGF,UAAM,QAAQ,IAAI,kBAAkB,SAAS,KAAK;AAElD,UAAI,IAAI,WAAW,GAAG;AAEpB,eAAO,IAAI,kBAAkB,kBAAkB,KAAK,CAAC;AACrD;;AAGF,UAAI,IAAI,WAAW,KAAK;AACtB,YAAI,UAAU,+BAA6B,IAAI;AAC/C,YAAI,cAAc;AAChB,qBAAW,gBAAc;;AAG3B,YAAI,OAAI;AACR,YAAI;AACF,iBAAO,KAAK,MAAM,YAAY;iBAC9BF,KAAM;;AAIR,eAAO,IAAI,kBAAkB,IAAI,QAAQ,OAAO,SAAS,IAAI,CAAC;AAC9D;;AAGF,UAAI;AACF,gBAAQ;UACN,MAAM,KAAK,MAAM,YAAY;UAC7B;SACD;eACM,KAAK;AACZ,eAAO,GAAG;;IAEd;AAEA,QAAI,KAAK,QAAQ,IAAI;EACvB,CAAC;AACH;AAEM,SAAU,eAAe,KAAuB;AACpD,MAAI,OAAO,IAAI,OAAO;AACpB,QAAI,SAAS,IAAI,MAAM,WAAW;AAElC,QAAI,CAAC,QAAQ;AACX,aAAO;;AAGT,QAAM,OAAO,OAAO,CAAC;AACrB,aAAS,IAAI,MAAM,6BAA6B;AAEhD,QAAI,QAAQ;AACV,aAAO,OAAO,CAAC;;AAGjB,QAAI,SAAS,QAAQ;AACnB,aAAO;;AAGT,WAAO;;AAGT,SAAO;AACT;AAEM,SAAU,iBAAiB,KAAuB;AACtD,MAAI,OAAO,IAAI,OAAO;AACpB,QAAM,SAAS,IAAI,MAAM,uBAAuB;AAChD,WAAO,SAAS,OAAO,CAAC,IAAI;;AAG9B,SAAO;AACT;AASM,SAAU,aAAa,OAAa;AACxC,MAAI,CAAC;AAAO,UAAM,IAAI,WAAW,eAAe,cAAc,gBAAgB;AAE9E,MAAM,WAAW,MAAM,MAAM,GAAG;AAChC,MAAI,SAAS,WAAW;AAAG,UAAM,IAAI,WAAW,eAAe,cAAc,yBAAyB;AAGtG,MAAM,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC;AAChE,MAAI,CAAC;AAAW,UAAM,IAAI,WAAW,eAAe,cAAc,2BAA2B;AAE7F,MAAI,YAA8B;AAElC,MAAI;AACF,gBAAY,KAAK,MAAM,oBAAoB,SAAS,SAAS,SAAS,CAAC,CAAC,CAAC;WAClE,OAAO;AACd,UAAM,IAAI,WAAW,eAAe,cAAc,qBAAqB;;AAGzE,MAAI,aAAa,MAAM;AACrB,UAAM,IAAI,WAAW,eAAe,cAAc,oBAAoB;;AAGxE,MAAI,UAAU,SAAS,MAAM;AAC3B,UAAM,IAAI,WAAW,eAAe,cAAc,sBAAsB;;AAG1E,MAAM,aAAa,UAAU,MAAM,MAAM,GAAG,EAAE,CAAC;AAC/C,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,WAAW,eAAe,cAAc,4BAA4B;;AAGhF,SAAO,EAAE,WAAW,YAAY,OAAO,UAAU,MAAK;AACxD;AAEM,SAAU,gBAAgB,MAAU;AACxC,MAAM,MAAM,OAAO,OAAO,OAAO,aAAa,OAAO;AAErD,SAAO,IAAI,gBAAgB,IAAI;AACjC;;;;ACpVO,IAAM,SAAS;EACpB,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,KAAK;EACL,KAAK;EACL,SAAS;;AAIJ,IAAM,mBAAkB,KAAA,CAAA,GAC7B,GAAC,OAAO,EAAE,IAAG;EACX,WAAW,CAAC,eAAe;EAC3B,WAAW,CAAC,mBAAmB;GAEjC,GAAC,OAAO,EAAE,IAAG;EACX,WAAW,CAAC,kBAAkB;EAC9B,WAAW,CAAC,sBAAsB;GAEpC,GAAC,OAAO,EAAE,IAAG;EACX,WAAW,CAAC,kBAAkB;EAC9B,WAAW,CAAC,sBAAsB;GAEpC,GAAC,OAAO,GAAG,IAAG;EACZ,WAAW,CAAC,mBAAmB;EAC/B,WAAW,CAAC,uBAAuB;GAErC,GAAC,OAAO,GAAG,IAAG;EACZ,WAAW,CAAC,mBAAmB;EAC/B,WAAW,CAAC,uBAAuB;GAErC,GAAC,OAAO,OAAO,IAAG;EAChB,WAAW,CAAC,yBAAyB;EACrC,WAAW,CAAC,6BAA6B;;;;AClC7C,yBAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBpB,SAAgB,WAAW,WAAmB,YAAoB,UAAsC;;;;AACtG,mBAAS,8BAAU,EAAE,IAAI,WAAW,QAAQ,WAAU,CAAE;AACxD,YAAS,WAAQ,+BAA6B;AACpD,aAAA,CAAA,GAAa,QAAQ,KAAK,EAAE,QAAQ,MAAK,CAAE,CAAC;;;;AAQ9C,SAAS,WAAW,QAAgB,KAAgC,YAAsB;AAChF,MAAA,MAAY,WAAU,KAAjB,KAAO,WAAU;AAC9B,SAAU,MAAG,cAAY,SAAM,eAAY,OAAO,OAAa,oBAAoB,GAAG,IAAI,OAAG,cAAY;AAC3G;AAeM,SAAU,gBACd,OACA,QACA,KACA,WAAiB;AAEjB,MAAM,MAAS,YAAS,cAAY,SAAM,eAAY,OAAO,OAAa,oBAAoB,GAAG,IAAI,OAAG;AACxG,SAAa,QACX,KACA;IACE,QAAQ;IACR,SAAe,eAAe,KAAK;GACpC;AAEL;AAaM,SAAU,YACd,OACA,KACA,OACA,YACA,SAAwD;AAExD,MAAM,SAAe,aAAa,KAAK,EAAE;AACzC,MAAM,MAAM,WAAW,QAAQ,KAAK,UAAU,KAAI,MAAI;AACtD,MAAM,UAAgB,yBAAyB,KAAK;AACpD,MAAI,QAAQ;AAAK,YAAQ,aAAa,IAAI,QAAQ;AAElD,SAAa,QAAyB,KAAGG,UAAAA,UAAA,CAAA,GACpC,OAAO,GAAA,EACV,QAAQ,OACR,QAAO,CAAA,CAAA;AAEX;AAUM,SAAU,eACd,OACA,KACA,YACA,SAAsC;AAEtC,MAAM,SAAe,aAAa,KAAK,EAAE;AACzC,MAAM,MAAM,WAAW,QAAQ,KAAK,UAAU;AAC9C,SAAa,QAA4B,KAAGA,UAAAA,UAAA,CAAA,GACvC,OAAO,GAAA,EACV,QAAQ,QACR,SAAe,oBAAoB,KAAK,EAAC,CAAA,CAAA;AAE7C;AA8BM,SAAU,OACd,KACA,MACA,SAAsC;AAEtC,SAAa,QAA4B,KAAGC,UAAA,EAC1C,QAAQ,QACR,MAAM,KAAI,GACP,OAAO,CAAA;AAEd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClJO,IAAM,qBAAqB;AAG3B,IAAM,mBAAmB,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG;AAC/C,IAAM,kBAAe,SAAO,kBAAgB,CAAE,GAAG,CAAA;AAyFxD,IAAM,KAAK,KAAA,IAAA,MAAQ,CAAC;AAEpB,IAAA;;EAAA,WAAA;AA8BE,aAAAC,MACE,SACA,UACU,UACA,QAAc;AADd,WAAA,WAAA;AACA,WAAA,SAAA;AA9BF,WAAA,UAAU;AACV,WAAA,aAAa;AAGb,WAAA,UAA4B,CAAA;AA6BpC,WAAK,SAAS,QAAQ;AACtB,aAAO,KAAK,kBAAkB,KAAK,MAAM;AAEzC,WAAK,WAAQC,UAAA,EACX,OAAO,GAAE,GACN,QAAQ,QAAQ;AAGrB,aAAO,KAAK,oBAAoB,KAAK,QAAQ;AAE7C,WAAK,MAAM,QAAQ;AACnB,WAAK,OAAO,QAAQ;AACpB,WAAK,QAAQ,QAAQ;AAErB,WAAK,SAAS,SAAS;AACvB,WAAK,UAAU,SAAS;AACxB,WAAK,aAAa,SAAS;AAE3B,UAAI;AACF,YAAM,YAAkB,aAAa,KAAK,KAAK;AAC/C,aAAK,aAAa,UAAU;AAC5B,aAAK,YAAY,UAAU;eACpB,OAAO;AACd,eAAO,MAAM,oCAAoC,KAAK;AACtD,aAAK,QAAQ,KAAK;;IAEtB;AAGgB,IAAAD,MAAA,UAAA,2BAAhB,WAAA;;;;;;AAEE,mBAAK,OAAO,KAAK,4BAA4B;AAC7B,qBAAA,CAAA,GAAM,KAAK,SAAS,MAClC,KAAK,WACL,KAAK,YACL,KAAK,OAAO,UAAU,CACvB;;AAJK,wBAAUE,IAAA,KAAA;AAMhB,kBAAI,WAAW,MAAM;AACnB,sBAAM,IAAI,WACR,eAAe,wBACf,2BAA2B;;AAI/B,kBAAI,KAAK,cAAc,QAAQ,KAAK,WAAW,SAAS,QAAQ,MAAM;AACpE,qBAAK,OAAO,KAAK,wBAAsB,KAAK,WAAW,OAAI,SAAO,QAAQ,OAAI,GAAG;qBAC5E;AACL,qBAAK,OAAO,KAAK,cAAY,QAAQ,OAAI,GAAG;;AAG9C,mBAAK,aAAa;;;;;;;;;AAIV,IAAAF,MAAA,UAAA,uBAAV,WAAA;AACE,WAAK,OAAO,KAAK,sBAAsB;AACvC,UAAI,KAAK,cAAc,QAAQ,KAAK,WAAW,SAAQ,GAAI;AACzD,aAAK,OAAO,KAAQ,KAAK,WAAW,OAAI,oBAAoB;AAC5D,aAAK,WAAW,SAAQ;;IAE5B;AAGQ,IAAAA,MAAA,UAAA,qBAAR,SAA2B,OAAiB;AAC1C,WAAK,OAAO,KAAK,oBAAoB;AACrC,UAAI,iBAAiB,qBAAqB,KAAK,cAAc,MAAM;AACjE,YAAI,iBAAiB,SAAS,MAAM,IAAI,GAAG;AACzC,eAAK,OAAO,KAAQ,KAAK,WAAW,OAAI,8BAA8B;AACtE,eAAK,WAAW,OAAM;;;IAG5B;AAEQ,IAAAA,MAAA,UAAA,cAAR,SAAoB,OAAiB;AACnC,WAAK,OAAO,MAAM,MAAM,OAAO;AAC/B,WAAK,QAAQ,KAAK;IACpB;AAMa,IAAAA,MAAA,UAAA,UAAb,WAAA;;;;;;AACE,mBAAK,UAAU;AACf,kBAAI,CAAC,KAAK,SAAS,OAAO;AACxB,qBAAK,OAAO,KAAK,yBAAyB;AAC1C,qBAAK,SAAS,QAAQ,KAAK,KAAK;;AAGlC,kBAAI,KAAK,KAAK,OAAO,MAAQ,IAAI;AAC/B,qBAAK,YAAY,IAAI,WACnB,eAAe,aACf,uCAAuC,CACxC;AACD,uBAAA;kBAAA;;gBAAA;;AAGF,kBAAI,KAAK,SAAS,YAAY;AAC5B,oBAAI,CAAO,kBAAkB,KAAK,SAAS,UAAU,GAAG;AACtD,uBAAK,YAAY,IAAI;oBACnB,eAAe;;oBAEf;kBAAsC,CACvC;AACD,yBAAA;oBAAA;;kBAAA;;;AAIJ,kBAAI,KAAK,SAAS,UAAU;AAC1B,oBAAI,CAAO,gBAAgB,KAAK,SAAS,QAAQ,GAAG;AAClD,uBAAK,YAAY,IAAI,WACnB,eAAe,iBACf,2CAA2C,CAC5C;AACD,yBAAA;oBAAA;;kBAAA;;;;;;AAKF,mBAAK,YAAW,oBAAI,KAAI,GAAG,QAAO;AAClC,qBAAA,CAAA,GAAM,KAAK,yBAAwB,CAAE;;AAArC,cAAAE,IAAA,KAAA;AACe,qBAAA,CAAA,GAAM,KAAK,IAAG,CAAE;;AAAzB,uBAASA,IAAA,KAAA;AACf,mBAAK,WAAW,OAAO,IAAI;AAC3B,mBAAK,qBAAoB;AACzB,mBAAK,QAAQ,OAAO,OAAO,GAAG;AAC9B,qBAAA;gBAAA;;cAAA;;;AAEA,kBAAI,KAAK,SAAS;AAChB,qBAAK,OAAO,KAAK,oBAAoB;AACrC,qBAAK,QAAQ,IAAI,EAAE;AACnB,uBAAA;kBAAA;;gBAAA;;AAGF,mBAAK,MAAK;AACV,mBAAK,OAAO,MAAM,KAAG;AACrB,kBAAI,iBAAe,mBAAmB;AACpC,qBAAK,QAAQ,MAAI,OAAO,MAAI,IAAI;AAGhC,qBAAK,mBAAmB,KAAG;AAErB,qCAAqB,EAAE,KAAK,cAAc,KAAK,OAAO;AACtD,4BAAY,gBAAgB,SAAS,MAAI,IAAI;AAKnD,oBAAI,aAAa,oBAAoB;AACnC,uBAAK,OAAO,KAAK,uBAAqB,KAAK,aAAU,MAAI,KAAK,OAAO,aAAU,GAAG;AAClF,uBAAK,QAAO;AACZ,yBAAA;oBAAA;;kBAAA;;;AAIJ,mBAAK,QAAQ,KAAG;;;;;;;;;;;AAIZ,IAAAF,MAAA,UAAA,QAAR,WAAA;AACE,WAAK,QAAQ,QAAQ,SAAA,KAAG;AACtB,YAAI,qBAAqB;AACzB,YAAI,MAAK;MACX,CAAC;AACD,WAAK,UAAU,CAAA;AACf,WAAK,OAAO,KAAK,wBAAwB;IAC3C;AAEO,IAAAA,MAAA,UAAA,OAAP,WAAA;AACE,WAAK,OAAO,KAAK,UAAU;AAC3B,WAAK,MAAK;AACV,WAAK,UAAU;IACjB;AAEO,IAAAA,MAAA,UAAA,SAAP,SAAc,KAAmB;AAC/B,WAAK,QAAQ,KAAK,GAAG;IACvB;AAEQ,IAAAA,MAAA,UAAA,UAAR,SAAgB,OAAe,MAAY;;AACzC,WAAK,OAAO,OAAO;QACjB;QACA;QACA,UAAU;QACV,QAAQ;QACR,MAAM,KAAK,KAAK;QAChB,MAAM,KAAK,MAAM,KAAK,WAAW,GAAI;QACrC,MAAY,gBAAcE,MAAC,KAAK,gBAAU,QAAAA,QAAA,SAAA,SAAAA,IAAE,OAAM,CAAA;QAClD,MAAY,kBAAgB,KAAC,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM,CAAA;QACpD,WAAW,KAAK,WAAW,KAAK,SAAS,MAAM,SAAS;QACxD,UAAU,KAAK,QAAO,oBAAI,KAAI,GAAG,QAAO,IAAK,KAAK,YAAY,GAAI;OACnE;IACH;AAEO,IAAAF,MAAA,UAAA,sBAAP,SAA2B,QAAgB,MAAc,WAAmB;AAC1E,aAAAC,UAAA;QACE;QACA;QACA,SAAS,SAAS,OAAO;MAAG,GACxB,aAAa,OAAO,CAAA,IAAK,EAAE,UAAS,CAAG;IAE/C;AACF,WAAAD;EAAA,EA9OA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnEA,SAAS,kBAAkB,GAAS;AAClC,MAAM,KAAK;AACX,SAAO,GAAG,KAAK,OAAO,CAAC,CAAC;AAC1B;AAEA,IAAA;;EAAA,SAAA,QAAA;AAAoC,IAAAG,WAAAC,SAAA,MAAA;AAApC,aAAAA,UAAA;;IAyQA;AAtOkB,IAAAA,QAAA,UAAA,MAAhB,WAAA;;;;;;;AACE,mBAAK,OAAO,KAAK,mBAAmB;AACpC,kBAAI,CAAC,KAAK,OAAO,aAAa,CAAC,kBAAkB,KAAK,OAAO,SAAS,GAAG;AACvE,sBAAM,IAAI,WACR,eAAe,kBACf,sCAAsC;;AAI1C,kBAAI,KAAK,OAAO,YAAY,MAAM;AAChC,sBAAM,IAAI,WACR,eAAe,kBACf,iCAAiC;;AAIrC,qBAAA,CAAA,GAAM,KAAK,uBAAsB,CAAE;;AAAnC,cAAAC,IAAA,KAAA;AAEM,qBAAO,IAAU,KACrB,SAAO,WAAoB;AAAA,uBAAAC,WAAA,OAAA,QAAA,QAAA,WAAA;;;;AACzB,4BAAI,KAAK,SAAS;AAChB,+BAAK,MAAK;AACV,gCAAM,IAAI,MAAM,iBAAiB;;AAGnC,+BAAA,CAAA,GAAM,KAAK,YAAY,SAAS,CAAC;;AAAjC,wBAAAD,IAAA,KAAA;;;;;;;;iBAEF,KAAK,OAAO,sBAAsB;AAGhC,+BAAiB;AACf,yBAAW,KAAK,YAAW;AAC3B,6BAAe,KAAK,OAAO,IAAI,SAAC,OAAO,OAAK;AAAK,uBAAA,KAAK,QAAQ,EAAE,OAAO,MAAK,CAAE;cAA7B,CAA8B;;;;AAGnF,qBAAA,CAAA,GAAM,QAAQ,IAAI,YAAY,CAAC;;AAA/B,cAAAA,IAAA,KAAA;AACiB,qBAAA,CAAA,GAAM,KAAK,UAAS,CAAE;;AAAvC,+BAAiBA,IAAA,KAAA;;;;AAGjB,kBAAI,mBAAiB,sBAAsB,QAAM,SAAS,OAAO,QAAM,SAAS,MAAM;AACpF,gBAAM,oBAAoB,UAAU,KAAK,MAAM;;AAGjD,oBAAM;;AAIR,cAAM,oBAAoB,UAAU,KAAK,MAAM;AAC/C,qBAAA,CAAA,GAAO,cAAc;;;;;AAGT,IAAAD,QAAA,UAAA,cAAd,SAA0B,WAAoB;;;;;;;AACpC,sBAAiB,UAAS,OAAnB,QAAU,UAAS;AAC5B,2BAAa,KAAK,mBAAmB,KAAK;AAChD,mBAAK,OAAO,KAAK,iBAAe,QAAK,YAAY,UAAU;AAErD,+BAAiB,KAAK,OAAO;AAC7B,2BAAa,WAAA;AACjB,sBAAK,cAAc,KAAK,IAAI;AAC5B,sBAAK,oBAAoB,MAAM,MAAM,KAAK;AAC1C,sBAAK,aAAa,KAAK,IAAI;AAC3B,sBAAK,iBAAgB;cACvB;AAGA,kBAAI,cAAc,CAAC,gBAAgB;AACjC,2BAAU;AACV,uBAAA;kBAAA;;gBAAA;;AAGU,qBAAA,CAAA,GAAY,WAAW,KAAK,CAAC;;AAAnC,oBAAMC,IAAA,KAAA;AACZ,mBAAK,OAAO,KAAK,sBAAsB,GAAG;AAE1C,kBAAI,cAAc,QAAQ,WAAW,KAAK;AACxC,2BAAU;AACV,uBAAA;kBAAA;;gBAAA;;AAIF,mBAAK,cAAc,KAAK,IAAI;AAEtB,2BAAa,SAAC,MAAc;AAChC,sBAAK,oBAAoB,KAAK,QAAQ,KAAK;cAC7C;AAEM,+BAAiB;gBACrB,MAAM;gBACN,KAAK,KAAK,OAAO,gBAAgB,MAAM;gBACvC;gBACA,UAAU,SAAC,KAAmB;AAAK,yBAAA,MAAK,OAAO,GAAG;gBAAf;;AAGrC,mBAAK,OAAO,KAAK,UAAQ,QAAK,mBAAmB;AAChC,qBAAA,CAAA,GAAM,YACrB,KAAK,OACL,KAAK,KACL,UAAU,QAAQ,GAClB,KAAK,cAAa,GAClB,cAAc,CACf;;AANK,yBAAWA,IAAA,KAAA;AAOjB,mBAAK,OAAO,KAAK,UAAQ,QAAK,oBAAoB;AAGlD,yBAAW;gBACT,QAAQ,MAAM;gBACd,OAAO,MAAM;eACd;AAED,mBAAK,aAAa,KAAK,IAAI;gBACzB,MAAM,SAAS,KAAK;gBACpB,KAAK,SAAS,KAAK;gBACnB,MAAM,MAAM;;AAGd,mBAAK,iBAAgB;;;;;;;;;AAGT,IAAAD,QAAA,UAAA,YAAd,WAAA;;;;;;;AACQ,qBAAIG,UAAAA,UAAAA,UAAA,EACR,OAAO,KAAK,aAAa,IAAI,SAAC,OAAO,OAAK;AAAK,uBAAC;kBAC9C,MAAM,MAAM;;kBAEZ,YAAY,QAAQ;;cAHyB,CAI7C,GACF,OAAO,KAAK,SAAS,MAAK,GACvB,KAAK,SAAS,YAAY,EAAE,UAAU,KAAK,SAAS,SAAQ,CAAE,GAC9D,KAAK,SAAS,cAAc,EAAE,YAAY,KAAK,SAAS,WAAU,CAAE,GACpE,KAAK,SAAS,YAAY,EAAE,UAAU,KAAK,SAAS,SAAQ,CAAE;AAGnE,mBAAK,OAAO,KAAK,sCAAsC,IAAI;AAC5C,qBAAA,CAAA,GAAM,eACnB,KAAK,OACL,KAAK,KACL,KAAK,cAAa,GAClB;gBACE,UAAU,SAAA,KAAG;AAAI,yBAAA,MAAK,OAAO,GAAG;gBAAf;gBACjB,MAAM,KAAK,UAAU,IAAI;eAC1B,CACF;;AARK,uBAASF,IAAA,KAAA;AAUf,mBAAK,OAAO,KAAK,yBAAyB;AAC1C,mBAAK,qBAAqB,CAAC;AAC3B,qBAAA,CAAA,GAAO,MAAM;;;;;AAGD,IAAAD,QAAA,UAAA,yBAAd,WAAA;;;;;;AACE,mBAAK,eAAe,CAAA;AACpB,mBAAK,gBAAgB,CAAA;AACf,2BAAmB,iBAAiB,KAAK,YAAW,GAAI,KAAK,MAAM;mBAIrE,CAAC;AAAD,uBAAA,CAAA,GAAA,CAAA;AACF,mBAAK,OAAO,KAAK,6BAA6B;AAClC,qBAAA,CAAA,GAAM,gBAChB,KAAK,OACL,KAAK,YACL,KAAK,KACL,KAAK,WAAY,OAAM,CAAE,CAC1B;;AALK,oBAAMC,IAAA,KAAA;AAMZ,mBAAK,OAAO,KAAK,+BAA6B,IAAI,KAAK,WAAQ,GAAG;AAClE,mBAAK,WAAW,IAAI,KAAK;AACzB,mBAAK,qBAAqB,CAAA;;;AAEpB,4BAAc;gBAClB;gBACA,WAAS,WAAW,KAAK,SAAM;gBAC/B,WAAS,WAAW,KAAE;;AAGxB,mBAAK,OAAO,KAAK,YAAY,KAAK,GAAG,CAAC;AACtC,mBAAK,qBAAqB,WAAW;AACrC,mBAAK,WAAW,WAAW;;;AAG7B,mBAAK,SAAe,UAAU,KAAK,MAAM,KAAK,OAAO,SAAS;AAC9D,mBAAK,SAAS;gBACZ,gBAAgB;gBAChB,QAAQ,KAAK,OAAO,IAAI,SAAA,GAAC;AAAI,yBAAA;gBAAA,CAAC;;AAEhC,mBAAK,qBAAoB;;;;;;;;;AAGnB,IAAAD,QAAA,UAAA,gBAAR,WAAA;AACE,aAAO;QACL,IAAI,KAAK;QACT,KAAK,KAAK,WAAY,OAAM;;IAEhC;AAEQ,IAAAA,QAAA,UAAA,cAAR,WAAA;AACE,aAAa,eAAe,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI;IACtE;AAEQ,IAAAA,QAAA,UAAA,mBAAR,WAAA;AACE,MAAM,iBAAiB,KAAK,YAAW,GAAI;QACzC,IAAI,KAAK;QACT,MAAM,KAAK;SACV,KAAK,MAAM;IAChB;AAEQ,IAAAA,QAAA,UAAA,sBAAR,SAA4B,QAAgB,OAAa;AACvD,WAAK,OAAO,OAAO,KAAK,IAAI;AAC5B,WAAK,qBAAoB;IAC3B;AAEQ,IAAAA,QAAA,UAAA,uBAAR,SAA6B,UAAe;AAC1C,WAAK,OAAO,iBAAiB;AAC7B,WAAK,qBAAoB;IAC3B;AAEQ,IAAAA,QAAA,UAAA,uBAAR,WAAA;AAAA,UAAA,QAAA;AACE,WAAK,WAAW;QACd,OAAO,KAAK;UACJ,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO;;UAE5C,KAAK,KAAK,OAAO;;;QAEnB,QAAQ,KAAK,OAAO,IAAI,SAAC,OAAO,OAAK;AACnC,cAAM,YAAY,MAAK,cAAc,KAAK;AAC1C,iBAAO,MAAK,oBAAoB,MAAK,OAAO,OAAO,KAAK,GAAG,MAAM,MAAM,SAAS;QAClF,CAAC;QACD,YAAY;UACV,IAAI,KAAK;UACT,KAAK,KAAK,WAAY,OAAM;;;AAGhC,WAAK,OAAO,KAAK,QAAQ;IAC3B;AACF,WAAAA;EAAA,EAzQoC,YAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BxC,IAAA;;EAAA,WAAA;AAAA,aAAAI,SAAA;AACU,WAAA,MAAM;AACN,WAAA,QAAQ,KAAK,UAAS;IA8EhC;AA5EU,IAAAA,OAAA,UAAA,YAAR,WAAA;AACE,UAAM,QAAQ,IAAI,MAAK;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,IAAI,GAAG;AAET,gBAAK,MAAM,IAAK;iBACX;AACL,mBAAO;;;AAGX,cAAM,CAAC,IAAI;;AAGb,aAAO;IACT;AAEQ,IAAAA,OAAA,UAAA,SAAR,SAAe,MAAgB;AAC7B,UAAI,MAAM,KAAK;AACf,eAAS,SAAS,GAAG,SAAS,KAAK,YAAY,UAAU;AACvD,cAAO,QAAQ,IAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,GAAI;;AAE5D,WAAK,MAAM;IACb;AAEQ,IAAAA,OAAA,UAAA,UAAR,WAAA;AACE,cAAQ,KAAK,MAAM,QAAQ;IAC7B;AAEc,IAAAA,OAAA,UAAA,mBAAd,SAA+B,MAAiB;;;;;;oBAC1C,OAAO,KAAK,gBAAgB;AAA5B,uBAAA,CAAA,GAAA,CAAA;oBACS,WAAU;AAAC,qBAAA,CAAA,GAAM,KAAK,YAAW,CAAE;;AAA9C,qBAAA,CAAA,GAAO,KAAAC,IAAA,MAAI,YAAU,CAAA,QAAC,GAAA,KAAA,CAAwB,CAAA,GAAA,CAAC;;AAGjD,qBAAA,CAAA,GAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,oBAAM,SAAS,IAAI,WAAU;AAC7B,uBAAO,SAAS,WAAA;AACd,sBAAI,OAAO,UAAU,MAAM;AACzB,2BAAM;AACN;;AAGF,sBAAI,OAAO,OAAO,WAAW,UAAU;AACrC,2BAAM;AACN;;AAGF,0BAAQ,IAAI,WAAW,OAAO,MAAM,CAAC;gBACvC;AACA,uBAAO,kBAAkB,IAAI;cAC/B,CAAC,CAAC;;;;;AAGE,IAAAD,OAAA,UAAA,OAAN,SAAW,MAAU;;;;;;oBACf,KAAK,QAAQ;AAAb,uBAAA,CAAA,GAAA,CAAA;AACF,cAAAC,MAAA,KAAK;AAAO,qBAAA,CAAA,GAAM,KAAK,iBAAiB,IAAI,CAAC;;AAA7C,cAAAA,IAAA,MAAA,MAAI,CAAQ,GAAA,KAAA,CAAiC,CAAA;AAC7C,qBAAA,CAAA,GAAO,KAAK,QAAO,CAAE;;AAGjB,sBAAQ,KAAK,KAAK,KAAK,OAAO,EAAE;AAC7B,sBAAQ;;;oBAAG,QAAQ;AAAK,uBAAA,CAAA,GAAA,CAAA;AACzB,sBAAQ,QAAQ;AAChB,oBAAM,UAAW,QAAQ,IAAK,KAAK,OAAO,QAAQ;AAE1C,qBAAA,CAAA,GAAM,KAAK,iBAAiB,KAAK,MAAM,OAAO,GAAG,CAAC,CAAC;;AAA3D,sBAAQ,GAAA,KAAA;AACd,mBAAK,OAAO,IAAI,WAAW,KAAK,CAAC;;;AALA;;;AAQnC,qBAAA,CAAA,GAAO,KAAK,QAAO,CAAE;;;;;AAGhB,IAAAD,OAAA,OAAP,SAAY,MAAU;AACpB,UAAM,MAAM,IAAIA,OAAK;AACrB,aAAO,IAAI,KAAK,IAAI;IACtB;AACF,WAAAA;EAAA,EAhFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHA,IAAA;;EAAA,SAAA,QAAA;AAAoC,IAAAE,WAAAC,SAAA,MAAA;AAApC,aAAAA,UAAA;;IAyDA;AAvDkB,IAAAA,QAAA,UAAA,MAAhB,WAAA;;;;;;;AACE,mBAAK,OAAO,KAAK,mBAAmB;AAE9B,yBAAW,IAAI,SAAQ;AAC7B,uBAAS,OAAO,QAAQ,KAAK,IAAI;AACjC,uBAAS,OAAO,SAAS,KAAK,KAAK;AACnC,kBAAI,KAAK,OAAO,MAAM;AACpB,yBAAS,OAAO,OAAO,KAAK,GAAG;;AAEjC,uBAAS,OAAO,SAAS,KAAK,SAAS,KAAK;mBAExC,KAAK,OAAO;AAAZ,uBAAA,CAAA,GAAA,CAAA;AACc,qBAAA,CAAA,GAAM,MAAM,KAAK,KAAK,IAAI,CAAC;;AAArC,wBAAUC,IAAA,KAAA;AAChB,uBAAS,OAAO,SAAS,QAAQ,SAAQ,CAAE;;;AAG7C,kBAAI,KAAK,SAAS,YAAY;AAC5B,qBAAK,OAAO,KAAK,kBAAkB;AAC3B,+BAAe,KAAK,SAAQ;AACpC,uBAAO,KAAK,YAAU,EAAE,QAAQ,SAAA,KAAG;AAAI,yBAAA,SAAS,OAAO,KAAK,aAAW,GAAG,EAAE,SAAQ,CAAE;gBAA/C,CAAgD;AACvF,qBAAK,OAAO,KAAK,oBAAoB;;AAGvC,mBAAK,OAAO,KAAK,kBAAkB;AACpB,qBAAA,CAAA,GAAM,OAAO,KAAK,WAAY,OAAM,GAAI,UAAU;gBAC/D,YAAY,SAAA,MAAI;AACd,wBAAK,qBAAqB,KAAK,QAAQ,KAAK,KAAK;gBACnD;gBACA,UAAU,SAAA,KAAG;AAAI,yBAAA,MAAK,OAAO,GAAG;gBAAf;eAClB,CAAC;;AALI,uBAASA,IAAA,KAAA;AAOf,mBAAK,OAAO,KAAK,yBAAyB;AAC1C,mBAAK,qBAAoB;AACzB,qBAAA,CAAA,GAAO,MAAM;;;;;AAGP,IAAAD,QAAA,UAAA,uBAAR,SAA6B,QAAgB,OAAa;AAExD,WAAK,WAAW,EAAE,OAAO,KAAK,oBAAoB,QAAQ,QAAQ,CAAC,EAAC;AACpE,WAAK,OAAO,KAAK,QAAQ;IAC3B;AAEQ,IAAAA,QAAA,UAAA,uBAAR,WAAA;AAEE,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,OAAO,KAAK,mBAAmB;AACpC,aAAK,WAAW,EAAE,OAAO,KAAK,oBAAoB,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,EAAC;AACjF,aAAK,OAAO,KAAK,QAAQ;AACzB;;AAGM,UAAA,QAAU,KAAK,SAAQ;AAC/B,WAAK,WAAW,EAAE,OAAO,KAAK,oBAAoB,MAAM,SAAS,GAAG,MAAM,IAAI,EAAC;AAC/E,WAAK,OAAO,KAAK,QAAQ;IAC3B;AACF,WAAAA;EAAA,EAzDoC,YAAI;;;;;ACelC,SAAU,SAAS,OAAe,MAAiB,OAAS;AAAT,MAAA,UAAA,QAAA;AAAA,YAAA;EAAS;AAChE,MAAM,MAAM,UAAS;AACrB,MAAI,KAAK,QAAQ,6BAA6B;AAC9C,MAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,MAAI,iBAAiB,iBAAiB,eAAe,KAAK,EAAE,aAAa;AACzE,MAAI,qBAAqB,WAAA;AACvB,QAAI,IAAI,eAAe,KAAK,IAAI,WAAW,OAAO,QAAQ,GAAG;AAC3D,eAAS,OAAO,MAAM,QAAQ,CAAC;;EAEnC;AAGA,MAAM,gBAAgB;IACpB,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,QAAQ;IACb,KAAK,aAAa;IAClB,KAAK,UAAU;IACf,KAAK,QAAQ;IACb,KAAK,GAAG;AAEV,MAAI,KAAK,aAAa;AACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CA,IAAA;;EAAA,WAAA;AAOE,aAAAE,QACU,OACA,eACA,OACA,QAAiB;AAFjB,UAAA,kBAAA,QAAA;AAAA,wBAAA;MAAoB;AACpB,UAAA,UAAA,QAAA;AAAA,gBAAA;MAAuB;AACvB,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAiB;AAHjB,WAAA,QAAA;AACA,WAAA,gBAAA;AACA,WAAA,QAAA;AACA,WAAA,SAAA;AANF,WAAA,KAAK,EAAEA,QAAO;IAOlB;AAEI,IAAAA,QAAA,UAAA,iBAAR,SAAuB,OAAe;AACpC,aAAO,mBAAiB,QAAK,OAAK,KAAK,SAAM,MAAI,KAAK,KAAE;IAC1D;AAOA,IAAAA,QAAA,UAAA,SAAA,SAAO,MAAiB,OAAc;AACpC,UAAI,KAAK;AAAe;AACxB,UAAI;AACF,iBAAS,KAAK,OAAO,MAAM,KAAK;eACzB,OAAO;AACd,aAAK,KAAK,KAAK;;IAEnB;AAMA,IAAAA,QAAA,UAAA,OAAA,WAAA;AAAK,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,aAAA,EAAA,IAAA,UAAA,EAAA;;AACH,UAAM,aAAyB,CAAC,MAAM;AACtC,UAAI,WAAW,SAAS,KAAK,KAAK,GAAG;AAEnC,gBAAQ,IAAG,MAAX,SAAOC,UAAA,CAAK,KAAK,eAAe,MAAM,CAAC,GAAK,IAAI,CAAA;;IAEpD;AAMA,IAAAD,QAAA,UAAA,OAAA,WAAA;AAAK,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,aAAA,EAAA,IAAA,UAAA,EAAA;;AACH,UAAM,aAAyB,CAAC,QAAQ,MAAM;AAC9C,UAAI,WAAW,SAAS,KAAK,KAAK,GAAG;AAEnC,gBAAQ,KAAI,MAAZ,SAAOC,UAAA,CAAM,KAAK,eAAe,MAAM,CAAC,GAAK,IAAI,CAAA;;IAErD;AAMA,IAAAD,QAAA,UAAA,QAAA,WAAA;AAAM,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,aAAA,EAAA,IAAA,UAAA,EAAA;;AACJ,UAAM,aAAyB,CAAC,QAAQ,QAAQ,OAAO;AACvD,UAAI,WAAW,SAAS,KAAK,KAAK,GAAG;AAEnC,gBAAQ,MAAK,MAAb,SAAOC,UAAA,CAAO,KAAK,eAAe,OAAO,CAAC,GAAK,IAAI,CAAA;;IAEvD;AAjEe,IAAAD,QAAA,KAAK;AAkEtB,WAAAA;IAnEA;;qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACErB,IAAM,kBAAkB,oBAAI,IAAG;AAE/B,IAAA;;EAAA,WAAA;AACE,aAAAE,MAAmB,MAAqB,UAAsC;AAA3D,WAAA,OAAA;AAAqB,WAAA,WAAA;IAA0C;AAKlF,IAAAA,MAAA,UAAA,WAAA,WAAA;AACE,UAAM,eAAc,oBAAI,KAAI,GAAG,QAAO;AACtC,UAAM,eAAe,gBAAgB,IAAI,KAAK,IAAI;AAClD,aAAO,gBAAgB,QAAQ,gBAAgB;IACjD;AAMA,IAAAA,MAAA,UAAA,SAAA,SAAO,MAAS;AAAT,UAAA,SAAA,QAAA;AAAA,eAAA;MAAS;AACd,UAAM,gBAAe,oBAAI,KAAI,GAAG,QAAO,IAAM,OAAO;AACpD,sBAAgB,IAAI,KAAK,MAAM,YAAY;IAC7C;AAKA,IAAAA,MAAA,UAAA,WAAA,WAAA;AACE,sBAAgB,QAAM,EAAC,KAAK,IAAI;IAClC;AAKA,IAAAA,MAAA,UAAA,SAAA,WAAA;AACE,aAAU,KAAK,WAAQ,QAAM,KAAK;IACpC;AAKA,IAAAA,MAAA,UAAA,kBAAA,WAAA;AACE,aAAO,gBAAgB,IAAI,KAAK,IAAI;IACtC;AACF,WAAAA;EAAA,EAzCA;;AA0CA,IAAA;;EAAA,WAAA;AAUE,aAAAC,UAAoB,WAAwB;AAAxB,UAAA,cAAA,QAAA;AAAA,oBAAA,CAAA;MAAwB;AAAxB,WAAA,YAAA;AANZ,WAAA,iBAAiB,oBAAI,IAAG;IAMgB;AAUxC,IAAAA,UAAA,UAAA,WAAR,SAAiB,WAAmB,YAAoB,OAAiB,UAAsC;AAC7G,WAAK,eAAe,IACf,YAAS,MAAI,YAChB,MAAM,IAAI,SAAA,MAAI;AAAI,eAAA,IAAI,KAAK,MAAM,QAAQ;MAAvB,CAAwB,CAAC;IAE/C;AASc,IAAAA,UAAA,UAAA,UAAd,SAAsB,WAAmB,YAAoB,UAAsC;;;;;;;AAC3F,+BAAiB,KAAK,eAAe,IAAO,YAAS,MAAI,UAAY,KAAK,CAAA;AAChF,kBAAI,eAAe,SAAS;AAAG,uBAAA;kBAAA;;gBAAA;AAE/B,kBAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,qBAAK,SAAS,WAAW,YAAY,KAAK,WAAW,QAAQ;AAC7D,uBAAA;kBAAA;;gBAAA;;AAGe,qBAAA,CAAA,GAAM,WAAW,WAAW,YAAY,QAAQ,CAAC;;AAA5D,yBAAW,GAAA,KAAA;AACjB,mBAAI,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,SAAQ,MAAM;AACpB,6BAAUC,YACV,MAAAC,MAAA,SAAS,KAAK,QAAE,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAG,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,CAAA,KAC/B,MAAA,KAAA,SAAS,KAAK,QAAE,QAAA,OAAA,SAAA,SAAA,GAAE,SAAG,QAAA,OAAA,SAAA,SAAA,GAAE,WAAU,CAAA,CAAG;AAE1C,qBAAK,SAAS,WAAW,YAAY,YAAY,QAAQ;;;;;;;;;;AAWhD,IAAAF,UAAA,UAAA,QAAb,SAAmB,WAAmB,YAAoB,UAAsC;;;;;;AAC9F,qBAAA,CAAA,GAAM,KAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC;;AAAnD,cAAAE,IAAA,KAAA;AACM,+BAAiB,KAAK,eAAe,IAAO,YAAS,MAAI,UAAY,KAAK,CAAA;AAEhF,kBAAI,eAAe,WAAW;AAAG,uBAAA,CAAA,GAAO,IAAI;AACtC,kCAAoB,eAAe,OAAO,SAAA,MAAI;AAAI,uBAAA,CAAC,KAAK,SAAQ;cAAd,CAAgB;AACxE,kBAAI,kBAAkB,SAAS;AAAG,uBAAA;kBAAA;kBAAO,kBAAkB,CAAC;;;AAGtD,8BAAgB,eACnB,MAAK,EAAG,KACP,SAAC,OAAO,OAAK;AAAK,wBAAC,MAAM,gBAAe,KAAM,MAAM,MAAM,gBAAe,KAAM;cAA7D,CAA+D;AAGrF,qBAAA,CAAA,GAAO,cAAc,CAAC,CAAC;;;;;AAE3B,WAAAF;EAAA,EA5EA;;;;ACtCM,SAAU,oBACd,SACA,UACA,UACA,QAAc;AAEd,MAAI,QAAQ,UAAU,QAAQ,OAAO,aAAa;AAChD,WAAO,KAAK,uBAAuB;AACnC,WAAO,IAAI,eAAO,SAAS,UAAU,UAAU,MAAM;;AAGvD,MAAI,QAAQ,KAAK,OAAO,IAAI,IAAI;AAC9B,WAAO,KAAK,gCAAgC;AAC5C,WAAO,IAAI,eAAO,SAAS,UAAU,UAAU,MAAM;;AAGvD,SAAO,KAAK,8CAA8C;AAC1D,SAAO,IAAI,eAAO,SAAS,UAAU,UAAU,MAAM;AACvD;AAUc,SAAP,OACL,MACA,KACA,OACA,UACA,QAAe;AAIf,MAAM,SAAS,IAAI,eAAO,OAAO,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,yBAAyB,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,eAAe,KAAK,IAAI;AAElG,MAAM,UAAyB;IAC7B;IACA;IACA;IACA;IACA,QAAQ,sBAAsB,QAAQ,MAAM;;AAI9C,MAAM,WAAW,IAAI,SAAS,QAAQ,OAAO,MAAM;AAEnD,SAAO,IAAI,WAAW,SAAC,UAItB;AACC,QAAM,UAAU,oBAAoB,SAAS;MAC3C,QAAQ,SAAC,MAAoB;AAAK,eAAA,SAAS,KAAK,IAAI;MAAlB;MAClC,SAAS,SAAC,KAAe;AAAK,eAAA,SAAS,MAAM,GAAG;MAAlB;MAC9B,YAAY,SAAC,KAAQ;AAAK,eAAA,SAAS,SAAS,GAAG;MAArB;OACzB,UAAU,MAAM;AACnB,YAAQ,QAAO;AACf,WAAO,QAAQ,KAAK,KAAK,OAAO;EAClC,CAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvEM,SAAU,sBAAsB,QAA0B,QAAe;AAC7E,MAAMG,MAAAC,UAAA,CAAA,GAA8C,MAAM,GAAlD,aAAUD,IAAA,YAAE,SAAMA,IAAA,QAAK,cAAW,OAAAA,KAApC,CAAA,cAAA,QAAA,CAAsC;AAE5C,MAAM,kBAAeC,UAAA,EACnB,QAAQ,CAAA,GACR,YAAY,GAEZ,YAAY,OACZ,aAAa,OACb,cAAc,MACd,eAAe,OACf,wBAAwB,GACxB,WAAW,oBAEX,YAAY,SAEZ,eAAe,OACf,yBAAyB,MAAK,GAE3B,WAAW;AAIhB,MAAI,YAAY;AACd,oBAAgB,aAAa,WAC1B,QAAQ,MAAM,EAAE;;AAGrB,MAAM,WAAqB,CAAA;AAE3B,MAAI,WAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,WAAU,SAAQ,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,WAAU,MAAM;AAC9D,WAAO,KAAK,+CAA+C;;AAI7D,MAAI,QAAQ;AACV,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,KAAI,MAAb,UAAQC,UAAS,MAAM,CAAA;WAClB;AACL,eAAS,KAAK,MAAM;;aAIb,oBAAe,QAAf,oBAAe,SAAA,SAAf,gBAAiB,QAAQ;AAClC,QAAM,UAAU,gBAAgB,oBAAe,QAAf,oBAAe,SAAA,SAAf,gBAAiB,MAAM;AACvD,QAAI,gBAAgB,cAAc;AAChC,eAAS,KAAI,MAAb,UAAQA,UAAS,QAAQ,SAAS,CAAA;WAC7B;AACL,eAAS,KAAI,MAAb,UAAQA,UAAS,QAAQ,SAAS,CAAA;;;AAItC,SAAAD,UAAAA,UAAA,CAAA,GACK,eAAe,GAAA,EAClB,QAAQ,SAAS,OAAO,OAAO,EAAC,CAAA;AAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCA,IAAM,YAAY;EAChB,KAAK;EACL,MAAM;EACN,MAAM;EACN,KAAK;;AAGP,IAAM,WAAW;AACjB,IAAM,cAAc,KAAK,IAAI,CAAC;AAC9B,IAAM,mBAAmB,OAAO,KAAK,SAAS,EAAE,IAAI,SAAA,MAAI;AAAI,SAAA,UAAU,IAAI;AAAd,CAAe;AAC3E,IAAM,cAAc,UAAU;AAI9B,SAAS,gBAAgB,MAAY;AACnC,SAAO,iBAAiB,SAAS,IAAI;AACvC;AAEA,IAAA;;EAAA,WAAA;AAGE,aAAAE,UAAoB,MAAoB,QAAuB;AAA3C,WAAA,OAAA;AAAoB,WAAA,SAAA;AACtC,WAAK,SAAMC,UAAA,EACT,SAAS,MACT,oBAAoB,MAAK,GACtB,KAAK,MAAM;IAElB;AAEM,IAAAD,UAAA,UAAA,UAAN,WAAA;;;;;;AACE,mBAAK,aAAa,KAAK,KAAK;AACtB,6BAA0B,CAAA;AAChC,kBAAI,CAAC,gBAAgB,KAAK,KAAK,IAAI,GAAG;AACpC,sBAAM,IAAI,WACR,eAAe,qBACf,4BAA0B,KAAK,KAAK,IAAM;;AAI1B,qBAAA,CAAA,GAAM,KAAK,eAAc,CAAE;;AAAzC,4BAAcE,IAAA,KAAA;AACL,qBAAA,CAAA,GAAM,KAAK,UAAU,WAAW,CAAC;;AAA1C,uBAASA,IAAA,KAAA;AACX,sBAAQ;AACZ,kBAAI,KAAK,OAAO,UAAU;AACxB,wBAAQ,KAAK,IAAI,GAAG,KAAK,OAAO,WAAW,OAAO,KAAK;;AAEzD,kBAAI,KAAK,OAAO,WAAW;AACzB,wBAAQ,KAAK,IAAI,GAAG,OAAO,KAAK,OAAO,YAAY,OAAO,MAAM;;AAElE,2BAAa,QAAQ,OAAO;AAC5B,2BAAa,SAAS,OAAO;AAET,qBAAA,CAAA,GAAM,KAAK,QAAQ,QAAQ,KAAK,CAAC;;AAA/C,4BAAcA,IAAA,KAAA;AACd,yBAAW,KAAK,OAAO,WAAW;AACxC,kBAAI,SAAS,OAAO,KAAK,KAAK,QAAQ,KAAK,OAAO,oBAAoB;AACpE,uBAAA,CAAA,GAAO;kBACL,MAAM,KAAK;kBACX,OAAO,aAAa;kBACpB,QAAQ,aAAa;iBACtB;;AAGH,qBAAA,CAAA,GAAO;gBACL,MAAM;gBACN,OAAO,YAAY;gBACnB,QAAQ,YAAY;eACrB;;;;;AAGH,IAAAF,UAAA,UAAA,QAAA,SAAM,KAA+B,OAAe,QAAc;AAEhE,UAAI,KAAK,eAAe,aAAa;AACnC,YAAI,YAAY;AAChB,YAAI,SAAS,GAAG,GAAG,OAAO,MAAM;aAC3B;AACL,YAAI,UAAU,GAAG,GAAG,OAAO,MAAM;;IAErC;AAGA,IAAAA,UAAA,UAAA,iBAAA,WAAA;AAAA,UAAA,QAAA;AACE,aAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,YAAM,MAAM,gBAAgB,MAAK,IAAI;AACrC,YAAM,MAAM,IAAI,MAAK;AACrB,YAAI,SAAS,WAAA;AACX,kBAAQ,GAAG;QACb;AACA,YAAI,UAAU,WAAA;AACZ,iBAAO,kBAAkB;QAC3B;AACA,YAAI,MAAM;MACZ,CAAC;IACH;AAEA,IAAAA,UAAA,UAAA,YAAA,SAAU,KAAqB;AAA/B,UAAA,QAAA;AACE,aAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,YAAM,UAAU,OAAO,WAAW,IAAI;AAEtC,YAAI,CAAC,SAAS;AACZ,iBAAO,IAAI,WACT,eAAe,wBACf,iBAAiB,CAClB;AACD;;AAGM,YAAA,QAAkB,IAAG,OAAd,SAAW,IAAG;AAC7B,eAAO,SAAS;AAChB,eAAO,QAAQ;AAEf,cAAK,MAAM,SAAS,OAAO,MAAM;AACjC,gBAAQ,UAAU,KAAK,GAAG,CAAC;AAC3B,gBAAQ,MAAM;MAChB,CAAC;IACH;AAEM,IAAAA,UAAA,UAAA,UAAN,SAAc,QAA2B,OAAa;;;;AACpD,cAAI,UAAU,GAAG;AACf,mBAAA,CAAA,GAAO,MAAM;;AAGT,iBAAO,OAAO,WAAW,IAAI;AAC7B,kBAAQ,KAAK,IAAI,UAAU,KAAK,KAAM,IAAI,QAAS,WAAW,CAAC;AAE/D,mBAAS,KAAA,IAAA,OAAU,IAAI,KAAM;AAE7B,mBAAS,SAAS,cAAc,QAAQ;AACxC,iBAAO,OAAO,WAAW,IAAI;AAE7B,kBAAkB,OAAM,OAAjB,SAAW,OAAM;AACxB,wBAAc;AACd,yBAAe;AACrB,iBAAO,QAAQ;AACf,iBAAO,SAAS;AAChB,cAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,kBAAM,IAAI,WACR,eAAe,wBACf,4BAA4B;;AAMhC,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAE1B,iBAAK,QAAQ,SAAS;AACtB,iBAAK,SAAS,SAAS;AAE3B,gBAAI,MAAM,QAAQ,GAAG;AACnB,mBAAK,cAAc;AACnB,mBAAK,eAAe;;AAGtB,gBAAI,IAAI,MAAM,GAAG;AACf,oBAAM;AACN,wBAAU;mBACL;AACL,oBAAM;AACN,wBAAU;;AAGZ,iBAAK,MAAM,SAAS,OAAO,MAAM;AACjC,oBAAQ,UAAU,KAAK,GAAG,GAAG,OAAO,QAAQ,GAAG,GAAG,IAAI,EAAE;AACxD,oBAAQ;AACR,qBAAS;;AAGL,mBAAS,QAAQ,SAAS,SAAS;AAEnC,iBAAO,QAAQ,aAAa,GAAG,GAAG,OAAO,MAAM;AAGrD,iBAAO,QAAQ;AACf,iBAAO,SAAS;AAGhB,kBAAQ,aAAa,MAAM,GAAG,CAAC;AAE/B,iBAAA,CAAA,GAAO,MAAM;;;;AAIf,IAAAA,UAAA,UAAA,SAAA,SAAO,QAAyB;AAC9B,UAAM,UAAU,OAAO,UAAU,KAAK,YAAY,KAAK,OAAO,OAAO;AACrE,UAAM,SAAS,KAAK,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,SAAA,MAAI;AAAI,eAAA,KAAK,WAAW,CAAC;MAAjB,CAAkB;AACnF,UAAM,OAAO,IAAI,KAAK,CAAC,IAAI,WAAW,MAAM,CAAC,GAAG,EAAE,MAAM,KAAK,WAAU,CAAE;AACzE,aAAO;IACT;AACF,WAAAA;EAAA,EA1KA;;;;ACnCA,eAAsBG,EAASC,GAAYC,GAAkBC,GAAaC,GAA8B;AAChG,QAAAC,IAAM,MAAMD,EAAO,SAAS;IAChC,UAAAF;IACA,KAAAC;IACA,MAAAF;IACA,GAAGG,EAAO;IACV,GAAGA;EAAA,CACJ;AAED,MAAIE,IAAe;AACf,SAAA,OAAOD,KAAQ,WACjBC,IAAe,EAAE,OAAOD,GAAK,SAAS,KAAI,IAE3BC,IAAAD,GAEjBC,EAAa,eAAkB,oBAAA,KAAA,GAAO,QAAA,IAAYA,EAAa,UAAU,KAClEA,EAAa;AACtB;AAEA,eAAeC,EAAS,EAAE,MAAAN,GAAM,UAAAC,GAAU,YAAAM,GAAY,SAAAC,EAAAA,GAAoC;AACxF,QAAMN,IAAM,MAAMO,GAAST,GAAMC,GAAUO,CAAO,GAC5CE,IAAQ,MAAMX,EAASC,GAAMC,GAAUC,GAAKM,CAAO;AAEzD,SAAO,IAAI,QAAQ,CAACG,GAASC,MAAU;AAGbC,IAAM,OAAOb,GAAME,GAAKQ,GAAOF,EAAQ,UAAUA,EAAQ,SAAS,EAC1D,UAAU;MACxC,KAAKM,GAAQ;AACPA,aACFP,EAAWO,EAAI,KAAK;MAExB;MACA,MAAMC,GAAQ;AACZH,UAAOG,CAAG;MACZ;MACA,MAAM,SAASD,GAAQ;AACrB,YAAIV,IAAW,EAAE,KAAKI,EAAQ,SAAS,MAAMN,GAAK,KAAAA,EAAAA;AAClD,YAAIM,EAAQ,eAAe;AACnBJ,cAAA,MAAMI,EAAQ,cAAcJ,CAAG,GACrCO,EAAQP,CAAG;AACX;QACD;AACDO,UAAQP,CAAG;MACb;IAAA,CACD;EAAA,CAEF;AACH;AAEA,eAAsBY,EAAOC,GAAkC;AACvD,QAAA,EAAE,WAAAC,EAAAA,IAAcC,GAAAA,GAChBC,IAASF,EAAU,OAAO,GAC1BV,IAAUS,EAAQ,SAClBd,IAASkB,cAAMC,kBAAUF,CAAM,GAAGZ,CAAO;AAC/C,SAAAS,EAAQ,UAAUd,GACX,MAAMG,EAASW,CAAO;AAC/B;", "names": ["undefined", "q", "hex", "SparkMD5", "QiniuErrorName", "QiniuError", "QiniuRequestError", "QiniuNetworkError", "Pool", "Subscription", "__extends", "Subscriber", "Observable", "_a", "__assign", "SparkMD5", "__assign", "__assign", "Base", "__assign", "_a", "__extends", "Resume", "_a", "__awaiter", "__assign", "CRC32", "_a", "__extends", "Direct", "_a", "<PERSON><PERSON>", "__spread", "Host", "HostPool", "__spread", "_a", "_a", "__assign", "__spread", "Compress", "__assign", "_a", "getToken", "file", "fileName", "key", "config", "ret", "tokenWrapper", "doUpload", "onProgress", "options", "buildKey", "token", "resolve", "reject", "qiniu", "res", "err", "upload", "context", "getConfig", "useUploader", "global", "merge", "cloneDeep"]}