<template>
  <div class="staff-analytics-dashboard">
    <!-- 搜索区域 -->
    <div class="mb-6 p-4 bg-white rounded-lg shadow-sm">
      <div class="flex flex-wrap items-center gap-4">
        <!-- 日期范围选择 -->
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium">时间范围:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            class="w-64"
            @change="loadDashboardData"
            :shortcuts="dateShortcuts"
          />
        </div>
        
        <!-- 显示全部客服开关 -->
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium">显示全部客服:</span>
          <el-switch
            v-model="showAllStaff"
            @change="loadDashboardData"
            :disabled="loading.dashboard"
          />
          <el-tooltip content="开启后显示所有客服，包括无数据的客服" placement="top">
            <el-icon class="text-gray-400 cursor-help"><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        
        <!-- 刷新按钮 -->
        <el-button
          type="primary"
          :loading="loading.dashboard || loading.refresh"
          @click="loadDashboardData"
          :icon="Refresh"
        >
          {{ loading.dashboard ? '获取中...' : '刷新数据' }}
        </el-button>
      </div>
      
      <!-- 智能并发控制进度显示 -->
      <div v-if="loading.dashboard && asyncTask.taskId" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <el-icon class="text-blue-600 animate-spin" v-if="asyncTask.status === 'running'"><Loading /></el-icon>
            <el-icon class="text-orange-500" v-else-if="asyncTask.status === 'queued'"><Clock /></el-icon>
            <el-icon class="text-blue-500" v-else-if="asyncTask.status === 'waiting'"><UserFilled /></el-icon>
            <span class="text-sm font-medium text-blue-800">
              {{ getStatusText(asyncTask.status) }}
            </span>
          </div>
          <span class="text-xs text-blue-600">任务ID: {{ asyncTask.taskId.substring(0, 8) }}...</span>
        </div>
        
        <!-- 进度条 -->
        <el-progress
          :percentage="asyncTask.progress"
          :status="asyncTask.progress === -1 ? 'exception' : undefined"
          :stroke-width="8"
          class="mb-3"
        />
        
        <!-- 状态信息 -->
        <div class="flex items-center justify-between text-sm text-blue-700 mb-3">
          <span>{{ asyncTask.message }}</span>
          <div v-if="asyncTask.status === 'queued'" class="flex items-center gap-3 text-xs">
            <span>排队位置: {{ asyncTask.queuePosition }}</span>
            <span>预计等待: {{ Math.ceil(asyncTask.estimatedWaitTime / 60) }}分钟</span>
          </div>
        </div>
        
        <!-- 智能并发控制说明 -->
        <div class="flex items-start gap-2 p-2 bg-blue-100 rounded text-xs text-blue-600">
          <el-icon class="mt-0.5 flex-shrink-0"><InfoFilled /></el-icon>
          <div>
            <div class="font-medium mb-1">智能并发控制机制</div>
            <div>系统自动管理多用户查询请求，避免数据冲突，确保查询结果的准确性和系统稳定性</div>
          </div>
        </div>
      </div>
    </div>



    <!-- 数据源状态 -->
    <div class="data-source-status" v-if="dashboardData && !loading.dashboard">
      <el-alert
        :title="`数据来源状态: 七鱼报表${dashboardData.data_sources.qiyu_reports_available ? '✓' : '✗'} | 团队概览${dashboardData.data_sources.team_overview_available ? '✓' : '✗'} | 情绪分析${dashboardData.data_sources.emotion_analysis_available ? '✓' : '✗'} | 客服数据: ${dashboardData.data_sources.total_staff_with_data}/${dashboardData.data_sources.total_staff_all}人`"
        :type="getDataSourceAlertType()"
        :closable="false"
        show-icon
        class="status-alert"
      />
    </div>

    <!-- 客服列表 -->
    <el-card class="staff-table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><DataAnalysis /></el-icon>
            <span class="header-title">客服详细数据</span>
          </div>
          <div class="header-right">
            
            <!-- 搜索 -->
            <el-input
              v-model="filterForm.search"
              placeholder="搜索客服姓名"
              clearable
              style="width: 200px; margin-right: 12px"
              @input="handleSearch"
            />
            <!-- 筛选 -->
            <el-popover
              placement="bottom-end"
              :width="320"
              trigger="click"
              title="高级筛选"
            >
              <template #reference>
                <el-button  size="default">筛选</el-button>
              </template>
              <div class="filter-panel">
                <div class="filter-item">
                  <label>综合得分范围:</label>
                  <el-slider
                    v-model="filterForm.scoreRange"
                    range
                    :min="0"
                    :max="100"
                    :step="1"
                    show-stops
                    show-input
                    @change="handleFilter"
                  />
                </div>
                <div class="filter-item">
                  <label>效率范围:</label>
                  <el-slider
                    v-model="filterForm.efficiencyRange"
                    range
                    :min="0"
                    :max="50"
                    :step="0.1"
                    show-input
                    @change="handleFilter"
                  />
                </div>
                <div class="filter-actions">
                  <el-button size="small" @click="resetFilter">重置</el-button>
                  <el-button type="primary" size="small" @click="handleFilter">应用</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredData"
        :loading="loading.dashboard"
        row-key="staff_id"
        @sort-change="handleSortChange"
        :default-sort="{prop: 'valid_sessions', order: 'descending'}"
        stripe
        size="default"
        height="600"
        class="staff-table"
        :key="tableKey"
      >
        <el-table-column prop="staff_name" label="客服姓名" width="120" align="center" fixed="left">
          <template #default="{ row }">
            <div class="staff-name-cell">
              <span :class="{ 'empty-staff': row.is_empty_record }">
                {{ row.staff_name }}
              </span>
              <el-tag v-if="row.is_empty_record" type="info" size="small" class="empty-tag">无数据</el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 暂时隐藏综合绩效列 -->
        <!-- <el-table-column prop="comprehensive_score" label="综合绩效" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <div class="score-cell">
              <el-progress
                type="circle"
                :width="50"
                :percentage="Math.round(row.comprehensive_score)"
                :color="getScoreColor(row.comprehensive_score)"
              />
              <span class="score-text">{{ row.comprehensive_score.toFixed(1) }}</span>
            </div>
          </template>
        </el-table-column> -->

        <el-table-column prop="satisfaction_ratio" label="满意度" width="100" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getSatisfactionType(row.satisfaction_ratio)" size="small">
              {{ row.satisfaction_ratio.toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="valid_sessions" label="有效会话" width="100" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip :content="`总会话: ${formatNumber(row.total_sessions)} | 有效会话: ${formatNumber(row.valid_sessions)}`" placement="top">
              <span class="session-count">{{ formatNumber(row.valid_sessions) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="emotion_contribution_per_hour" label="每小时情绪贡献度" width="140" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip placement="top" effect="dark">
              <template #content>
                <div class="emotion-tooltip">
                  <div class="tooltip-title">{{ row.staff_name }} 情绪贡献详情</div>
                  <div class="tooltip-content">
                    <div class="tooltip-row">
                      <span class="label">分析次数:</span>
                      <span class="value">{{ row.emotion_analysis_count || 0 }}</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">平均情绪改善:</span>
                      <span class="value">{{ (row.avg_emotion_change || 0).toFixed(3) }}</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">在线时长:</span>
                      <span class="value">{{ (row.online_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">每小时贡献度:</span>
                      <span class="value primary">{{ (row.emotion_contribution_per_hour || 0).toFixed(4) }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <span class="emotion-value">{{ (row.emotion_contribution_per_hour || 0).toFixed(3) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="emotion_contribution" label="总计情绪贡献度" width="140" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip placement="top" effect="dark">
              <template #content>
                <div class="emotion-tooltip">
                  <div class="tooltip-title">{{ row.staff_name }} 总计情绪贡献</div>
                  <div class="tooltip-content">
                    <div class="tooltip-row">
                      <span class="label">总计贡献度:</span>
                      <span class="value primary">{{ (row.emotion_contribution || 0).toFixed(4) }}</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">分析次数:</span>
                      <span class="value">{{ row.emotion_analysis_count || 0 }}</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">平均变化:</span>
                      <span class="value">{{ (row.avg_emotion_change || 0).toFixed(3) }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <span class="emotion-value">{{ (row.emotion_contribution || 0).toFixed(3) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="online_duration" label="在线时长" width="100" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip placement="top" effect="dark">
              <template #content>
                <div class="time-tooltip">
                  <div class="tooltip-title">{{ row.staff_name }} 时间详情</div>
                  <div class="tooltip-content">
                    <div class="tooltip-row">
                      <span class="label">登录时长:</span>
                      <span class="value">{{ (row.login_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">在线时长:</span>
                      <span class="value primary">{{ (row.online_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">小休时长:</span>
                      <span class="value warning">{{ (row.rest_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">挂起时长:</span>
                      <span class="value danger">{{ (row.pend_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">空闲时长:</span>
                      <span class="value">{{ (row.resume_free_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">挂机时长:</span>
                      <span class="value">{{ (row.hang_session_duration || 0).toFixed(1) }}h</span>
                    </div>
                  </div>
                </div>
              </template>
              <span class="time-value">{{ (row.online_duration || 0).toFixed(1) }}h</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="hourly_efficiency" label="小时效率" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip placement="top" effect="dark">
              <template #content>
                <div class="efficiency-tooltip">
                  <div class="tooltip-title">{{ row.staff_name }} 效率详情</div>
                  <div class="tooltip-content">
                    <div class="tooltip-row">
                      <span class="label">有效会话数:</span>
                      <span class="value">{{ row.valid_sessions || 0 }}</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">在线时长:</span>
                      <span class="value">{{ (row.online_duration || 0).toFixed(1) }}h</span>
                    </div>
                    <div class="tooltip-row">
                      <span class="label">小时效率:</span>
                      <span class="value primary">{{ (row.hourly_efficiency || 0).toFixed(2) }}单/小时</span>
                    </div>
                    <div v-if="(row.hourly_efficiency || 0) > 50" class="tooltip-warning">
                      <span class="warning-text">⚠️ 效率值较高，可能存在数据异常</span>
                    </div>
                  </div>
                </div>
              </template>
              <div class="efficiency-container">
                <span class="efficiency-value" :class="{ 'abnormal': (row.hourly_efficiency || 0) > 50 }">
                  {{ (row.hourly_efficiency || 0).toFixed(1) }}
                </span>
                <el-icon v-if="(row.hourly_efficiency || 0) > 50" class="warning-icon" color="#E6A23C">
                  <QuestionFilled />
                </el-icon>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="one_off_ratio" label="一次解决率" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            {{ row.one_off_ratio.toFixed(1) }}%
          </template>
        </el-table-column>

        <el-table-column prop="response_metrics" label="响应指标" width="140" align="center">
          <template #default="{ row }">
            <div class="response-metrics">
              <div class="metric-item">
                <span class="metric-label">首响:</span>
                <span class="metric-value">{{ ((row.avg_first_resp_time || 0) / 1000).toFixed(1) }}s</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">平响:</span>
                <span class="metric-value">{{ ((row.avg_resp_time || 0) / 1000).toFixed(1) }}s</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="quality_metrics" label="质量指标" width="180" align="center">
          <template #default="{ row }">
            <div class="quality-metrics">
              <div class="metric-row">
                <el-tag size="small" :type="row.satisfaction_ratio >= 80 ? 'success' : row.satisfaction_ratio >= 60 ? 'warning' : 'danger'">
                  满意: {{ (row.satisfaction_ratio || 0).toFixed(1) }}%
                </el-tag>
                <el-tag size="small" :type="row.reply_ratio >= 80 ? 'success' : row.reply_ratio >= 60 ? 'warning' : 'danger'">
                  应答: {{ (row.reply_ratio || 0).toFixed(1) }}%
                </el-tag>
              </div>
              <div class="metric-row">
                <el-tag size="small" :type="row.one_off_ratio >= 60 ? 'success' : row.one_off_ratio >= 40 ? 'warning' : 'danger'">
                  解决: {{ (row.one_off_ratio || 0).toFixed(1) }}%
                </el-tag>
                <el-tag size="small" :type="row.eva_ratio >= 50 ? 'success' : row.eva_ratio >= 30 ? 'warning' : 'danger'">
                  参评: {{ (row.eva_ratio || 0).toFixed(1) }}%
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="emotion_analysis_count" label="情绪分析" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tooltip 
              :content="`正向变化: ${row.positive_emotion_changes || 0} | 负向变化: ${row.negative_emotion_changes || 0} | 平均变化: ${(row.avg_emotion_change || 0).toFixed(2)}`" 
              placement="top"
            >
              <div class="emotion-summary">
                <div class="emotion-count">{{ row.emotion_analysis_count || 0 }}</div>
                <div class="emotion-trend" :class="getEmotionTrendClass(row.avg_emotion_change)">
                  {{ getEmotionTrendText(row.avg_emotion_change) }}
                </div>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" fixed="right" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="showStaffDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 客服详情弹窗 -->
    <StaffDetailDialog
      v-model:visible="detailDialog.visible"
      :staff-data="detailDialog.data"
      :loading="detailDialog.loading"
      :date-range="currentDateRange"
      @refresh="loadStaffDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  Refresh,
  View,
  QuestionFilled,
  InfoFilled,
  Loading,
  Clock,
  UserFilled
} from '@element-plus/icons-vue';

import * as api from './api';
import type { 
  DashboardData, TeamSummary, StaffMetrics, 
  LoadingState, FilterState, StaffDetailDialogState 
} from './types';
import StaffDetailDialog from './components/StaffDetailDialog.vue';

// 响应式数据
const loading = reactive<LoadingState>({
  dashboard: false,
  staffDetail: false,
  refresh: false
});

const dashboardData = ref<DashboardData | null>(null);
const detailDialog = reactive<StaffDetailDialogState>({
  visible: false,
  data: null,
  loading: false
});

// 异步任务相关
const asyncTask = reactive({
  taskId: '',
  status: '',
  progress: 0,
  message: '',
  queuePosition: 0,
  estimatedWaitTime: 0
});

// 表格刷新key
const tableKey = ref(0);

// 日期范围
const dateRange = ref<string[]>([]);
const currentDateRange = computed(() => ({
  start_date: dateRange.value[0],
  end_date: dateRange.value[1]
}));

// 显示控制
const showAllStaff = ref(false); // 是否显示全部客服（包括0会话的）

// 筛选后的数据（使用ref确保响应性）
const filteredData = ref<StaffMetrics[]>([]);

// 筛选状态
const filterForm = reactive<FilterState>({
  search: '',
  scoreRange: [0, 100] as [number, number],
  efficiencyRange: [0, 50] as [number, number]
});

// 排序状态
const sortState = ref({
  prop: '',
  order: ''
});

// 计算属性
const teamSummary = computed<TeamSummary | null>(() => {
  return dashboardData.value?.team_summary || null;
});

const staffList = computed<StaffMetrics[]>(() => {
  return dashboardData.value?.staff_list || [];
});

// 日期快捷选择
const dateShortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = new Date();
      end.setDate(end.getDate() - 1); // 昨天
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return [start, end];
    }
  },
  {
    text: '最近15天',
    value: () => {
      const end = new Date();
      end.setDate(end.getDate() - 1);
      const start = new Date();
      start.setDate(start.getDate() - 15);
      return [start, end];
    }
  },
  {
    text: '最近31天',
    value: () => {
      const end = new Date();
      end.setDate(end.getDate() - 1);
      const start = new Date();
      start.setDate(start.getDate() - 31);
      return [start, end];
    }
  }
];

const initDefaultDate = () => {
  const end = new Date();
  end.setDate(end.getDate() - 1); // 昨天
  const start = new Date();
  start.setDate(start.getDate() - 1);
  
  dateRange.value = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ];
};

// 方法
const disabledDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() >= today.getTime();
};

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

const getScoreColor = (score: number): string => {
  if (score >= 80) return '#67C23A';
  if (score >= 60) return '#E6A23C';
  return '#F56C6C';
};

const getSatisfactionType = (ratio: number) => {
  if (ratio >= 90) return 'success';
  if (ratio >= 80) return 'warning';
  return 'danger';
};

const getEmotionClass = (contribution: number) => {
  if (contribution > 0) return 'emotion-positive';
  if (contribution < 0) return 'emotion-negative';
  return 'emotion-neutral';
};

const getDataSourceAlertType = () => {
  if (!dashboardData.value) return 'info';
  const sources = dashboardData.value.data_sources;
  if (sources.qiyu_reports_available && sources.emotion_analysis_available) {
    return 'success';
  }
  if (sources.qiyu_reports_available || sources.emotion_analysis_available) {
    return 'warning';
  }
  return 'error';
};

const getEmotionTrendClass = (change: number): string => {
  if (change > 0.5) return 'trend-positive';
  if (change < -0.5) return 'trend-negative';
  return 'trend-neutral';
};

const getEmotionTrendText = (change: number): string => {
  if (change > 0.5) return '↗ 提升';
  if (change < -0.5) return '↘ 下降'; 
  return '→ 稳定';
};

const getStatusText = (status: string): string => {
  switch (status) {
    case 'running':
      return '数据获取中';
    case 'queued':
      return '队列等待中';
    case 'waiting':
      return '等待其他任务完成';
    default:
      return '未知状态';
  }
};

// 加载Dashboard数据
const loadDashboardData = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) return;
  
  loading.dashboard = true;
  
  try {
    const response = await api.getDashboardData({
      start_date: dateRange.value[0],
      end_date: dateRange.value[1],
      show_all: showAllStaff.value,
      async_mode: true  // 默认启用异步模式
    });
    
    if (response.code === 2000) {
      if (response.data.task_id) {
        // 异步模式：开始轮询任务状态
        asyncTask.taskId = response.data.task_id;
        asyncTask.status = response.data.status;
        asyncTask.progress = response.data.progress || 0;
        asyncTask.message = response.data.message || '';
        asyncTask.queuePosition = response.data.queue_position || 0;
        asyncTask.estimatedWaitTime = response.data.estimated_wait_time || 0;
        
        if (response.data.status === 'queued') {
          ElMessage.info(`任务已加入队列，排队位置: ${asyncTask.queuePosition}`);
        } else if (response.data.status === 'running') {
          ElMessage.info('任务已开始执行');
        } else if (response.data.status === 'waiting') {
          ElMessage.info('检测到相同查询正在执行，已自动加入等待队列');
        }
        
        // 开始轮询
        startTaskPolling();
      } else {
        // 缓存数据直接返回
        dashboardData.value = response.data;
        loading.dashboard = false;
        
        // 🔧 确保数据更新后立即应用筛选
        nextTick(() => {
          applyFilter();
          // 强制刷新表格
          tableKey.value++;
        });
        
        ElMessage.success(response.msg || '数据加载成功');
      }
    } else {
      loading.dashboard = false;
      ElMessage.error(response.msg || '数据加载失败');
    }
  } catch (error) {
    loading.dashboard = false;
    ElMessage.error('数据加载失败');
  }
};

// 轮询任务状态
const startTaskPolling = () => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await api.getTaskStatus(asyncTask.taskId);
      
      if (response.code === 2000) {
        const taskStatus = response.data;
        
        asyncTask.status = taskStatus.status;
        asyncTask.progress = taskStatus.progress || 0;
        asyncTask.message = taskStatus.message || '';
        asyncTask.queuePosition = taskStatus.queue_position || 0;
        asyncTask.estimatedWaitTime = taskStatus.estimated_wait_time || 0;
        
        if (taskStatus.status === 'completed') {
          // 任务完成
          clearInterval(pollInterval);
          
          // 🔧 确保数据完全更新
          dashboardData.value = taskStatus.result;
          loading.dashboard = false;
          
          // 立即应用筛选
          nextTick(() => {
            applyFilter();
            // 强制刷新表格
            tableKey.value++;
          });
          
          ElNotification({
            title: '数据获取完成',
            message: '客服分析数据已更新',
            type: 'success',
            duration: 3000
          });
          
          // 重置异步任务状态
          Object.assign(asyncTask, {
            taskId: '',
            status: '',
            progress: 0,
            message: '',
            queuePosition: 0,
            estimatedWaitTime: 0
          });
          
        } else if (taskStatus.status === 'not_found') {
          // 任务不存在或已过期
          clearInterval(pollInterval);
          loading.dashboard = false;
          ElMessage.error('任务已过期或不存在');
          
        } else if (taskStatus.progress === -1) {
          // 任务失败
          clearInterval(pollInterval);
          loading.dashboard = false;
          ElMessage.error(`任务失败: ${taskStatus.message}`);
        }
        // waiting和其他状态继续轮询
      }
    } catch (error) {
      clearInterval(pollInterval);
      loading.dashboard = false;
      ElMessage.error('获取任务状态失败');
    }
  }, 2000); // 每2秒轮询一次
  
  // 设置最大轮询时间（10分钟）
  setTimeout(() => {
    clearInterval(pollInterval);
    if (loading.dashboard) {
      loading.dashboard = false;
      ElMessage.error('任务超时，请重新尝试');
    }
  }, 600000);
};

// 加载客服详情
const loadStaffDetail = async (staffId: string) => {
  detailDialog.loading = true;
  try {
    const response = await api.getStaffDetail({
      staff_id: staffId,
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    });
    
    if (response.code === 2000) {
      // 直接使用后端返回的完整数据结构
      detailDialog.data = response.data;
    } else {
      ElMessage.error(response.msg || '详情加载失败');
    }
  } catch (error) {
    ElMessage.error('详情加载失败');
  } finally {
    detailDialog.loading = false;
  }
};

// 事件处理
const handleDateChange = () => {
  loadDashboardData();
};

const handleRefresh = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) {
    ElMessage.warning('请先选择日期范围');
    return;
  }
  
  loading.refresh = true;
  try {
    // 先刷新缓存
    await api.refreshCache({
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    });
    
    // 再重新加载数据
    await loadDashboardData();
    
    ElNotification({
      title: '刷新成功',
      message: '数据已更新到最新状态',
      type: 'success'
    });
  } catch (error) {
    // 错误已记录
    ElMessage.error('刷新失败');
  } finally {
    loading.refresh = false;
  }
};

const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};


// 处理搜索
const handleSearch = () => {
  // 立即应用筛选
  applyFilter();
};

const handleFilter = () => {
  // 立即应用筛选
  applyFilter();
};

const resetFilter = () => {
  filterForm.search = '';
  filterForm.scoreRange = [0, 100];
  filterForm.efficiencyRange = [0, 50];
  // 重置后立即应用筛选
  applyFilter();
};

const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  sortState.value.prop = prop || '';
  sortState.value.order = order || '';
  // 排序状态已更新，立即应用筛选
  applyFilter();
};

const showStaffDetail = (staff: StaffMetrics) => {
  detailDialog.visible = true;
  loadStaffDetail(staff.staff_id);
};

// 生命周期
onMounted(() => {
  initDefaultDate();
  // 确保数据加载完成后应用筛选
  nextTick(async () => {
    await loadDashboardData();
    // 数据加载完成后，筛选逻辑会自动通过computed属性生效
  });
});

// 手动筛选函数
const applyFilter = () => {
  let result = staffList.value;
  
  // 🔧 新增：过滤掉效率大于50的异常数据
  result = result.filter(staff => 
    staff.hourly_efficiency <= 50  // 过滤掉效率大于50的异常数据
  );
  
  // 搜索过滤
  if (filterForm.search) {
    const search = filterForm.search.toLowerCase();
    result = result.filter(staff => 
      staff.staff_name.toLowerCase().includes(search) ||
      staff.staff_account.toLowerCase().includes(search)
    );
  }
  
  // 分数范围过滤
  result = result.filter(staff => 
    staff.comprehensive_score >= filterForm.scoreRange[0] &&
    staff.comprehensive_score <= filterForm.scoreRange[1]
  );
  
  // 效率范围过滤（已经预过滤了>50的数据）
  result = result.filter(staff => 
    staff.hourly_efficiency >= filterForm.efficiencyRange[0] &&
    staff.hourly_efficiency <= filterForm.efficiencyRange[1]
  );
  
  // 排序处理
  if (sortState.value.prop && sortState.value.order) {
    const { prop, order } = sortState.value;
    result.sort((a, b) => {
      const aVal = a[prop as keyof StaffMetrics] as number;
      const bVal = b[prop as keyof StaffMetrics] as number;
      
      if (order === 'ascending') {
        return aVal - bVal;
      } else {
        return bVal - aVal;
      }
    });
  } else {
    // 默认按有效会话数降序排列
    result.sort((a, b) => (b.valid_sessions || 0) - (a.valid_sessions || 0));
  }
  
  filteredData.value = result;
};
</script>

<style scoped>
.staff-analytics-dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 头部样式 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.title-icon {
  color: #409EFF;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}



/* 数据源状态 */
.data-source-status {
  margin-bottom: 24px;
}

.status-alert {
  border-radius: 8px;
}

/* 客服表格样式 */
.staff-table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409EFF;
}

.header-title {
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 表格单元格样式 */
.staff-name-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.empty-staff {
  color: #909399;
  font-style: italic;
}

.empty-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
}

.score-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
}

.emotion-positive { color: #67C23A; font-weight: 500; }
.emotion-negative { color: #F56C6C; font-weight: 500; }
.emotion-neutral { color: #909399; }

/* 筛选面板 */
.filter-panel {
  padding: 8px;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .staff-analytics-dashboard {
    padding: 12px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-left, .header-right {
    justify-content: center;
  }
  

}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .staff-analytics-dashboard {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}

.session-count {
  font-weight: 600;
  color: #409EFF;
}

.efficiency-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.efficiency-value {
  font-weight: 600;
}

.efficiency-value.abnormal {
  color: #E6A23C;
}

.warning-icon {
  font-size: 14px;
  cursor: help;
}

/* tooltip 警告样式 */
.tooltip-warning {
  margin-top: 8px;
  padding: 6px 8px;
  background: rgba(230, 162, 60, 0.1);
  border-radius: 4px;
  border-left: 3px solid #E6A23C;
}

.warning-text {
  color: #E6A23C;
  font-size: 12px;
  font-weight: 500;
}

.duration-value {
  font-weight: 600;
  color: #E6A23C;
}

.response-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #909399;
  font-size: 11px;
}

.metric-value {
  font-weight: 600;
  color: #303133;
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.metric-row {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.emotion-summary {
  text-align: center;
}

.emotion-count {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.emotion-trend {
  font-size: 11px;
  font-weight: 500;
  margin-top: 2px;
}

.trend-positive {
  color: #67C23A;
}

.trend-negative {
  color: #F56C6C;
}

.trend-neutral {
  color: #909399;
}

.attendance-tooltip {
  max-width: 300px;
}

.tooltip-title {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.tooltip-content {
  color: #ffffff;
}

.tooltip-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.tooltip-row:last-child {
  margin-bottom: 0;
}

.tooltip-row .label {
  color: rgba(255, 255, 255, 0.8);
}

.tooltip-row .value {
  color: #ffffff;
  font-weight: 500;
}

.show-all-switch {
  display: flex;
  align-items: center;
  margin-right: 16px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
  font-size: 12px;
  color: #606266;
}

.show-all-switch .el-switch {
  margin-left: 8px;
}
</style> 