# Generated by Django 4.2.7 on 2025-07-23 14:12

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('cs_manage', '0008_dailymetricgroup_data_version_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='avg_first_resp',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='eva_count',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='eva_ratio',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='fcr_ratio',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='invite_count',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='manual_satisfaction',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='online_ratio',
        ),
        migrations.RemoveField(
            model_name='dailymetricgroup',
            name='resp_30_ratio',
        ),
    ]
