from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Av<PERSON>, <PERSON><PERSON>, <PERSON>, Q
from rest_framework.decorators import action
from rest_framework.response import Response
from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from dvadmin.utils.viewset import CustomModelViewSet
from apps.cs_manage.models import CsQualityCheck, CsQualityDeduction
from apps.cs_manage.serializers.ai_cs_qc import (
    CsQcExportSerializer, CsQcListSerializer, CsQcDetailSerializer
)
import json


class CsQualityCheckViewSet(CustomModelViewSet):
    """
    客服质检新版本视图集 - 修正版本
    使用CustomModelViewSet的标准分页和返回格式
    """
    queryset = CsQualityCheck.objects.all()
    ordering = ['-create_datetime']
    
    # 导出配置
    export_field_label = {
        'session_id': '会话ID',
        'service_name': '客服姓名',
        'service_id': '客服ID',
        'service_account': '客服账号',
        'game_name': '游戏',
        'overall_score': '总分',
        'total_deductions': '总扣分',
        'session_duration': '会话时长(分钟)',
        'session_start_time_str': '会话开始时间',
        'session_end_time_str': '会话结束时间',
        'create_datetime_str': '质检时间',
        'analysis_summary': '分析摘要',
        'deductions_text': '扣分项',
        'service_performance_text': '客服表现'
    }
    export_serializer_class = CsQcExportSerializer
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CsQcListSerializer
        elif self.action == 'retrieve':
            return CsQcDetailSerializer
        return super().get_serializer_class()
    
    def get_queryset(self):
        """
        重构后的查询集构建方法
        前置获取所有查询参数，统一构建查询条件
        """
        queryset = super().get_queryset()
        
        # 前置获取所有可能的查询参数
        query_params = self._extract_query_params()
        
        # 构建查询条件
        filters = self._build_filters(query_params)
        
        # 应用查询条件
        if filters:
            queryset = queryset.filter(**filters)
        
        # 处理特殊筛选条件（需要distinct的）
        queryset = self._apply_special_filters(queryset, query_params)
        
        return queryset
    
    def _extract_query_params(self):
        """
        提取所有查询参数
        """
        params = self.request.query_params
        
        return {
            # 时间范围参数
            'start_date': params.get('start_date'),
            'end_date': params.get('end_date'),
            'created_datetime_0': params.get('created_datetime[0]'),
            'created_datetime_1': params.get('created_datetime[1]'),
            
            # 基础筛选参数
            'game_id': params.get('game_id'),
            'service_id': params.get('service_id'),
            'session_id': params.get('session_id'),
            'user_id': params.get('user_id'),
            'deduction_item': params.get('deduction_item'),
            'only_deductions': params.get('only_deductions'),
        }
    
    def _build_filters(self, query_params):
        """
        构建查询条件字典
        """
        filters = {}
        
        # 处理时间范围筛选
        start_date, end_date = self._parse_datetime_params(query_params)
        
        if start_date:
            filters['create_datetime__gte'] = start_date
        
        if end_date:
            filters['create_datetime__lt'] = end_date
        
        # 处理基础筛选条件
        if query_params['game_id']:
            filters['game_id'] = query_params['game_id']
        
        if query_params['service_id']:
            filters['service_id'] = query_params['service_id']
        
        if query_params['session_id']:
            filters['session_id__icontains'] = query_params['session_id']
        
        if query_params['user_id']:
            filters['service_user_id'] = query_params['user_id']
        
        if query_params['only_deductions'] == 'true':
            filters['total_deductions__gt'] = 0
        
        return filters
    
    def _parse_datetime_params(self, query_params):
        """
        解析时间参数，支持多种格式
        """
        start_date = query_params['start_date']
        end_date = query_params['end_date']
        
        # 处理 created_datetime 数组参数
        if query_params['created_datetime_0'] and query_params['created_datetime_1']:
            start_date = query_params['created_datetime_0']
            end_date = query_params['created_datetime_1']
        
        # 解析开始时间
        start_dt = None
        if start_date:
            start_dt = self._parse_datetime_string(start_date)
        
        # 解析结束时间
        end_dt = None
        if end_date:
            end_dt = self._parse_datetime_string(end_date)
            # 如果是纯日期格式，需要加一天
            if end_dt and 'T' not in end_date:
                end_dt = end_dt + timedelta(days=1)
        
        return start_dt, end_dt
    
    def _parse_datetime_string(self, date_string):
        """
        解析时间字符串，支持多种格式
        """
        if not date_string:
            return None
        
        try:
            # 处理 ISO 格式时间字符串（如 2025-07-01T16:00:00.000Z）
            if 'T' in date_string:
                # 移除时区信息并解析
                date_clean = date_string.split('T')[0] + ' ' + date_string.split('T')[1].split('.')[0]
                return datetime.strptime(date_clean, '%Y-%m-%d %H:%M:%S')
            else:
                # 尝试解析纯日期格式
                return datetime.strptime(date_string, '%Y-%m-%d')
        except ValueError:
            try:
                # 尝试解析带时间的格式
                return datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return None
    
    def _apply_special_filters(self, queryset, query_params):
        """
        应用特殊筛选条件（需要distinct等特殊处理的）
        """
        # 扣分项筛选（需要distinct）
        if query_params['deduction_item']:
            queryset = queryset.filter(deductions__item_name__icontains=query_params['deduction_item']).distinct()
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """
        质检看板数据
        """
        try:
            # 临时设置request为self.request以便复用参数提取逻辑
            original_request = self.request
            self.request = request
            
            # 复用参数提取和查询构建逻辑
            query_params = self._extract_query_params()
            filters = self._build_filters(query_params)
            
            # 构建查询集
            queryset = CsQualityCheck.objects.all()
            
            # 应用查询条件
            if filters:
                queryset = queryset.filter(**filters)
            
            # 处理特殊筛选条件
            queryset = self._apply_special_filters(queryset, query_params)
            
            # 恢复原始request
            self.request = original_request
            
            # 总体统计
            total_stats = queryset.aggregate(
                total_sessions=Count('id'),
                avg_score=Avg('overall_score'),
                total_deductions=Sum('total_deductions'),
                avg_duration=Avg('session_duration')
            )
            
            # 计算有扣分的会话数
            sessions_with_deductions = queryset.filter(total_deductions__gt=0).count()
            total_stats['sessions_with_deductions'] = sessions_with_deductions
            
            # 客服排行榜
            service_ranking = queryset.values(
                'service_id', 'service_name'
            ).annotate(
                session_count=Count('id'),
                avg_score=Avg('overall_score'),
                total_deductions=Sum('total_deductions')
            ).order_by('-avg_score')[:10]
            
            # 分数分布
            score_distribution = {
                'excellent': queryset.filter(overall_score__gte=90).count(),
                'good': queryset.filter(overall_score__gte=80, overall_score__lt=90).count(),
                'average': queryset.filter(overall_score__gte=70, overall_score__lt=80).count(),
                'poor': queryset.filter(overall_score__lt=70).count()
            }
            
            # 扣分项统计 (Top 10)
            deduction_stats = CsQualityDeduction.objects.filter(
                quality_check__in=queryset
            ).values('item_name').annotate(
                count=Count('id'),
                total_deduction=Sum('deduction_score'),
                avg_deduction=Avg('deduction_score')
            ).order_by('-count')[:10]
            
            # 游戏分布统计
            game_stats = queryset.filter(
                game_id__isnull=False
            ).values('game_id').annotate(
                session_count=Count('id'),
                avg_score=Avg('overall_score')
            ).order_by('-session_count')
            
            # 获取游戏名称
            try:
                from apps.kcs.models import Game
                game_dict = {g.id: g.name for g in Game.objects.all()}
                for stat in game_stats:
                    stat['game_name'] = game_dict.get(stat['game_id'], '未知游戏')
            except ImportError:
                for stat in game_stats:
                    stat['game_name'] = f'游戏{stat["game_id"]}'
            
            return DetailResponse(data={
                'total_stats': total_stats,
                'service_ranking': list(service_ranking),
                'score_distribution': score_distribution,
                'deduction_stats': list(deduction_stats),
                'game_stats': list(game_stats)
            })
            
        except Exception as e:
            return ErrorResponse(msg=f"获取看板数据失败: {str(e)}")
    
    # 注意：service_list方法已废弃，数据统一从dashboard接口获取
    
    # 注意：service_detail、deduction_stats、trend方法已废弃，数据统一从dashboard接口获取
    
    @action(detail=False, methods=['get'])
    def user_list(self, request):
        """
        有质检记录的用户列表
        """
        try:
            from dvadmin.system.models import Users
            
            # 获取有质检记录的用户
            user_ids = CsQualityCheck.objects.filter(
                service_user_id__isnull=False
            ).values_list('service_user_id', flat=True).distinct()
            
            users = Users.objects.filter(id__in=user_ids).values('id', 'name', 'username')
            
            return DetailResponse(data=list(users))
            
        except Exception as e:
            return ErrorResponse(msg=f"获取用户列表失败: {str(e)}")
    
    @action(detail=False, methods=['get'])
    def game_list(self, request):
        """
        游戏列表
        """
        try:
            from apps.kcs.models import Game
            
            games = Game.objects.all().values('id', 'name')
            
            return DetailResponse(data=list(games))
            
        except ImportError:
            # 如果没有Game模型，返回空列表
            return DetailResponse(data=[])
        except Exception as e:
            return ErrorResponse(msg=f"获取游戏列表失败: {str(e)}")
    
    @action(detail=False, methods=['post'])
    def manual_check(self, request):
        """
        手动触发质检
        """
        try:
            session_id = request.data.get('session_id')
            if not session_id:
                return ErrorResponse(msg="缺少session_id参数")
            
            force_reprocess = request.data.get('force_reprocess', False)
            
            # 调用质检处理函数
            try:
                from apps.cs_manage.utils.LLM_auto_qc import process_session_for_qc
                success = process_session_for_qc(session_id, force_reprocess)
                
                if success:
                    return DetailResponse(msg="质检处理成功")
                else:
                    return ErrorResponse(msg="质检处理失败")
            except ImportError:
                return ErrorResponse(msg="质检处理模块未找到")
            
        except Exception as e:
            return ErrorResponse(msg=f"手动质检失败: {str(e)}")