import {
  e
} from "./chunk-KVKFRDRF.js";
import {
  Me,
  bt,
  he
} from "./chunk-WQP6ZAOK.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import {
  forEach_default,
  merge_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  createBaseVNode,
  createElementBlock,
  defineComponent,
  openBlock,
  vModelText,
  withDirectives
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/index-47ee27c0.mjs
var Nt = { exports: {} };
(function(et, ot) {
  (function(n, t) {
    et.exports = t();
  })(window, function() {
    return (
      /******/
      function(s) {
        var n = {};
        function t(e2) {
          if (n[e2])
            return n[e2].exports;
          var a = n[e2] = {
            /******/
            i: e2,
            /******/
            l: false,
            /******/
            exports: {}
            /******/
          };
          return s[e2].call(a.exports, a, a.exports, t), a.l = true, a.exports;
        }
        return t.m = s, t.c = n, t.d = function(e2, a, r) {
          t.o(e2, a) || Object.defineProperty(e2, a, { enumerable: true, get: r });
        }, t.r = function(e2) {
          typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
        }, t.t = function(e2, a) {
          if (a & 1 && (e2 = t(e2)), a & 8 || a & 4 && typeof e2 == "object" && e2 && e2.__esModule)
            return e2;
          var r = /* @__PURE__ */ Object.create(null);
          if (t.r(r), Object.defineProperty(r, "default", { enumerable: true, value: e2 }), a & 2 && typeof e2 != "string")
            for (var o in e2)
              t.d(r, o, (function(v) {
                return e2[v];
              }).bind(null, o));
          return r;
        }, t.n = function(e2) {
          var a = e2 && e2.__esModule ? (
            /******/
            function() {
              return e2.default;
            }
          ) : (
            /******/
            function() {
              return e2;
            }
          );
          return t.d(a, "a", a), a;
        }, t.o = function(e2, a) {
          return Object.prototype.hasOwnProperty.call(e2, a);
        }, t.p = "", t(t.s = 141);
      }([
        /* 0 */
        /***/
        function(s, n) {
          function t(e2) {
            return e2 && e2.__esModule ? e2 : {
              default: e2
            };
          }
          s.exports = t;
        },
        /* 1 */
        /***/
        function(s, n, t) {
          s.exports = t(142);
        },
        /* 2 */
        /***/
        function(s, n, t) {
          t.r(n), t.d(n, "__extends", function() {
            return a;
          }), t.d(n, "__assign", function() {
            return r;
          }), t.d(n, "__rest", function() {
            return o;
          }), t.d(n, "__decorate", function() {
            return v;
          }), t.d(n, "__param", function() {
            return g;
          }), t.d(n, "__metadata", function() {
            return d;
          }), t.d(n, "__awaiter", function() {
            return p;
          }), t.d(n, "__generator", function() {
            return f;
          }), t.d(n, "__createBinding", function() {
            return i;
          }), t.d(n, "__exportStar", function() {
            return u;
          }), t.d(n, "__values", function() {
            return l;
          }), t.d(n, "__read", function() {
            return m;
          }), t.d(n, "__spread", function() {
            return c;
          }), t.d(n, "__spreadArrays", function() {
            return h;
          }), t.d(n, "__spreadArray", function() {
            return A;
          }), t.d(n, "__await", function() {
            return y;
          }), t.d(n, "__asyncGenerator", function() {
            return x;
          }), t.d(n, "__asyncDelegator", function() {
            return S;
          }), t.d(n, "__asyncValues", function() {
            return T;
          }), t.d(n, "__makeTemplateObject", function() {
            return I;
          }), t.d(n, "__importStar", function() {
            return E;
          }), t.d(n, "__importDefault", function() {
            return D;
          }), t.d(n, "__classPrivateFieldGet", function() {
            return P;
          }), t.d(n, "__classPrivateFieldSet", function() {
            return M;
          });
          var e2 = function(R, N) {
            return e2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(B, F) {
              B.__proto__ = F;
            } || function(B, F) {
              for (var O in F)
                Object.prototype.hasOwnProperty.call(F, O) && (B[O] = F[O]);
            }, e2(R, N);
          };
          function a(R, N) {
            if (typeof N != "function" && N !== null)
              throw new TypeError("Class extends value " + String(N) + " is not a constructor or null");
            e2(R, N);
            function B() {
              this.constructor = R;
            }
            R.prototype = N === null ? Object.create(N) : (B.prototype = N.prototype, new B());
          }
          var r = function() {
            return r = Object.assign || function(N) {
              for (var B, F = 1, O = arguments.length; F < O; F++) {
                B = arguments[F];
                for (var H in B)
                  Object.prototype.hasOwnProperty.call(B, H) && (N[H] = B[H]);
              }
              return N;
            }, r.apply(this, arguments);
          };
          function o(R, N) {
            var B = {};
            for (var F in R)
              Object.prototype.hasOwnProperty.call(R, F) && N.indexOf(F) < 0 && (B[F] = R[F]);
            if (R != null && typeof Object.getOwnPropertySymbols == "function")
              for (var O = 0, F = Object.getOwnPropertySymbols(R); O < F.length; O++)
                N.indexOf(F[O]) < 0 && Object.prototype.propertyIsEnumerable.call(R, F[O]) && (B[F[O]] = R[F[O]]);
            return B;
          }
          function v(R, N, B, F) {
            var O = arguments.length, H = O < 3 ? N : F === null ? F = Object.getOwnPropertyDescriptor(N, B) : F, L;
            if (typeof Reflect == "object" && typeof Reflect.decorate == "function")
              H = Reflect.decorate(R, N, B, F);
            else
              for (var U = R.length - 1; U >= 0; U--)
                (L = R[U]) && (H = (O < 3 ? L(H) : O > 3 ? L(N, B, H) : L(N, B)) || H);
            return O > 3 && H && Object.defineProperty(N, B, H), H;
          }
          function g(R, N) {
            return function(B, F) {
              N(B, F, R);
            };
          }
          function d(R, N) {
            if (typeof Reflect == "object" && typeof Reflect.metadata == "function")
              return Reflect.metadata(R, N);
          }
          function p(R, N, B, F) {
            function O(H) {
              return H instanceof B ? H : new B(function(L) {
                L(H);
              });
            }
            return new (B || (B = Promise))(function(H, L) {
              function U(K) {
                try {
                  j(F.next(K));
                } catch (V) {
                  L(V);
                }
              }
              function z(K) {
                try {
                  j(F.throw(K));
                } catch (V) {
                  L(V);
                }
              }
              function j(K) {
                K.done ? H(K.value) : O(K.value).then(U, z);
              }
              j((F = F.apply(R, N || [])).next());
            });
          }
          function f(R, N) {
            var B = { label: 0, sent: function() {
              if (H[0] & 1)
                throw H[1];
              return H[1];
            }, trys: [], ops: [] }, F, O, H, L;
            return L = { next: U(0), throw: U(1), return: U(2) }, typeof Symbol == "function" && (L[Symbol.iterator] = function() {
              return this;
            }), L;
            function U(j) {
              return function(K) {
                return z([j, K]);
              };
            }
            function z(j) {
              if (F)
                throw new TypeError("Generator is already executing.");
              for (; B; )
                try {
                  if (F = 1, O && (H = j[0] & 2 ? O.return : j[0] ? O.throw || ((H = O.return) && H.call(O), 0) : O.next) && !(H = H.call(O, j[1])).done)
                    return H;
                  switch (O = 0, H && (j = [j[0] & 2, H.value]), j[0]) {
                    case 0:
                    case 1:
                      H = j;
                      break;
                    case 4:
                      return B.label++, { value: j[1], done: false };
                    case 5:
                      B.label++, O = j[1], j = [0];
                      continue;
                    case 7:
                      j = B.ops.pop(), B.trys.pop();
                      continue;
                    default:
                      if (H = B.trys, !(H = H.length > 0 && H[H.length - 1]) && (j[0] === 6 || j[0] === 2)) {
                        B = 0;
                        continue;
                      }
                      if (j[0] === 3 && (!H || j[1] > H[0] && j[1] < H[3])) {
                        B.label = j[1];
                        break;
                      }
                      if (j[0] === 6 && B.label < H[1]) {
                        B.label = H[1], H = j;
                        break;
                      }
                      if (H && B.label < H[2]) {
                        B.label = H[2], B.ops.push(j);
                        break;
                      }
                      H[2] && B.ops.pop(), B.trys.pop();
                      continue;
                  }
                  j = N.call(R, B);
                } catch (K) {
                  j = [6, K], O = 0;
                } finally {
                  F = H = 0;
                }
              if (j[0] & 5)
                throw j[1];
              return { value: j[0] ? j[1] : void 0, done: true };
            }
          }
          var i = Object.create ? function(R, N, B, F) {
            F === void 0 && (F = B), Object.defineProperty(R, F, { enumerable: true, get: function() {
              return N[B];
            } });
          } : function(R, N, B, F) {
            F === void 0 && (F = B), R[F] = N[B];
          };
          function u(R, N) {
            for (var B in R)
              B !== "default" && !Object.prototype.hasOwnProperty.call(N, B) && i(N, R, B);
          }
          function l(R) {
            var N = typeof Symbol == "function" && Symbol.iterator, B = N && R[N], F = 0;
            if (B)
              return B.call(R);
            if (R && typeof R.length == "number")
              return {
                next: function() {
                  return R && F >= R.length && (R = void 0), { value: R && R[F++], done: !R };
                }
              };
            throw new TypeError(N ? "Object is not iterable." : "Symbol.iterator is not defined.");
          }
          function m(R, N) {
            var B = typeof Symbol == "function" && R[Symbol.iterator];
            if (!B)
              return R;
            var F = B.call(R), O, H = [], L;
            try {
              for (; (N === void 0 || N-- > 0) && !(O = F.next()).done; )
                H.push(O.value);
            } catch (U) {
              L = { error: U };
            } finally {
              try {
                O && !O.done && (B = F.return) && B.call(F);
              } finally {
                if (L)
                  throw L.error;
              }
            }
            return H;
          }
          function c() {
            for (var R = [], N = 0; N < arguments.length; N++)
              R = R.concat(m(arguments[N]));
            return R;
          }
          function h() {
            for (var R = 0, N = 0, B = arguments.length; N < B; N++)
              R += arguments[N].length;
            for (var F = Array(R), O = 0, N = 0; N < B; N++)
              for (var H = arguments[N], L = 0, U = H.length; L < U; L++, O++)
                F[O] = H[L];
            return F;
          }
          function A(R, N) {
            for (var B = 0, F = N.length, O = R.length; B < F; B++, O++)
              R[O] = N[B];
            return R;
          }
          function y(R) {
            return this instanceof y ? (this.v = R, this) : new y(R);
          }
          function x(R, N, B) {
            if (!Symbol.asyncIterator)
              throw new TypeError("Symbol.asyncIterator is not defined.");
            var F = B.apply(R, N || []), O, H = [];
            return O = {}, L("next"), L("throw"), L("return"), O[Symbol.asyncIterator] = function() {
              return this;
            }, O;
            function L(Q) {
              F[Q] && (O[Q] = function(w) {
                return new Promise(function(G, X) {
                  H.push([Q, w, G, X]) > 1 || U(Q, w);
                });
              });
            }
            function U(Q, w) {
              try {
                z(F[Q](w));
              } catch (G) {
                V(H[0][3], G);
              }
            }
            function z(Q) {
              Q.value instanceof y ? Promise.resolve(Q.value.v).then(j, K) : V(H[0][2], Q);
            }
            function j(Q) {
              U("next", Q);
            }
            function K(Q) {
              U("throw", Q);
            }
            function V(Q, w) {
              Q(w), H.shift(), H.length && U(H[0][0], H[0][1]);
            }
          }
          function S(R) {
            var N, B;
            return N = {}, F("next"), F("throw", function(O) {
              throw O;
            }), F("return"), N[Symbol.iterator] = function() {
              return this;
            }, N;
            function F(O, H) {
              N[O] = R[O] ? function(L) {
                return (B = !B) ? { value: y(R[O](L)), done: O === "return" } : H ? H(L) : L;
              } : H;
            }
          }
          function T(R) {
            if (!Symbol.asyncIterator)
              throw new TypeError("Symbol.asyncIterator is not defined.");
            var N = R[Symbol.asyncIterator], B;
            return N ? N.call(R) : (R = typeof l == "function" ? l(R) : R[Symbol.iterator](), B = {}, F("next"), F("throw"), F("return"), B[Symbol.asyncIterator] = function() {
              return this;
            }, B);
            function F(H) {
              B[H] = R[H] && function(L) {
                return new Promise(function(U, z) {
                  L = R[H](L), O(U, z, L.done, L.value);
                });
              };
            }
            function O(H, L, U, z) {
              Promise.resolve(z).then(function(j) {
                H({ value: j, done: U });
              }, L);
            }
          }
          function I(R, N) {
            return Object.defineProperty ? Object.defineProperty(R, "raw", { value: N }) : R.raw = N, R;
          }
          var C = Object.create ? function(R, N) {
            Object.defineProperty(R, "default", { enumerable: true, value: N });
          } : function(R, N) {
            R.default = N;
          };
          function E(R) {
            if (R && R.__esModule)
              return R;
            var N = {};
            if (R != null)
              for (var B in R)
                B !== "default" && Object.prototype.hasOwnProperty.call(R, B) && i(N, R, B);
            return C(N, R), N;
          }
          function D(R) {
            return R && R.__esModule ? R : { default: R };
          }
          function P(R, N) {
            if (!N.has(R))
              throw new TypeError("attempted to get private field on non-instance");
            return N.get(R);
          }
          function M(R, N, B) {
            if (!N.has(R))
              throw new TypeError("attempted to set private field on non-instance");
            return N.set(R, B), B;
          }
        },
        /* 3 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(89)), o = e2(t(4)), v = e2(t(26)), g = e2(t(17)), d = e2(t(121)), p = e2(t(27)), f = e2(t(91)), i = e2(t(70)), u = e2(t(28)), l = e2(t(57));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.DomElement = void 0;
          var m = t(2), c = t(6), h = [];
          function A(C) {
            var E = document.createElement("div");
            E.innerHTML = C;
            var D = E.children;
            return c.toArray(D);
          }
          function y(C) {
            return C ? C instanceof HTMLCollection || C instanceof NodeList : false;
          }
          function x(C) {
            var E = document.querySelectorAll(C);
            return c.toArray(E);
          }
          function S(C) {
            var E = [], D = [];
            return (0, r.default)(C) ? E = C : E = C.split(";"), (0, o.default)(E).call(E, function(P) {
              var M, R = (0, v.default)(M = P.split(":")).call(M, function(N) {
                return (0, g.default)(N).call(N);
              });
              R.length === 2 && D.push(R[0] + ":" + R[1]);
            }), D;
          }
          var T = (
            /** @class */
            function() {
              function C(E) {
                if (this.elems = [], this.length = this.elems.length, this.dataSource = new d.default(), !!E) {
                  if (E instanceof C)
                    return E;
                  var D = [], P = E instanceof Node ? E.nodeType : -1;
                  if (this.selector = E, P === 1 || P === 9)
                    D = [E];
                  else if (y(E))
                    D = c.toArray(E);
                  else if (E instanceof Array)
                    D = E;
                  else if (typeof E == "string") {
                    var M, R = (0, g.default)(M = E.replace(`/
/mg`, "")).call(M);
                    (0, p.default)(R).call(R, "<") === 0 ? D = A(R) : D = x(R);
                  }
                  var N = D.length;
                  if (!N)
                    return this;
                  for (var B = 0; B < N; B++)
                    this.elems.push(D[B]);
                  this.length = N;
                }
              }
              return (0, a.default)(C.prototype, "id", {
                /**
                 * 获取元素 id
                 */
                get: function() {
                  return this.elems[0].id;
                },
                enumerable: false,
                configurable: true
              }), C.prototype.forEach = function(E) {
                for (var D = 0; D < this.length; D++) {
                  var P = this.elems[D], M = E.call(P, P, D);
                  if (M === false)
                    break;
                }
                return this;
              }, C.prototype.clone = function(E) {
                var D;
                E === void 0 && (E = false);
                var P = [];
                return (0, o.default)(D = this.elems).call(D, function(M) {
                  P.push(M.cloneNode(!!E));
                }), I(P);
              }, C.prototype.get = function(E) {
                E === void 0 && (E = 0);
                var D = this.length;
                return E >= D && (E = E % D), I(this.elems[E]);
              }, C.prototype.first = function() {
                return this.get(0);
              }, C.prototype.last = function() {
                var E = this.length;
                return this.get(E - 1);
              }, C.prototype.on = function(E, D, P) {
                var M;
                return E ? (typeof D == "function" && (P = D, D = ""), (0, o.default)(M = this).call(M, function(R) {
                  if (!D) {
                    R.addEventListener(E, P);
                    return;
                  }
                  var N = function(F) {
                    var O = F.target;
                    O.matches(D) && P.call(O, F);
                  };
                  R.addEventListener(E, N), h.push({
                    elem: R,
                    selector: D,
                    fn: P,
                    agentFn: N
                  });
                })) : this;
              }, C.prototype.off = function(E, D, P) {
                var M;
                return E ? (typeof D == "function" && (P = D, D = ""), (0, o.default)(M = this).call(M, function(R) {
                  if (D) {
                    for (var N = -1, B = 0; B < h.length; B++) {
                      var F = h[B];
                      if (F.selector === D && F.fn === P && F.elem === R) {
                        N = B;
                        break;
                      }
                    }
                    if (N !== -1) {
                      var O = (0, f.default)(h).call(h, N, 1)[0].agentFn;
                      R.removeEventListener(E, O);
                    }
                  } else
                    R.removeEventListener(E, P);
                })) : this;
              }, C.prototype.attr = function(E, D) {
                var P;
                return D == null ? this.elems[0].getAttribute(E) || "" : (0, o.default)(P = this).call(P, function(M) {
                  M.setAttribute(E, D);
                });
              }, C.prototype.removeAttr = function(E) {
                var D;
                (0, o.default)(D = this).call(D, function(P) {
                  P.removeAttribute(E);
                });
              }, C.prototype.addClass = function(E) {
                var D;
                return E ? (0, o.default)(D = this).call(D, function(P) {
                  if (P.className) {
                    var M = P.className.split(/\s/);
                    M = (0, i.default)(M).call(M, function(R) {
                      return !!(0, g.default)(R).call(R);
                    }), (0, p.default)(M).call(M, E) < 0 && M.push(E), P.className = M.join(" ");
                  } else
                    P.className = E;
                }) : this;
              }, C.prototype.removeClass = function(E) {
                var D;
                return E ? (0, o.default)(D = this).call(D, function(P) {
                  if (P.className) {
                    var M = P.className.split(/\s/);
                    M = (0, i.default)(M).call(M, function(R) {
                      return R = (0, g.default)(R).call(R), !(!R || R === E);
                    }), P.className = M.join(" ");
                  }
                }) : this;
              }, C.prototype.hasClass = function(E) {
                if (!E)
                  return false;
                var D = this.elems[0];
                if (!D.className)
                  return false;
                var P = D.className.split(/\s/);
                return (0, u.default)(P).call(P, E);
              }, C.prototype.css = function(E, D) {
                var P, M;
                return D == "" ? M = "" : M = E + ":" + D + ";", (0, o.default)(P = this).call(P, function(R) {
                  var N, B = (0, g.default)(N = R.getAttribute("style") || "").call(N);
                  if (B) {
                    var F = S(B);
                    F = (0, v.default)(F).call(F, function(O) {
                      return (0, p.default)(O).call(O, E) === 0 ? M : O;
                    }), M != "" && (0, p.default)(F).call(F, M) < 0 && F.push(M), M == "" && (F = S(F)), R.setAttribute("style", F.join("; "));
                  } else
                    R.setAttribute("style", M);
                });
              }, C.prototype.getBoundingClientRect = function() {
                var E = this.elems[0];
                return E.getBoundingClientRect();
              }, C.prototype.show = function() {
                return this.css("display", "block");
              }, C.prototype.hide = function() {
                return this.css("display", "none");
              }, C.prototype.children = function() {
                var E = this.elems[0];
                return E ? I(E.children) : null;
              }, C.prototype.childNodes = function() {
                var E = this.elems[0];
                return E ? I(E.childNodes) : null;
              }, C.prototype.replaceChildAll = function(E) {
                for (var D = this.getNode(), P = this.elems[0]; P.hasChildNodes(); )
                  D.firstChild && P.removeChild(D.firstChild);
                this.append(E);
              }, C.prototype.append = function(E) {
                var D;
                return (0, o.default)(D = this).call(D, function(P) {
                  (0, o.default)(E).call(E, function(M) {
                    P.appendChild(M);
                  });
                });
              }, C.prototype.remove = function() {
                var E;
                return (0, o.default)(E = this).call(E, function(D) {
                  if (D.remove)
                    D.remove();
                  else {
                    var P = D.parentElement;
                    P && P.removeChild(D);
                  }
                });
              }, C.prototype.isContain = function(E) {
                var D = this.elems[0], P = E.elems[0];
                return D.contains(P);
              }, C.prototype.getNodeName = function() {
                var E = this.elems[0];
                return E.nodeName;
              }, C.prototype.getNode = function(E) {
                E === void 0 && (E = 0);
                var D;
                return D = this.elems[E], D;
              }, C.prototype.find = function(E) {
                var D = this.elems[0];
                return I(D.querySelectorAll(E));
              }, C.prototype.text = function(E) {
                if (E) {
                  var P;
                  return (0, o.default)(P = this).call(P, function(M) {
                    M.innerHTML = E;
                  });
                } else {
                  var D = this.elems[0];
                  return D.innerHTML.replace(/<[^>]+>/g, function() {
                    return "";
                  });
                }
              }, C.prototype.html = function(E) {
                var D = this.elems[0];
                return E ? (D.innerHTML = E, this) : D.innerHTML;
              }, C.prototype.val = function() {
                var E, D = this.elems[0];
                return (0, g.default)(E = D.value).call(E);
              }, C.prototype.focus = function() {
                var E;
                return (0, o.default)(E = this).call(E, function(D) {
                  D.focus();
                });
              }, C.prototype.prev = function() {
                var E = this.elems[0];
                return I(E.previousElementSibling);
              }, C.prototype.next = function() {
                var E = this.elems[0];
                return I(E.nextElementSibling);
              }, C.prototype.getNextSibling = function() {
                var E = this.elems[0];
                return I(E.nextSibling);
              }, C.prototype.parent = function() {
                var E = this.elems[0];
                return I(E.parentElement);
              }, C.prototype.parentUntil = function(E, D) {
                var P = D || this.elems[0];
                if (P.nodeName === "BODY")
                  return null;
                var M = P.parentElement;
                return M === null ? null : M.matches(E) ? I(M) : this.parentUntil(E, M);
              }, C.prototype.parentUntilEditor = function(E, D, P) {
                var M = P || this.elems[0];
                if (I(M).equal(D.$textContainerElem) || I(M).equal(D.$toolbarElem))
                  return null;
                var R = M.parentElement;
                return R === null ? null : R.matches(E) ? I(R) : this.parentUntilEditor(E, D, R);
              }, C.prototype.equal = function(E) {
                return E instanceof C ? this.elems[0] === E.elems[0] : E instanceof HTMLElement ? this.elems[0] === E : false;
              }, C.prototype.insertBefore = function(E) {
                var D, P = I(E), M = P.elems[0];
                return M ? (0, o.default)(D = this).call(D, function(R) {
                  var N = M.parentNode;
                  N == null || N.insertBefore(R, M);
                }) : this;
              }, C.prototype.insertAfter = function(E) {
                var D, P = I(E), M = P.elems[0], R = M && M.nextSibling;
                return M ? (0, o.default)(D = this).call(D, function(N) {
                  var B = M.parentNode;
                  R ? B.insertBefore(N, R) : B.appendChild(N);
                }) : this;
              }, C.prototype.data = function(E, D) {
                if (D != null)
                  this.dataSource.set(E, D);
                else
                  return this.dataSource.get(E);
              }, C.prototype.getNodeTop = function(E) {
                if (this.length < 1)
                  return this;
                var D = this.parent();
                return E.$textElem.equal(this) || E.$textElem.equal(D) ? this : (D.prior = this, D.getNodeTop(E));
              }, C.prototype.getOffsetData = function() {
                var E = this.elems[0];
                return {
                  top: E.offsetTop,
                  left: E.offsetLeft,
                  width: E.offsetWidth,
                  height: E.offsetHeight,
                  parent: E.offsetParent
                };
              }, C.prototype.scrollTop = function(E) {
                var D = this.elems[0];
                D.scrollTo({
                  top: E
                });
              }, C;
            }()
          );
          n.DomElement = T;
          function I() {
            for (var C = [], E = 0; E < arguments.length; E++)
              C[E] = arguments[E];
            return new ((0, l.default)(T).apply(T, m.__spreadArrays([void 0], C)))();
          }
          n.default = I;
        },
        /* 4 */
        /***/
        function(s, n, t) {
          s.exports = t(180);
        },
        /* 5 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(71).f, r = t(101), o = t(9), v = t(40), g = t(19), d = t(16), p = function(f) {
            var i = function(u, l, m) {
              if (this instanceof f) {
                switch (arguments.length) {
                  case 0:
                    return new f();
                  case 1:
                    return new f(u);
                  case 2:
                    return new f(u, l);
                }
                return new f(u, l, m);
              }
              return f.apply(this, arguments);
            };
            return i.prototype = f.prototype, i;
          };
          s.exports = function(f, i) {
            var u = f.target, l = f.global, m = f.stat, c = f.proto, h = l ? e2 : m ? e2[u] : (e2[u] || {}).prototype, A = l ? o : o[u] || (o[u] = {}), y = A.prototype, x, S, T, I, C, E, D, P, M;
            for (I in i)
              x = r(l ? I : u + (m ? "." : "#") + I, f.forced), S = !x && h && d(h, I), E = A[I], S && (f.noTargetGet ? (M = a(h, I), D = M && M.value) : D = h[I]), C = S && D ? D : i[I], !(S && typeof E == typeof C) && (f.bind && S ? P = v(C, e2) : f.wrap && S ? P = p(C) : c && typeof C == "function" ? P = v(Function.call, C) : P = C, (f.sham || C && C.sham || E && E.sham) && g(P, "sham", true), A[I] = P, c && (T = u + "Prototype", d(o, T) || g(o, T, {}), o[T][I] = C, f.real && y && !y[I] && g(y, I, C)));
          };
        },
        /* 6 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(92)), r = e2(t(1)), o = e2(t(256)), v = e2(t(45)), g = e2(t(46)), d = e2(t(89)), p = e2(t(26));
          (0, r.default)(n, "__esModule", {
            value: true
          }), n.hexToRgb = n.getRandomCode = n.toArray = n.deepClone = n.isFunction = n.debounce = n.throttle = n.arrForEach = n.forEach = n.replaceSpecialSymbol = n.replaceHtmlSymbol = n.getRandom = n.UA = void 0;
          var f = t(2), i = (
            /** @class */
            function() {
              function E() {
                this._ua = navigator.userAgent;
                var D = this._ua.match(/(Edge?)\/(\d+)/);
                this.isOldEdge = !!(D && D[1] == "Edge" && (0, o.default)(D[2]) < 19), this.isFirefox = !!(/Firefox\/\d+/.test(this._ua) && !/Seamonkey\/\d+/.test(this._ua));
              }
              return E.prototype.isIE = function() {
                return "ActiveXObject" in window;
              }, E.prototype.isWebkit = function() {
                return /webkit/i.test(this._ua);
              }, E;
            }()
          );
          n.UA = new i();
          function u(E) {
            var D;
            return E === void 0 && (E = ""), E + (0, v.default)(D = Math.random().toString()).call(D, 2);
          }
          n.getRandom = u;
          function l(E) {
            return E.replace(/</gm, "&lt;").replace(/>/gm, "&gt;").replace(/"/gm, "&quot;").replace(/(\r\n|\r|\n)/g, "<br/>");
          }
          n.replaceHtmlSymbol = l;
          function m(E) {
            return E.replace(/&lt;/gm, "<").replace(/&gt;/gm, ">").replace(/&quot;/gm, '"');
          }
          n.replaceSpecialSymbol = m;
          function c(E, D) {
            for (var P in E)
              if (Object.prototype.hasOwnProperty.call(E, P)) {
                var M = D(P, E[P]);
                if (M === false)
                  break;
              }
          }
          n.forEach = c;
          function h(E, D) {
            var P, M, R, N = E.length || 0;
            for (P = 0; P < N && (M = E[P], R = D.call(E, M, P), R !== false); P++)
              ;
          }
          n.arrForEach = h;
          function A(E, D) {
            D === void 0 && (D = 200);
            var P = false;
            return function() {
              for (var M = this, R = [], N = 0; N < arguments.length; N++)
                R[N] = arguments[N];
              P || (P = true, (0, g.default)(function() {
                P = false, E.call.apply(E, f.__spreadArrays([M], R));
              }, D));
            };
          }
          n.throttle = A;
          function y(E, D) {
            D === void 0 && (D = 200);
            var P = 0;
            return function() {
              for (var M = this, R = [], N = 0; N < arguments.length; N++)
                R[N] = arguments[N];
              P && window.clearTimeout(P), P = (0, g.default)(function() {
                P = 0, E.call.apply(E, f.__spreadArrays([M], R));
              }, D);
            };
          }
          n.debounce = y;
          function x(E) {
            return typeof E == "function";
          }
          n.isFunction = x;
          function S(E) {
            if ((0, a.default)(E) !== "object" || typeof E == "function" || E === null)
              return E;
            var D;
            (0, d.default)(E) && (D = []), (0, d.default)(E) || (D = {});
            for (var P in E)
              Object.prototype.hasOwnProperty.call(E, P) && (D[P] = S(E[P]));
            return D;
          }
          n.deepClone = S;
          function T(E) {
            return (0, v.default)(Array.prototype).call(E);
          }
          n.toArray = T;
          function I() {
            var E;
            return (0, v.default)(E = Math.random().toString(36)).call(E, -5);
          }
          n.getRandomCode = I;
          function C(E) {
            var D = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(E);
            if (D == null)
              return null;
            var P = (0, p.default)(D).call(D, function(B) {
              return (0, o.default)(B, 16);
            }), M = P[1], R = P[2], N = P[3];
            return "rgb(" + M + ", " + R + ", " + N + ")";
          }
          n.hexToRgb = C;
        },
        /* 7 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.EMPTY_P_REGEX = n.EMPTY_P_LAST_REGEX = n.EMPTY_P = n.urlRegex = n.EMPTY_FN = void 0;
          function r() {
          }
          n.EMPTY_FN = r, n.urlRegex = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&amp;:/~+#]*[\w\-@?^=%&amp;/~+#])?/g, n.EMPTY_P = '<p data-we-empty-p=""><br></p>', n.EMPTY_P_LAST_REGEX = /<p data-we-empty-p=""><br\/?><\/p>$/gim, n.EMPTY_P_REGEX = /<p data-we-empty-p="">/gim;
        },
        /* 8 */
        /***/
        function(s, n, t) {
          (function(e2) {
            var a = function(r) {
              return r && r.Math == Math && r;
            };
            s.exports = // eslint-disable-next-line no-undef
            a(typeof globalThis == "object" && globalThis) || a(typeof window == "object" && window) || a(typeof self == "object" && self) || a(typeof e2 == "object" && e2) || // eslint-disable-next-line no-new-func
            Function("return this")();
          }).call(this, t(145));
        },
        /* 9 */
        /***/
        function(s, n) {
          s.exports = {};
        },
        /* 10 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(74), r = t(16), o = t(64), v = t(76), g = t(106), d = a("wks"), p = e2.Symbol, f = g ? p : p && p.withoutSetter || o;
          s.exports = function(i) {
            return r(d, i) || (v && r(p, i) ? d[i] = p[i] : d[i] = f("Symbol." + i)), d[i];
          };
        },
        /* 11 */
        /***/
        function(s, n) {
          s.exports = function(t) {
            try {
              return !!t();
            } catch {
              return true;
            }
          };
        },
        /* 12 */
        /***/
        function(s, n, t) {
          var e2 = t(9), a = t(16), r = t(93), o = t(18).f;
          s.exports = function(v) {
            var g = e2.Symbol || (e2.Symbol = {});
            a(g, v) || o(g, v, {
              value: r.f(v)
            });
          };
        },
        /* 13 */
        /***/
        function(s, n) {
          s.exports = function(t) {
            return typeof t == "object" ? t !== null : typeof t == "function";
          };
        },
        /* 14 */
        /***/
        function(s, n, t) {
          var e2 = t(11);
          s.exports = !e2(function() {
            return Object.defineProperty({}, 1, { get: function() {
              return 7;
            } })[1] != 7;
          });
        },
        /* 15 */
        /***/
        function(s, n, t) {
          var e2 = t(9);
          s.exports = function(a) {
            return e2[a + "Prototype"];
          };
        },
        /* 16 */
        /***/
        function(s, n) {
          var t = {}.hasOwnProperty;
          s.exports = function(e2, a) {
            return t.call(e2, a);
          };
        },
        /* 17 */
        /***/
        function(s, n, t) {
          s.exports = t(192);
        },
        /* 18 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(100), r = t(25), o = t(60), v = Object.defineProperty;
          n.f = e2 ? v : function(d, p, f) {
            if (r(d), p = o(p, true), r(f), a)
              try {
                return v(d, p, f);
              } catch {
              }
            if ("get" in f || "set" in f)
              throw TypeError("Accessors not supported");
            return "value" in f && (d[p] = f.value), d;
          };
        },
        /* 19 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(18), r = t(48);
          s.exports = e2 ? function(o, v, g) {
            return a.f(o, v, r(1, g));
          } : function(o, v, g) {
            return o[v] = g, o;
          };
        },
        /* 20 */
        /***/
        function(s, n, t) {
          var e2 = function() {
            var h;
            return function() {
              return typeof h > "u" && (h = !!(window && document && document.all && !window.atob)), h;
            };
          }(), a = function() {
            var h = {};
            return function(y) {
              if (typeof h[y] > "u") {
                var x = document.querySelector(y);
                if (window.HTMLIFrameElement && x instanceof window.HTMLIFrameElement)
                  try {
                    x = x.contentDocument.head;
                  } catch {
                    x = null;
                  }
                h[y] = x;
              }
              return h[y];
            };
          }(), r = [];
          function o(c) {
            for (var h = -1, A = 0; A < r.length; A++)
              if (r[A].identifier === c) {
                h = A;
                break;
              }
            return h;
          }
          function v(c, h) {
            for (var A = {}, y = [], x = 0; x < c.length; x++) {
              var S = c[x], T = h.base ? S[0] + h.base : S[0], I = A[T] || 0, C = "".concat(T, " ").concat(I);
              A[T] = I + 1;
              var E = o(C), D = {
                css: S[1],
                media: S[2],
                sourceMap: S[3]
              };
              E !== -1 ? (r[E].references++, r[E].updater(D)) : r.push({
                identifier: C,
                updater: m(D, h),
                references: 1
              }), y.push(C);
            }
            return y;
          }
          function g(c) {
            var h = document.createElement("style"), A = c.attributes || {};
            if (typeof A.nonce > "u") {
              var y = t.nc;
              y && (A.nonce = y);
            }
            if (Object.keys(A).forEach(function(S) {
              h.setAttribute(S, A[S]);
            }), typeof c.insert == "function")
              c.insert(h);
            else {
              var x = a(c.insert || "head");
              if (!x)
                throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
              x.appendChild(h);
            }
            return h;
          }
          function d(c) {
            if (c.parentNode === null)
              return false;
            c.parentNode.removeChild(c);
          }
          var p = function() {
            var h = [];
            return function(y, x) {
              return h[y] = x, h.filter(Boolean).join(`
`);
            };
          }();
          function f(c, h, A, y) {
            var x = A ? "" : y.media ? "@media ".concat(y.media, " {").concat(y.css, "}") : y.css;
            if (c.styleSheet)
              c.styleSheet.cssText = p(h, x);
            else {
              var S = document.createTextNode(x), T = c.childNodes;
              T[h] && c.removeChild(T[h]), T.length ? c.insertBefore(S, T[h]) : c.appendChild(S);
            }
          }
          function i(c, h, A) {
            var y = A.css, x = A.media, S = A.sourceMap;
            if (x ? c.setAttribute("media", x) : c.removeAttribute("media"), S && typeof btoa < "u" && (y += `
/*# sourceMappingURL=data:application/json;base64,`.concat(btoa(unescape(encodeURIComponent(JSON.stringify(S)))), " */")), c.styleSheet)
              c.styleSheet.cssText = y;
            else {
              for (; c.firstChild; )
                c.removeChild(c.firstChild);
              c.appendChild(document.createTextNode(y));
            }
          }
          var u = null, l = 0;
          function m(c, h) {
            var A, y, x;
            if (h.singleton) {
              var S = l++;
              A = u || (u = g(h)), y = f.bind(null, A, S, false), x = f.bind(null, A, S, true);
            } else
              A = g(h), y = i.bind(null, A, h), x = function() {
                d(A);
              };
            return y(c), function(I) {
              if (I) {
                if (I.css === c.css && I.media === c.media && I.sourceMap === c.sourceMap)
                  return;
                y(c = I);
              } else
                x();
            };
          }
          s.exports = function(c, h) {
            h = h || {}, !h.singleton && typeof h.singleton != "boolean" && (h.singleton = e2()), c = c || [];
            var A = v(c, h);
            return function(x) {
              if (x = x || [], Object.prototype.toString.call(x) === "[object Array]") {
                for (var S = 0; S < A.length; S++) {
                  var T = A[S], I = o(T);
                  r[I].references--;
                }
                for (var C = v(x, h), E = 0; E < A.length; E++) {
                  var D = A[E], P = o(D);
                  r[P].references === 0 && (r[P].updater(), r.splice(P, 1));
                }
                A = C;
              }
            };
          };
        },
        /* 21 */
        /***/
        function(s, n, t) {
          s.exports = function(r) {
            var o = [];
            return o.toString = function() {
              return this.map(function(g) {
                var d = e2(g, r);
                return g[2] ? "@media ".concat(g[2], " {").concat(d, "}") : d;
              }).join("");
            }, o.i = function(v, g, d) {
              typeof v == "string" && (v = [[null, v, ""]]);
              var p = {};
              if (d)
                for (var f = 0; f < this.length; f++) {
                  var i = this[f][0];
                  i != null && (p[i] = true);
                }
              for (var u = 0; u < v.length; u++) {
                var l = [].concat(v[u]);
                d && p[l[0]] || (g && (l[2] ? l[2] = "".concat(g, " and ").concat(l[2]) : l[2] = g), o.push(l));
              }
            }, o;
          };
          function e2(r, o) {
            var v = r[1] || "", g = r[3];
            if (!g)
              return v;
            if (o && typeof btoa == "function") {
              var d = a(g), p = g.sources.map(function(f) {
                return "/*# sourceURL=".concat(g.sourceRoot || "").concat(f, " */");
              });
              return [v].concat(p).concat([d]).join(`
`);
            }
            return [v].join(`
`);
          }
          function a(r) {
            var o = btoa(unescape(encodeURIComponent(JSON.stringify(r)))), v = "sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(o);
            return "/*# ".concat(v, " */");
          }
        },
        /* 22 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(11), r = t(16), o = Object.defineProperty, v = {}, g = function(d) {
            throw d;
          };
          s.exports = function(d, p) {
            if (r(v, d))
              return v[d];
            p || (p = {});
            var f = [][d], i = r(p, "ACCESSORS") ? p.ACCESSORS : false, u = r(p, 0) ? p[0] : g, l = r(p, 1) ? p[1] : void 0;
            return v[d] = !!f && !a(function() {
              if (i && !e2)
                return true;
              var m = { length: -1 };
              i ? o(m, 1, { enumerable: true, get: g }) : m[1] = 1, f.call(m, u, l);
            });
          };
        },
        /* 23 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(95)), v = (
            /** @class */
            function(g) {
              r.__extends(d, g);
              function d(p, f) {
                return g.call(this, p, f) || this;
              }
              return d;
            }(o.default)
          );
          n.default = v;
        },
        /* 24 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(46));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = v.__importDefault(t(95)), p = v.__importDefault(t(134)), f = (
            /** @class */
            function(i) {
              v.__extends(u, i);
              function u(l, m, c) {
                var h = i.call(this, l, m) || this;
                c.title = m.i18next.t("menus.dropListMenu." + c.title);
                var A = m.config.lang === "zh-CN" ? "" : "w-e-drop-list-tl";
                if (A !== "" && c.type === "list") {
                  var y;
                  (0, r.default)(y = c.list).call(y, function(S) {
                    var T = S.$elem, I = g.default(T.children());
                    if (I.length > 0) {
                      var C = I == null ? void 0 : I.getNodeName();
                      C && C === "I" && T.addClass(A);
                    }
                  });
                }
                var x = new p.default(h, c);
                return h.dropList = x, l.on("click", function() {
                  var S;
                  m.selection.getRange() != null && (l.css("z-index", m.zIndex.get("menu")), (0, r.default)(S = m.txt.eventHooks.dropListMenuHoverEvents).call(S, function(T) {
                    return T();
                  }), x.show());
                }).on("mouseleave", function() {
                  l.css("z-index", "auto"), x.hideTimeoutId = (0, o.default)(function() {
                    x.hide();
                  });
                }), h;
              }
              return u;
            }(d.default)
          );
          n.default = f;
        },
        /* 25 */
        /***/
        function(s, n, t) {
          var e2 = t(13);
          s.exports = function(a) {
            if (!e2(a))
              throw TypeError(String(a) + " is not an object");
            return a;
          };
        },
        /* 26 */
        /***/
        function(s, n, t) {
          s.exports = t(188);
        },
        /* 27 */
        /***/
        function(s, n, t) {
          s.exports = t(201);
        },
        /* 28 */
        /***/
        function(s, n, t) {
          s.exports = t(213);
        },
        /* 29 */
        /***/
        function(s, n, t) {
          s.exports = t(283);
        },
        /* 30 */
        /***/
        function(s, n, t) {
          var e2 = t(72), a = t(49);
          s.exports = function(r) {
            return e2(a(r));
          };
        },
        /* 31 */
        /***/
        function(s, n, t) {
          var e2 = t(49);
          s.exports = function(a) {
            return Object(e2(a));
          };
        },
        /* 32 */
        /***/
        function(s, n, t) {
          var e2 = t(40), a = t(72), r = t(31), o = t(35), v = t(88), g = [].push, d = function(p) {
            var f = p == 1, i = p == 2, u = p == 3, l = p == 4, m = p == 6, c = p == 5 || m;
            return function(h, A, y, x) {
              for (var S = r(h), T = a(S), I = e2(A, y, 3), C = o(T.length), E = 0, D = x || v, P = f ? D(h, C) : i ? D(h, 0) : void 0, M, R; C > E; E++)
                if ((c || E in T) && (M = T[E], R = I(M, E, S), p)) {
                  if (f)
                    P[E] = R;
                  else if (R)
                    switch (p) {
                      case 3:
                        return true;
                      case 5:
                        return M;
                      case 6:
                        return E;
                      case 2:
                        g.call(P, M);
                    }
                  else if (l)
                    return false;
                }
              return m ? -1 : u || l ? l : P;
            };
          };
          s.exports = {
            // `Array.prototype.forEach` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.foreach
            forEach: d(0),
            // `Array.prototype.map` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.map
            map: d(1),
            // `Array.prototype.filter` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.filter
            filter: d(2),
            // `Array.prototype.some` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.some
            some: d(3),
            // `Array.prototype.every` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.every
            every: d(4),
            // `Array.prototype.find` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.find
            find: d(5),
            // `Array.prototype.findIndex` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.findIndex
            findIndex: d(6)
          };
        },
        /* 33 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(29)), v = e2(t(132));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var g = t(2), d = g.__importDefault(t(3)), p = t(7), f = (
            /** @class */
            function() {
              function i(u, l) {
                this.menu = u, this.conf = l, this.$container = d.default('<div class="w-e-panel-container"></div>');
                var m = u.editor;
                m.txt.eventHooks.clickEvents.push(i.hideCurAllPanels), m.txt.eventHooks.toolbarClickEvents.push(i.hideCurAllPanels), m.txt.eventHooks.dropListMenuHoverEvents.push(i.hideCurAllPanels);
              }
              return i.prototype.create = function() {
                var u = this, l = this.menu;
                if (!i.createdMenus.has(l)) {
                  var m = this.conf, c = this.$container, h = m.width || 300, A = l.editor.$toolbarElem.getBoundingClientRect(), y = l.$elem.getBoundingClientRect(), x = A.height + A.top - y.top, S = (A.width - h) / 2 + A.left - y.left, T = 300;
                  Math.abs(S) > T && (y.left < document.documentElement.clientWidth / 2 ? S = -y.width / 2 : S = -h + y.width / 2), c.css("width", h + "px").css("margin-top", x + "px").css("margin-left", S + "px").css("z-index", l.editor.zIndex.get("panel"));
                  var I = d.default('<i class="w-e-icon-close w-e-panel-close"></i>');
                  c.append(I), I.on("click", function() {
                    u.remove();
                  });
                  var C = d.default('<ul class="w-e-panel-tab-title"></ul>'), E = d.default('<div class="w-e-panel-tab-content"></div>');
                  c.append(C).append(E);
                  var D = m.height;
                  D && E.css("height", D + "px").css("overflow-y", "auto");
                  var P = m.tabs || [], M = [], R = [];
                  (0, r.default)(P).call(P, function(B, F) {
                    if (B) {
                      var O = B.title || "", H = B.tpl || "", L = d.default('<li class="w-e-item">' + O + "</li>");
                      C.append(L);
                      var U = d.default(H);
                      E.append(U), M.push(L), R.push(U), F === 0 ? (L.data("active", true), L.addClass("w-e-active")) : U.hide(), L.on("click", function() {
                        L.data("active") || ((0, r.default)(M).call(M, function(z) {
                          z.data("active", false), z.removeClass("w-e-active");
                        }), (0, r.default)(R).call(R, function(z) {
                          z.hide();
                        }), L.data("active", true), L.addClass("w-e-active"), U.show());
                      });
                    }
                  }), c.on("click", function(B) {
                    B.stopPropagation();
                  }), l.$elem.append(c), m.setLinkValue && m.setLinkValue(c, "text"), m.setLinkValue && m.setLinkValue(c, "link"), (0, r.default)(P).call(P, function(B, F) {
                    if (B) {
                      var O = B.events || [];
                      (0, r.default)(O).call(O, function(H) {
                        var L, U = H.selector, z = H.type, j = H.fn || p.EMPTY_FN, K = R[F], V = (L = H.bindEnter) !== null && L !== void 0 ? L : false, Q = function(G) {
                          return g.__awaiter(u, void 0, void 0, function() {
                            var X;
                            return g.__generator(this, function(tt) {
                              switch (tt.label) {
                                case 0:
                                  return G.stopPropagation(), [
                                    4,
                                    j(G)
                                    // 执行完事件之后，是否要关闭 panel
                                  ];
                                case 1:
                                  return X = tt.sent(), X && this.remove(), [
                                    2
                                    /*return*/
                                  ];
                              }
                            });
                          });
                        };
                        (0, o.default)(K).call(K, U).on(z, Q), V && z === "click" && K.on("keyup", function(w) {
                          w.keyCode == 13 && Q(w);
                        });
                      });
                    }
                  });
                  var N = (0, o.default)(c).call(c, "input[type=text],textarea");
                  N.length && N.get(0).focus(), i.hideCurAllPanels(), l.setPanel(this), i.createdMenus.add(l);
                }
              }, i.prototype.remove = function() {
                var u = this.menu, l = this.$container;
                l && l.remove(), i.createdMenus.delete(u);
              }, i.hideCurAllPanels = function() {
                var u;
                i.createdMenus.size !== 0 && (0, r.default)(u = i.createdMenus).call(u, function(l) {
                  var m = l.panel;
                  m && m.remove();
                });
              }, i.createdMenus = new v.default(), i;
            }()
          );
          n.default = f;
        },
        /* 34 */
        /***/
        function(s, n) {
          var t = {}.toString;
          s.exports = function(e2) {
            return t.call(e2).slice(8, -1);
          };
        },
        /* 35 */
        /***/
        function(s, n, t) {
          var e2 = t(62), a = Math.min;
          s.exports = function(r) {
            return r > 0 ? a(e2(r), 9007199254740991) : 0;
          };
        },
        /* 36 */
        /***/
        function(s, n, t) {
          var e2 = t(9), a = t(8), r = function(o) {
            return typeof o == "function" ? o : void 0;
          };
          s.exports = function(o, v) {
            return arguments.length < 2 ? r(e2[o]) || r(a[o]) : e2[o] && e2[o][v] || a[o] && a[o][v];
          };
        },
        /* 37 */
        /***/
        function(s, n, t) {
          var e2 = t(81), a = t(18).f, r = t(19), o = t(16), v = t(170), g = t(10), d = g("toStringTag");
          s.exports = function(p, f, i, u) {
            if (p) {
              var l = i ? p : p.prototype;
              o(l, d) || a(l, d, { configurable: true, value: f }), u && !e2 && r(l, "toString", v);
            }
          };
        },
        /* 38 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(95)), v = (
            /** @class */
            function(g) {
              r.__extends(d, g);
              function d(p, f) {
                return g.call(this, p, f) || this;
              }
              return d.prototype.setPanel = function(p) {
                this.panel = p;
              }, d;
            }(o.default)
          );
          n.default = v;
        },
        /* 39 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(57));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = (
            /** @class */
            function() {
              function p(f, i, u) {
                this.editor = f, this.$targetElem = i, this.conf = u, this._show = false, this._isInsertTextContainer = false;
                var l = g.default("<div></div>");
                l.addClass("w-e-tooltip"), this.$container = l;
              }
              return p.prototype.getPositionData = function() {
                var f = this.$container, i = 0, u = 0, l = 20, m = document.documentElement.scrollTop, c = this.$targetElem.getBoundingClientRect(), h = this.editor.$textElem.getBoundingClientRect(), A = this.$targetElem.getOffsetData(), y = g.default(A.parent), x = this.editor.$textElem.elems[0].scrollTop;
                if (this._isInsertTextContainer = y.equal(this.editor.$textContainerElem), this._isInsertTextContainer) {
                  var S = y.getBoundingClientRect().height, T = A.top, I = A.left, C = A.height, E = T - x;
                  E > l + 5 ? (i = E - l - 15, f.addClass("w-e-tooltip-up")) : E + C + l < S ? (i = E + C + 10, f.addClass("w-e-tooltip-down")) : (i = (E > 0 ? E : 0) + l + 10, f.addClass("w-e-tooltip-down")), I < 0 ? u = 0 : u = I;
                } else
                  c.top < l || c.top - h.top < l ? (i = c.bottom + m + 5, f.addClass("w-e-tooltip-down")) : (i = c.top + m - l - 15, f.addClass("w-e-tooltip-up")), c.left < 0 ? u = 0 : u = c.left;
                return {
                  top: i,
                  left: u
                };
              }, p.prototype.appendMenus = function() {
                var f = this, i = this.conf, u = this.editor, l = this.$targetElem, m = this.$container;
                (0, r.default)(i).call(i, function(c, h) {
                  var A = c.$elem, y = g.default("<div></div>");
                  y.addClass("w-e-tooltip-item-wrapper "), y.append(A), m.append(y), A.on("click", function(x) {
                    x.preventDefault();
                    var S = c.onClick(u, l);
                    S && f.remove();
                  });
                });
              }, p.prototype.create = function() {
                var f, i, u = this.editor, l = this.$container;
                this.appendMenus();
                var m = this.getPositionData(), c = m.top, h = m.left;
                l.css("top", c + "px"), l.css("left", h + "px"), l.css("z-index", u.zIndex.get("tooltip")), this._isInsertTextContainer ? this.editor.$textContainerElem.append(l) : g.default("body").append(l), this._show = true, u.beforeDestroy((0, o.default)(f = this.remove).call(f, this)), u.txt.eventHooks.onBlurEvents.push((0, o.default)(i = this.remove).call(i, this));
              }, p.prototype.remove = function() {
                this.$container.remove(), this._show = false;
              }, (0, a.default)(p.prototype, "isShow", {
                /**
                 * 是否显示
                 */
                get: function() {
                  return this._show;
                },
                enumerable: false,
                configurable: true
              }), p;
            }()
          );
          n.default = d;
        },
        /* 40 */
        /***/
        function(s, n, t) {
          var e2 = t(41);
          s.exports = function(a, r, o) {
            if (e2(a), r === void 0)
              return a;
            switch (o) {
              case 0:
                return function() {
                  return a.call(r);
                };
              case 1:
                return function(v) {
                  return a.call(r, v);
                };
              case 2:
                return function(v, g) {
                  return a.call(r, v, g);
                };
              case 3:
                return function(v, g, d) {
                  return a.call(r, v, g, d);
                };
            }
            return function() {
              return a.apply(r, arguments);
            };
          };
        },
        /* 41 */
        /***/
        function(s, n) {
          s.exports = function(t) {
            if (typeof t != "function")
              throw TypeError(String(t) + " is not a function");
            return t;
          };
        },
        /* 42 */
        /***/
        function(s, n, t) {
          var e2 = t(165), a = t(8), r = t(13), o = t(19), v = t(16), g = t(63), d = t(51), p = a.WeakMap, f, i, u, l = function(S) {
            return u(S) ? i(S) : f(S, {});
          }, m = function(S) {
            return function(T) {
              var I;
              if (!r(T) || (I = i(T)).type !== S)
                throw TypeError("Incompatible receiver, " + S + " required");
              return I;
            };
          };
          if (e2) {
            var c = new p(), h = c.get, A = c.has, y = c.set;
            f = function(S, T) {
              return y.call(c, S, T), T;
            }, i = function(S) {
              return h.call(c, S) || {};
            }, u = function(S) {
              return A.call(c, S);
            };
          } else {
            var x = g("state");
            d[x] = true, f = function(S, T) {
              return o(S, x, T), T;
            }, i = function(S) {
              return v(S, x) ? S[x] : {};
            }, u = function(S) {
              return v(S, x);
            };
          }
          s.exports = {
            set: f,
            get: i,
            has: u,
            enforce: l,
            getterFor: m
          };
        },
        /* 43 */
        /***/
        function(s, n) {
          s.exports = true;
        },
        /* 44 */
        /***/
        function(s, n) {
          s.exports = {};
        },
        /* 45 */
        /***/
        function(s, n, t) {
          s.exports = t(261);
        },
        /* 46 */
        /***/
        function(s, n, t) {
          s.exports = t(265);
        },
        /* 47 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createElementFragment = n.createDocumentFragment = n.createElement = n.insertBefore = n.getEndPoint = n.getStartPoint = n.updateRange = n.filterSelectionNodes = void 0;
          var o = t(2), v = t(137), g = o.__importDefault(t(3));
          function d(h) {
            var A = [];
            return (0, r.default)(h).call(h, function(y) {
              var x = y.getNodeName();
              if (x !== v.ListType.OrderedList && x !== v.ListType.UnorderedList)
                A.push(y);
              else if (y.prior)
                A.push(y.prior);
              else {
                var S = y.children();
                S == null || (0, r.default)(S).call(S, function(T) {
                  A.push(g.default(T));
                });
              }
            }), A;
          }
          n.filterSelectionNodes = d;
          function p(h, A, y) {
            var x = h.selection, S = document.createRange();
            A.length > 1 ? (S.setStart(A.elems[0], 0), S.setEnd(A.elems[A.length - 1], A.elems[A.length - 1].childNodes.length)) : S.selectNodeContents(A.elems[0]), y && S.collapse(false), x.saveRange(S), x.restoreSelection();
          }
          n.updateRange = p;
          function f(h) {
            var A;
            return h.prior ? h.prior : g.default((A = h.children()) === null || A === void 0 ? void 0 : A.elems[0]);
          }
          n.getStartPoint = f;
          function i(h) {
            var A;
            return h.prior ? h.prior : g.default((A = h.children()) === null || A === void 0 ? void 0 : A.last().elems[0]);
          }
          n.getEndPoint = i;
          function u(h, A, y) {
            y === void 0 && (y = null), h.parent().elems[0].insertBefore(A, y);
          }
          n.insertBefore = u;
          function l(h) {
            return document.createElement(h);
          }
          n.createElement = l;
          function m() {
            return document.createDocumentFragment();
          }
          n.createDocumentFragment = m;
          function c(h, A, y) {
            return y === void 0 && (y = "li"), (0, r.default)(h).call(h, function(x) {
              var S = l(y);
              S.innerHTML = x.html(), A.appendChild(S), x.remove();
            }), A;
          }
          n.createElementFragment = c;
        },
        /* 48 */
        /***/
        function(s, n) {
          s.exports = function(t, e2) {
            return {
              enumerable: !(t & 1),
              configurable: !(t & 2),
              writable: !(t & 4),
              value: e2
            };
          };
        },
        /* 49 */
        /***/
        function(s, n) {
          s.exports = function(t) {
            if (t == null)
              throw TypeError("Can't call method on " + t);
            return t;
          };
        },
        /* 50 */
        /***/
        function(s, n, t) {
          var e2 = t(164).charAt, a = t(42), r = t(75), o = "String Iterator", v = a.set, g = a.getterFor(o);
          r(String, "String", function(d) {
            v(this, {
              type: o,
              string: String(d),
              index: 0
            });
          }, function() {
            var p = g(this), f = p.string, i = p.index, u;
            return i >= f.length ? { value: void 0, done: true } : (u = e2(f, i), p.index += u.length, { value: u, done: false });
          });
        },
        /* 51 */
        /***/
        function(s, n) {
          s.exports = {};
        },
        /* 52 */
        /***/
        function(s, n, t) {
          var e2 = t(107), a = t(80);
          s.exports = Object.keys || function(o) {
            return e2(o, a);
          };
        },
        /* 53 */
        /***/
        function(s, n, t) {
          var e2 = t(19);
          s.exports = function(a, r, o, v) {
            v && v.enumerable ? a[r] = o : e2(a, r, o);
          };
        },
        /* 54 */
        /***/
        function(s, n, t) {
          t(173);
          var e2 = t(174), a = t(8), r = t(65), o = t(19), v = t(44), g = t(10), d = g("toStringTag");
          for (var p in e2) {
            var f = a[p], i = f && f.prototype;
            i && r(i) !== d && o(i, d, p), v[p] = v.Array;
          }
        },
        /* 55 */
        /***/
        function(s, n, t) {
          var e2 = t(34);
          s.exports = Array.isArray || function(r) {
            return e2(r) == "Array";
          };
        },
        /* 56 */
        /***/
        function(s, n, t) {
          var e2 = t(11), a = t(10), r = t(86), o = a("species");
          s.exports = function(v) {
            return r >= 51 || !e2(function() {
              var g = [], d = g.constructor = {};
              return d[o] = function() {
                return { foo: 1 };
              }, g[v](Boolean).foo !== 1;
            });
          };
        },
        /* 57 */
        /***/
        function(s, n, t) {
          s.exports = t(222);
        },
        /* 58 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.ListHandle = void 0;
          var r = t(2), o = r.__importDefault(t(373)), v = (
            /** @class */
            function() {
              function g(d) {
                this.options = d, this.selectionRangeElem = new o.default();
              }
              return g;
            }()
          );
          n.ListHandle = v;
        },
        /* 59 */
        /***/
        function(s, n, t) {
          var e2 = {}.propertyIsEnumerable, a = Object.getOwnPropertyDescriptor, r = a && !e2.call({ 1: 2 }, 1);
          n.f = r ? function(v) {
            var g = a(this, v);
            return !!g && g.enumerable;
          } : e2;
        },
        /* 60 */
        /***/
        function(s, n, t) {
          var e2 = t(13);
          s.exports = function(a, r) {
            if (!e2(a))
              return a;
            var o, v;
            if (r && typeof (o = a.toString) == "function" && !e2(v = o.call(a)) || typeof (o = a.valueOf) == "function" && !e2(v = o.call(a)) || !r && typeof (o = a.toString) == "function" && !e2(v = o.call(a)))
              return v;
            throw TypeError("Can't convert object to primitive value");
          };
        },
        /* 61 */
        /***/
        function(s, n) {
        },
        /* 62 */
        /***/
        function(s, n) {
          var t = Math.ceil, e2 = Math.floor;
          s.exports = function(a) {
            return isNaN(a = +a) ? 0 : (a > 0 ? e2 : t)(a);
          };
        },
        /* 63 */
        /***/
        function(s, n, t) {
          var e2 = t(74), a = t(64), r = e2("keys");
          s.exports = function(o) {
            return r[o] || (r[o] = a(o));
          };
        },
        /* 64 */
        /***/
        function(s, n) {
          var t = 0, e2 = Math.random();
          s.exports = function(a) {
            return "Symbol(" + String(a === void 0 ? "" : a) + ")_" + (++t + e2).toString(36);
          };
        },
        /* 65 */
        /***/
        function(s, n, t) {
          var e2 = t(81), a = t(34), r = t(10), o = r("toStringTag"), v = a(function() {
            return arguments;
          }()) == "Arguments", g = function(d, p) {
            try {
              return d[p];
            } catch {
            }
          };
          s.exports = e2 ? a : function(d) {
            var p, f, i;
            return d === void 0 ? "Undefined" : d === null ? "Null" : typeof (f = g(p = Object(d), o)) == "string" ? f : v ? a(p) : (i = a(p)) == "Object" && typeof p.callee == "function" ? "Arguments" : i;
          };
        },
        /* 66 */
        /***/
        function(s, n, t) {
          var e2 = t(25), a = t(112), r = t(35), o = t(40), v = t(113), g = t(114), d = function(f, i) {
            this.stopped = f, this.result = i;
          }, p = s.exports = function(f, i, u, l, m) {
            var c = o(i, u, l ? 2 : 1), h, A, y, x, S, T, I;
            if (m)
              h = f;
            else {
              if (A = v(f), typeof A != "function")
                throw TypeError("Target is not iterable");
              if (a(A)) {
                for (y = 0, x = r(f.length); x > y; y++)
                  if (S = l ? c(e2(I = f[y])[0], I[1]) : c(f[y]), S && S instanceof d)
                    return S;
                return new d(false);
              }
              h = A.call(f);
            }
            for (T = h.next; !(I = T.call(h)).done; )
              if (S = g(h, c, I.value, l), typeof S == "object" && S && S instanceof d)
                return S;
            return new d(false);
          };
          p.stop = function(f) {
            return new d(true, f);
          };
        },
        /* 67 */
        /***/
        function(s, n, t) {
          var e2 = t(11);
          s.exports = function(a, r) {
            var o = [][a];
            return !!o && e2(function() {
              o.call(null, r || function() {
                throw 1;
              }, 1);
            });
          };
        },
        /* 68 */
        /***/
        function(s, n) {
          s.exports = `	
\v\f\r                　\u2028\u2029\uFEFF`;
        },
        /* 69 */
        /***/
        function(s, n, t) {
          var e2 = t(60), a = t(18), r = t(48);
          s.exports = function(o, v, g) {
            var d = e2(v);
            d in o ? a.f(o, d, r(0, g)) : o[d] = g;
          };
        },
        /* 70 */
        /***/
        function(s, n, t) {
          s.exports = t(209);
        },
        /* 71 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(59), r = t(48), o = t(30), v = t(60), g = t(16), d = t(100), p = Object.getOwnPropertyDescriptor;
          n.f = e2 ? p : function(i, u) {
            if (i = o(i), u = v(u, true), d)
              try {
                return p(i, u);
              } catch {
              }
            if (g(i, u))
              return r(!a.f.call(i, u), i[u]);
          };
        },
        /* 72 */
        /***/
        function(s, n, t) {
          var e2 = t(11), a = t(34), r = "".split;
          s.exports = e2(function() {
            return !Object("z").propertyIsEnumerable(0);
          }) ? function(o) {
            return a(o) == "String" ? r.call(o, "") : Object(o);
          } : Object;
        },
        /* 73 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(13), r = e2.document, o = a(r) && a(r.createElement);
          s.exports = function(v) {
            return o ? r.createElement(v) : {};
          };
        },
        /* 74 */
        /***/
        function(s, n, t) {
          var e2 = t(43), a = t(103);
          (s.exports = function(r, o) {
            return a[r] || (a[r] = o !== void 0 ? o : {});
          })("versions", []).push({
            version: "3.6.4",
            mode: e2 ? "pure" : "global",
            copyright: "© 2020 Denis Pushkarev (zloirock.ru)"
          });
        },
        /* 75 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(167), r = t(105), o = t(171), v = t(37), g = t(19), d = t(53), p = t(10), f = t(43), i = t(44), u = t(104), l = u.IteratorPrototype, m = u.BUGGY_SAFARI_ITERATORS, c = p("iterator"), h = "keys", A = "values", y = "entries", x = function() {
            return this;
          };
          s.exports = function(S, T, I, C, E, D, P) {
            a(I, T, C);
            var M = function(j) {
              if (j === E && O)
                return O;
              if (!m && j in B)
                return B[j];
              switch (j) {
                case h:
                  return function() {
                    return new I(this, j);
                  };
                case A:
                  return function() {
                    return new I(this, j);
                  };
                case y:
                  return function() {
                    return new I(this, j);
                  };
              }
              return function() {
                return new I(this);
              };
            }, R = T + " Iterator", N = false, B = S.prototype, F = B[c] || B["@@iterator"] || E && B[E], O = !m && F || M(E), H = T == "Array" && B.entries || F, L, U, z;
            if (H && (L = r(H.call(new S())), l !== Object.prototype && L.next && (!f && r(L) !== l && (o ? o(L, l) : typeof L[c] != "function" && g(L, c, x)), v(L, R, true, true), f && (i[R] = x))), E == A && F && F.name !== A && (N = true, O = function() {
              return F.call(this);
            }), (!f || P) && B[c] !== O && g(B, c, O), i[T] = O, E)
              if (U = {
                values: M(A),
                keys: D ? O : M(h),
                entries: M(y)
              }, P)
                for (z in U)
                  (m || N || !(z in B)) && d(B, z, U[z]);
              else
                e2({ target: T, proto: true, forced: m || N }, U);
            return U;
          };
        },
        /* 76 */
        /***/
        function(s, n, t) {
          var e2 = t(11);
          s.exports = !!Object.getOwnPropertySymbols && !e2(function() {
            return !String(Symbol());
          });
        },
        /* 77 */
        /***/
        function(s, n, t) {
          var e2 = t(25), a = t(169), r = t(80), o = t(51), v = t(108), g = t(73), d = t(63), p = ">", f = "<", i = "prototype", u = "script", l = d("IE_PROTO"), m = function() {
          }, c = function(S) {
            return f + u + p + S + f + "/" + u + p;
          }, h = function(S) {
            S.write(c("")), S.close();
            var T = S.parentWindow.Object;
            return S = null, T;
          }, A = function() {
            var S = g("iframe"), T = "java" + u + ":", I;
            return S.style.display = "none", v.appendChild(S), S.src = String(T), I = S.contentWindow.document, I.open(), I.write(c("document.F=Object")), I.close(), I.F;
          }, y, x = function() {
            try {
              y = document.domain && new ActiveXObject("htmlfile");
            } catch {
            }
            x = y ? h(y) : A();
            for (var S = r.length; S--; )
              delete x[i][r[S]];
            return x();
          };
          o[l] = true, s.exports = Object.create || function(T, I) {
            var C;
            return T !== null ? (m[i] = e2(T), C = new m(), m[i] = null, C[l] = T) : C = x(), I === void 0 ? C : a(C, I);
          };
        },
        /* 78 */
        /***/
        function(s, n, t) {
          var e2 = t(30), a = t(35), r = t(79), o = function(v) {
            return function(g, d, p) {
              var f = e2(g), i = a(f.length), u = r(p, i), l;
              if (v && d != d) {
                for (; i > u; )
                  if (l = f[u++], l != l)
                    return true;
              } else
                for (; i > u; u++)
                  if ((v || u in f) && f[u] === d)
                    return v || u || 0;
              return !v && -1;
            };
          };
          s.exports = {
            // `Array.prototype.includes` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.includes
            includes: o(true),
            // `Array.prototype.indexOf` method
            // https://tc39.github.io/ecma262/#sec-array.prototype.indexof
            indexOf: o(false)
          };
        },
        /* 79 */
        /***/
        function(s, n, t) {
          var e2 = t(62), a = Math.max, r = Math.min;
          s.exports = function(o, v) {
            var g = e2(o);
            return g < 0 ? a(g + v, 0) : r(g, v);
          };
        },
        /* 80 */
        /***/
        function(s, n) {
          s.exports = [
            "constructor",
            "hasOwnProperty",
            "isPrototypeOf",
            "propertyIsEnumerable",
            "toLocaleString",
            "toString",
            "valueOf"
          ];
        },
        /* 81 */
        /***/
        function(s, n, t) {
          var e2 = t(10), a = e2("toStringTag"), r = {};
          r[a] = "z", s.exports = String(r) === "[object z]";
        },
        /* 82 */
        /***/
        function(s, n) {
          s.exports = function() {
          };
        },
        /* 83 */
        /***/
        function(s, n) {
          s.exports = function(t, e2, a) {
            if (!(t instanceof e2))
              throw TypeError("Incorrect " + (a ? a + " " : "") + "invocation");
            return t;
          };
        },
        /* 84 */
        /***/
        function(s, n, t) {
          var e2 = t(36);
          s.exports = e2("navigator", "userAgent") || "";
        },
        /* 85 */
        /***/
        function(s, n, t) {
          var e2 = t(41), a = function(r) {
            var o, v;
            this.promise = new r(function(g, d) {
              if (o !== void 0 || v !== void 0)
                throw TypeError("Bad Promise constructor");
              o = g, v = d;
            }), this.resolve = e2(o), this.reject = e2(v);
          };
          s.exports.f = function(r) {
            return new a(r);
          };
        },
        /* 86 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(84), r = e2.process, o = r && r.versions, v = o && o.v8, g, d;
          v ? (g = v.split("."), d = g[0] + g[1]) : a && (g = a.match(/Edge\/(\d+)/), (!g || g[1] >= 74) && (g = a.match(/Chrome\/(\d+)/), g && (d = g[1]))), s.exports = d && +d;
        },
        /* 87 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(6), d = o.__importDefault(t(267)), p = o.__importDefault(t(280)), f = o.__importDefault(t(281)), i = o.__importDefault(t(282)), u = o.__importDefault(t(301)), l = o.__importStar(t(416)), m = o.__importDefault(t(417)), c = o.__importDefault(t(418)), h = o.__importDefault(t(419)), A = o.__importStar(t(420)), y = o.__importDefault(t(423)), x = o.__importDefault(t(424)), S = o.__importDefault(t(425)), T = o.__importDefault(t(427)), I = o.__importDefault(t(437)), C = o.__importDefault(t(440)), E = o.__importStar(t(441)), D = o.__importDefault(t(23)), P = o.__importDefault(t(134)), M = o.__importDefault(t(24)), R = o.__importDefault(t(33)), N = o.__importDefault(t(38)), B = o.__importDefault(t(39)), F = 1, O = (
            /** @class */
            function() {
              function H(L, U) {
                this.pluginsFunctionList = {}, this.beforeDestroyHooks = [], this.id = "wangEditor-" + F++, this.toolbarSelector = L, this.textSelector = U, l.selectorValidator(this), this.config = g.deepClone(d.default), this.$toolbarElem = v.default("<div></div>"), this.$textContainerElem = v.default("<div></div>"), this.$textElem = v.default("<div></div>"), this.toolbarElemId = "", this.textElemId = "", this.isFocus = false, this.isComposing = false, this.isCompatibleMode = false, this.selection = new p.default(this), this.cmd = new f.default(this), this.txt = new i.default(this), this.menus = new u.default(this), this.zIndex = new x.default(), this.change = new S.default(this), this.history = new T.default(this), this.onSelectionChange = new C.default(this);
                var z = I.default(this), j = z.disable, K = z.enable;
                this.disable = j, this.enable = K, this.isEnable = true;
              }
              return H.prototype.initSelection = function(L) {
                m.default(this, L);
              }, H.prototype.create = function() {
                this.zIndex.init(this), this.isCompatibleMode = this.config.compatibleMode(), this.isCompatibleMode || (this.config.onchangeTimeout = 30), h.default(this), l.default(this), this.txt.init(), this.menus.init(), A.default(this), this.initSelection(true), c.default(this), this.change.observe(), this.history.observe(), E.default(this);
              }, H.prototype.beforeDestroy = function(L) {
                return this.beforeDestroyHooks.push(L), this;
              }, H.prototype.destroy = function() {
                var L, U = this;
                (0, r.default)(L = this.beforeDestroyHooks).call(L, function(z) {
                  return z.call(U);
                }), this.$toolbarElem.remove(), this.$textContainerElem.remove();
              }, H.prototype.fullScreen = function() {
                A.setFullScreen(this);
              }, H.prototype.unFullScreen = function() {
                A.setUnFullScreen(this);
              }, H.prototype.scrollToHead = function(L) {
                y.default(this, L);
              }, H.registerMenu = function(L, U) {
                !U || typeof U != "function" || (H.globalCustomMenuConstructorList[L] = U);
              }, H.prototype.registerPlugin = function(L, U) {
                E.registerPlugin(L, U, this.pluginsFunctionList);
              }, H.registerPlugin = function(L, U) {
                E.registerPlugin(L, U, H.globalPluginsFunctionList);
              }, H.$ = v.default, H.BtnMenu = D.default, H.DropList = P.default, H.DropListMenu = M.default, H.Panel = R.default, H.PanelMenu = N.default, H.Tooltip = B.default, H.globalCustomMenuConstructorList = {}, H.globalPluginsFunctionList = {}, H;
            }()
          );
          n.default = O;
        },
        /* 88 */
        /***/
        function(s, n, t) {
          var e2 = t(13), a = t(55), r = t(10), o = r("species");
          s.exports = function(v, g) {
            var d;
            return a(v) && (d = v.constructor, typeof d == "function" && (d === Array || a(d.prototype)) ? d = void 0 : e2(d) && (d = d[o], d === null && (d = void 0))), new (d === void 0 ? Array : d)(g === 0 ? 0 : g);
          };
        },
        /* 89 */
        /***/
        function(s, n, t) {
          s.exports = t(185);
        },
        /* 90 */
        /***/
        function(s, n, t) {
          var e2 = t(49), a = t(68), r = "[" + a + "]", o = RegExp("^" + r + r + "*"), v = RegExp(r + r + "*$"), g = function(d) {
            return function(p) {
              var f = String(e2(p));
              return d & 1 && (f = f.replace(o, "")), d & 2 && (f = f.replace(v, "")), f;
            };
          };
          s.exports = {
            // `String.prototype.{ trimLeft, trimStart }` methods
            // https://tc39.github.io/ecma262/#sec-string.prototype.trimstart
            start: g(1),
            // `String.prototype.{ trimRight, trimEnd }` methods
            // https://tc39.github.io/ecma262/#sec-string.prototype.trimend
            end: g(2),
            // `String.prototype.trim` method
            // https://tc39.github.io/ecma262/#sec-string.prototype.trim
            trim: g(3)
          };
        },
        /* 91 */
        /***/
        function(s, n, t) {
          s.exports = t(205);
        },
        /* 92 */
        /***/
        function(s, n, t) {
          var e2 = t(227), a = t(230);
          function r(o) {
            "@babel/helpers - typeof";
            return typeof a == "function" && typeof e2 == "symbol" ? s.exports = r = function(g) {
              return typeof g;
            } : s.exports = r = function(g) {
              return g && typeof a == "function" && g.constructor === a && g !== a.prototype ? "symbol" : typeof g;
            }, r(o);
          }
          s.exports = r;
        },
        /* 93 */
        /***/
        function(s, n, t) {
          var e2 = t(10);
          n.f = e2;
        },
        /* 94 */
        /***/
        function(s, n, t) {
          s.exports = t(306);
        },
        /* 95 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(33)), g = (
            /** @class */
            function() {
              function d(p, f) {
                var i = this;
                this.$elem = p, this.editor = f, this._active = false, p.on("click", function(u) {
                  var l;
                  v.default.hideCurAllPanels(), (0, r.default)(l = f.txt.eventHooks.menuClickEvents).call(l, function(m) {
                    return m();
                  }), u.stopPropagation(), f.selection.getRange() != null && i.clickHandler(u);
                });
              }
              return d.prototype.clickHandler = function(p) {
              }, d.prototype.active = function() {
                this._active = true, this.$elem.addClass("w-e-active");
              }, d.prototype.unActive = function() {
                this._active = false, this.$elem.removeClass("w-e-active");
              }, (0, a.default)(d.prototype, "isActive", {
                /**
                 * 是否处于激活状态
                 */
                get: function() {
                  return this._active;
                },
                enumerable: false,
                configurable: true
              }), d;
            }()
          );
          n.default = g;
        },
        /* 96 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.getParentNodeA = n.EXTRA_TAG = void 0, n.EXTRA_TAG = ["B", "FONT", "I", "STRIKE"];
          function o(g) {
            for (var d = g.elems[0]; d && (0, r.default)(p = n.EXTRA_TAG).call(p, d.nodeName); ) {
              var p;
              if (d = d.parentElement, d.nodeName === "A")
                return d;
            }
          }
          n.getParentNodeA = o;
          function v(g) {
            var d, p = g.selection.getSelectionContainerElem();
            if (!(!((d = p == null ? void 0 : p.elems) === null || d === void 0) && d.length))
              return false;
            if (p.getNodeName() === "A")
              return true;
            var f = o(p);
            return !!(f && f.nodeName === "A");
          }
          n.default = v;
        },
        /* 97 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(57)), o = e2(t(4)), v = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var g = t(2), d = t(6), p = g.__importDefault(t(135)), f = g.__importDefault(t(136)), i = (
            /** @class */
            function() {
              function u(l) {
                this.editor = l;
              }
              return u.prototype.insertImg = function(l, m, c) {
                var h = this.editor, A = h.config, y = "validate.", x = function(D, P) {
                  return P === void 0 && (P = y), h.i18next.t(P + D);
                }, S = l.replace(/</g, "&lt;").replace(/>/g, "&gt;");
                S = S.replace("'", '"');
                var T = "";
                c && (T = c.replace("'", '"'), T = "data-href='" + encodeURIComponent(T) + "' ");
                var I = "";
                m && (I = m.replace(/</g, "&lt;").replace(/>/g, "&gt;"), I = I.replace("'", '"'), I = "alt='" + I + "' "), h.cmd.do("insertHTML", "<img src='" + S + "' " + I + T + 'style="max-width:100%;" contenteditable="false"/>'), A.linkImgCallback(l, m, c);
                var C = document.createElement("img");
                C.onload = function() {
                  C = null;
                }, C.onerror = function() {
                  A.customAlert(x("插入图片错误"), "error", "wangEditor: " + x("插入图片错误") + "，" + x("图片链接") + ' "' + l + '"，' + x("下载链接失败")), C = null;
                }, C.onabort = function() {
                  return C = null;
                }, C.src = l;
              }, u.prototype.uploadImg = function(l) {
                var m = this;
                if (l.length) {
                  var c = this.editor, h = c.config, A = "validate.", y = function(Q) {
                    return c.i18next.t(A + Q);
                  }, x = h.uploadImgServer, S = h.uploadImgShowBase64, T = h.uploadImgMaxSize, I = T / 1024 / 1024, C = h.uploadImgMaxLength, E = h.uploadFileName, D = h.uploadImgParams, P = h.uploadImgParamsWithUrl, M = h.uploadImgHeaders, R = h.uploadImgHooks, N = h.uploadImgTimeout, B = h.withCredentials, F = h.customUploadImg;
                  if (!(!F && !x && !S)) {
                    var O = [], H = [];
                    if (d.arrForEach(l, function(V) {
                      if (V) {
                        var Q = V.name || V.type.replace("/", "."), w = V.size;
                        if (!(!Q || !w)) {
                          var G = c.config.uploadImgAccept.join("|"), X = ".(" + G + ")$", tt = new RegExp(X, "i");
                          if (tt.test(Q) === false) {
                            H.push("【" + Q + "】" + y("不是图片"));
                            return;
                          }
                          if (T < w) {
                            H.push("【" + Q + "】" + y("大于") + " " + I + "M");
                            return;
                          }
                          O.push(V);
                        }
                      }
                    }), H.length) {
                      h.customAlert(y("图片验证未通过") + `: 
` + H.join(`
`), "warning");
                      return;
                    }
                    if (O.length === 0) {
                      h.customAlert(y("传入的文件不合法"), "warning");
                      return;
                    }
                    if (O.length > C) {
                      h.customAlert(y("一次最多上传") + C + y("张图片"), "warning");
                      return;
                    }
                    if (F && typeof F == "function") {
                      var L;
                      F(O, (0, r.default)(L = this.insertImg).call(L, this));
                      return;
                    }
                    var U = new FormData();
                    if ((0, o.default)(O).call(O, function(V, Q) {
                      var w = E || V.name;
                      O.length > 1 && (w = w + (Q + 1)), U.append(w, V);
                    }), x) {
                      var z = x.split("#");
                      x = z[0];
                      var j = z[1] || "";
                      (0, o.default)(d).call(d, D, function(V, Q) {
                        P && ((0, v.default)(x).call(x, "?") > 0 ? x += "&" : x += "?", x = x + V + "=" + Q), U.append(V, Q);
                      }), j && (x += "#" + j);
                      var K = p.default(x, {
                        timeout: N,
                        formData: U,
                        headers: M,
                        withCredentials: !!B,
                        beforeSend: function(Q) {
                          if (R.before)
                            return R.before(Q, c, O);
                        },
                        onTimeout: function(Q) {
                          h.customAlert(y("上传图片超时"), "error"), R.timeout && R.timeout(Q, c);
                        },
                        onProgress: function(Q, w) {
                          var G = new f.default(c);
                          w.lengthComputable && (Q = w.loaded / w.total, G.show(Q));
                        },
                        onError: function(Q) {
                          h.customAlert(y("上传图片错误"), "error", y("上传图片错误") + "，" + y("服务器返回状态") + ": " + Q.status), R.error && R.error(Q, c);
                        },
                        onFail: function(Q, w) {
                          h.customAlert(y("上传图片失败"), "error", y("上传图片返回结果错误") + ("，" + y("返回结果") + ": ") + w), R.fail && R.fail(Q, c, w);
                        },
                        onSuccess: function(Q, w) {
                          if (R.customInsert) {
                            var G;
                            R.customInsert((0, r.default)(G = m.insertImg).call(G, m), w, c);
                            return;
                          }
                          if (w.errno != "0") {
                            h.customAlert(y("上传图片失败"), "error", y("上传图片返回结果错误") + "，" + y("返回结果") + " errno=" + w.errno), R.fail && R.fail(Q, c, w);
                            return;
                          }
                          var X = w.data;
                          (0, o.default)(X).call(X, function(tt) {
                            typeof tt == "string" ? m.insertImg(tt) : m.insertImg(tt.url, tt.alt, tt.href);
                          }), R.success && R.success(Q, c, w);
                        }
                      });
                      typeof K == "string" && h.customAlert(K, "error");
                      return;
                    }
                    S && d.arrForEach(l, function(V) {
                      var Q = m, w = new FileReader();
                      w.readAsDataURL(V), w.onload = function() {
                        if (this.result) {
                          var G = this.result.toString();
                          Q.insertImg(G, G);
                        }
                      };
                    });
                  }
                }
              }, u;
            }()
          );
          n.default = i;
        },
        /* 98 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(410)), o = e2(t(4)), v = e2(t(45));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.dealTextNode = n.isAllTodo = n.isTodo = n.getCursorNextNode = void 0;
          function g(u) {
            return u.length ? u.attr("class") === "w-e-todo" : false;
          }
          n.isTodo = g;
          function d(u) {
            var l = u.selection.getSelectionRangeTopNodes();
            if (l.length !== 0)
              return (0, r.default)(l).call(l, function(m) {
                return g(m);
              });
          }
          n.isAllTodo = d;
          function p(u, l, m) {
            var c;
            if (u.hasChildNodes()) {
              var h = u.cloneNode(), A = false;
              l.nodeValue === "" && (A = true);
              var y = [];
              return (0, o.default)(c = u.childNodes).call(c, function(x) {
                if (!f(x, l) && A && (h.appendChild(x.cloneNode(true)), x.nodeName !== "BR" && y.push(x)), f(x, l)) {
                  if (x.nodeType === 1) {
                    var S = p(x, l, m);
                    S && S.textContent !== "" && (h == null || h.appendChild(S));
                  }
                  if (x.nodeType === 3 && l.isEqualNode(x)) {
                    var T = i(x, m);
                    h.textContent = T;
                  }
                  A = true;
                }
              }), (0, o.default)(y).call(y, function(x) {
                var S = x;
                S.remove();
              }), h;
            }
          }
          n.getCursorNextNode = p;
          function f(u, l) {
            return u.nodeType === 3 ? u.nodeValue === l.nodeValue : u.contains(l);
          }
          function i(u, l, m) {
            m === void 0 && (m = true);
            var c = u.nodeValue, h = c == null ? void 0 : (0, v.default)(c).call(c, 0, l);
            if (c = c == null ? void 0 : (0, v.default)(c).call(c, l), !m) {
              var A = c;
              c = h, h = A;
            }
            return u.nodeValue = h, c;
          }
          n.dealTextNode = i;
        },
        /* 99 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(430), o = (
            /** @class */
            function() {
              function v(g) {
                this.maxSize = g, this.isRe = false, this.data = new r.CeilStack(g), this.revokeData = new r.CeilStack(g);
              }
              return (0, a.default)(v.prototype, "size", {
                /**
                 * 返回当前栈中的数据长度。格式为：[正常的数据的条数，被撤销的数据的条数]
                 */
                get: function() {
                  return [this.data.size, this.revokeData.size];
                },
                enumerable: false,
                configurable: true
              }), v.prototype.resetMaxSize = function(g) {
                this.data.resetMax(g), this.revokeData.resetMax(g);
              }, v.prototype.save = function(g) {
                return this.isRe && (this.revokeData.clear(), this.isRe = false), this.data.instack(g), this;
              }, v.prototype.revoke = function(g) {
                !this.isRe && (this.isRe = true);
                var d = this.data.outstack();
                return d ? (this.revokeData.instack(d), g(d), true) : false;
              }, v.prototype.restore = function(g) {
                !this.isRe && (this.isRe = true);
                var d = this.revokeData.outstack();
                return d ? (this.data.instack(d), g(d), true) : false;
              }, v;
            }()
          );
          n.default = o;
        },
        /* 100 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(11), r = t(73);
          s.exports = !e2 && !a(function() {
            return Object.defineProperty(r("div"), "a", {
              get: function() {
                return 7;
              }
            }).a != 7;
          });
        },
        /* 101 */
        /***/
        function(s, n, t) {
          var e2 = t(11), a = /#|\.prototype\./, r = function(p, f) {
            var i = v[o(p)];
            return i == d ? true : i == g ? false : typeof f == "function" ? e2(f) : !!f;
          }, o = r.normalize = function(p) {
            return String(p).replace(a, ".").toLowerCase();
          }, v = r.data = {}, g = r.NATIVE = "N", d = r.POLYFILL = "P";
          s.exports = r;
        },
        /* 102 */
        /***/
        function(s, n, t) {
          var e2 = t(103), a = Function.toString;
          typeof e2.inspectSource != "function" && (e2.inspectSource = function(r) {
            return a.call(r);
          }), s.exports = e2.inspectSource;
        },
        /* 103 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(166), r = "__core-js_shared__", o = e2[r] || a(r, {});
          s.exports = o;
        },
        /* 104 */
        /***/
        function(s, n, t) {
          var e2 = t(105), a = t(19), r = t(16), o = t(10), v = t(43), g = o("iterator"), d = false, p = function() {
            return this;
          }, f, i, u;
          [].keys && (u = [].keys(), "next" in u ? (i = e2(e2(u)), i !== Object.prototype && (f = i)) : d = true), f == null && (f = {}), !v && !r(f, g) && a(f, g, p), s.exports = {
            IteratorPrototype: f,
            BUGGY_SAFARI_ITERATORS: d
          };
        },
        /* 105 */
        /***/
        function(s, n, t) {
          var e2 = t(16), a = t(31), r = t(63), o = t(168), v = r("IE_PROTO"), g = Object.prototype;
          s.exports = o ? Object.getPrototypeOf : function(d) {
            return d = a(d), e2(d, v) ? d[v] : typeof d.constructor == "function" && d instanceof d.constructor ? d.constructor.prototype : d instanceof Object ? g : null;
          };
        },
        /* 106 */
        /***/
        function(s, n, t) {
          var e2 = t(76);
          s.exports = e2 && !Symbol.sham && typeof Symbol.iterator == "symbol";
        },
        /* 107 */
        /***/
        function(s, n, t) {
          var e2 = t(16), a = t(30), r = t(78).indexOf, o = t(51);
          s.exports = function(v, g) {
            var d = a(v), p = 0, f = [], i;
            for (i in d)
              !e2(o, i) && e2(d, i) && f.push(i);
            for (; g.length > p; )
              e2(d, i = g[p++]) && (~r(f, i) || f.push(i));
            return f;
          };
        },
        /* 108 */
        /***/
        function(s, n, t) {
          var e2 = t(36);
          s.exports = e2("document", "documentElement");
        },
        /* 109 */
        /***/
        function(s, n, t) {
          var e2 = t(8);
          s.exports = e2.Promise;
        },
        /* 110 */
        /***/
        function(s, n, t) {
          var e2 = t(53);
          s.exports = function(a, r, o) {
            for (var v in r)
              o && o.unsafe && a[v] ? a[v] = r[v] : e2(a, v, r[v], o);
            return a;
          };
        },
        /* 111 */
        /***/
        function(s, n, t) {
          var e2 = t(36), a = t(18), r = t(10), o = t(14), v = r("species");
          s.exports = function(g) {
            var d = e2(g), p = a.f;
            o && d && !d[v] && p(d, v, {
              configurable: true,
              get: function() {
                return this;
              }
            });
          };
        },
        /* 112 */
        /***/
        function(s, n, t) {
          var e2 = t(10), a = t(44), r = e2("iterator"), o = Array.prototype;
          s.exports = function(v) {
            return v !== void 0 && (a.Array === v || o[r] === v);
          };
        },
        /* 113 */
        /***/
        function(s, n, t) {
          var e2 = t(65), a = t(44), r = t(10), o = r("iterator");
          s.exports = function(v) {
            if (v != null)
              return v[o] || v["@@iterator"] || a[e2(v)];
          };
        },
        /* 114 */
        /***/
        function(s, n, t) {
          var e2 = t(25);
          s.exports = function(a, r, o, v) {
            try {
              return v ? r(e2(o)[0], o[1]) : r(o);
            } catch (d) {
              var g = a.return;
              throw g !== void 0 && e2(g.call(a)), d;
            }
          };
        },
        /* 115 */
        /***/
        function(s, n, t) {
          var e2 = t(10), a = e2("iterator"), r = false;
          try {
            var o = 0, v = {
              next: function() {
                return { done: !!o++ };
              },
              return: function() {
                r = true;
              }
            };
            v[a] = function() {
              return this;
            }, Array.from(v, function() {
              throw 2;
            });
          } catch {
          }
          s.exports = function(g, d) {
            if (!d && !r)
              return false;
            var p = false;
            try {
              var f = {};
              f[a] = function() {
                return {
                  next: function() {
                    return { done: p = true };
                  }
                };
              }, g(f);
            } catch {
            }
            return p;
          };
        },
        /* 116 */
        /***/
        function(s, n, t) {
          var e2 = t(25), a = t(41), r = t(10), o = r("species");
          s.exports = function(v, g) {
            var d = e2(v).constructor, p;
            return d === void 0 || (p = e2(d)[o]) == null ? g : a(p);
          };
        },
        /* 117 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(11), r = t(34), o = t(40), v = t(108), g = t(73), d = t(118), p = e2.location, f = e2.setImmediate, i = e2.clearImmediate, u = e2.process, l = e2.MessageChannel, m = e2.Dispatch, c = 0, h = {}, A = "onreadystatechange", y, x, S, T = function(D) {
            if (h.hasOwnProperty(D)) {
              var P = h[D];
              delete h[D], P();
            }
          }, I = function(D) {
            return function() {
              T(D);
            };
          }, C = function(D) {
            T(D.data);
          }, E = function(D) {
            e2.postMessage(D + "", p.protocol + "//" + p.host);
          };
          (!f || !i) && (f = function(P) {
            for (var M = [], R = 1; arguments.length > R; )
              M.push(arguments[R++]);
            return h[++c] = function() {
              (typeof P == "function" ? P : Function(P)).apply(void 0, M);
            }, y(c), c;
          }, i = function(P) {
            delete h[P];
          }, r(u) == "process" ? y = function(D) {
            u.nextTick(I(D));
          } : m && m.now ? y = function(D) {
            m.now(I(D));
          } : l && !d ? (x = new l(), S = x.port2, x.port1.onmessage = C, y = o(S.postMessage, S, 1)) : e2.addEventListener && typeof postMessage == "function" && !e2.importScripts && !a(E) && p.protocol !== "file:" ? (y = E, e2.addEventListener("message", C, false)) : A in g("script") ? y = function(D) {
            v.appendChild(g("script"))[A] = function() {
              v.removeChild(this), T(D);
            };
          } : y = function(D) {
            setTimeout(I(D), 0);
          }), s.exports = {
            set: f,
            clear: i
          };
        },
        /* 118 */
        /***/
        function(s, n, t) {
          var e2 = t(84);
          s.exports = /(iphone|ipod|ipad).*applewebkit/i.test(e2);
        },
        /* 119 */
        /***/
        function(s, n, t) {
          var e2 = t(25), a = t(13), r = t(85);
          s.exports = function(o, v) {
            if (e2(o), a(v) && v.constructor === o)
              return v;
            var g = r.f(o), d = g.resolve;
            return d(v), g.promise;
          };
        },
        /* 120 */
        /***/
        function(s, n) {
          s.exports = function(t) {
            try {
              return { error: false, value: t() };
            } catch (e2) {
              return { error: true, value: e2 };
            }
          };
        },
        /* 121 */
        /***/
        function(s, n, t) {
          s.exports = t(197);
        },
        /* 122 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(8), r = t(123), o = t(11), v = t(19), g = t(66), d = t(83), p = t(13), f = t(37), i = t(18).f, u = t(32).forEach, l = t(14), m = t(42), c = m.set, h = m.getterFor;
          s.exports = function(A, y, x) {
            var S = A.indexOf("Map") !== -1, T = A.indexOf("Weak") !== -1, I = S ? "set" : "add", C = a[A], E = C && C.prototype, D = {}, P;
            if (!l || typeof C != "function" || !(T || E.forEach && !o(function() {
              new C().entries().next();
            })))
              P = x.getConstructor(y, A, S, I), r.REQUIRED = true;
            else {
              P = y(function(R, N) {
                c(d(R, P, A), {
                  type: A,
                  collection: new C()
                }), N != null && g(N, R[I], R, S);
              });
              var M = h(A);
              u(["add", "clear", "delete", "forEach", "get", "has", "set", "keys", "values", "entries"], function(R) {
                var N = R == "add" || R == "set";
                R in E && !(T && R == "clear") && v(P.prototype, R, function(B, F) {
                  var O = M(this).collection;
                  if (!N && T && !p(B))
                    return R == "get" ? void 0 : false;
                  var H = O[R](B === 0 ? 0 : B, F);
                  return N ? this : H;
                });
              }), T || i(P.prototype, "size", {
                configurable: true,
                get: function() {
                  return M(this).collection.size;
                }
              });
            }
            return f(P, A, false, true), D[A] = P, e2({ global: true, forced: true }, D), T || x.setStrong(P, A, S), P;
          };
        },
        /* 123 */
        /***/
        function(s, n, t) {
          var e2 = t(51), a = t(13), r = t(16), o = t(18).f, v = t(64), g = t(200), d = v("meta"), p = 0, f = Object.isExtensible || function() {
            return true;
          }, i = function(h) {
            o(h, d, { value: {
              objectID: "O" + ++p,
              // object ID
              weakData: {}
              // weak collections IDs
            } });
          }, u = function(h, A) {
            if (!a(h))
              return typeof h == "symbol" ? h : (typeof h == "string" ? "S" : "P") + h;
            if (!r(h, d)) {
              if (!f(h))
                return "F";
              if (!A)
                return "E";
              i(h);
            }
            return h[d].objectID;
          }, l = function(h, A) {
            if (!r(h, d)) {
              if (!f(h))
                return true;
              if (!A)
                return false;
              i(h);
            }
            return h[d].weakData;
          }, m = function(h) {
            return g && c.REQUIRED && f(h) && !r(h, d) && i(h), h;
          }, c = s.exports = {
            REQUIRED: false,
            fastKey: u,
            getWeakData: l,
            onFreeze: m
          };
          e2[d] = true;
        },
        /* 124 */
        /***/
        function(s, n, t) {
          var e2 = t(18).f, a = t(77), r = t(110), o = t(40), v = t(83), g = t(66), d = t(75), p = t(111), f = t(14), i = t(123).fastKey, u = t(42), l = u.set, m = u.getterFor;
          s.exports = {
            getConstructor: function(c, h, A, y) {
              var x = c(function(C, E) {
                v(C, x, h), l(C, {
                  type: h,
                  index: a(null),
                  first: void 0,
                  last: void 0,
                  size: 0
                }), f || (C.size = 0), E != null && g(E, C[y], C, A);
              }), S = m(h), T = function(C, E, D) {
                var P = S(C), M = I(C, E), R, N;
                return M ? M.value = D : (P.last = M = {
                  index: N = i(E, true),
                  key: E,
                  value: D,
                  previous: R = P.last,
                  next: void 0,
                  removed: false
                }, P.first || (P.first = M), R && (R.next = M), f ? P.size++ : C.size++, N !== "F" && (P.index[N] = M)), C;
              }, I = function(C, E) {
                var D = S(C), P = i(E), M;
                if (P !== "F")
                  return D.index[P];
                for (M = D.first; M; M = M.next)
                  if (M.key == E)
                    return M;
              };
              return r(x.prototype, {
                // 23.1.3.1 Map.prototype.clear()
                // 23.2.3.2 Set.prototype.clear()
                clear: function() {
                  for (var E = this, D = S(E), P = D.index, M = D.first; M; )
                    M.removed = true, M.previous && (M.previous = M.previous.next = void 0), delete P[M.index], M = M.next;
                  D.first = D.last = void 0, f ? D.size = 0 : E.size = 0;
                },
                // 23.1.3.3 Map.prototype.delete(key)
                // 23.2.3.4 Set.prototype.delete(value)
                delete: function(C) {
                  var E = this, D = S(E), P = I(E, C);
                  if (P) {
                    var M = P.next, R = P.previous;
                    delete D.index[P.index], P.removed = true, R && (R.next = M), M && (M.previous = R), D.first == P && (D.first = M), D.last == P && (D.last = R), f ? D.size-- : E.size--;
                  }
                  return !!P;
                },
                // 23.2.3.6 Set.prototype.forEach(callbackfn, thisArg = undefined)
                // 23.1.3.5 Map.prototype.forEach(callbackfn, thisArg = undefined)
                forEach: function(E) {
                  for (var D = S(this), P = o(E, arguments.length > 1 ? arguments[1] : void 0, 3), M; M = M ? M.next : D.first; )
                    for (P(M.value, M.key, this); M && M.removed; )
                      M = M.previous;
                },
                // 23.1.3.7 Map.prototype.has(key)
                // 23.2.3.7 Set.prototype.has(value)
                has: function(E) {
                  return !!I(this, E);
                }
              }), r(x.prototype, A ? {
                // 23.1.3.6 Map.prototype.get(key)
                get: function(E) {
                  var D = I(this, E);
                  return D && D.value;
                },
                // 23.1.3.9 Map.prototype.set(key, value)
                set: function(E, D) {
                  return T(this, E === 0 ? 0 : E, D);
                }
              } : {
                // 23.2.3.1 Set.prototype.add(value)
                add: function(E) {
                  return T(this, E = E === 0 ? 0 : E, E);
                }
              }), f && e2(x.prototype, "size", {
                get: function() {
                  return S(this).size;
                }
              }), x;
            },
            setStrong: function(c, h, A) {
              var y = h + " Iterator", x = m(h), S = m(y);
              d(c, h, function(T, I) {
                l(this, {
                  type: y,
                  target: T,
                  state: x(T),
                  kind: I,
                  last: void 0
                });
              }, function() {
                for (var T = S(this), I = T.kind, C = T.last; C && C.removed; )
                  C = C.previous;
                return !T.target || !(T.last = C = C ? C.next : T.state.first) ? (T.target = void 0, { value: void 0, done: true }) : I == "keys" ? { value: C.key, done: false } : I == "values" ? { value: C.value, done: false } : { value: [C.key, C.value], done: false };
              }, A ? "entries" : "values", !A, true), p(h);
            }
          };
        },
        /* 125 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("iterator");
        },
        /* 126 */
        /***/
        function(s, n, t) {
          var e2 = t(107), a = t(80), r = a.concat("length", "prototype");
          n.f = Object.getOwnPropertyNames || function(v) {
            return e2(v, r);
          };
        },
        /* 127 */
        /***/
        function(s, n) {
          n.f = Object.getOwnPropertySymbols;
        },
        /* 128 */
        /***/
        function(s, n, t) {
          s.exports = t(268);
        },
        /* 129 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            zIndex: 1e4
          };
        },
        /* 130 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            focus: true,
            height: 300,
            placeholder: "请输入正文",
            zIndexFullScreen: 10002,
            showFullScreen: true
          };
        },
        /* 131 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.getPasteImgs = n.getPasteHtml = n.getPasteText = void 0;
          var o = t(2), v = t(6), g = o.__importDefault(t(292));
          function d(i) {
            var u = i.clipboardData, l = "";
            return u == null ? l = window.clipboardData && window.clipboardData.getData("text") : l = u.getData("text/plain"), v.replaceHtmlSymbol(l);
          }
          n.getPasteText = d;
          function p(i, u, l) {
            u === void 0 && (u = true), l === void 0 && (l = false);
            var m = i.clipboardData, c = "";
            if (m && (c = m.getData("text/html")), !c) {
              var h = d(i);
              if (!h)
                return "";
              c = "<p>" + h + "</p>";
            }
            return c = c.replace(/<(\d)/gm, function(A, y) {
              return "&lt;" + y;
            }), c = c.replace(/<(\/?meta.*?)>/gim, ""), c = g.default(c, u, l), c;
          }
          n.getPasteHtml = p;
          function f(i) {
            var u, l = [], m = d(i);
            if (m)
              return l;
            var c = (u = i.clipboardData) === null || u === void 0 ? void 0 : u.items;
            return c && (0, r.default)(v).call(v, c, function(h, A) {
              var y = A.type;
              /image/i.test(y) && l.push(A.getAsFile());
            }), l;
          }
          n.getPasteImgs = f;
        },
        /* 132 */
        /***/
        function(s, n, t) {
          s.exports = t(294);
        },
        /* 133 */
        /***/
        function(s, n, t) {
          s.exports = t(310);
        },
        /* 134 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(46));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = t(7), p = (
            /** @class */
            function() {
              function f(i, u) {
                var l = this;
                this.hideTimeoutId = 0, this.menu = i, this.conf = u;
                var m = g.default('<div class="w-e-droplist"></div>'), c = g.default("<p>" + u.title + "</p>");
                c.addClass("w-e-dp-title"), m.append(c);
                var h = u.list || [], A = u.type || "list", y = u.clickHandler || d.EMPTY_FN, x = g.default('<ul class="' + (A === "list" ? "w-e-list" : "w-e-block") + '"></ul>');
                (0, r.default)(h).call(h, function(S) {
                  var T = S.$elem, I = S.value, C = g.default('<li class="w-e-item"></li>');
                  T && (C.append(T), x.append(C), C.on("click", function(E) {
                    y(I), E.stopPropagation(), l.hideTimeoutId = (0, o.default)(function() {
                      l.hide();
                    });
                  }));
                }), m.append(x), m.on("mouseleave", function() {
                  l.hideTimeoutId = (0, o.default)(function() {
                    l.hide();
                  });
                }), this.$container = m, this.rendered = false, this._show = false;
              }
              return f.prototype.show = function() {
                this.hideTimeoutId && clearTimeout(this.hideTimeoutId);
                var i = this.menu, u = i.$elem, l = this.$container;
                if (!this._show) {
                  if (this.rendered)
                    l.show();
                  else {
                    var m = u.getBoundingClientRect().height || 0, c = this.conf.width || 100;
                    l.css("margin-top", m + "px").css("width", c + "px"), u.append(l), this.rendered = true;
                  }
                  this._show = true;
                }
              }, f.prototype.hide = function() {
                var i = this.$container;
                this._show && (i.hide(), this._show = false);
              }, (0, a.default)(f.prototype, "isShow", {
                get: function() {
                  return this._show;
                },
                enumerable: false,
                configurable: true
              }), f;
            }()
          );
          n.default = p;
        },
        /* 135 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(92)), r = e2(t(1)), o = e2(t(4));
          (0, r.default)(n, "__esModule", {
            value: true
          });
          var v = t(6);
          function g(d, p) {
            var f = new XMLHttpRequest();
            if (f.open("POST", d), f.timeout = p.timeout || 10 * 1e3, f.ontimeout = function() {
              p.onTimeout && p.onTimeout(f);
            }, f.upload && (f.upload.onprogress = function(u) {
              var l = u.loaded / u.total;
              p.onProgress && p.onProgress(l, u);
            }), p.headers && (0, o.default)(v).call(v, p.headers, function(u, l) {
              f.setRequestHeader(u, l);
            }), f.withCredentials = !!p.withCredentials, p.beforeSend) {
              var i = p.beforeSend(f);
              if (i && (0, a.default)(i) === "object" && i.prevent)
                return i.msg;
            }
            return f.onreadystatechange = function() {
              if (f.readyState === 4) {
                var u = f.status;
                if (!(u < 200) && !(u >= 300 && u < 400)) {
                  if (u >= 400) {
                    p.onError && p.onError(f);
                    return;
                  }
                  var l = f.responseText, m;
                  if ((0, a.default)(l) !== "object")
                    try {
                      m = JSON.parse(l);
                    } catch {
                      p.onFail && p.onFail(f, l);
                      return;
                    }
                  else
                    m = l;
                  p.onSuccess(f, m);
                }
              }
            }, f.send(p.formData || null), f;
          }
          n.default = g;
        },
        /* 136 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(342)), o = e2(t(46));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = (
            /** @class */
            function() {
              function p(f) {
                this.editor = f, this.$textContainer = f.$textContainerElem, this.$bar = g.default('<div class="w-e-progress"></div>'), this.isShow = false, this.time = 0, this.timeoutId = 0;
              }
              return p.prototype.show = function(f) {
                var i = this;
                if (!this.isShow) {
                  this.isShow = true;
                  var u = this.$bar, l = this.$textContainer;
                  l.append(u), (0, r.default)() - this.time > 100 && f <= 1 && (u.css("width", f * 100 + "%"), this.time = (0, r.default)());
                  var m = this.timeoutId;
                  m && clearTimeout(m), this.timeoutId = (0, o.default)(function() {
                    i.hide();
                  }, 500);
                }
              }, p.prototype.hide = function() {
                var f = this.$bar;
                f.remove(), this.isShow = false, this.time = 0, this.timeoutId = 0;
              }, p;
            }()
          );
          n.default = d;
        },
        /* 137 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.ListType = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(24)), g = t(47), d = r.__importStar(t(371)), p;
          (function(i) {
            i.OrderedList = "OL", i.UnorderedList = "UL";
          })(p = n.ListType || (n.ListType = {}));
          var f = (
            /** @class */
            function(i) {
              r.__extends(u, i);
              function u(l) {
                var m = this, c = o.default(`<div class="w-e-menu" data-title="序列">
                <i class="w-e-icon-list2"></i>
            </div>`), h = {
                  width: 130,
                  title: "序列",
                  type: "list",
                  list: [{
                    $elem: o.default(`
                        <p>
                            <i class="w-e-icon-list2 w-e-drop-list-item"></i>
                            ` + l.i18next.t("menus.dropListMenu.list.无序列表") + `
                        <p>`),
                    value: p.UnorderedList
                  }, {
                    $elem: o.default(`<p>
                            <i class="w-e-icon-list-numbered w-e-drop-list-item"></i>
                            ` + l.i18next.t("menus.dropListMenu.list.有序列表") + `
                        <p>`),
                    value: p.OrderedList
                  }],
                  clickHandler: function(y) {
                    m.command(y);
                  }
                };
                return m = i.call(this, c, l, h) || this, m;
              }
              return u.prototype.command = function(l) {
                var m = this.editor, c = m.selection.getSelectionContainerElem();
                c !== void 0 && (this.handleSelectionRangeNodes(l), this.tryChangeActive());
              }, u.prototype.validator = function(l, m, c) {
                return !(!l.length || !m.length || c.equal(l) || c.equal(m));
              }, u.prototype.handleSelectionRangeNodes = function(l) {
                var m = this.editor, c = m.selection, h = l.toLowerCase(), A = c.getSelectionContainerElem(), y = c.getSelectionStartElem().getNodeTop(m), x = c.getSelectionEndElem().getNodeTop(m);
                if (this.validator(y, x, m.$textElem)) {
                  var S = c.getRange(), T = S == null ? void 0 : S.collapsed;
                  m.$textElem.equal(A) || (A = A.getNodeTop(m));
                  var I = {
                    editor: m,
                    listType: l,
                    listTarget: h,
                    $selectionElem: A,
                    $startElem: y,
                    $endElem: x
                  }, C;
                  this.isOrderElem(A) ? C = d.ClassType.Wrap : this.isOrderElem(y) && this.isOrderElem(x) ? C = d.ClassType.Join : this.isOrderElem(y) ? C = d.ClassType.StartJoin : this.isOrderElem(x) ? C = d.ClassType.EndJoin : C = d.ClassType.Other;
                  var E = new d.default(d.createListHandle(C, I, S));
                  g.updateRange(m, E.getSelectionRangeElem(), !!T);
                }
              }, u.prototype.isOrderElem = function(l) {
                var m = l.getNodeName();
                return m === p.OrderedList || m === p.UnorderedList;
              }, u.prototype.tryChangeActive = function() {
              }, u;
            }(v.default)
          );
          n.default = f;
        },
        /* 138 */
        /***/
        function(s, n, t) {
          s.exports = t(395);
        },
        /* 139 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function r(o) {
            var v = o.selection.getSelectionContainerElem();
            return v != null && v.length ? !!(v.getNodeName() == "CODE" || v.getNodeName() == "PRE" || v.parent().getNodeName() == "CODE" || v.parent().getNodeName() == "PRE" || /hljs/.test(v.parent().attr("class"))) : false;
          }
          n.default = r;
        },
        /* 140 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.todo = void 0;
          var o = t(2), v = o.__importDefault(t(3)), g = (
            /** @class */
            function() {
              function p(f) {
                var i;
                this.template = '<ul class="w-e-todo"><li><span contenteditable="false"><input type="checkbox"></span></li></ul>', this.checked = false, this.$todo = v.default(this.template), this.$child = (i = f == null ? void 0 : f.childNodes()) === null || i === void 0 ? void 0 : i.clone(true);
              }
              return p.prototype.init = function() {
                var f = this.$child, i = this.getInputContainer();
                f && f.insertAfter(i);
              }, p.prototype.getInput = function() {
                var f = this.$todo, i = (0, r.default)(f).call(f, "input");
                return i;
              }, p.prototype.getInputContainer = function() {
                var f = this.getInput().parent();
                return f;
              }, p.prototype.getTodo = function() {
                return this.$todo;
              }, p;
            }()
          );
          n.todo = g;
          function d(p) {
            var f = new g(p);
            return f.init(), f;
          }
          n.default = d;
        },
        /* 141 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2);
          t(146), t(148), t(152), t(154), t(156), t(158), t(160);
          var o = r.__importDefault(t(87));
          r.__exportStar(t(442), n);
          try {
          } catch {
            throw new Error("请在浏览器环境下运行");
          }
          n.default = o.default;
        },
        /* 142 */
        /***/
        function(s, n, t) {
          var e2 = t(143);
          s.exports = e2;
        },
        /* 143 */
        /***/
        function(s, n, t) {
          t(144);
          var e2 = t(9), a = e2.Object, r = s.exports = function(v, g, d) {
            return a.defineProperty(v, g, d);
          };
          a.defineProperty.sham && (r.sham = true);
        },
        /* 144 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(14), r = t(18);
          e2({ target: "Object", stat: true, forced: !a, sham: !a }, {
            defineProperty: r.f
          });
        },
        /* 145 */
        /***/
        function(s, n) {
          var t;
          t = function() {
            return this;
          }();
          try {
            t = t || new Function("return this")();
          } catch {
            typeof window == "object" && (t = window);
          }
          s.exports = t;
        },
        /* 146 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(147);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 147 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-toolbar,
.w-e-text-container,
.w-e-menu-panel {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: #fff;
  /*表情菜单样式*/
  /*分割线样式*/
}
.w-e-toolbar h1,
.w-e-text-container h1,
.w-e-menu-panel h1 {
  font-size: 32px !important;
}
.w-e-toolbar h2,
.w-e-text-container h2,
.w-e-menu-panel h2 {
  font-size: 24px !important;
}
.w-e-toolbar h3,
.w-e-text-container h3,
.w-e-menu-panel h3 {
  font-size: 18.72px !important;
}
.w-e-toolbar h4,
.w-e-text-container h4,
.w-e-menu-panel h4 {
  font-size: 16px !important;
}
.w-e-toolbar h5,
.w-e-text-container h5,
.w-e-menu-panel h5 {
  font-size: 13.28px !important;
}
.w-e-toolbar p,
.w-e-text-container p,
.w-e-menu-panel p {
  font-size: 16px !important;
}
.w-e-toolbar .eleImg,
.w-e-text-container .eleImg,
.w-e-menu-panel .eleImg {
  cursor: pointer;
  display: inline-block;
  font-size: 18px;
  padding: 0 3px;
}
.w-e-toolbar *,
.w-e-text-container *,
.w-e-menu-panel * {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.w-e-toolbar hr,
.w-e-text-container hr,
.w-e-menu-panel hr {
  cursor: pointer;
  display: block;
  height: 0px;
  border: 0;
  border-top: 3px solid #ccc;
  margin: 20px 0;
}
.w-e-clear-fix:after {
  content: "";
  display: table;
  clear: both;
}
.w-e-drop-list-item {
  position: relative;
  top: 1px;
  padding-right: 7px;
  color: #333 !important;
}
.w-e-drop-list-tl {
  padding-left: 10px;
  text-align: left;
}
`, ""]), s.exports = n;
        },
        /* 148 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(149);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 149 */
        /***/
        function(s, n, t) {
          var e2 = t(21), a = t(150), r = t(151);
          n = e2(false);
          var o = a(r);
          n.push([s.i, `@font-face {
  font-family: 'w-e-icon';
  src: url(` + o + `) format('truetype');
  font-weight: normal;
  font-style: normal;
}
[class^="w-e-icon-"],
[class*=" w-e-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'w-e-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.w-e-icon-close:before {
  content: "\\f00d";
}
.w-e-icon-upload2:before {
  content: "\\e9c6";
}
.w-e-icon-trash-o:before {
  content: "\\f014";
}
.w-e-icon-header:before {
  content: "\\f1dc";
}
.w-e-icon-pencil2:before {
  content: "\\e906";
}
.w-e-icon-paint-brush:before {
  content: "\\f1fc";
}
.w-e-icon-image:before {
  content: "\\e90d";
}
.w-e-icon-play:before {
  content: "\\e912";
}
.w-e-icon-location:before {
  content: "\\e947";
}
.w-e-icon-undo:before {
  content: "\\e965";
}
.w-e-icon-redo:before {
  content: "\\e966";
}
.w-e-icon-quotes-left:before {
  content: "\\e977";
}
.w-e-icon-list-numbered:before {
  content: "\\e9b9";
}
.w-e-icon-list2:before {
  content: "\\e9bb";
}
.w-e-icon-link:before {
  content: "\\e9cb";
}
.w-e-icon-happy:before {
  content: "\\e9df";
}
.w-e-icon-bold:before {
  content: "\\ea62";
}
.w-e-icon-underline:before {
  content: "\\ea63";
}
.w-e-icon-italic:before {
  content: "\\ea64";
}
.w-e-icon-strikethrough:before {
  content: "\\ea65";
}
.w-e-icon-table2:before {
  content: "\\ea71";
}
.w-e-icon-paragraph-left:before {
  content: "\\ea77";
}
.w-e-icon-paragraph-center:before {
  content: "\\ea78";
}
.w-e-icon-paragraph-right:before {
  content: "\\ea79";
}
.w-e-icon-paragraph-justify:before {
  content: "\\ea7a";
}
.w-e-icon-terminal:before {
  content: "\\f120";
}
.w-e-icon-page-break:before {
  content: "\\ea68";
}
.w-e-icon-cancel-circle:before {
  content: "\\ea0d";
}
.w-e-icon-font:before {
  content: "\\ea5c";
}
.w-e-icon-text-heigh:before {
  content: "\\ea5f";
}
.w-e-icon-paint-format:before {
  content: "\\e90c";
}
.w-e-icon-indent-increase:before {
  content: "\\ea7b";
}
.w-e-icon-indent-decrease:before {
  content: "\\ea7c";
}
.w-e-icon-row-height:before {
  content: "\\e9be";
}
.w-e-icon-fullscreen_exit:before {
  content: "\\e900";
}
.w-e-icon-fullscreen:before {
  content: "\\e901";
}
.w-e-icon-split-line:before {
  content: "\\ea0b";
}
.w-e-icon-checkbox-checked:before {
  content: "\\ea52";
}
`, ""]), s.exports = n;
        },
        /* 150 */
        /***/
        function(s, n, t) {
          s.exports = function(e2, a) {
            return a || (a = {}), e2 = e2 && e2.__esModule ? e2.default : e2, typeof e2 != "string" ? e2 : (/^['"].*['"]$/.test(e2) && (e2 = e2.slice(1, -1)), a.hash && (e2 += a.hash), /["'() \t\n]/.test(e2) || a.needQuotes ? '"'.concat(e2.replace(/"/g, '\\"').replace(/\n/g, "\\n"), '"') : e2);
          };
        },
        /* 151 */
        /***/
        function(s, n, t) {
          t.r(n), n.default = "data:font/woff;base64,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";
        },
        /* 152 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(153);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 153 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-toolbar {
  display: flex;
  padding: 0 6px;
  flex-wrap: wrap;
  position: relative;
  /* 单个菜单 */
}
.w-e-toolbar .w-e-menu {
  position: relative;
  display: flex;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
}
.w-e-toolbar .w-e-menu i {
  color: #999;
}
.w-e-toolbar .w-e-menu:hover {
  background-color: #F6F6F6;
}
.w-e-toolbar .w-e-menu:hover i {
  color: #333;
}
.w-e-toolbar .w-e-active i {
  color: #1e88e5;
}
.w-e-toolbar .w-e-active:hover i {
  color: #1e88e5;
}
.w-e-menu-tooltip {
  position: absolute;
  display: flex;
  color: #f1f1f1;
  background-color: rgba(0, 0, 0, 0.75);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 4px 5px 6px;
  justify-content: center;
  align-items: center;
}
.w-e-menu-tooltip-up::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-top-color: rgba(0, 0, 0, 0.73);
}
.w-e-menu-tooltip-down::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0.73);
}
.w-e-menu-tooltip-item-wrapper {
  font-size: 14px;
  margin: 0 5px;
}
`, ""]), s.exports = n;
        },
        /* 154 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(155);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 155 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-text-container {
  position: relative;
  height: 100%;
}
.w-e-text-container .w-e-progress {
  position: absolute;
  background-color: #1e88e5;
  top: 0;
  left: 0;
  height: 1px;
}
.w-e-text-container .placeholder {
  color: #D4D4D4;
  position: absolute;
  font-size: 11pt;
  line-height: 22px;
  left: 10px;
  top: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  z-index: -1;
}
.w-e-text {
  padding: 0 10px;
  overflow-y: auto;
}
.w-e-text p,
.w-e-text h1,
.w-e-text h2,
.w-e-text h3,
.w-e-text h4,
.w-e-text h5,
.w-e-text table,
.w-e-text pre {
  margin: 10px 0;
  line-height: 1.5;
}
.w-e-text ul,
.w-e-text ol {
  margin: 10px 0 10px 20px;
}
.w-e-text blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}
.w-e-text code {
  display: inline-block;
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}
.w-e-text pre code {
  display: block;
}
.w-e-text table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.w-e-text table td,
.w-e-text table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
  min-height: 30px;
  height: 30px;
}
.w-e-text table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
  background-color: #f1f1f1;
}
.w-e-text:focus {
  outline: none;
}
.w-e-text img {
  cursor: pointer;
}
.w-e-text img:hover {
  box-shadow: 0 0 5px #333;
}
.w-e-text .w-e-todo {
  margin: 0 0 0 20px;
}
.w-e-text .w-e-todo li {
  list-style: none;
  font-size: 1em;
}
.w-e-text .w-e-todo li span:nth-child(1) {
  position: relative;
  left: -18px;
}
.w-e-text .w-e-todo li span:nth-child(1) input {
  position: absolute;
  margin-right: 3px;
}
.w-e-text .w-e-todo li span:nth-child(1) input[type=checkbox] {
  top: 50%;
  margin-top: -6px;
}
.w-e-tooltip {
  position: absolute;
  display: flex;
  color: #f1f1f1;
  background-color: rgba(0, 0, 0, 0.75);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 4px 5px 6px;
  justify-content: center;
  align-items: center;
}
.w-e-tooltip-up::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-top-color: rgba(0, 0, 0, 0.73);
}
.w-e-tooltip-down::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0.73);
}
.w-e-tooltip-item-wrapper {
  cursor: pointer;
  font-size: 14px;
  margin: 0 5px;
}
.w-e-tooltip-item-wrapper:hover {
  color: #ccc;
  text-decoration: underline;
}
`, ""]), s.exports = n;
        },
        /* 156 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(157);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 157 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-menu .w-e-panel-container {
  position: absolute;
  top: 0;
  left: 50%;
  border: 1px solid #ccc;
  border-top: 0;
  box-shadow: 1px 1px 2px #ccc;
  color: #333;
  background-color: #fff;
  text-align: left;
  /* 为 emotion panel 定制的样式 */
  /* 上传图片、上传视频的 panel 定制样式 */
}
.w-e-menu .w-e-panel-container .w-e-panel-close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px;
  margin: 2px 5px 0 0;
  cursor: pointer;
  color: #999;
}
.w-e-menu .w-e-panel-container .w-e-panel-close:hover {
  color: #333;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title {
  list-style: none;
  display: flex;
  font-size: 14px;
  margin: 2px 10px 0 10px;
  border-bottom: 1px solid #f1f1f1;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-item {
  padding: 3px 5px;
  color: #999;
  cursor: pointer;
  margin: 0 3px;
  position: relative;
  top: 1px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-active {
  color: #333;
  border-bottom: 1px solid #333;
  cursor: default;
  font-weight: 700;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content {
  padding: 10px 15px 10px 15px;
  font-size: 16px;
  /* 输入框的样式 */
  /* 按钮的样式 */
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input:focus,
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus,
.w-e-menu .w-e-panel-container .w-e-panel-tab-content button:focus {
  outline: none;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea {
  width: 100%;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus {
  border-color: #1e88e5;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text] {
  border: none;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  height: 20px;
  color: #333;
  text-align: left;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].small {
  width: 30px;
  text-align: center;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].block {
  display: block;
  width: 100%;
  margin: 10px 0;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {
  border-bottom: 2px solid #1e88e5;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {
  font-size: 14px;
  color: #1e88e5;
  border: none;
  padding: 5px 10px;
  background-color: #fff;
  cursor: pointer;
  border-radius: 3px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {
  float: left;
  margin-right: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {
  float: right;
  margin-left: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {
  color: #999;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {
  color: #c24f4a;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {
  background-color: #f1f1f1;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {
  content: "";
  display: table;
  clear: both;
}
.w-e-menu .w-e-panel-container .w-e-emoticon-container .w-e-item {
  cursor: pointer;
  font-size: 18px;
  padding: 0 3px;
  display: inline-block;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container,
.w-e-menu .w-e-panel-container .w-e-up-video-container {
  text-align: center;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn,
.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn {
  display: inline-block;
  color: #999;
  cursor: pointer;
  font-size: 60px;
  line-height: 1;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover,
.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn:hover {
  color: #333;
}
`, ""]), s.exports = n;
        },
        /* 158 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(159);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 159 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-toolbar .w-e-droplist {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fff;
  border: 1px solid #f1f1f1;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
}
.w-e-toolbar .w-e-droplist .w-e-dp-title {
  text-align: center;
  color: #999;
  line-height: 2;
  border-bottom: 1px solid #f1f1f1;
  font-size: 13px;
}
.w-e-toolbar .w-e-droplist ul.w-e-list {
  list-style: none;
  line-height: 1;
}
.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {
  color: #333;
  padding: 5px 0;
}
.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {
  background-color: #f1f1f1;
}
.w-e-toolbar .w-e-droplist ul.w-e-block {
  list-style: none;
  text-align: left;
  padding: 5px;
}
.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {
  display: inline-block;
  padding: 3px 5px;
}
.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {
  background-color: #f1f1f1;
}
`, ""]), s.exports = n;
        },
        /* 160 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(161));
          Element.prototype.matches || (Element.prototype.matches = function(r) {
            var o = this.ownerDocument.querySelectorAll(r), v = o.length;
            for (v; v >= 0 && o.item(v) !== this; v--)
              ;
            return v > -1;
          }), a.default || (window.Promise = a.default);
        },
        /* 161 */
        /***/
        function(s, n, t) {
          s.exports = t(162);
        },
        /* 162 */
        /***/
        function(s, n, t) {
          var e2 = t(163);
          s.exports = e2;
        },
        /* 163 */
        /***/
        function(s, n, t) {
          t(61), t(50), t(54), t(175), t(178), t(179);
          var e2 = t(9);
          s.exports = e2.Promise;
        },
        /* 164 */
        /***/
        function(s, n, t) {
          var e2 = t(62), a = t(49), r = function(o) {
            return function(v, g) {
              var d = String(a(v)), p = e2(g), f = d.length, i, u;
              return p < 0 || p >= f ? o ? "" : void 0 : (i = d.charCodeAt(p), i < 55296 || i > 56319 || p + 1 === f || (u = d.charCodeAt(p + 1)) < 56320 || u > 57343 ? o ? d.charAt(p) : i : o ? d.slice(p, p + 2) : (i - 55296 << 10) + (u - 56320) + 65536);
            };
          };
          s.exports = {
            // `String.prototype.codePointAt` method
            // https://tc39.github.io/ecma262/#sec-string.prototype.codepointat
            codeAt: r(false),
            // `String.prototype.at` method
            // https://github.com/mathiasbynens/String.prototype.at
            charAt: r(true)
          };
        },
        /* 165 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(102), r = e2.WeakMap;
          s.exports = typeof r == "function" && /native code/.test(a(r));
        },
        /* 166 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(19);
          s.exports = function(r, o) {
            try {
              a(e2, r, o);
            } catch {
              e2[r] = o;
            }
            return o;
          };
        },
        /* 167 */
        /***/
        function(s, n, t) {
          var e2 = t(104).IteratorPrototype, a = t(77), r = t(48), o = t(37), v = t(44), g = function() {
            return this;
          };
          s.exports = function(d, p, f) {
            var i = p + " Iterator";
            return d.prototype = a(e2, { next: r(1, f) }), o(d, i, false, true), v[i] = g, d;
          };
        },
        /* 168 */
        /***/
        function(s, n, t) {
          var e2 = t(11);
          s.exports = !e2(function() {
            function a() {
            }
            return a.prototype.constructor = null, Object.getPrototypeOf(new a()) !== a.prototype;
          });
        },
        /* 169 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(18), r = t(25), o = t(52);
          s.exports = e2 ? Object.defineProperties : function(g, d) {
            r(g);
            for (var p = o(d), f = p.length, i = 0, u; f > i; )
              a.f(g, u = p[i++], d[u]);
            return g;
          };
        },
        /* 170 */
        /***/
        function(s, n, t) {
          var e2 = t(81), a = t(65);
          s.exports = e2 ? {}.toString : function() {
            return "[object " + a(this) + "]";
          };
        },
        /* 171 */
        /***/
        function(s, n, t) {
          var e2 = t(25), a = t(172);
          s.exports = Object.setPrototypeOf || ("__proto__" in {} ? function() {
            var r = false, o = {}, v;
            try {
              v = Object.getOwnPropertyDescriptor(Object.prototype, "__proto__").set, v.call(o, []), r = o instanceof Array;
            } catch {
            }
            return function(d, p) {
              return e2(d), a(p), r ? v.call(d, p) : d.__proto__ = p, d;
            };
          }() : void 0);
        },
        /* 172 */
        /***/
        function(s, n, t) {
          var e2 = t(13);
          s.exports = function(a) {
            if (!e2(a) && a !== null)
              throw TypeError("Can't set " + String(a) + " as a prototype");
            return a;
          };
        },
        /* 173 */
        /***/
        function(s, n, t) {
          var e2 = t(30), a = t(82), r = t(44), o = t(42), v = t(75), g = "Array Iterator", d = o.set, p = o.getterFor(g);
          s.exports = v(Array, "Array", function(f, i) {
            d(this, {
              type: g,
              target: e2(f),
              // target
              index: 0,
              // next index
              kind: i
              // kind
            });
          }, function() {
            var f = p(this), i = f.target, u = f.kind, l = f.index++;
            return !i || l >= i.length ? (f.target = void 0, { value: void 0, done: true }) : u == "keys" ? { value: l, done: false } : u == "values" ? { value: i[l], done: false } : { value: [l, i[l]], done: false };
          }, "values"), r.Arguments = r.Array, a("keys"), a("values"), a("entries");
        },
        /* 174 */
        /***/
        function(s, n) {
          s.exports = {
            CSSRuleList: 0,
            CSSStyleDeclaration: 0,
            CSSValueList: 0,
            ClientRectList: 0,
            DOMRectList: 0,
            DOMStringList: 0,
            DOMTokenList: 1,
            DataTransferItemList: 0,
            FileList: 0,
            HTMLAllCollection: 0,
            HTMLCollection: 0,
            HTMLFormElement: 0,
            HTMLSelectElement: 0,
            MediaList: 0,
            MimeTypeArray: 0,
            NamedNodeMap: 0,
            NodeList: 1,
            PaintRequestList: 0,
            Plugin: 0,
            PluginArray: 0,
            SVGLengthList: 0,
            SVGNumberList: 0,
            SVGPathSegList: 0,
            SVGPointList: 0,
            SVGStringList: 0,
            SVGTransformList: 0,
            SourceBufferList: 0,
            StyleSheetList: 0,
            TextTrackCueList: 0,
            TextTrackList: 0,
            TouchList: 0
          };
        },
        /* 175 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(43), r = t(8), o = t(36), v = t(109), g = t(53), d = t(110), p = t(37), f = t(111), i = t(13), u = t(41), l = t(83), m = t(34), c = t(102), h = t(66), A = t(115), y = t(116), x = t(117).set, S = t(176), T = t(119), I = t(177), C = t(85), E = t(120), D = t(42), P = t(101), M = t(10), R = t(86), N = M("species"), B = "Promise", F = D.get, O = D.set, H = D.getterFor(B), L = v, U = r.TypeError, z = r.document, j = r.process, K = o("fetch"), V = C.f, Q = V, w = m(j) == "process", G = !!(z && z.createEvent && r.dispatchEvent), X = "unhandledrejection", tt = "rejectionhandled", nt = 0, st = 1, It = 2, it = 1, Ct = 2, gt, lt, dt, At, ct = P(B, function() {
            var J = c(L) !== String(L);
            if (!J && (R === 66 || !w && typeof PromiseRejectionEvent != "function") || a && !L.prototype.finally)
              return true;
            if (R >= 51 && /native code/.test(L))
              return false;
            var $ = L.resolve(1), Z = function(Y) {
              Y(function() {
              }, function() {
              });
            }, q = $.constructor = {};
            return q[N] = Z, !($.then(function() {
            }) instanceof Z);
          }), Rt = ct || !A(function(J) {
            L.all(J).catch(function() {
            });
          }), yt = function(J) {
            var $;
            return i(J) && typeof ($ = J.then) == "function" ? $ : false;
          }, pt = function(J, $, Z) {
            if (!$.notified) {
              $.notified = true;
              var q = $.reactions;
              S(function() {
                for (var Y = $.value, b = $.state == st, W = 0; q.length > W; ) {
                  var k = q[W++], _ = b ? k.ok : k.fail, rt = k.resolve, ut = k.reject, ft = k.domain, at, Tt, Pt;
                  try {
                    _ ? (b || ($.rejection === Ct && St(J, $), $.rejection = it), _ === true ? at = Y : (ft && ft.enter(), at = _(Y), ft && (ft.exit(), Pt = true)), at === k.promise ? ut(U("Promise-chain cycle")) : (Tt = yt(at)) ? Tt.call(at, rt, ut) : rt(at)) : ut(Y);
                  } catch (Bt) {
                    ft && !Pt && ft.exit(), ut(Bt);
                  }
                }
                $.reactions = [], $.notified = false, Z && !$.rejection && Dt(J, $);
              });
            }
          }, Et = function(J, $, Z) {
            var q, Y;
            G ? (q = z.createEvent("Event"), q.promise = $, q.reason = Z, q.initEvent(J, false, true), r.dispatchEvent(q)) : q = { promise: $, reason: Z }, (Y = r["on" + J]) ? Y(q) : J === X && I("Unhandled promise rejection", Z);
          }, Dt = function(J, $) {
            x.call(r, function() {
              var Z = $.value, q = xt($), Y;
              if (q && (Y = E(function() {
                w ? j.emit("unhandledRejection", Z, J) : Et(X, J, Z);
              }), $.rejection = w || xt($) ? Ct : it, Y.error))
                throw Y.value;
            });
          }, xt = function(J) {
            return J.rejection !== it && !J.parent;
          }, St = function(J, $) {
            x.call(r, function() {
              w ? j.emit("rejectionHandled", J) : Et(tt, J, $.value);
            });
          }, vt = function(J, $, Z, q) {
            return function(Y) {
              J($, Z, Y, q);
            };
          }, mt = function(J, $, Z, q) {
            $.done || ($.done = true, q && ($ = q), $.value = Z, $.state = It, pt(J, $, true));
          }, ht = function(J, $, Z, q) {
            if (!$.done) {
              $.done = true, q && ($ = q);
              try {
                if (J === Z)
                  throw U("Promise can't be resolved itself");
                var Y = yt(Z);
                Y ? S(function() {
                  var b = { done: false };
                  try {
                    Y.call(
                      Z,
                      vt(ht, J, b, $),
                      vt(mt, J, b, $)
                    );
                  } catch (W) {
                    mt(J, b, W, $);
                  }
                }) : ($.value = Z, $.state = st, pt(J, $, false));
              } catch (b) {
                mt(J, { done: false }, b, $);
              }
            }
          };
          ct && (L = function($) {
            l(this, L, B), u($), gt.call(this);
            var Z = F(this);
            try {
              $(vt(ht, this, Z), vt(mt, this, Z));
            } catch (q) {
              mt(this, Z, q);
            }
          }, gt = function($) {
            O(this, {
              type: B,
              done: false,
              notified: false,
              parent: false,
              reactions: [],
              rejection: false,
              state: nt,
              value: void 0
            });
          }, gt.prototype = d(L.prototype, {
            // `Promise.prototype.then` method
            // https://tc39.github.io/ecma262/#sec-promise.prototype.then
            then: function($, Z) {
              var q = H(this), Y = V(y(this, L));
              return Y.ok = typeof $ == "function" ? $ : true, Y.fail = typeof Z == "function" && Z, Y.domain = w ? j.domain : void 0, q.parent = true, q.reactions.push(Y), q.state != nt && pt(this, q, false), Y.promise;
            },
            // `Promise.prototype.catch` method
            // https://tc39.github.io/ecma262/#sec-promise.prototype.catch
            catch: function(J) {
              return this.then(void 0, J);
            }
          }), lt = function() {
            var J = new gt(), $ = F(J);
            this.promise = J, this.resolve = vt(ht, J, $), this.reject = vt(mt, J, $);
          }, C.f = V = function(J) {
            return J === L || J === dt ? new lt(J) : Q(J);
          }, !a && typeof v == "function" && (At = v.prototype.then, g(v.prototype, "then", function($, Z) {
            var q = this;
            return new L(function(Y, b) {
              At.call(q, Y, b);
            }).then($, Z);
          }, { unsafe: true }), typeof K == "function" && e2({ global: true, enumerable: true, forced: true }, {
            // eslint-disable-next-line no-unused-vars
            fetch: function($) {
              return T(L, K.apply(r, arguments));
            }
          }))), e2({ global: true, wrap: true, forced: ct }, {
            Promise: L
          }), p(L, B, false, true), f(B), dt = o(B), e2({ target: B, stat: true, forced: ct }, {
            // `Promise.reject` method
            // https://tc39.github.io/ecma262/#sec-promise.reject
            reject: function($) {
              var Z = V(this);
              return Z.reject.call(void 0, $), Z.promise;
            }
          }), e2({ target: B, stat: true, forced: a || ct }, {
            // `Promise.resolve` method
            // https://tc39.github.io/ecma262/#sec-promise.resolve
            resolve: function($) {
              return T(a && this === dt ? L : this, $);
            }
          }), e2({ target: B, stat: true, forced: Rt }, {
            // `Promise.all` method
            // https://tc39.github.io/ecma262/#sec-promise.all
            all: function($) {
              var Z = this, q = V(Z), Y = q.resolve, b = q.reject, W = E(function() {
                var k = u(Z.resolve), _ = [], rt = 0, ut = 1;
                h($, function(ft) {
                  var at = rt++, Tt = false;
                  _.push(void 0), ut++, k.call(Z, ft).then(function(Pt) {
                    Tt || (Tt = true, _[at] = Pt, --ut || Y(_));
                  }, b);
                }), --ut || Y(_);
              });
              return W.error && b(W.value), q.promise;
            },
            // `Promise.race` method
            // https://tc39.github.io/ecma262/#sec-promise.race
            race: function($) {
              var Z = this, q = V(Z), Y = q.reject, b = E(function() {
                var W = u(Z.resolve);
                h($, function(k) {
                  W.call(Z, k).then(q.resolve, Y);
                });
              });
              return b.error && Y(b.value), q.promise;
            }
          });
        },
        /* 176 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(71).f, r = t(34), o = t(117).set, v = t(118), g = e2.MutationObserver || e2.WebKitMutationObserver, d = e2.process, p = e2.Promise, f = r(d) == "process", i = a(e2, "queueMicrotask"), u = i && i.value, l, m, c, h, A, y, x, S;
          u || (l = function() {
            var T, I;
            for (f && (T = d.domain) && T.exit(); m; ) {
              I = m.fn, m = m.next;
              try {
                I();
              } catch (C) {
                throw m ? h() : c = void 0, C;
              }
            }
            c = void 0, T && T.enter();
          }, f ? h = function() {
            d.nextTick(l);
          } : g && !v ? (A = true, y = document.createTextNode(""), new g(l).observe(y, { characterData: true }), h = function() {
            y.data = A = !A;
          }) : p && p.resolve ? (x = p.resolve(void 0), S = x.then, h = function() {
            S.call(x, l);
          }) : h = function() {
            o.call(e2, l);
          }), s.exports = u || function(T) {
            var I = { fn: T, next: void 0 };
            c && (c.next = I), m || (m = I, h()), c = I;
          };
        },
        /* 177 */
        /***/
        function(s, n, t) {
          var e2 = t(8);
          s.exports = function(a, r) {
            var o = e2.console;
            o && o.error;
          };
        },
        /* 178 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(41), r = t(85), o = t(120), v = t(66);
          e2({ target: "Promise", stat: true }, {
            allSettled: function(d) {
              var p = this, f = r.f(p), i = f.resolve, u = f.reject, l = o(function() {
                var m = a(p.resolve), c = [], h = 0, A = 1;
                v(d, function(y) {
                  var x = h++, S = false;
                  c.push(void 0), A++, m.call(p, y).then(function(T) {
                    S || (S = true, c[x] = { status: "fulfilled", value: T }, --A || i(c));
                  }, function(T) {
                    S || (S = true, c[x] = { status: "rejected", reason: T }, --A || i(c));
                  });
                }), --A || i(c);
              });
              return l.error && u(l.value), f.promise;
            }
          });
        },
        /* 179 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(43), r = t(109), o = t(11), v = t(36), g = t(116), d = t(119), p = t(53), f = !!r && o(function() {
            r.prototype.finally.call({ then: function() {
            } }, function() {
            });
          });
          e2({ target: "Promise", proto: true, real: true, forced: f }, {
            finally: function(i) {
              var u = g(this, v("Promise")), l = typeof i == "function";
              return this.then(
                l ? function(m) {
                  return d(u, i()).then(function() {
                    return m;
                  });
                } : i,
                l ? function(m) {
                  return d(u, i()).then(function() {
                    throw m;
                  });
                } : i
              );
            }
          }), !a && typeof r == "function" && !r.prototype.finally && p(r.prototype, "finally", v("Promise").prototype.finally);
        },
        /* 180 */
        /***/
        function(s, n, t) {
          t(54);
          var e2 = t(181), a = t(65), r = Array.prototype, o = {
            DOMTokenList: true,
            NodeList: true
          };
          s.exports = function(v) {
            var g = v.forEach;
            return v === r || v instanceof Array && g === r.forEach || o.hasOwnProperty(a(v)) ? e2 : g;
          };
        },
        /* 181 */
        /***/
        function(s, n, t) {
          var e2 = t(182);
          s.exports = e2;
        },
        /* 182 */
        /***/
        function(s, n, t) {
          t(183);
          var e2 = t(15);
          s.exports = e2("Array").forEach;
        },
        /* 183 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(184);
          e2({ target: "Array", proto: true, forced: [].forEach != a }, {
            forEach: a
          });
        },
        /* 184 */
        /***/
        function(s, n, t) {
          var e2 = t(32).forEach, a = t(67), r = t(22), o = a("forEach"), v = r("forEach");
          s.exports = !o || !v ? function(d) {
            return e2(this, d, arguments.length > 1 ? arguments[1] : void 0);
          } : [].forEach;
        },
        /* 185 */
        /***/
        function(s, n, t) {
          var e2 = t(186);
          s.exports = e2;
        },
        /* 186 */
        /***/
        function(s, n, t) {
          t(187);
          var e2 = t(9);
          s.exports = e2.Array.isArray;
        },
        /* 187 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(55);
          e2({ target: "Array", stat: true }, {
            isArray: a
          });
        },
        /* 188 */
        /***/
        function(s, n, t) {
          var e2 = t(189);
          s.exports = e2;
        },
        /* 189 */
        /***/
        function(s, n, t) {
          var e2 = t(190), a = Array.prototype;
          s.exports = function(r) {
            var o = r.map;
            return r === a || r instanceof Array && o === a.map ? e2 : o;
          };
        },
        /* 190 */
        /***/
        function(s, n, t) {
          t(191);
          var e2 = t(15);
          s.exports = e2("Array").map;
        },
        /* 191 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(32).map, r = t(56), o = t(22), v = r("map"), g = o("map");
          e2({ target: "Array", proto: true, forced: !v || !g }, {
            map: function(p) {
              return a(this, p, arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 192 */
        /***/
        function(s, n, t) {
          var e2 = t(193);
          s.exports = e2;
        },
        /* 193 */
        /***/
        function(s, n, t) {
          var e2 = t(194), a = String.prototype;
          s.exports = function(r) {
            var o = r.trim;
            return typeof r == "string" || r === a || r instanceof String && o === a.trim ? e2 : o;
          };
        },
        /* 194 */
        /***/
        function(s, n, t) {
          t(195);
          var e2 = t(15);
          s.exports = e2("String").trim;
        },
        /* 195 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(90).trim, r = t(196);
          e2({ target: "String", proto: true, forced: r("trim") }, {
            trim: function() {
              return a(this);
            }
          });
        },
        /* 196 */
        /***/
        function(s, n, t) {
          var e2 = t(11), a = t(68), r = "​᠎";
          s.exports = function(o) {
            return e2(function() {
              return !!a[o]() || r[o]() != r || a[o].name !== o;
            });
          };
        },
        /* 197 */
        /***/
        function(s, n, t) {
          var e2 = t(198);
          s.exports = e2;
        },
        /* 198 */
        /***/
        function(s, n, t) {
          t(199), t(61), t(50), t(54);
          var e2 = t(9);
          s.exports = e2.Map;
        },
        /* 199 */
        /***/
        function(s, n, t) {
          var e2 = t(122), a = t(124);
          s.exports = e2("Map", function(r) {
            return function() {
              return r(this, arguments.length ? arguments[0] : void 0);
            };
          }, a);
        },
        /* 200 */
        /***/
        function(s, n, t) {
          var e2 = t(11);
          s.exports = !e2(function() {
            return Object.isExtensible(Object.preventExtensions({}));
          });
        },
        /* 201 */
        /***/
        function(s, n, t) {
          var e2 = t(202);
          s.exports = e2;
        },
        /* 202 */
        /***/
        function(s, n, t) {
          var e2 = t(203), a = Array.prototype;
          s.exports = function(r) {
            var o = r.indexOf;
            return r === a || r instanceof Array && o === a.indexOf ? e2 : o;
          };
        },
        /* 203 */
        /***/
        function(s, n, t) {
          t(204);
          var e2 = t(15);
          s.exports = e2("Array").indexOf;
        },
        /* 204 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(78).indexOf, r = t(67), o = t(22), v = [].indexOf, g = !!v && 1 / [1].indexOf(1, -0) < 0, d = r("indexOf"), p = o("indexOf", { ACCESSORS: true, 1: 0 });
          e2({ target: "Array", proto: true, forced: g || !d || !p }, {
            indexOf: function(i) {
              return g ? v.apply(this, arguments) || 0 : a(this, i, arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 205 */
        /***/
        function(s, n, t) {
          var e2 = t(206);
          s.exports = e2;
        },
        /* 206 */
        /***/
        function(s, n, t) {
          var e2 = t(207), a = Array.prototype;
          s.exports = function(r) {
            var o = r.splice;
            return r === a || r instanceof Array && o === a.splice ? e2 : o;
          };
        },
        /* 207 */
        /***/
        function(s, n, t) {
          t(208);
          var e2 = t(15);
          s.exports = e2("Array").splice;
        },
        /* 208 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(79), r = t(62), o = t(35), v = t(31), g = t(88), d = t(69), p = t(56), f = t(22), i = p("splice"), u = f("splice", { ACCESSORS: true, 0: 0, 1: 2 }), l = Math.max, m = Math.min, c = 9007199254740991, h = "Maximum allowed length exceeded";
          e2({ target: "Array", proto: true, forced: !i || !u }, {
            splice: function(y, x) {
              var S = v(this), T = o(S.length), I = a(y, T), C = arguments.length, E, D, P, M, R, N;
              if (C === 0 ? E = D = 0 : C === 1 ? (E = 0, D = T - I) : (E = C - 2, D = m(l(r(x), 0), T - I)), T + E - D > c)
                throw TypeError(h);
              for (P = g(S, D), M = 0; M < D; M++)
                R = I + M, R in S && d(P, M, S[R]);
              if (P.length = D, E < D) {
                for (M = I; M < T - D; M++)
                  R = M + D, N = M + E, R in S ? S[N] = S[R] : delete S[N];
                for (M = T; M > T - D + E; M--)
                  delete S[M - 1];
              } else if (E > D)
                for (M = T - D; M > I; M--)
                  R = M + D - 1, N = M + E - 1, R in S ? S[N] = S[R] : delete S[N];
              for (M = 0; M < E; M++)
                S[M + I] = arguments[M + 2];
              return S.length = T - D + E, P;
            }
          });
        },
        /* 209 */
        /***/
        function(s, n, t) {
          var e2 = t(210);
          s.exports = e2;
        },
        /* 210 */
        /***/
        function(s, n, t) {
          var e2 = t(211), a = Array.prototype;
          s.exports = function(r) {
            var o = r.filter;
            return r === a || r instanceof Array && o === a.filter ? e2 : o;
          };
        },
        /* 211 */
        /***/
        function(s, n, t) {
          t(212);
          var e2 = t(15);
          s.exports = e2("Array").filter;
        },
        /* 212 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(32).filter, r = t(56), o = t(22), v = r("filter"), g = o("filter");
          e2({ target: "Array", proto: true, forced: !v || !g }, {
            filter: function(p) {
              return a(this, p, arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 213 */
        /***/
        function(s, n, t) {
          var e2 = t(214);
          s.exports = e2;
        },
        /* 214 */
        /***/
        function(s, n, t) {
          var e2 = t(215), a = t(217), r = Array.prototype, o = String.prototype;
          s.exports = function(v) {
            var g = v.includes;
            return v === r || v instanceof Array && g === r.includes ? e2 : typeof v == "string" || v === o || v instanceof String && g === o.includes ? a : g;
          };
        },
        /* 215 */
        /***/
        function(s, n, t) {
          t(216);
          var e2 = t(15);
          s.exports = e2("Array").includes;
        },
        /* 216 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(78).includes, r = t(82), o = t(22), v = o("indexOf", { ACCESSORS: true, 1: 0 });
          e2({ target: "Array", proto: true, forced: !v }, {
            includes: function(d) {
              return a(this, d, arguments.length > 1 ? arguments[1] : void 0);
            }
          }), r("includes");
        },
        /* 217 */
        /***/
        function(s, n, t) {
          t(218);
          var e2 = t(15);
          s.exports = e2("String").includes;
        },
        /* 218 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(219), r = t(49), o = t(221);
          e2({ target: "String", proto: true, forced: !o("includes") }, {
            includes: function(g) {
              return !!~String(r(this)).indexOf(a(g), arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 219 */
        /***/
        function(s, n, t) {
          var e2 = t(220);
          s.exports = function(a) {
            if (e2(a))
              throw TypeError("The method doesn't accept regular expressions");
            return a;
          };
        },
        /* 220 */
        /***/
        function(s, n, t) {
          var e2 = t(13), a = t(34), r = t(10), o = r("match");
          s.exports = function(v) {
            var g;
            return e2(v) && ((g = v[o]) !== void 0 ? !!g : a(v) == "RegExp");
          };
        },
        /* 221 */
        /***/
        function(s, n, t) {
          var e2 = t(10), a = e2("match");
          s.exports = function(r) {
            var o = /./;
            try {
              "/./"[r](o);
            } catch {
              try {
                return o[a] = false, "/./"[r](o);
              } catch {
              }
            }
            return false;
          };
        },
        /* 222 */
        /***/
        function(s, n, t) {
          var e2 = t(223);
          s.exports = e2;
        },
        /* 223 */
        /***/
        function(s, n, t) {
          var e2 = t(224), a = Function.prototype;
          s.exports = function(r) {
            var o = r.bind;
            return r === a || r instanceof Function && o === a.bind ? e2 : o;
          };
        },
        /* 224 */
        /***/
        function(s, n, t) {
          t(225);
          var e2 = t(15);
          s.exports = e2("Function").bind;
        },
        /* 225 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(226);
          e2({ target: "Function", proto: true }, {
            bind: a
          });
        },
        /* 226 */
        /***/
        function(s, n, t) {
          var e2 = t(41), a = t(13), r = [].slice, o = {}, v = function(g, d, p) {
            if (!(d in o)) {
              for (var f = [], i = 0; i < d; i++)
                f[i] = "a[" + i + "]";
              o[d] = Function("C,a", "return new C(" + f.join(",") + ")");
            }
            return o[d](g, p);
          };
          s.exports = Function.bind || function(d) {
            var p = e2(this), f = r.call(arguments, 1), i = function() {
              var l = f.concat(r.call(arguments));
              return this instanceof i ? v(p, l.length, l) : p.apply(d, l);
            };
            return a(p.prototype) && (i.prototype = p.prototype), i;
          };
        },
        /* 227 */
        /***/
        function(s, n, t) {
          s.exports = t(228);
        },
        /* 228 */
        /***/
        function(s, n, t) {
          var e2 = t(229);
          s.exports = e2;
        },
        /* 229 */
        /***/
        function(s, n, t) {
          t(125), t(50), t(54);
          var e2 = t(93);
          s.exports = e2.f("iterator");
        },
        /* 230 */
        /***/
        function(s, n, t) {
          s.exports = t(231);
        },
        /* 231 */
        /***/
        function(s, n, t) {
          var e2 = t(232);
          t(251), t(252), t(253), t(254), t(255), s.exports = e2;
        },
        /* 232 */
        /***/
        function(s, n, t) {
          t(233), t(61), t(234), t(236), t(237), t(238), t(239), t(125), t(240), t(241), t(242), t(243), t(244), t(245), t(246), t(247), t(248), t(249), t(250);
          var e2 = t(9);
          s.exports = e2.Symbol;
        },
        /* 233 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(11), r = t(55), o = t(13), v = t(31), g = t(35), d = t(69), p = t(88), f = t(56), i = t(10), u = t(86), l = i("isConcatSpreadable"), m = 9007199254740991, c = "Maximum allowed index exceeded", h = u >= 51 || !a(function() {
            var S = [];
            return S[l] = false, S.concat()[0] !== S;
          }), A = f("concat"), y = function(S) {
            if (!o(S))
              return false;
            var T = S[l];
            return T !== void 0 ? !!T : r(S);
          }, x = !h || !A;
          e2({ target: "Array", proto: true, forced: x }, {
            concat: function(T) {
              var I = v(this), C = p(I, 0), E = 0, D, P, M, R, N;
              for (D = -1, M = arguments.length; D < M; D++)
                if (N = D === -1 ? I : arguments[D], y(N)) {
                  if (R = g(N.length), E + R > m)
                    throw TypeError(c);
                  for (P = 0; P < R; P++, E++)
                    P in N && d(C, E, N[P]);
                } else {
                  if (E >= m)
                    throw TypeError(c);
                  d(C, E++, N);
                }
              return C.length = E, C;
            }
          });
        },
        /* 234 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(8), r = t(36), o = t(43), v = t(14), g = t(76), d = t(106), p = t(11), f = t(16), i = t(55), u = t(13), l = t(25), m = t(31), c = t(30), h = t(60), A = t(48), y = t(77), x = t(52), S = t(126), T = t(235), I = t(127), C = t(71), E = t(18), D = t(59), P = t(19), M = t(53), R = t(74), N = t(63), B = t(51), F = t(64), O = t(10), H = t(93), L = t(12), U = t(37), z = t(42), j = t(32).forEach, K = N("hidden"), V = "Symbol", Q = "prototype", w = O("toPrimitive"), G = z.set, X = z.getterFor(V), tt = Object[Q], nt = a.Symbol, st = r("JSON", "stringify"), It = C.f, it = E.f, Ct = T.f, gt = D.f, lt = R("symbols"), dt = R("op-symbols"), At = R("string-to-symbol-registry"), ct = R("symbol-to-string-registry"), Rt = R("wks"), yt = a.QObject, pt = !yt || !yt[Q] || !yt[Q].findChild, Et = v && p(function() {
            return y(it({}, "a", {
              get: function() {
                return it(this, "a", { value: 7 }).a;
              }
            })).a != 7;
          }) ? function(Y, b, W) {
            var k = It(tt, b);
            k && delete tt[b], it(Y, b, W), k && Y !== tt && it(tt, b, k);
          } : it, Dt = function(Y, b) {
            var W = lt[Y] = y(nt[Q]);
            return G(W, {
              type: V,
              tag: Y,
              description: b
            }), v || (W.description = b), W;
          }, xt = d ? function(Y) {
            return typeof Y == "symbol";
          } : function(Y) {
            return Object(Y) instanceof nt;
          }, St = function(b, W, k) {
            b === tt && St(dt, W, k), l(b);
            var _ = h(W, true);
            return l(k), f(lt, _) ? (k.enumerable ? (f(b, K) && b[K][_] && (b[K][_] = false), k = y(k, { enumerable: A(0, false) })) : (f(b, K) || it(b, K, A(1, {})), b[K][_] = true), Et(b, _, k)) : it(b, _, k);
          }, vt = function(b, W) {
            l(b);
            var k = c(W), _ = x(k).concat(Z(k));
            return j(_, function(rt) {
              (!v || ht.call(k, rt)) && St(b, rt, k[rt]);
            }), b;
          }, mt = function(b, W) {
            return W === void 0 ? y(b) : vt(y(b), W);
          }, ht = function(b) {
            var W = h(b, true), k = gt.call(this, W);
            return this === tt && f(lt, W) && !f(dt, W) ? false : k || !f(this, W) || !f(lt, W) || f(this, K) && this[K][W] ? k : true;
          }, J = function(b, W) {
            var k = c(b), _ = h(W, true);
            if (!(k === tt && f(lt, _) && !f(dt, _))) {
              var rt = It(k, _);
              return rt && f(lt, _) && !(f(k, K) && k[K][_]) && (rt.enumerable = true), rt;
            }
          }, $ = function(b) {
            var W = Ct(c(b)), k = [];
            return j(W, function(_) {
              !f(lt, _) && !f(B, _) && k.push(_);
            }), k;
          }, Z = function(b) {
            var W = b === tt, k = Ct(W ? dt : c(b)), _ = [];
            return j(k, function(rt) {
              f(lt, rt) && (!W || f(tt, rt)) && _.push(lt[rt]);
            }), _;
          };
          if (g || (nt = function() {
            if (this instanceof nt)
              throw TypeError("Symbol is not a constructor");
            var b = !arguments.length || arguments[0] === void 0 ? void 0 : String(arguments[0]), W = F(b), k = function(_) {
              this === tt && k.call(dt, _), f(this, K) && f(this[K], W) && (this[K][W] = false), Et(this, W, A(1, _));
            };
            return v && pt && Et(tt, W, { configurable: true, set: k }), Dt(W, b);
          }, M(nt[Q], "toString", function() {
            return X(this).tag;
          }), M(nt, "withoutSetter", function(Y) {
            return Dt(F(Y), Y);
          }), D.f = ht, E.f = St, C.f = J, S.f = T.f = $, I.f = Z, H.f = function(Y) {
            return Dt(O(Y), Y);
          }, v && (it(nt[Q], "description", {
            configurable: true,
            get: function() {
              return X(this).description;
            }
          }), o || M(tt, "propertyIsEnumerable", ht, { unsafe: true }))), e2({ global: true, wrap: true, forced: !g, sham: !g }, {
            Symbol: nt
          }), j(x(Rt), function(Y) {
            L(Y);
          }), e2({ target: V, stat: true, forced: !g }, {
            // `Symbol.for` method
            // https://tc39.github.io/ecma262/#sec-symbol.for
            for: function(Y) {
              var b = String(Y);
              if (f(At, b))
                return At[b];
              var W = nt(b);
              return At[b] = W, ct[W] = b, W;
            },
            // `Symbol.keyFor` method
            // https://tc39.github.io/ecma262/#sec-symbol.keyfor
            keyFor: function(b) {
              if (!xt(b))
                throw TypeError(b + " is not a symbol");
              if (f(ct, b))
                return ct[b];
            },
            useSetter: function() {
              pt = true;
            },
            useSimple: function() {
              pt = false;
            }
          }), e2({ target: "Object", stat: true, forced: !g, sham: !v }, {
            // `Object.create` method
            // https://tc39.github.io/ecma262/#sec-object.create
            create: mt,
            // `Object.defineProperty` method
            // https://tc39.github.io/ecma262/#sec-object.defineproperty
            defineProperty: St,
            // `Object.defineProperties` method
            // https://tc39.github.io/ecma262/#sec-object.defineproperties
            defineProperties: vt,
            // `Object.getOwnPropertyDescriptor` method
            // https://tc39.github.io/ecma262/#sec-object.getownpropertydescriptors
            getOwnPropertyDescriptor: J
          }), e2({ target: "Object", stat: true, forced: !g }, {
            // `Object.getOwnPropertyNames` method
            // https://tc39.github.io/ecma262/#sec-object.getownpropertynames
            getOwnPropertyNames: $,
            // `Object.getOwnPropertySymbols` method
            // https://tc39.github.io/ecma262/#sec-object.getownpropertysymbols
            getOwnPropertySymbols: Z
          }), e2({ target: "Object", stat: true, forced: p(function() {
            I.f(1);
          }) }, {
            getOwnPropertySymbols: function(b) {
              return I.f(m(b));
            }
          }), st) {
            var q = !g || p(function() {
              var Y = nt();
              return st([Y]) != "[null]" || st({ a: Y }) != "{}" || st(Object(Y)) != "{}";
            });
            e2({ target: "JSON", stat: true, forced: q }, {
              // eslint-disable-next-line no-unused-vars
              stringify: function(b, W, k) {
                for (var _ = [b], rt = 1, ut; arguments.length > rt; )
                  _.push(arguments[rt++]);
                if (ut = W, !(!u(W) && b === void 0 || xt(b)))
                  return i(W) || (W = function(ft, at) {
                    if (typeof ut == "function" && (at = ut.call(this, ft, at)), !xt(at))
                      return at;
                  }), _[1] = W, st.apply(null, _);
              }
            });
          }
          nt[Q][w] || P(nt[Q], w, nt[Q].valueOf), U(nt, V), B[K] = true;
        },
        /* 235 */
        /***/
        function(s, n, t) {
          var e2 = t(30), a = t(126).f, r = {}.toString, o = typeof window == "object" && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [], v = function(g) {
            try {
              return a(g);
            } catch {
              return o.slice();
            }
          };
          s.exports.f = function(d) {
            return o && r.call(d) == "[object Window]" ? v(d) : a(e2(d));
          };
        },
        /* 236 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("asyncIterator");
        },
        /* 237 */
        /***/
        function(s, n) {
        },
        /* 238 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("hasInstance");
        },
        /* 239 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("isConcatSpreadable");
        },
        /* 240 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("match");
        },
        /* 241 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("matchAll");
        },
        /* 242 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("replace");
        },
        /* 243 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("search");
        },
        /* 244 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("species");
        },
        /* 245 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("split");
        },
        /* 246 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("toPrimitive");
        },
        /* 247 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("toStringTag");
        },
        /* 248 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("unscopables");
        },
        /* 249 */
        /***/
        function(s, n, t) {
          var e2 = t(37);
          e2(Math, "Math", true);
        },
        /* 250 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(37);
          a(e2.JSON, "JSON", true);
        },
        /* 251 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("asyncDispose");
        },
        /* 252 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("dispose");
        },
        /* 253 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("observable");
        },
        /* 254 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("patternMatch");
        },
        /* 255 */
        /***/
        function(s, n, t) {
          var e2 = t(12);
          e2("replaceAll");
        },
        /* 256 */
        /***/
        function(s, n, t) {
          s.exports = t(257);
        },
        /* 257 */
        /***/
        function(s, n, t) {
          var e2 = t(258);
          s.exports = e2;
        },
        /* 258 */
        /***/
        function(s, n, t) {
          t(259);
          var e2 = t(9);
          s.exports = e2.parseInt;
        },
        /* 259 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(260);
          e2({ global: true, forced: parseInt != a }, {
            parseInt: a
          });
        },
        /* 260 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(90).trim, r = t(68), o = e2.parseInt, v = /^[+-]?0[Xx]/, g = o(r + "08") !== 8 || o(r + "0x16") !== 22;
          s.exports = g ? function(p, f) {
            var i = a(String(p));
            return o(i, f >>> 0 || (v.test(i) ? 16 : 10));
          } : o;
        },
        /* 261 */
        /***/
        function(s, n, t) {
          var e2 = t(262);
          s.exports = e2;
        },
        /* 262 */
        /***/
        function(s, n, t) {
          var e2 = t(263), a = Array.prototype;
          s.exports = function(r) {
            var o = r.slice;
            return r === a || r instanceof Array && o === a.slice ? e2 : o;
          };
        },
        /* 263 */
        /***/
        function(s, n, t) {
          t(264);
          var e2 = t(15);
          s.exports = e2("Array").slice;
        },
        /* 264 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(13), r = t(55), o = t(79), v = t(35), g = t(30), d = t(69), p = t(10), f = t(56), i = t(22), u = f("slice"), l = i("slice", { ACCESSORS: true, 0: 0, 1: 2 }), m = p("species"), c = [].slice, h = Math.max;
          e2({ target: "Array", proto: true, forced: !u || !l }, {
            slice: function(y, x) {
              var S = g(this), T = v(S.length), I = o(y, T), C = o(x === void 0 ? T : x, T), E, D, P;
              if (r(S) && (E = S.constructor, typeof E == "function" && (E === Array || r(E.prototype)) ? E = void 0 : a(E) && (E = E[m], E === null && (E = void 0)), E === Array || E === void 0))
                return c.call(S, I, C);
              for (D = new (E === void 0 ? Array : E)(h(C - I, 0)), P = 0; I < C; I++, P++)
                I in S && d(D, P, S[I]);
              return D.length = P, D;
            }
          });
        },
        /* 265 */
        /***/
        function(s, n, t) {
          t(266);
          var e2 = t(9);
          s.exports = e2.setTimeout;
        },
        /* 266 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(8), r = t(84), o = [].slice, v = /MSIE .\./.test(r), g = function(d) {
            return function(p, f) {
              var i = arguments.length > 2, u = i ? o.call(arguments, 2) : void 0;
              return d(i ? function() {
                (typeof p == "function" ? p : Function(p)).apply(this, u);
              } : p, f);
            };
          };
          e2({ global: true, bind: true, forced: v }, {
            // `setTimeout` method
            // https://html.spec.whatwg.org/multipage/timers-and-user-prompts.html#dom-settimeout
            setTimeout: g(a.setTimeout),
            // `setInterval` method
            // https://html.spec.whatwg.org/multipage/timers-and-user-prompts.html#dom-setinterval
            setInterval: g(a.setInterval)
          });
        },
        /* 267 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(128));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(272)), g = o.__importDefault(t(273)), d = o.__importDefault(t(129)), p = o.__importDefault(t(274)), f = o.__importDefault(t(275)), i = o.__importDefault(t(276)), u = o.__importDefault(t(130)), l = o.__importDefault(t(277)), m = o.__importDefault(t(278)), c = o.__importDefault(t(279)), h = (0, r.default)(
            {},
            v.default,
            g.default,
            d.default,
            f.default,
            p.default,
            i.default,
            u.default,
            l.default,
            m.default,
            c.default,
            //链接校验的配置函数
            {
              linkCheck: function(y, x) {
                return true;
              }
            }
          );
          n.default = h;
        },
        /* 268 */
        /***/
        function(s, n, t) {
          var e2 = t(269);
          s.exports = e2;
        },
        /* 269 */
        /***/
        function(s, n, t) {
          t(270);
          var e2 = t(9);
          s.exports = e2.Object.assign;
        },
        /* 270 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(271);
          e2({ target: "Object", stat: true, forced: Object.assign !== a }, {
            assign: a
          });
        },
        /* 271 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(11), r = t(52), o = t(127), v = t(59), g = t(31), d = t(72), p = Object.assign, f = Object.defineProperty;
          s.exports = !p || a(function() {
            if (e2 && p({ b: 1 }, p(f({}, "a", {
              enumerable: true,
              get: function() {
                f(this, "b", {
                  value: 3,
                  enumerable: false
                });
              }
            }), { b: 2 })).b !== 1)
              return true;
            var i = {}, u = {}, l = Symbol(), m = "abcdefghijklmnopqrst";
            return i[l] = 7, m.split("").forEach(function(c) {
              u[c] = c;
            }), p({}, i)[l] != 7 || r(p({}, u)).join("") != m;
          }) ? function(u, l) {
            for (var m = g(u), c = arguments.length, h = 1, A = o.f, y = v.f; c > h; )
              for (var x = d(arguments[h++]), S = A ? r(x).concat(A(x)) : r(x), T = S.length, I = 0, C; T > I; )
                C = S[I++], (!e2 || y.call(x, C)) && (m[C] = x[C]);
            return m;
          } : p;
        },
        /* 272 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            menus: [
              "head",
              "bold",
              "fontSize",
              // 'customFontSize',
              "fontName",
              "italic",
              "underline",
              "strikeThrough",
              "indent",
              "lineHeight",
              "foreColor",
              "backColor",
              "link",
              "list",
              "todo",
              "justify",
              "quote",
              "emoticon",
              "image",
              "video",
              "table",
              "code",
              "splitLine",
              "undo",
              "redo"
            ],
            fontNames: ["黑体", "仿宋", "楷体", "标楷体", "华文仿宋", "华文楷体", "宋体", "微软雅黑", "Arial", "Tahoma", "Verdana", "Times New Roman", "Courier New"],
            //  fontNames: [{ name: '宋体', value: '宋体' }],
            fontSizes: {
              "x-small": {
                name: "10px",
                value: "1"
              },
              small: {
                name: "13px",
                value: "2"
              },
              normal: {
                name: "16px",
                value: "3"
              },
              large: {
                name: "18px",
                value: "4"
              },
              "x-large": {
                name: "24px",
                value: "5"
              },
              "xx-large": {
                name: "32px",
                value: "6"
              },
              "xxx-large": {
                name: "48px",
                value: "7"
              }
            },
            // customFontSize: [ // 该菜单暂时不用 - 王福朋 20200924
            //     { value: '9px', text: '9' },
            //     { value: '10px', text: '10' },
            //     { value: '12px', text: '12' },
            //     { value: '14px', text: '14' },
            //     { value: '16px', text: '16' },
            //     { value: '20px', text: '20' },
            //     { value: '42px', text: '42' },
            //     { value: '72px', text: '72' },
            // ],
            colors: ["#000000", "#ffffff", "#eeece0", "#1c487f", "#4d80bf", "#c24f4a", "#8baa4a", "#7b5ba1", "#46acc8", "#f9963b"],
            //插入代码语言配置
            languageType: ["Bash", "C", "C#", "C++", "CSS", "Java", "JavaScript", "JSON", "TypeScript", "Plain text", "Html", "XML", "SQL", "Go", "Kotlin", "Lua", "Markdown", "PHP", "Python", "Shell Session", "Ruby"],
            languageTab: "　　　　",
            /**
             * 表情配置菜单
             * 如果为emoji表情直接作为元素插入
             * emoticon:Array<EmotionsType>
             */
            emotions: [{
              // tab 的标题
              title: "表情",
              // type -> 'emoji' / 'image'
              type: "emoji",
              // content -> 数组
              content: "😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😛 😝 😜 🤓 😎 😏 😒 😞 😔 😟 😕 🙁 😣 😖 😫 😩 😢 😭 😤 😠 😡 😳 😱 😨 🤗 🤔 😶 😑 😬 🙄 😯 😴 😷 🤑 😈 🤡 💩 👻 💀 👀 👣".split(/\s/)
            }, {
              // tab 的标题
              title: "手势",
              // type -> 'emoji' / 'image'
              type: "emoji",
              // content -> 数组
              content: "👐 🙌 👏 🤝 👍 👎 👊 ✊ 🤛 🤜 🤞 ✌️ 🤘 👌 👈 👉 👆 👇 ☝️ ✋ 🤚 🖐 🖖 👋 🤙 💪 🖕 ✍️ 🙏".split(/\s/)
            }],
            lineHeights: ["1", "1.15", "1.6", "2", "2.5", "3"],
            undoLimit: 20,
            indentation: "2em",
            showMenuTooltips: true,
            // 菜单栏tooltip为上标还是下标
            menuTooltipPosition: "up"
          };
        },
        /* 273 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(7);
          function o(v, g, d) {
            window.alert(v);
          }
          n.default = {
            onchangeTimeout: 200,
            onchange: null,
            onfocus: r.EMPTY_FN,
            onblur: r.EMPTY_FN,
            onCatalogChange: null,
            customAlert: o
          };
        },
        /* 274 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            // 粘贴过滤样式，默认开启
            pasteFilterStyle: true,
            // 粘贴内容时，忽略图片。默认关闭
            pasteIgnoreImg: false,
            // 对粘贴的文字进行自定义处理，返回处理后的结果。编辑器会将处理后的结果粘贴到编辑区域中。
            // IE 暂时不支持
            pasteTextHandle: function(o) {
              return o;
            }
          };
        },
        /* 275 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            styleWithCSS: false
          };
        },
        /* 276 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(7);
          n.default = {
            // 网络图片校验的配置函数
            linkImgCheck: function(v, g, d) {
              return true;
            },
            // 显示“插入网络图片”
            showLinkImg: true,
            // 显示“插入图片alt”
            showLinkImgAlt: true,
            // 显示“插入图片href”
            showLinkImgHref: true,
            // 插入图片成功之后的回调函数
            linkImgCallback: r.EMPTY_FN,
            // accept
            uploadImgAccept: ["jpg", "jpeg", "png", "gif", "bmp"],
            // 服务端地址
            uploadImgServer: "",
            // 使用 base64 存储图片
            uploadImgShowBase64: false,
            // 上传图片的最大体积，默认 5M
            uploadImgMaxSize: 5 * 1024 * 1024,
            // 一次最多上传多少个图片
            uploadImgMaxLength: 100,
            // 自定义上传图片的名称
            uploadFileName: "",
            // 上传图片自定义参数
            uploadImgParams: {},
            // 自定义参数拼接到 url 中
            uploadImgParamsWithUrl: false,
            // 上传图片自定义 header
            uploadImgHeaders: {},
            // 钩子函数
            uploadImgHooks: {},
            // 上传图片超时时间 ms
            uploadImgTimeout: 10 * 1e3,
            // 跨域带 cookie
            withCredentials: false,
            // 自定义上传
            customUploadImg: null,
            // 从媒体库上传
            uploadImgFromMedia: null
          };
        },
        /* 277 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.default = {
            lang: "zh-CN",
            languages: {
              "zh-CN": {
                wangEditor: {
                  重置: "重置",
                  插入: "插入",
                  默认: "默认",
                  创建: "创建",
                  修改: "修改",
                  如: "如",
                  请输入正文: "请输入正文",
                  menus: {
                    title: {
                      标题: "标题",
                      加粗: "加粗",
                      字号: "字号",
                      字体: "字体",
                      斜体: "斜体",
                      下划线: "下划线",
                      删除线: "删除线",
                      缩进: "缩进",
                      行高: "行高",
                      文字颜色: "文字颜色",
                      背景色: "背景色",
                      链接: "链接",
                      序列: "序列",
                      对齐: "对齐",
                      引用: "引用",
                      表情: "表情",
                      图片: "图片",
                      视频: "视频",
                      表格: "表格",
                      代码: "代码",
                      分割线: "分割线",
                      恢复: "恢复",
                      撤销: "撤销",
                      全屏: "全屏",
                      取消全屏: "取消全屏",
                      待办事项: "待办事项"
                    },
                    dropListMenu: {
                      设置标题: "设置标题",
                      背景颜色: "背景颜色",
                      文字颜色: "文字颜色",
                      设置字号: "设置字号",
                      设置字体: "设置字体",
                      设置缩进: "设置缩进",
                      对齐方式: "对齐方式",
                      设置行高: "设置行高",
                      序列: "序列",
                      head: {
                        正文: "正文"
                      },
                      indent: {
                        增加缩进: "增加缩进",
                        减少缩进: "减少缩进"
                      },
                      justify: {
                        靠左: "靠左",
                        居中: "居中",
                        靠右: "靠右",
                        两端: "两端"
                      },
                      list: {
                        无序列表: "无序列表",
                        有序列表: "有序列表"
                      }
                    },
                    panelMenus: {
                      emoticon: {
                        默认: "默认",
                        新浪: "新浪",
                        emoji: "emoji",
                        手势: "手势"
                      },
                      image: {
                        上传图片: "上传图片",
                        网络图片: "网络图片",
                        图片地址: "图片地址",
                        图片文字说明: "图片文字说明",
                        跳转链接: "跳转链接"
                      },
                      link: {
                        链接: "链接",
                        链接文字: "链接文字",
                        取消链接: "取消链接",
                        查看链接: "查看链接"
                      },
                      video: {
                        插入视频: "插入视频",
                        上传视频: "上传视频"
                      },
                      table: {
                        行: "行",
                        列: "列",
                        的: "的",
                        表格: "表格",
                        添加行: "添加行",
                        删除行: "删除行",
                        添加列: "添加列",
                        删除列: "删除列",
                        设置表头: "设置表头",
                        取消表头: "取消表头",
                        插入表格: "插入表格",
                        删除表格: "删除表格"
                      },
                      code: {
                        删除代码: "删除代码",
                        修改代码: "修改代码",
                        插入代码: "插入代码"
                      }
                    }
                  },
                  validate: {
                    张图片: "张图片",
                    大于: "大于",
                    图片链接: "图片链接",
                    不是图片: "不是图片",
                    返回结果: "返回结果",
                    上传图片超时: "上传图片超时",
                    上传图片错误: "上传图片错误",
                    上传图片失败: "上传图片失败",
                    插入图片错误: "插入图片错误",
                    一次最多上传: "一次最多上传",
                    下载链接失败: "下载链接失败",
                    图片验证未通过: "图片验证未通过",
                    服务器返回状态: "服务器返回状态",
                    上传图片返回结果错误: "上传图片返回结果错误",
                    请替换为支持的图片类型: "请替换为支持的图片类型",
                    您插入的网络图片无法识别: "您插入的网络图片无法识别",
                    您刚才插入的图片链接未通过编辑器校验: "您刚才插入的图片链接未通过编辑器校验",
                    插入视频错误: "插入视频错误",
                    视频链接: "视频链接",
                    不是视频: "不是视频",
                    视频验证未通过: "视频验证未通过",
                    个视频: "个视频",
                    上传视频超时: "上传视频超时",
                    上传视频错误: "上传视频错误",
                    上传视频失败: "上传视频失败",
                    上传视频返回结果错误: "上传视频返回结果错误"
                  }
                }
              },
              en: {
                wangEditor: {
                  重置: "reset",
                  插入: "insert",
                  默认: "default",
                  创建: "create",
                  修改: "edit",
                  如: "like",
                  请输入正文: "please enter the text",
                  menus: {
                    title: {
                      标题: "head",
                      加粗: "bold",
                      字号: "font size",
                      字体: "font family",
                      斜体: "italic",
                      下划线: "underline",
                      删除线: "strikethrough",
                      缩进: "indent",
                      行高: "line heihgt",
                      文字颜色: "font color",
                      背景色: "background",
                      链接: "link",
                      序列: "numbered list",
                      对齐: "align",
                      引用: "quote",
                      表情: "emoticons",
                      图片: "image",
                      视频: "media",
                      表格: "table",
                      代码: "code",
                      分割线: "split line",
                      恢复: "redo",
                      撤销: "undo",
                      全屏: "fullscreen",
                      取消全屏: "cancel fullscreen",
                      待办事项: "todo"
                    },
                    dropListMenu: {
                      设置标题: "title",
                      背景颜色: "background",
                      文字颜色: "font color",
                      设置字号: "font size",
                      设置字体: "font family",
                      设置缩进: "indent",
                      对齐方式: "align",
                      设置行高: "line heihgt",
                      序列: "list",
                      head: {
                        正文: "text"
                      },
                      indent: {
                        增加缩进: "indent",
                        减少缩进: "outdent"
                      },
                      justify: {
                        靠左: "left",
                        居中: "center",
                        靠右: "right",
                        两端: "justify"
                      },
                      list: {
                        无序列表: "unordered",
                        有序列表: "ordered"
                      }
                    },
                    panelMenus: {
                      emoticon: {
                        表情: "emoji",
                        手势: "gesture"
                      },
                      image: {
                        上传图片: "upload image",
                        网络图片: "network image",
                        图片地址: "image link",
                        图片文字说明: "image alt",
                        跳转链接: "hyperlink"
                      },
                      link: {
                        链接: "link",
                        链接文字: "link text",
                        取消链接: "unlink",
                        查看链接: "view links"
                      },
                      video: {
                        插入视频: "insert video",
                        上传视频: "upload local video"
                      },
                      table: {
                        行: "rows",
                        列: "columns",
                        的: " ",
                        表格: "table",
                        添加行: "insert row",
                        删除行: "delete row",
                        添加列: "insert column",
                        删除列: "delete column",
                        设置表头: "set header",
                        取消表头: "cancel header",
                        插入表格: "insert table",
                        删除表格: "delete table"
                      },
                      code: {
                        删除代码: "delete code",
                        修改代码: "edit code",
                        插入代码: "insert code"
                      }
                    }
                  },
                  validate: {
                    张图片: "images",
                    大于: "greater than",
                    图片链接: "image link",
                    不是图片: "is not image",
                    返回结果: "return results",
                    上传图片超时: "upload image timeout",
                    上传图片错误: "upload image error",
                    上传图片失败: "upload image failed",
                    插入图片错误: "insert image error",
                    一次最多上传: "once most at upload",
                    下载链接失败: "download link failed",
                    图片验证未通过: "image validate failed",
                    服务器返回状态: "server return status",
                    上传图片返回结果错误: "upload image return results error",
                    请替换为支持的图片类型: "please replace with a supported image type",
                    您插入的网络图片无法识别: "the network picture you inserted is not recognized",
                    您刚才插入的图片链接未通过编辑器校验: "the image link you just inserted did not pass the editor verification",
                    插入视频错误: "insert video error",
                    视频链接: "video link",
                    不是视频: "is not video",
                    视频验证未通过: "video validate failed",
                    个视频: "videos",
                    上传视频超时: "upload video timeout",
                    上传视频错误: "upload video error",
                    上传视频失败: "upload video failed",
                    上传视频返回结果错误: "upload video return results error"
                  }
                }
              }
            }
          };
        },
        /* 278 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(6);
          function o() {
            return !!(r.UA.isIE() || r.UA.isOldEdge);
          }
          n.default = {
            compatibleMode: o,
            historyMaxSize: 30
          };
        },
        /* 279 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(7);
          n.default = {
            // 插入网络视频前的回调函数
            onlineVideoCheck: function(v) {
              return true;
            },
            // 插入网络视频成功之后的回调函数
            onlineVideoCallback: r.EMPTY_FN,
            // 显示“插入视频”
            showLinkVideo: true,
            // accept
            uploadVideoAccept: ["mp4"],
            // 服务端地址
            uploadVideoServer: "",
            // 上传视频的最大体积，默认 1024M
            uploadVideoMaxSize: 1 * 1024 * 1024 * 1024,
            // 一次最多上传多少个视频
            // uploadVideoMaxLength: 2,
            // 自定义上传视频的名称
            uploadVideoName: "",
            // 上传视频自定义参数
            uploadVideoParams: {},
            // 自定义参数拼接到 url 中
            uploadVideoParamsWithUrl: false,
            // 上传视频自定义 header
            uploadVideoHeaders: {},
            // 钩子函数
            uploadVideoHooks: {},
            // 上传视频超时时间 ms 默认2个小时
            uploadVideoTimeout: 1e3 * 60 * 60 * 2,
            // 跨域带 cookie
            withVideoCredentials: false,
            // 自定义上传
            customUploadVideo: null,
            // 自定义插入视频
            customInsertVideo: null
          };
        },
        /* 280 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(6), d = t(7), p = (
            /** @class */
            function() {
              function f(i) {
                this._currentRange = null, this.editor = i;
              }
              return f.prototype.getRange = function() {
                return this._currentRange;
              }, f.prototype.saveRange = function(i) {
                if (i) {
                  this._currentRange = i;
                  return;
                }
                var u = window.getSelection();
                if (u.rangeCount !== 0) {
                  var l = u.getRangeAt(0), m = this.getSelectionContainerElem(l);
                  if (m != null && m.length && !(m.attr("contenteditable") === "false" || m.parentUntil("[contenteditable=false]"))) {
                    var c = this.editor, h = c.$textElem;
                    if (h.isContain(m)) {
                      if (h.elems[0] === m.elems[0]) {
                        var A;
                        if ((0, r.default)(A = h.html()).call(A) === d.EMPTY_P) {
                          var y = h.children(), x = y == null ? void 0 : y.last();
                          c.selection.createRangeByElem(x, true, true), c.selection.restoreSelection();
                        }
                      }
                      this._currentRange = l;
                    }
                  }
                }
              }, f.prototype.collapseRange = function(i) {
                i === void 0 && (i = false);
                var u = this._currentRange;
                u && u.collapse(i);
              }, f.prototype.getSelectionText = function() {
                var i = this._currentRange;
                return i ? i.toString() : "";
              }, f.prototype.getSelectionContainerElem = function(i) {
                var u;
                u = i || this._currentRange;
                var l;
                if (u)
                  return l = u.commonAncestorContainer, v.default(l.nodeType === 1 ? l : l.parentNode);
              }, f.prototype.getSelectionStartElem = function(i) {
                var u;
                u = i || this._currentRange;
                var l;
                if (u)
                  return l = u.startContainer, v.default(l.nodeType === 1 ? l : l.parentNode);
              }, f.prototype.getSelectionEndElem = function(i) {
                var u;
                u = i || this._currentRange;
                var l;
                if (u)
                  return l = u.endContainer, v.default(l.nodeType === 1 ? l : l.parentNode);
              }, f.prototype.isSelectionEmpty = function() {
                var i = this._currentRange;
                return !!(i && i.startContainer && i.startContainer === i.endContainer && i.startOffset === i.endOffset);
              }, f.prototype.restoreSelection = function() {
                var i = window.getSelection(), u = this._currentRange;
                i && u && (i.removeAllRanges(), i.addRange(u));
              }, f.prototype.createEmptyRange = function() {
                var i = this.editor, u = this.getRange(), l;
                if (u && this.isSelectionEmpty())
                  try {
                    g.UA.isWebkit() ? (i.cmd.do("insertHTML", "&#8203;"), u.setEnd(u.endContainer, u.endOffset + 1), this.saveRange(u)) : (l = v.default("<strong>&#8203;</strong>"), i.cmd.do("insertElem", l), this.createRangeByElem(l, true));
                  } catch {
                  }
              }, f.prototype.createRangeByElems = function(i, u) {
                var l = window.getSelection ? window.getSelection() : document.getSelection();
                l == null || l.removeAllRanges();
                var m = document.createRange();
                m.setStart(i, 0), m.setEnd(u, u.childNodes.length || 1), this.saveRange(m), this.restoreSelection();
              }, f.prototype.createRangeByElem = function(i, u, l) {
                if (i.length) {
                  var m = i.elems[0], c = document.createRange();
                  l ? c.selectNodeContents(m) : c.selectNode(m), u != null && (c.collapse(u), u || (this.saveRange(c), this.editor.selection.moveCursor(m))), this.saveRange(c);
                }
              }, f.prototype.getSelectionRangeTopNodes = function() {
                var i, u, l, m = (i = this.getSelectionStartElem()) === null || i === void 0 ? void 0 : i.getNodeTop(this.editor), c = (u = this.getSelectionEndElem()) === null || u === void 0 ? void 0 : u.getNodeTop(this.editor);
                return l = this.recordSelectionNodes(v.default(m), v.default(c)), l;
              }, f.prototype.moveCursor = function(i, u) {
                var l, m = this.getRange(), c = i.nodeType === 3 ? (l = i.nodeValue) === null || l === void 0 ? void 0 : l.length : i.childNodes.length;
                (g.UA.isFirefox || g.UA.isIE()) && c !== 0 && (i.nodeType === 3 || i.childNodes[c - 1].nodeName === "BR") && (c = c - 1);
                var h = u ?? c;
                m && i && (m.setStart(i, h), m.setEnd(i, h), this.restoreSelection());
              }, f.prototype.getCursorPos = function() {
                var i = window.getSelection();
                return i == null ? void 0 : i.anchorOffset;
              }, f.prototype.clearWindowSelectionRange = function() {
                var i = window.getSelection();
                i && i.removeAllRanges();
              }, f.prototype.recordSelectionNodes = function(i, u) {
                var l = [], m = true;
                try {
                  for (var c = i, h = this.editor.$textElem; m; ) {
                    var A = c == null ? void 0 : c.getNodeTop(this.editor);
                    A.getNodeName() === "BODY" && (m = false), A.length > 0 && (l.push(v.default(c)), u != null && u.equal(A) || h.equal(A) ? m = false : c = A.getNextSibling());
                  }
                } catch {
                  m = false;
                }
                return l;
              }, f.prototype.setRangeToElem = function(i) {
                var u = this.getRange();
                u == null || u.setStart(i, 0), u == null || u.setEnd(i, 0);
              }, f;
            }()
          );
          n.default = p;
        },
        /* 281 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = (
            /** @class */
            function() {
              function g(d) {
                this.editor = d;
              }
              return g.prototype.do = function(d, p) {
                var f = this.editor;
                f.config.styleWithCSS && document.execCommand("styleWithCSS", false, "true");
                var i = f.selection;
                if (i.getRange()) {
                  switch (i.restoreSelection(), d) {
                    case "insertHTML":
                      this.insertHTML(p);
                      break;
                    case "insertElem":
                      this.insertElem(p);
                      break;
                    default:
                      this.execCommand(d, p);
                      break;
                  }
                  f.menus.changeActive(), i.saveRange(), i.restoreSelection();
                }
              }, g.prototype.insertHTML = function(d) {
                var p = this.editor, f = p.selection.getRange();
                if (f != null) {
                  if (this.queryCommandSupported("insertHTML"))
                    this.execCommand("insertHTML", d);
                  else if (f.insertNode) {
                    if (f.deleteContents(), o.default(d).elems.length > 0)
                      f.insertNode(o.default(d).elems[0]);
                    else {
                      var i = document.createElement("p");
                      i.appendChild(document.createTextNode(d)), f.insertNode(i);
                    }
                    p.selection.collapseRange();
                  }
                }
              }, g.prototype.insertElem = function(d) {
                var p = this.editor, f = p.selection.getRange();
                f != null && f.insertNode && (f.deleteContents(), f.insertNode(d.elems[0]));
              }, g.prototype.execCommand = function(d, p) {
                document.execCommand(d, false, p);
              }, g.prototype.queryCommandValue = function(d) {
                return document.queryCommandValue(d);
              }, g.prototype.queryCommandState = function(d) {
                return document.queryCommandState(d);
              }, g.prototype.queryCommandSupported = function(d) {
                return document.queryCommandSupported(d);
              }, g;
            }()
          );
          n.default = v;
        },
        /* 282 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29)), o = e2(t(4)), v = e2(t(17)), g = e2(t(27)), d = e2(t(46));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var p = t(2), f = p.__importDefault(t(3)), i = p.__importDefault(t(287)), u = t(6), l = p.__importDefault(t(299)), m = p.__importDefault(t(300)), c = t(7), h = (
            /** @class */
            function() {
              function A(y) {
                this.editor = y, this.eventHooks = {
                  onBlurEvents: [],
                  changeEvents: [],
                  dropEvents: [],
                  clickEvents: [],
                  keydownEvents: [],
                  keyupEvents: [],
                  tabUpEvents: [],
                  tabDownEvents: [],
                  enterUpEvents: [],
                  enterDownEvents: [],
                  deleteUpEvents: [],
                  deleteDownEvents: [],
                  pasteEvents: [],
                  linkClickEvents: [],
                  codeClickEvents: [],
                  textScrollEvents: [],
                  toolbarClickEvents: [],
                  imgClickEvents: [],
                  imgDragBarMouseDownEvents: [],
                  tableClickEvents: [],
                  menuClickEvents: [],
                  dropListMenuHoverEvents: [],
                  splitLineEvents: [],
                  videoClickEvents: []
                };
              }
              return A.prototype.init = function() {
                this._saveRange(), this._bindEventHooks(), i.default(this);
              }, A.prototype.togglePlaceholder = function() {
                var y, x = this.html(), S = (0, r.default)(y = this.editor.$textContainerElem).call(y, ".placeholder");
                S.hide(), !this.editor.isComposing && (!x || x === " ") && S.show();
              }, A.prototype.clear = function() {
                this.html(c.EMPTY_P);
              }, A.prototype.html = function(y) {
                var x = this.editor, S = x.$textElem;
                if (y == null) {
                  var T = S.html();
                  T = T.replace(/\u200b/gm, ""), T = T.replace(/<p><\/p>/gim, ""), T = T.replace(c.EMPTY_P_LAST_REGEX, ""), T = T.replace(c.EMPTY_P_REGEX, "<p>");
                  var I = T.match(/<(img|br|hr|input)[^>]*>/gi);
                  return I !== null && (0, o.default)(I).call(I, function(C) {
                    C.match(/\/>/) || (T = T.replace(C, C.substring(0, C.length - 1) + "/>"));
                  }), T;
                }
                y = (0, v.default)(y).call(y), y === "" && (y = c.EMPTY_P), (0, g.default)(y).call(y, "<") !== 0 && (y = "<p>" + y + "</p>"), S.html(y), x.initSelection();
              }, A.prototype.setJSON = function(y) {
                var x = m.default(y).children(), S = this.editor, T = S.$textElem;
                x && T.replaceChildAll(x);
              }, A.prototype.getJSON = function() {
                var y = this.editor, x = y.$textElem;
                return l.default(x);
              }, A.prototype.text = function(y) {
                var x = this.editor, S = x.$textElem;
                if (y == null) {
                  var T = S.text();
                  return T = T.replace(/\u200b/gm, ""), T;
                }
                S.text("<p>" + y + "</p>"), x.initSelection();
              }, A.prototype.append = function(y) {
                var x = this.editor;
                (0, g.default)(y).call(y, "<") !== 0 && (y = "<p>" + y + "</p>"), this.html(this.html() + y), x.initSelection();
              }, A.prototype._saveRange = function() {
                var y = this.editor, x = y.$textElem, S = f.default(document);
                function T() {
                  y.selection.saveRange(), y.menus.changeActive();
                }
                x.on("keyup", T);
                function I() {
                  T(), x.off("click", I);
                }
                x.on("click", I);
                function C() {
                  T(), S.off("mouseup", C);
                }
                function E() {
                  S.on("mouseup", C), x.off("mouseleave", E);
                }
                x.on("mousedown", function() {
                  x.on("mouseleave", E);
                }), x.on("mouseup", function(D) {
                  x.off("mouseleave", E), (0, d.default)(function() {
                    var P = y.selection, M = P.getRange();
                    M !== null && T();
                  }, 0);
                });
              }, A.prototype._bindEventHooks = function() {
                var y = this.editor, x = y.$textElem, S = this.eventHooks;
                x.on("click", function(I) {
                  var C = S.clickEvents;
                  (0, o.default)(C).call(C, function(E) {
                    return E(I);
                  });
                }), x.on("keyup", function(I) {
                  if (I.keyCode === 13) {
                    var C = S.enterUpEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("keyup", function(I) {
                  var C = S.keyupEvents;
                  (0, o.default)(C).call(C, function(E) {
                    return E(I);
                  });
                }), x.on("keydown", function(I) {
                  var C = S.keydownEvents;
                  (0, o.default)(C).call(C, function(E) {
                    return E(I);
                  });
                }), x.on("keyup", function(I) {
                  if (!(I.keyCode !== 8 && I.keyCode !== 46)) {
                    var C = S.deleteUpEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("keydown", function(I) {
                  if (!(I.keyCode !== 8 && I.keyCode !== 46)) {
                    var C = S.deleteDownEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("paste", function(I) {
                  if (!u.UA.isIE()) {
                    I.preventDefault();
                    var C = S.pasteEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("keydown", function(I) {
                  (y.isFocus || y.isCompatibleMode) && (I.ctrlKey || I.metaKey) && I.keyCode === 90 && (I.preventDefault(), I.shiftKey ? y.history.restore() : y.history.revoke());
                }), x.on("keyup", function(I) {
                  if (I.keyCode === 9) {
                    I.preventDefault();
                    var C = S.tabUpEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("keydown", function(I) {
                  if (I.keyCode === 9) {
                    I.preventDefault();
                    var C = S.tabDownEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on(
                  "scroll",
                  // 使用节流
                  u.throttle(function(I) {
                    var C = S.textScrollEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }, 100)
                );
                function T(I) {
                  I.preventDefault();
                }
                f.default(document).on("dragleave", T).on("drop", T).on("dragenter", T).on("dragover", T), y.beforeDestroy(function() {
                  f.default(document).off("dragleave", T).off("drop", T).off("dragenter", T).off("dragover", T);
                }), x.on("drop", function(I) {
                  I.preventDefault();
                  var C = S.dropEvents;
                  (0, o.default)(C).call(C, function(E) {
                    return E(I);
                  });
                }), x.on("click", function(I) {
                  var C = null, E = I.target, D = f.default(E);
                  if (D.getNodeName() === "A")
                    C = D;
                  else {
                    var P = D.parentUntil("a");
                    P != null && (C = P);
                  }
                  if (C) {
                    var M = S.linkClickEvents;
                    (0, o.default)(M).call(M, function(R) {
                      return R(C);
                    });
                  }
                }), x.on("click", function(I) {
                  var C = null, E = I.target, D = f.default(E);
                  if (D.getNodeName() === "IMG" && !D.elems[0].getAttribute("data-emoji") && (I.stopPropagation(), C = D), !!C) {
                    var P = S.imgClickEvents;
                    (0, o.default)(P).call(P, function(M) {
                      return M(C);
                    });
                  }
                }), x.on("click", function(I) {
                  var C = null, E = I.target, D = f.default(E);
                  if (D.getNodeName() === "PRE")
                    C = D;
                  else {
                    var P = D.parentUntil("pre");
                    P !== null && (C = P);
                  }
                  if (C) {
                    var M = S.codeClickEvents;
                    (0, o.default)(M).call(M, function(R) {
                      return R(C);
                    });
                  }
                }), x.on("click", function(I) {
                  var C = null, E = I.target, D = f.default(E);
                  if (D.getNodeName() === "HR" && (C = D), !!C) {
                    y.selection.createRangeByElem(C), y.selection.restoreSelection();
                    var P = S.splitLineEvents;
                    (0, o.default)(P).call(P, function(M) {
                      return M(C);
                    });
                  }
                }), y.$toolbarElem.on("click", function(I) {
                  var C = S.toolbarClickEvents;
                  (0, o.default)(C).call(C, function(E) {
                    return E(I);
                  });
                }), y.$textContainerElem.on("mousedown", function(I) {
                  var C = I.target, E = f.default(C);
                  if (E.hasClass("w-e-img-drag-rb")) {
                    var D = S.imgDragBarMouseDownEvents;
                    (0, o.default)(D).call(D, function(P) {
                      return P();
                    });
                  }
                }), x.on("click", function(I) {
                  var C = null, E = I.target;
                  if (C = f.default(E).parentUntilEditor("TABLE", y, E), !!C) {
                    var D = S.tableClickEvents;
                    (0, o.default)(D).call(D, function(P) {
                      return P(C, I);
                    });
                  }
                }), x.on("keydown", function(I) {
                  if (I.keyCode === 13) {
                    var C = S.enterDownEvents;
                    (0, o.default)(C).call(C, function(E) {
                      return E(I);
                    });
                  }
                }), x.on("click", function(I) {
                  var C = null, E = I.target, D = f.default(E);
                  if (D.getNodeName() === "VIDEO" && (I.stopPropagation(), C = D), !!C) {
                    var P = S.videoClickEvents;
                    (0, o.default)(P).call(P, function(M) {
                      return M(C);
                    });
                  }
                });
              }, A;
            }()
          );
          n.default = h;
        },
        /* 283 */
        /***/
        function(s, n, t) {
          var e2 = t(284);
          s.exports = e2;
        },
        /* 284 */
        /***/
        function(s, n, t) {
          var e2 = t(285), a = Array.prototype;
          s.exports = function(r) {
            var o = r.find;
            return r === a || r instanceof Array && o === a.find ? e2 : o;
          };
        },
        /* 285 */
        /***/
        function(s, n, t) {
          t(286);
          var e2 = t(15);
          s.exports = e2("Array").find;
        },
        /* 286 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(32).find, r = t(82), o = t(22), v = "find", g = true, d = o(v);
          v in [] && Array(1)[v](function() {
            g = false;
          }), e2({ target: "Array", proto: true, forced: g || !d }, {
            find: function(f) {
              return a(this, f, arguments.length > 1 ? arguments[1] : void 0);
            }
          }), r(v);
        },
        /* 287 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(288)), v = r.__importStar(t(289)), g = r.__importDefault(t(290)), d = r.__importDefault(t(291)), p = r.__importDefault(t(298));
          function f(i) {
            var u = i.editor, l = i.eventHooks;
            o.default(u, l.enterUpEvents, l.enterDownEvents), v.default(u, l.deleteUpEvents, l.deleteDownEvents), v.cutToKeepP(u, l.keyupEvents), g.default(u, l.tabDownEvents), d.default(u, l.pasteEvents), p.default(u, l.imgClickEvents);
          }
          n.default = f;
        },
        /* 288 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(7), g = o.__importDefault(t(3));
          function d(p, f, i) {
            function u(c) {
              var h, A = g.default(v.EMPTY_P);
              if (A.insertBefore(c), (0, r.default)(h = c.html()).call(h, "<img") >= 0) {
                A.remove();
                return;
              }
              p.selection.createRangeByElem(A, true, true), p.selection.restoreSelection(), c.remove();
            }
            function l() {
              var c = p.$textElem, h = p.selection.getSelectionContainerElem(), A = h.parent();
              if (A.html() === "<code><br></code>") {
                u(A);
                return;
              }
              if (h.getNodeName() === "FONT" && h.text() === "" && h.attr("face") === "monospace") {
                u(A);
                return;
              }
              if (A.equal(c)) {
                var y = h.getNodeName();
                y === "P" && h.attr("data-we-empty-p") === null || h.text() || u(h);
              }
            }
            f.push(l);
            function m(c) {
              var h;
              p.selection.saveRange((h = getSelection()) === null || h === void 0 ? void 0 : h.getRangeAt(0));
              var A = p.selection.getSelectionContainerElem();
              A.id === p.textElemId && (c.preventDefault(), p.cmd.do("insertHTML", "<p><br></p>"));
            }
            i.push(m);
          }
          n.default = d;
        },
        /* 289 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17)), o = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.cutToKeepP = void 0;
          var v = t(2), g = t(7), d = v.__importDefault(t(3));
          function p(i, u, l) {
            function m() {
              var h = i.$textElem, A = i.$textElem.html(), y = i.$textElem.text(), x = (0, r.default)(A).call(A), S = ["<p><br></p>", "<br>", '<p data-we-empty-p=""></p>', g.EMPTY_P];
              if (/^\s*$/.test(y) && (!x || (0, o.default)(S).call(S, x))) {
                h.html(g.EMPTY_P);
                var T = h.getNode();
                i.selection.createRangeByElems(T.childNodes[0], T.childNodes[0]);
                var I = i.selection.getSelectionContainerElem();
                i.selection.restoreSelection(), i.selection.moveCursor(I.getNode(), 0);
              }
            }
            u.push(m);
            function c(h) {
              var A, y = i.$textElem, x = (0, r.default)(A = y.html().toLowerCase()).call(A);
              if (x === g.EMPTY_P) {
                h.preventDefault();
                return;
              }
            }
            l.push(c);
          }
          function f(i, u) {
            function l(m) {
              var c;
              if (m.keyCode === 88) {
                var h = i.$textElem, A = (0, r.default)(c = h.html().toLowerCase()).call(c);
                if (!A || A === "<br>") {
                  var y = d.default(g.EMPTY_P);
                  h.html(" "), h.append(y), i.selection.createRangeByElem(y, false, true), i.selection.restoreSelection(), i.selection.moveCursor(y.getNode(), 0);
                }
              }
            }
            u.push(l);
          }
          n.cutToKeepP = f, n.default = p;
        },
        /* 290 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function r(o, v) {
            function g() {
              if (o.cmd.queryCommandSupported("insertHTML")) {
                var d = o.selection.getSelectionContainerElem();
                if (d) {
                  var p = d.parent(), f = d.getNodeName(), i = p.getNodeName();
                  f == "CODE" || i === "CODE" || i === "PRE" || /hljs/.test(i) ? o.cmd.do("insertHTML", o.config.languageTab) : o.cmd.do("insertHTML", "&nbsp;&nbsp;&nbsp;&nbsp;");
                }
              }
            }
            v.push(g);
          }
          n.default = r;
        },
        /* 291 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17)), o = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(131), g = t(6), d = t(7);
          function p(m) {
            var c, h = (0, r.default)(c = m.replace(/<div>/gim, "<p>").replace(/<\/div>/gim, "</p>")).call(c), A = document.createElement("div");
            return A.innerHTML = h, A.innerHTML.replace(/<p><\/p>/gim, "");
          }
          function f(m) {
            var c = m.replace(/<br>|<br\/>/gm, `
`).replace(/<[^>]+>/gm, "");
            return c;
          }
          function i(m) {
            var c;
            if (m === "")
              return false;
            var h = document.createElement("div");
            return h.innerHTML = m, ((c = h.firstChild) === null || c === void 0 ? void 0 : c.nodeName) === "P";
          }
          function u(m) {
            if (!(m != null && m.length))
              return false;
            var c = m.elems[0];
            return c.nodeName === "P" && c.innerHTML === "<br>";
          }
          function l(m, c) {
            function h(A) {
              var y = m.config, x = y.pasteFilterStyle, S = y.pasteIgnoreImg, T = y.pasteTextHandle, I = v.getPasteHtml(A, x, S), C = v.getPasteText(A);
              C = C.replace(/\n/gm, "<br>");
              var E = m.selection.getSelectionContainerElem();
              if (E) {
                var D = E == null ? void 0 : E.getNodeName(), P = E == null ? void 0 : E.getNodeTop(m), M = "";
                if (P.elems[0] && (M = P == null ? void 0 : P.getNodeName()), D === "CODE" || M === "PRE") {
                  T && g.isFunction(T) && (C = "" + (T(C) || "")), m.cmd.do("insertHTML", f(C));
                  return;
                }
                if (d.urlRegex.test(C) && x) {
                  T && g.isFunction(T) && (C = "" + (T(C) || ""));
                  var R = C.replace(d.urlRegex, function(z) {
                    return '<a href="' + z + '" target="_blank">' + z + "</a>";
                  }), N = m.selection.getRange(), B = document.createElement("div"), F = document.createDocumentFragment();
                  if (B.innerHTML = R, N == null)
                    return;
                  for (; B.childNodes.length; )
                    F.append(B.childNodes[0]);
                  var O = F.querySelectorAll("a");
                  (0, o.default)(O).call(O, function(z) {
                    z.innerText = z.href;
                  }), N.insertNode && (N.deleteContents(), N.insertNode(F)), m.selection.clearWindowSelectionRange();
                  return;
                }
                if (I)
                  try {
                    T && g.isFunction(T) && (I = "" + (T(I) || ""));
                    var H = /[\.\#\@]?\w+[ ]+\{[^}]*\}/.test(I);
                    if (H && x)
                      m.cmd.do("insertHTML", "" + p(C));
                    else {
                      var L = p(I);
                      if (i(L)) {
                        var U = m.$textElem;
                        if (m.cmd.do("insertHTML", L), U.equal(E)) {
                          m.selection.createEmptyRange();
                          return;
                        }
                        u(P) && P.remove();
                      } else
                        m.cmd.do("insertHTML", L);
                    }
                  } catch {
                    T && g.isFunction(T) && (C = "" + (T(C) || "")), m.cmd.do("insertHTML", "" + p(C));
                  }
              }
            }
            c.push(h);
          }
          n.default = l;
        },
        /* 292 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17)), o = e2(t(4)), v = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var g = t(2), d = t(293), p = g.__importDefault(t(297));
          function f(c) {
            var h = /<span>.*?<\/span>/gi, A = /<span>(.*?)<\/span>/;
            return c.replace(h, function(y) {
              var x = y.match(A);
              return x == null ? "" : x[1];
            });
          }
          function i(c, h) {
            var A;
            return c = (0, r.default)(A = c.toLowerCase()).call(A), !!(d.IGNORE_TAGS.has(c) || h && c === "img");
          }
          function u(c, h) {
            var A = "";
            A = "<" + c;
            var y = [];
            (0, o.default)(h).call(h, function(S) {
              y.push(S.name + '="' + S.value + '"');
            }), y.length > 0 && (A = A + " " + y.join(" "));
            var x = d.EMPTY_TAGS.has(c);
            return A = A + (x ? "/" : "") + ">", A;
          }
          function l(c) {
            return "</" + c + ">";
          }
          function m(c, h, A) {
            h === void 0 && (h = true), A === void 0 && (A = false);
            var y = [], x = "";
            function S(E) {
              E = (0, r.default)(E).call(E), E && (d.EMPTY_TAGS.has(E) || (x = E));
            }
            function T() {
              x = "";
            }
            var I = new p.default();
            I.parse(c, {
              startElement: function(D, P) {
                if (S(D), !i(D, A)) {
                  var M = d.NECESSARY_ATTRS.get(D) || [], R = [];
                  (0, o.default)(P).call(P, function(B) {
                    var F = B.name;
                    if (F === "style") {
                      h || R.push(B);
                      return;
                    }
                    (0, v.default)(M).call(M, F) !== false && R.push(B);
                  });
                  var N = u(D, R);
                  y.push(N);
                }
              },
              characters: function(D) {
                D && (i(x, A) || y.push(D));
              },
              endElement: function(D) {
                if (!i(D, A)) {
                  var P = l(D);
                  y.push(P), T();
                }
              },
              comment: function(D) {
                S(D);
              }
            });
            var C = y.join("");
            return C = f(C), C;
          }
          n.default = m;
        },
        /* 293 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(132)), o = e2(t(121));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.TOP_LEVEL_TAGS = n.EMPTY_TAGS = n.NECESSARY_ATTRS = n.IGNORE_TAGS = void 0, n.IGNORE_TAGS = new r.default(["doctype", "!doctype", "html", "head", "meta", "body", "script", "style", "link", "frame", "iframe", "title", "svg", "center", "o:p"]), n.NECESSARY_ATTRS = new o.default([["img", ["src", "alt"]], ["a", ["href", "target"]], ["td", ["colspan", "rowspan"]], ["th", ["colspan", "rowspan"]]]), n.EMPTY_TAGS = new r.default(["area", "base", "basefont", "br", "col", "hr", "img", "input", "isindex", "embed"]), n.TOP_LEVEL_TAGS = new r.default(["h1", "h2", "h3", "h4", "h5", "p", "ul", "ol", "table", "blockquote", "pre", "hr", "form"]);
        },
        /* 294 */
        /***/
        function(s, n, t) {
          var e2 = t(295);
          s.exports = e2;
        },
        /* 295 */
        /***/
        function(s, n, t) {
          t(296), t(61), t(50), t(54);
          var e2 = t(9);
          s.exports = e2.Set;
        },
        /* 296 */
        /***/
        function(s, n, t) {
          var e2 = t(122), a = t(124);
          s.exports = e2("Set", function(r) {
            return function() {
              return r(this, arguments.length ? arguments[0] : void 0);
            };
          }, a);
        },
        /* 297 */
        /***/
        function(s, n) {
          function t() {
          }
          t.prototype = {
            handler: null,
            // regexps
            startTagRe: /^<([^>\s\/]+)((\s+[^=>\s]+(\s*=\s*((\"[^"]*\")|(\'[^']*\')|[^>\s]+))?)*)\s*\/?\s*>/m,
            endTagRe: /^<\/([^>\s]+)[^>]*>/m,
            attrRe: /([^=\s]+)(\s*=\s*((\"([^"]*)\")|(\'([^']*)\')|[^>\s]+))?/gm,
            parse: function(e2, a) {
              a && (this.contentHandler = a);
              for (var r, o, v, g = false, d = this; e2.length > 0; )
                e2.substring(0, 4) == "<!--" ? (v = e2.indexOf("-->"), v != -1 ? (this.contentHandler.comment(e2.substring(4, v)), e2 = e2.substring(v + 3), g = false) : g = true) : e2.substring(0, 2) == "</" ? this.endTagRe.test(e2) ? (r = RegExp.lastMatch, o = RegExp.rightContext, r.replace(this.endTagRe, function() {
                  return d.parseEndTag.apply(d, arguments);
                }), e2 = o, g = false) : g = true : e2.charAt(0) == "<" && (this.startTagRe.test(e2) ? (r = RegExp.lastMatch, o = RegExp.rightContext, r.replace(this.startTagRe, function() {
                  return d.parseStartTag.apply(d, arguments);
                }), e2 = o, g = false) : g = true), g && (v = e2.indexOf("<"), v == -1 ? (this.contentHandler.characters(e2), e2 = "") : (this.contentHandler.characters(e2.substring(0, v)), e2 = e2.substring(v))), g = true;
            },
            parseStartTag: function(e2, a, r) {
              var o = this.parseAttributes(a, r);
              this.contentHandler.startElement(a, o);
            },
            parseEndTag: function(e2, a) {
              this.contentHandler.endElement(a);
            },
            parseAttributes: function(e2, a) {
              var r = this, o = [];
              return a.replace(this.attrRe, function(v, g, d, p, f, i, u, l) {
                o.push(r.parseAttribute(e2, v, g, d, p, f, i, u, l));
              }), o;
            },
            parseAttribute: function(e2, a, r) {
              var o = "";
              arguments[7] ? o = arguments[8] : arguments[5] ? o = arguments[6] : arguments[3] && (o = arguments[4]);
              var v = !o && !arguments[3];
              return { name: r, value: v ? null : o };
            }
          }, s.exports = t;
        },
        /* 298 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function r(o, v) {
            function g(d) {
              o.selection.createRangeByElem(d), o.selection.restoreSelection();
            }
            v.push(g);
          }
          n.default = r;
        },
        /* 299 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(6), g = o.__importDefault(t(3));
          function d(p) {
            var f = [], i = p.childNodes() || [];
            return (0, r.default)(i).call(i, function(u) {
              var l, m = u.nodeType;
              if (m === 3 && (l = u.textContent || "", l = v.replaceHtmlSymbol(l)), m === 1) {
                l = {}, l = l, l.tag = u.nodeName.toLowerCase();
                for (var c = [], h = u.attributes, A = h.length || 0, y = 0; y < A; y++) {
                  var x = h[y];
                  c.push({
                    name: x.name,
                    value: x.value
                  });
                }
                l.attrs = c, l.children = d(g.default(u));
              }
              l && f.push(l);
            }), f;
          }
          n.default = d;
        },
        /* 300 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(92)), r = e2(t(1)), o = e2(t(4));
          (0, r.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3));
          function d(p, f) {
            f === void 0 && (f = document.createElement("div"));
            var i = f;
            return (0, o.default)(p).call(p, function(u) {
              var l;
              if (typeof u == "string" && (l = document.createTextNode(u)), (0, a.default)(u) === "object") {
                var m;
                l = document.createElement(u.tag), (0, o.default)(m = u.attrs).call(m, function(c) {
                  g.default(l).attr(c.name, c.value);
                }), u.children && u.children.length > 0 && d(u.children, l.getRootNode());
              }
              l && i.appendChild(l);
            }), g.default(i);
          }
          n.default = d;
        },
        /* 301 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(89)), o = e2(t(70)), v = e2(t(28)), g = e2(t(302)), d = e2(t(4)), p = e2(t(94)), f = e2(t(133)), i = e2(t(46)), u = e2(t(57));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var l = t(2), m = l.__importDefault(t(87)), c = l.__importDefault(t(314)), h = l.__importDefault(t(3)), A = (
            /** @class */
            function() {
              function y(x) {
                this.editor = x, this.menuList = [], this.constructorList = c.default;
              }
              return y.prototype.extend = function(x, S) {
                !S || typeof S != "function" || (this.constructorList[x] = S);
              }, y.prototype.init = function() {
                var x, S, T = this, I = this.editor.config, C = I.excludeMenus;
                (0, r.default)(C) === false && (C = []), I.menus = (0, o.default)(x = I.menus).call(x, function(F) {
                  return (0, v.default)(C).call(C, F) === false;
                });
                var E = (0, g.default)(m.default.globalCustomMenuConstructorList);
                E = (0, o.default)(E).call(E, function(F) {
                  return (0, v.default)(C).call(C, F);
                }), (0, d.default)(E).call(E, function(F) {
                  delete m.default.globalCustomMenuConstructorList[F];
                }), (0, d.default)(S = I.menus).call(S, function(F) {
                  var O = T.constructorList[F];
                  T._initMenuList(F, O);
                });
                for (var D = 0, P = (0, p.default)(m.default.globalCustomMenuConstructorList); D < P.length; D++) {
                  var M = P[D], R = M[0], N = M[1], B = N;
                  this._initMenuList(R, B);
                }
                this._addToToolbar(), I.showMenuTooltips && this._bindMenuTooltips();
              }, y.prototype._initMenuList = function(x, S) {
                var T;
                if (!(S == null || typeof S != "function") && !(0, f.default)(T = this.menuList).call(T, function(C) {
                  return C.key === x;
                })) {
                  var I = new S(this.editor);
                  I.key = x, this.menuList.push(I);
                }
              }, y.prototype._bindMenuTooltips = function() {
                var x = this.editor, S = x.$toolbarElem, T = x.config, I = T.menuTooltipPosition, C = h.default('<div class="w-e-menu-tooltip w-e-menu-tooltip-' + I + `">
            <div class="w-e-menu-tooltip-item-wrapper">
              <div></div>
            </div>
          </div>`);
                C.css("visibility", "hidden"), S.append(C), C.css("z-index", x.zIndex.get("tooltip"));
                var E = 0;
                function D() {
                  E && clearTimeout(E);
                }
                function P() {
                  D(), C.css("visibility", "hidden");
                }
                S.on("mouseover", function(M) {
                  var R = M.target, N = h.default(R), B, F;
                  if (N.isContain(S)) {
                    P();
                    return;
                  }
                  if (N.parentUntil(".w-e-droplist") != null)
                    P();
                  else if (N.attr("data-title"))
                    B = N.attr("data-title"), F = N;
                  else {
                    var O = N.parentUntil(".w-e-menu");
                    O != null && (B = O.attr("data-title"), F = O);
                  }
                  if (B && F) {
                    D();
                    var H = F.getOffsetData();
                    C.text(x.i18next.t("menus.title." + B));
                    var L = C.getOffsetData(), U = H.left + H.width / 2 - L.width / 2;
                    C.css("left", U + "px"), I === "up" ? C.css("top", H.top - L.height - 8 + "px") : I === "down" && C.css("top", H.top + H.height + 8 + "px"), E = (0, i.default)(function() {
                      C.css("visibility", "visible");
                    }, 200);
                  } else
                    P();
                }).on("mouseleave", function() {
                  P();
                });
              }, y.prototype._addToToolbar = function() {
                var x, S = this.editor, T = S.$toolbarElem;
                (0, d.default)(x = this.menuList).call(x, function(I) {
                  var C = I.$elem;
                  C && T.append(C);
                });
              }, y.prototype.menuFind = function(x) {
                for (var S = this.menuList, T = 0, I = S.length; T < I; T++)
                  if (S[T].key === x)
                    return S[T];
                return S[0];
              }, y.prototype.changeActive = function() {
                var x;
                (0, d.default)(x = this.menuList).call(x, function(S) {
                  var T;
                  (0, i.default)((0, u.default)(T = S.tryChangeActive).call(T, S), 100);
                });
              }, y;
            }()
          );
          n.default = A;
        },
        /* 302 */
        /***/
        function(s, n, t) {
          s.exports = t(303);
        },
        /* 303 */
        /***/
        function(s, n, t) {
          var e2 = t(304);
          s.exports = e2;
        },
        /* 304 */
        /***/
        function(s, n, t) {
          t(305);
          var e2 = t(9);
          s.exports = e2.Object.keys;
        },
        /* 305 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(31), r = t(52), o = t(11), v = o(function() {
            r(1);
          });
          e2({ target: "Object", stat: true, forced: v }, {
            keys: function(d) {
              return r(a(d));
            }
          });
        },
        /* 306 */
        /***/
        function(s, n, t) {
          var e2 = t(307);
          s.exports = e2;
        },
        /* 307 */
        /***/
        function(s, n, t) {
          t(308);
          var e2 = t(9);
          s.exports = e2.Object.entries;
        },
        /* 308 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(309).entries;
          e2({ target: "Object", stat: true }, {
            entries: function(o) {
              return a(o);
            }
          });
        },
        /* 309 */
        /***/
        function(s, n, t) {
          var e2 = t(14), a = t(52), r = t(30), o = t(59).f, v = function(g) {
            return function(d) {
              for (var p = r(d), f = a(p), i = f.length, u = 0, l = [], m; i > u; )
                m = f[u++], (!e2 || o.call(p, m)) && l.push(g ? [m, p[m]] : p[m]);
              return l;
            };
          };
          s.exports = {
            // `Object.entries` method
            // https://tc39.github.io/ecma262/#sec-object.entries
            entries: v(true),
            // `Object.values` method
            // https://tc39.github.io/ecma262/#sec-object.values
            values: v(false)
          };
        },
        /* 310 */
        /***/
        function(s, n, t) {
          var e2 = t(311);
          s.exports = e2;
        },
        /* 311 */
        /***/
        function(s, n, t) {
          var e2 = t(312), a = Array.prototype;
          s.exports = function(r) {
            var o = r.some;
            return r === a || r instanceof Array && o === a.some ? e2 : o;
          };
        },
        /* 312 */
        /***/
        function(s, n, t) {
          t(313);
          var e2 = t(15);
          s.exports = e2("Array").some;
        },
        /* 313 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(32).some, r = t(67), o = t(22), v = r("some"), g = o("some");
          e2({ target: "Array", proto: true, forced: !v || !g }, {
            some: function(p) {
              return a(this, p, arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 314 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(315)), v = r.__importDefault(t(316)), g = r.__importDefault(t(321)), d = r.__importDefault(t(326)), p = r.__importDefault(t(327)), f = r.__importDefault(t(328)), i = r.__importDefault(t(329)), u = r.__importDefault(t(331)), l = r.__importDefault(t(333)), m = r.__importDefault(t(334)), c = r.__importDefault(t(337)), h = r.__importDefault(t(338)), A = r.__importDefault(t(339)), y = r.__importDefault(t(350)), x = r.__importDefault(t(365)), S = r.__importDefault(t(369)), T = r.__importDefault(t(137)), I = r.__importDefault(t(378)), C = r.__importDefault(t(380)), E = r.__importDefault(t(381)), D = r.__importDefault(t(382)), P = r.__importDefault(t(401)), M = r.__importDefault(t(406)), R = r.__importDefault(t(409));
          n.default = {
            bold: o.default,
            head: v.default,
            italic: d.default,
            link: g.default,
            underline: p.default,
            strikeThrough: f.default,
            fontName: i.default,
            fontSize: u.default,
            justify: l.default,
            quote: m.default,
            backColor: c.default,
            foreColor: h.default,
            video: A.default,
            image: y.default,
            indent: x.default,
            emoticon: S.default,
            list: T.default,
            lineHeight: I.default,
            undo: C.default,
            redo: E.default,
            table: D.default,
            code: P.default,
            splitLine: M.default,
            todo: R.default
          };
        },
        /* 315 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(23)), v = r.__importDefault(t(3)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = v.default(`<div class="w-e-menu" data-title="加粗">
                <i class="w-e-icon-bold"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor, i = f.selection.isSelectionEmpty();
                i && f.selection.createEmptyRange(), f.cmd.do("bold"), i && (f.selection.collapseRange(), f.selection.restoreSelection());
              }, p.prototype.tryChangeActive = function() {
                var f = this.editor;
                f.cmd.queryCommandState("bold") ? this.active() : this.unActive();
              }, p;
            }(o.default)
          );
          n.default = g;
        },
        /* 316 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(27)), o = e2(t(29)), v = e2(t(4)), g = e2(t(317)), d = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var p = t(2), f = p.__importDefault(t(24)), i = p.__importDefault(t(3)), u = t(6), l = t(7), m = (
            /** @class */
            function(c) {
              p.__extends(h, c);
              function h(A) {
                var y = this, x = i.default('<div class="w-e-menu" data-title="标题"><i class="w-e-icon-header"></i></div>'), S = {
                  width: 100,
                  title: "设置标题",
                  type: "list",
                  list: [{
                    $elem: i.default("<h1>H1</h1>"),
                    value: "<h1>"
                  }, {
                    $elem: i.default("<h2>H2</h2>"),
                    value: "<h2>"
                  }, {
                    $elem: i.default("<h3>H3</h3>"),
                    value: "<h3>"
                  }, {
                    $elem: i.default("<h4>H4</h4>"),
                    value: "<h4>"
                  }, {
                    $elem: i.default("<h5>H5</h5>"),
                    value: "<h5>"
                  }, {
                    $elem: i.default("<p>" + A.i18next.t("menus.dropListMenu.head.正文") + "</p>"),
                    value: "<p>"
                  }],
                  clickHandler: function(C) {
                    y.command(C);
                  }
                };
                y = c.call(this, x, A, S) || this;
                var T = A.config.onCatalogChange;
                return T && (y.oldCatalogs = [], y.addListenerCatalog(), y.getCatalogs()), y;
              }
              return h.prototype.command = function(A) {
                var y = this.editor, x = y.selection.getSelectionContainerElem();
                if (x && y.$textElem.equal(x))
                  this.setMultilineHead(A);
                else {
                  var S;
                  if ((0, r.default)(S = ["OL", "UL", "LI", "TABLE", "TH", "TR", "CODE", "HR"]).call(S, i.default(x).getNodeName()) > -1)
                    return;
                  y.cmd.do("formatBlock", A);
                }
                A !== "<p>" && this.addUidForSelectionElem();
              }, h.prototype.addUidForSelectionElem = function() {
                var A = this.editor, y = A.selection.getSelectionContainerElem(), x = u.getRandomCode();
                i.default(y).attr("id", x);
              }, h.prototype.addListenerCatalog = function() {
                var A = this, y = this.editor;
                y.txt.eventHooks.changeEvents.push(function() {
                  A.getCatalogs();
                });
              }, h.prototype.getCatalogs = function() {
                var A = this.editor, y = this.editor.$textElem, x = A.config.onCatalogChange, S = (0, o.default)(y).call(y, "h1,h2,h3,h4,h5"), T = [];
                (0, v.default)(S).call(S, function(I, C) {
                  var E = i.default(I), D = E.attr("id"), P = E.getNodeName(), M = E.text();
                  D || (D = u.getRandomCode(), E.attr("id", D)), M && T.push({
                    tag: P,
                    id: D,
                    text: M
                  });
                }), (0, g.default)(this.oldCatalogs) !== (0, g.default)(T) && (this.oldCatalogs = T, x && x(T));
              }, h.prototype.setMultilineHead = function(A) {
                var y = this, x, S, T = this.editor, I = T.selection, C = (x = I.getSelectionContainerElem()) === null || x === void 0 ? void 0 : x.elems[0], E = ["IMG", "VIDEO", "TABLE", "TH", "TR", "UL", "OL", "PRE", "HR", "BLOCKQUOTE"], D = i.default(I.getSelectionStartElem()), P = i.default(I.getSelectionEndElem());
                P.elems[0].outerHTML === i.default(l.EMPTY_P).elems[0].outerHTML && !P.elems[0].nextSibling && (P = P.prev());
                var M = [];
                M.push(D.getNodeTop(T));
                var R = [], N = (S = I.getRange()) === null || S === void 0 ? void 0 : S.commonAncestorContainer.childNodes;
                N == null || (0, v.default)(N).call(N, function(O, H) {
                  O === M[0].getNode() && R.push(H), O === P.getNodeTop(T).getNode() && R.push(H);
                });
                for (var B = 0; M[B].getNode() !== P.getNodeTop(T).getNode(); ) {
                  if (!M[B].elems[0])
                    return;
                  var F = i.default(M[B].next().getNode());
                  M.push(F), B++;
                }
                M == null || (0, v.default)(M).call(M, function(O, H) {
                  if (!y.hasTag(O, E)) {
                    var L = i.default(A), U = O.parent().getNode();
                    L.html("" + O.html()), U.insertBefore(L.getNode(), O.getNode()), O.remove();
                  }
                }), I.createRangeByElems(C.children[R[0]], C.children[R[1]]);
              }, h.prototype.hasTag = function(A, y) {
                var x = this, S;
                if (!A)
                  return false;
                if ((0, d.default)(y).call(y, A == null ? void 0 : A.getNodeName()))
                  return true;
                var T = false;
                return (S = A.children()) === null || S === void 0 || (0, v.default)(S).call(S, function(I) {
                  T = x.hasTag(i.default(I), y);
                }), T;
              }, h.prototype.tryChangeActive = function() {
                var A = this.editor, y = /^h/i, x = A.cmd.queryCommandValue("formatBlock");
                y.test(x) ? this.active() : this.unActive();
              }, h;
            }(f.default)
          );
          n.default = m;
        },
        /* 317 */
        /***/
        function(s, n, t) {
          s.exports = t(318);
        },
        /* 318 */
        /***/
        function(s, n, t) {
          var e2 = t(319);
          s.exports = e2;
        },
        /* 319 */
        /***/
        function(s, n, t) {
          t(320);
          var e2 = t(9);
          e2.JSON || (e2.JSON = { stringify: JSON.stringify }), s.exports = function(r, o, v) {
            return e2.JSON.stringify.apply(null, arguments);
          };
        },
        /* 320 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(36), r = t(11), o = a("JSON", "stringify"), v = /[\uD800-\uDFFF]/g, g = /^[\uD800-\uDBFF]$/, d = /^[\uDC00-\uDFFF]$/, p = function(i, u, l) {
            var m = l.charAt(u - 1), c = l.charAt(u + 1);
            return g.test(i) && !d.test(c) || d.test(i) && !g.test(m) ? "\\u" + i.charCodeAt(0).toString(16) : i;
          }, f = r(function() {
            return o("\uDF06\uD834") !== '"\\udf06\\ud834"' || o("\uDEAD") !== '"\\udead"';
          });
          o && e2({ target: "JSON", stat: true, forced: f }, {
            // eslint-disable-next-line no-unused-vars
            stringify: function(u, l, m) {
              var c = o.apply(null, arguments);
              return typeof c == "string" ? c.replace(v, p) : c;
            }
          });
        },
        /* 321 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(38)), g = o.__importDefault(t(3)), d = o.__importDefault(t(322)), p = o.__importStar(t(96)), f = o.__importDefault(t(33)), i = o.__importDefault(t(324)), u = t(7), l = (
            /** @class */
            function(m) {
              o.__extends(c, m);
              function c(h) {
                var A = this, y = g.default('<div class="w-e-menu" data-title="链接"><i class="w-e-icon-link"></i></div>');
                return A = m.call(this, y, h) || this, i.default(h), A;
              }
              return c.prototype.clickHandler = function() {
                var h = this.editor, A, y = h.selection.getSelectionContainerElem(), x = h.$textElem, S = x.html(), T = (0, r.default)(S).call(S);
                if (T === u.EMPTY_P) {
                  var I = x.children();
                  h.selection.createRangeByElem(I, true, true), y = h.selection.getSelectionContainerElem();
                }
                if (!(y && h.$textElem.equal(y)))
                  if (this.isActive) {
                    var C = "", E = "";
                    if (A = h.selection.getSelectionContainerElem(), !A)
                      return;
                    if (A.getNodeName() !== "A") {
                      var D = p.getParentNodeA(A);
                      A = g.default(D);
                    }
                    C = A.elems[0].innerText, E = A.attr("href"), this.createPanel(C, E);
                  } else
                    h.selection.isSelectionEmpty() ? this.createPanel("", "") : this.createPanel(h.selection.getSelectionText(), "");
              }, c.prototype.createPanel = function(h, A) {
                var y = d.default(this.editor, h, A), x = new f.default(this, y);
                x.create();
              }, c.prototype.tryChangeActive = function() {
                var h = this.editor;
                p.default(h) ? this.active() : this.unActive();
              }, c;
            }(v.default)
          );
          n.default = l;
        },
        /* 322 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(28)), o = e2(t(17)), v = e2(t(29));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var g = t(2), d = t(6), p = g.__importDefault(t(3)), f = g.__importStar(t(96)), i = t(323);
          function u(l, m, c) {
            var h = d.getRandom("input-link"), A = d.getRandom("input-text"), y = d.getRandom("btn-ok"), x = d.getRandom("btn-del"), S = f.default(l) ? "inline-block" : "none", T;
            function I() {
              if (f.default(l)) {
                var M = l.selection.getSelectionContainerElem();
                M && (l.selection.createRangeByElem(M), l.selection.restoreSelection(), T = M);
              }
            }
            function C(M, R) {
              var N = M.replace(/</g, "&lt;").replace(/>/g, "&gt;"), B = p.default('<a target="_blank">' + N + "</a>"), F = B.elems[0];
              F.innerText = M, F.href = R, f.default(l) && I(), l.cmd.do("insertElem", B);
            }
            function E() {
              if (f.default(l))
                if (I(), T.getNodeName() === "A") {
                  var M, R = T.elems[0], N = R.parentElement;
                  N && (0, r.default)(M = f.EXTRA_TAG).call(M, N.nodeName) ? N.innerHTML = R.innerHTML : l.cmd.do("insertHTML", "<span>" + R.innerHTML + "</span>");
                } else {
                  var B = f.getParentNodeA(T), F = B.innerHTML;
                  l.cmd.do("insertHTML", "<span>" + F + "</span>");
                }
            }
            function D(M, R) {
              var N = l.config.linkCheck(M, R);
              if (N !== void 0) {
                if (N === true)
                  return true;
                l.config.customAlert(N, "warning");
              }
              return false;
            }
            var P = {
              width: 300,
              height: 0,
              // 拼接字符串的：xss 攻击：
              //    如值为："><img src=1 onerror=alert(/xss/)>， 插入后：value=""><img src=1 onerror=alert(/xss/)>", 插入一个img元素
              // panel 中可包含多个 tab
              tabs: [{
                // tab 的标题
                title: l.i18next.t("menus.panelMenus.link.链接"),
                // 模板
                tpl: `<div>
                        <input
                            id="` + A + `"
                            type="text"
                            class="block"
                            placeholder="` + l.i18next.t("menus.panelMenus.link.链接文字") + `"/>
                        </td>
                        <input
                            id="` + h + `"
                            type="text"
                            class="block"
                            placeholder="` + l.i18next.t("如") + ` https://..."/>
                        </td>
                        <div class="w-e-button-container">
                            <button type="button" id="` + y + `" class="right">
                                ` + l.i18next.t("插入") + `
                            </button>
                            <button type="button" id="` + x + '" class="gray right" style="display:' + S + `">
                                ` + l.i18next.t("menus.panelMenus.link.取消链接") + `
                            </button>
                        </div>
                    </div>`,
                // 事件绑定
                events: [
                  // 插入链接
                  {
                    selector: "#" + y,
                    type: "click",
                    fn: function() {
                      var R, N, B, F, O, H = l.selection.getSelectionContainerElem(), L = H == null ? void 0 : H.elems[0];
                      l.selection.restoreSelection();
                      var U = l.selection.getSelectionRangeTopNodes()[0].getNode(), z = window.getSelection(), j = p.default("#" + h), K = p.default("#" + A), V = (0, o.default)(R = j.val()).call(R), Q = (0, o.default)(N = K.val()).call(N), w = "";
                      z && !(z != null && z.isCollapsed) && (w = (F = i.insertHtml(z, U)) === null || F === void 0 ? void 0 : (0, o.default)(F).call(F));
                      var G = w == null ? void 0 : w.replace(/<.*?>/g, ""), X = (O = G == null ? void 0 : G.length) !== null && O !== void 0 ? O : 0;
                      if (X <= Q.length) {
                        var tt = Q.substring(0, X), nt = Q.substring(X);
                        G === tt && (Q = G + nt);
                      }
                      if (V && (Q || (Q = V), !!D(Q, V))) {
                        if ((L == null ? void 0 : L.nodeName) === "A")
                          return L.setAttribute("href", V), L.innerText = Q, true;
                        if ((L == null ? void 0 : L.nodeName) !== "A" && (0, r.default)(B = f.EXTRA_TAG).call(B, L.nodeName)) {
                          var st = f.getParentNodeA(H);
                          if (st)
                            return st.setAttribute("href", V), L.innerText = Q, true;
                        }
                        return C(Q, V), true;
                      }
                    },
                    bindEnter: true
                  },
                  // 取消链接
                  {
                    selector: "#" + x,
                    type: "click",
                    fn: function() {
                      return E(), true;
                    }
                  }
                ]
              }],
              /**
               * 设置input的值，分别为文案和链接地址设置值
               *
               * 利用dom 设置链接文案的值，防止回填拼接引号问题, 出现xss攻击
               *
               * @param $container 对应上面生成的dom容器
               * @param type text | link
               */
              setLinkValue: function(R, N) {
                var B = "", F = "", O;
                N === "text" && (B = "#" + A, F = m), N === "link" && (B = "#" + h, F = c), O = (0, v.default)(R).call(R, B).elems[0], O.value = F;
              }
            };
            return P;
          }
          n.default = u;
        },
        /* 323 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.insertHtml = n.createPartHtml = n.makeHtmlString = n.getTopNode = void 0;
          function o(i, u) {
            var l = i, m = i;
            do {
              if (l.textContent === u)
                break;
              m = l, l.parentNode && (l = l == null ? void 0 : l.parentNode);
            } while ((l == null ? void 0 : l.nodeName) !== "P");
            return m;
          }
          n.getTopNode = o;
          function v(i, u) {
            var l = i.nodeName, m = "";
            if (i.nodeType === 3 || /^(h|H)[1-6]$/.test(l))
              return u;
            if (i.nodeType === 1) {
              var c = i.getAttribute("style"), h = i.getAttribute("face"), A = i.getAttribute("color");
              c && (m = m + (' style="' + c + '"')), h && (m = m + (' face="' + h + '"')), A && (m = m + (' color="' + A + '"'));
            }
            return l = l.toLowerCase(), "<" + l + m + ">" + u + "</" + l + ">";
          }
          n.makeHtmlString = v;
          function g(i, u, l, m) {
            var c, h = (c = u.textContent) === null || c === void 0 ? void 0 : c.substring(l, m), A = u, y = "";
            do
              y = v(A, h ?? ""), h = y, A = A == null ? void 0 : A.parentElement;
            while (A && A.textContent !== i);
            return y;
          }
          n.createPartHtml = g;
          function d(i, u) {
            var l, m, c, h, A, y = i.anchorNode, x = i.focusNode, S = i.anchorOffset, T = i.focusOffset, I = (l = u.textContent) !== null && l !== void 0 ? l : "", C = p(u), E = "", D = "", P = "", M = "", R = y, N = x, B = y;
            if (y != null && y.isEqualNode(x ?? null)) {
              var F = g(I, y, S, T);
              return F = f(C, F), F;
            }
            for (y && (D = g(I, y, S ?? 0)), x && (M = g(I, x, 0, T)), y && (R = o(y, I)), x && (N = o(x, I)), B = (m = R == null ? void 0 : R.nextSibling) !== null && m !== void 0 ? m : y; !(B != null && B.isEqualNode(N ?? null)); ) {
              var O = B == null ? void 0 : B.nodeName;
              if (O === "#text")
                P = P + (B == null ? void 0 : B.textContent);
              else {
                var H = (h = (c = B == null ? void 0 : B.firstChild) === null || c === void 0 ? void 0 : c.parentElement) === null || h === void 0 ? void 0 : h.innerHTML;
                B && (P = P + v(B, H ?? ""));
              }
              var L = (A = B == null ? void 0 : B.nextSibling) !== null && A !== void 0 ? A : B;
              if (L === B)
                break;
              B = L;
            }
            return E = "" + D + P + M, E = f(C, E), E;
          }
          n.insertHtml = d;
          function p(i) {
            for (var u, l = (u = i.textContent) !== null && u !== void 0 ? u : "", m = []; (i == null ? void 0 : i.textContent) === l; )
              i.nodeName !== "P" && i.nodeName !== "TABLE" && m.push(i), i = i.childNodes[0];
            return m;
          }
          function f(i, u) {
            return (0, r.default)(i).call(i, function(l) {
              u = v(l, u);
            }), u;
          }
        },
        /* 324 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(325));
          function v(g) {
            o.default(g);
          }
          n.default = v;
        },
        /* 325 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = o.__importDefault(t(39)), d = t(96);
          function p(i) {
            var u;
            function l(c) {
              var h = [{
                $elem: v.default("<span>" + i.i18next.t("menus.panelMenus.link.查看链接") + "</span>"),
                onClick: function(y, x) {
                  var S = x.attr("href");
                  return window.open(S, "_target"), true;
                }
              }, {
                $elem: v.default("<span>" + i.i18next.t("menus.panelMenus.link.取消链接") + "</span>"),
                onClick: function(y, x) {
                  var S, T;
                  y.selection.createRangeByElem(x), y.selection.restoreSelection();
                  var I = x.childNodes();
                  if ((I == null ? void 0 : I.getNodeName()) === "IMG") {
                    var C = (T = (S = y.selection.getSelectionContainerElem()) === null || S === void 0 ? void 0 : S.children()) === null || T === void 0 ? void 0 : T.elems[0].children[0];
                    y.cmd.do("insertHTML", `<img 
                                src=` + (C == null ? void 0 : C.getAttribute("src")) + ` 
                                style=` + (C == null ? void 0 : C.getAttribute("style")) + ">");
                  } else {
                    var E, D = x.elems[0], P = D.innerHTML, M = D.parentElement;
                    M && (0, r.default)(E = d.EXTRA_TAG).call(E, M.nodeName) ? M.innerHTML = P : y.cmd.do("insertHTML", "<span>" + P + "</span>");
                  }
                  return true;
                }
              }];
              u = new g.default(i, c, h), u.create();
            }
            function m() {
              u && (u.remove(), u = null);
            }
            return {
              showLinkTooltip: l,
              hideLinkTooltip: m
            };
          }
          function f(i) {
            var u = p(i), l = u.showLinkTooltip, m = u.hideLinkTooltip;
            i.txt.eventHooks.linkClickEvents.push(l), i.txt.eventHooks.clickEvents.push(m), i.txt.eventHooks.keyupEvents.push(m), i.txt.eventHooks.toolbarClickEvents.push(m), i.txt.eventHooks.menuClickEvents.push(m), i.txt.eventHooks.textScrollEvents.push(m);
          }
          n.default = f;
        },
        /* 326 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(23)), v = r.__importDefault(t(3)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = v.default(`<div class="w-e-menu" data-title="斜体">
                <i class="w-e-icon-italic"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor, i = f.selection.isSelectionEmpty();
                i && f.selection.createEmptyRange(), f.cmd.do("italic"), i && (f.selection.collapseRange(), f.selection.restoreSelection());
              }, p.prototype.tryChangeActive = function() {
                var f = this.editor;
                f.cmd.queryCommandState("italic") ? this.active() : this.unActive();
              }, p;
            }(o.default)
          );
          n.default = g;
        },
        /* 327 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(23)), v = r.__importDefault(t(3)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = v.default(`<div class="w-e-menu" data-title="下划线">
                <i class="w-e-icon-underline"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor, i = f.selection.isSelectionEmpty();
                i && f.selection.createEmptyRange(), f.cmd.do("underline"), i && (f.selection.collapseRange(), f.selection.restoreSelection());
              }, p.prototype.tryChangeActive = function() {
                var f = this.editor;
                f.cmd.queryCommandState("underline") ? this.active() : this.unActive();
              }, p;
            }(o.default)
          );
          n.default = g;
        },
        /* 328 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(23)), v = r.__importDefault(t(3)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = v.default(`<div class="w-e-menu" data-title="删除线">
                <i class="w-e-icon-strikethrough"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor, i = f.selection.isSelectionEmpty();
                i && f.selection.createEmptyRange(), f.cmd.do("strikeThrough"), i && (f.selection.collapseRange(), f.selection.restoreSelection());
              }, p.prototype.tryChangeActive = function() {
                var f = this.editor;
                f.cmd.queryCommandState("strikeThrough") ? this.active() : this.unActive();
              }, p;
            }(o.default)
          );
          n.default = g;
        },
        /* 329 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(24)), v = r.__importDefault(t(3)), g = r.__importDefault(t(330)), d = (
            /** @class */
            function(p) {
              r.__extends(f, p);
              function f(i) {
                var u = this, l = v.default(`<div class="w-e-menu" data-title="字体">
                <i class="w-e-icon-font"></i>
            </div>`), m = new g.default(i.config.fontNames), c = {
                  width: 100,
                  title: "设置字体",
                  type: "list",
                  list: m.getItemList(),
                  clickHandler: function(A) {
                    u.command(A);
                  }
                };
                return u = p.call(this, l, i, c) || this, u;
              }
              return f.prototype.command = function(i) {
                var u, l = this.editor, m = l.selection.isSelectionEmpty(), c = (u = l.selection.getSelectionContainerElem()) === null || u === void 0 ? void 0 : u.elems[0];
                if (c != null) {
                  var h = (c == null ? void 0 : c.nodeName.toLowerCase()) !== "p", A = (c == null ? void 0 : c.getAttribute("face")) === i;
                  if (m) {
                    if (h && !A) {
                      var y = l.selection.getSelectionRangeTopNodes();
                      l.selection.createRangeByElem(y[0]), l.selection.moveCursor(y[0].elems[0]);
                    }
                    l.selection.setRangeToElem(c), l.selection.createEmptyRange();
                  }
                  l.cmd.do("fontName", i), m && (l.selection.collapseRange(), l.selection.restoreSelection());
                }
              }, f.prototype.tryChangeActive = function() {
              }, f;
            }(o.default)
          );
          n.default = d;
        },
        /* 330 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = (
            /** @class */
            function() {
              function d(p) {
                var f = this;
                this.itemList = [], (0, r.default)(p).call(p, function(i) {
                  var u = typeof i == "string" ? i : i.value, l = typeof i == "string" ? i : i.name;
                  f.itemList.push({
                    $elem: v.default(`<p style="font-family:'` + u + `'">` + l + "</p>"),
                    value: l
                  });
                });
              }
              return d.prototype.getItemList = function() {
                return this.itemList;
              }, d;
            }()
          );
          n.default = g;
        },
        /* 331 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(24)), v = r.__importDefault(t(3)), g = r.__importDefault(t(332)), d = (
            /** @class */
            function(p) {
              r.__extends(f, p);
              function f(i) {
                var u = this, l = v.default(`<div class="w-e-menu" data-title="字号">
                <i class="w-e-icon-text-heigh"></i>
            </div>`), m = new g.default(i.config.fontSizes), c = {
                  width: 160,
                  title: "设置字号",
                  type: "list",
                  list: m.getItemList(),
                  clickHandler: function(A) {
                    u.command(A);
                  }
                };
                return u = p.call(this, l, i, c) || this, u;
              }
              return f.prototype.command = function(i) {
                var u, l = this.editor, m = l.selection.isSelectionEmpty(), c = (u = l.selection.getSelectionContainerElem()) === null || u === void 0 ? void 0 : u.elems[0];
                c != null && (l.cmd.do("fontSize", i), m && (l.selection.collapseRange(), l.selection.restoreSelection()));
              }, f.prototype.tryChangeActive = function() {
              }, f;
            }(o.default)
          );
          n.default = d;
        },
        /* 332 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = (
            /** @class */
            function() {
              function g(d) {
                this.itemList = [];
                for (var p in d) {
                  var f = d[p];
                  this.itemList.push({
                    $elem: o.default('<p style="font-size:' + p + '">' + f.name + "</p>"),
                    value: f.value
                  });
                }
              }
              return g.prototype.getItemList = function() {
                return this.itemList;
              }, g;
            }()
          );
          n.default = v;
        },
        /* 333 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(24)), d = v.__importDefault(t(3)), p = ["LI"], f = ["BLOCKQUOTE"], i = (
            /** @class */
            function(u) {
              v.__extends(l, u);
              function l(m) {
                var c = this, h = d.default('<div class="w-e-menu" data-title="对齐"><i class="w-e-icon-paragraph-left"></i></div>'), A = {
                  width: 100,
                  title: "对齐方式",
                  type: "list",
                  list: [{
                    $elem: d.default(`<p>
                            <i class="w-e-icon-paragraph-left w-e-drop-list-item"></i>
                            ` + m.i18next.t("menus.dropListMenu.justify.靠左") + `
                        </p>`),
                    value: "left"
                  }, {
                    $elem: d.default(`<p>
                            <i class="w-e-icon-paragraph-center w-e-drop-list-item"></i>
                            ` + m.i18next.t("menus.dropListMenu.justify.居中") + `
                        </p>`),
                    value: "center"
                  }, {
                    $elem: d.default(`<p>
                            <i class="w-e-icon-paragraph-right w-e-drop-list-item"></i>
                            ` + m.i18next.t("menus.dropListMenu.justify.靠右") + `
                        </p>`),
                    value: "right"
                  }, {
                    $elem: d.default(`<p>
                            <i class="w-e-icon-paragraph-justify w-e-drop-list-item"></i>
                            ` + m.i18next.t("menus.dropListMenu.justify.两端") + `
                        </p>`),
                    value: "justify"
                  }],
                  clickHandler: function(x) {
                    c.command(x);
                  }
                };
                return c = u.call(this, h, m, A) || this, c;
              }
              return l.prototype.command = function(m) {
                var c = this.editor, h = c.selection, A = h.getSelectionContainerElem();
                h.saveRange();
                var y = c.selection.getSelectionRangeTopNodes();
                if (A != null && A.length)
                  if (this.isSpecialNode(A, y[0]) || this.isSpecialTopNode(y[0])) {
                    var x = this.getSpecialNodeUntilTop(A, y[0]);
                    if (x == null)
                      return;
                    d.default(x).css("text-align", m);
                  } else
                    (0, r.default)(y).call(y, function(S) {
                      S.css("text-align", m);
                    });
                h.restoreSelection();
              }, l.prototype.getSpecialNodeUntilTop = function(m, c) {
                for (var h = m.elems[0], A = c.elems[0]; h != null; ) {
                  if ((0, o.default)(p).call(p, h == null ? void 0 : h.nodeName) !== -1 || h.parentNode === A)
                    return h;
                  h = h.parentNode;
                }
                return h;
              }, l.prototype.isSpecialNode = function(m, c) {
                var h = this.getSpecialNodeUntilTop(m, c);
                return h == null ? false : (0, o.default)(p).call(p, h.nodeName) !== -1;
              }, l.prototype.isSpecialTopNode = function(m) {
                var c;
                return m == null ? false : (0, o.default)(f).call(f, (c = m.elems[0]) === null || c === void 0 ? void 0 : c.nodeName) !== -1;
              }, l.prototype.tryChangeActive = function() {
              }, l;
            }(g.default)
          );
          n.default = i;
        },
        /* 334 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = o.__importDefault(t(23)), d = o.__importDefault(t(335)), p = o.__importDefault(t(336)), f = t(7), i = (
            /** @class */
            function(u) {
              o.__extends(l, u);
              function l(m) {
                var c = this, h = v.default(`<div class="w-e-menu" data-title="引用">
                <i class="w-e-icon-quotes-left"></i>
            </div>`);
                return c = u.call(this, h, m) || this, d.default(m), c;
              }
              return l.prototype.clickHandler = function() {
                var m, c, h = this.editor, A = h.selection.isSelectionEmpty(), y = h.selection.getSelectionRangeTopNodes(), x = y[y.length - 1], S = this.getTopNodeName();
                if (S === "BLOCKQUOTE") {
                  var T = v.default(x.childNodes()), I = T.length, C = x;
                  (0, r.default)(T).call(T, function(M) {
                    var R = v.default(M);
                    R.insertAfter(C), C = R;
                  }), x.remove(), h.selection.moveCursor(T.elems[I - 1]), this.tryChangeActive();
                } else {
                  var E = p.default(y);
                  if (h.$textElem.equal(x)) {
                    var D = (m = h.selection.getSelectionContainerElem()) === null || m === void 0 ? void 0 : m.elems[0];
                    h.selection.createRangeByElems(D.children[0], D.children[0]), y = h.selection.getSelectionRangeTopNodes(), E = p.default(y), x.append(E);
                  } else
                    E.insertAfter(x);
                  this.delSelectNode(y);
                  var P = (c = E.childNodes()) === null || c === void 0 ? void 0 : c.last().getNode();
                  if (P == null)
                    return;
                  P.textContent ? h.selection.moveCursor(P) : h.selection.moveCursor(P, 0), this.tryChangeActive(), v.default(f.EMPTY_P).insertAfter(E);
                  return;
                }
                A && (h.selection.collapseRange(), h.selection.restoreSelection());
              }, l.prototype.tryChangeActive = function() {
                var m, c = this.editor, h = (m = c.selection.getSelectionRangeTopNodes()[0]) === null || m === void 0 ? void 0 : m.getNodeName();
                h === "BLOCKQUOTE" ? this.active() : this.unActive();
              }, l.prototype.getTopNodeName = function() {
                var m = this.editor, c = m.selection.getSelectionRangeTopNodes()[0], h = c == null ? void 0 : c.getNodeName();
                return h;
              }, l.prototype.delSelectNode = function(m) {
                (0, r.default)(m).call(m, function(c) {
                  c.remove();
                });
              }, l;
            }(g.default)
          );
          n.default = i;
        },
        /* 335 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = t(7), v = r.__importDefault(t(3));
          function g(d) {
            function p(f) {
              var i, u = d.selection.getSelectionContainerElem(), l = d.selection.getSelectionRangeTopNodes()[0];
              if ((l == null ? void 0 : l.getNodeName()) === "BLOCKQUOTE") {
                if (u.getNodeName() === "BLOCKQUOTE") {
                  var m = (i = u.childNodes()) === null || i === void 0 ? void 0 : i.getNode();
                  d.selection.moveCursor(m);
                }
                if (u.text() === "") {
                  f.preventDefault(), u.remove();
                  var c = v.default(o.EMPTY_P);
                  c.insertAfter(l), d.selection.moveCursor(c.getNode(), 0);
                }
                l.text() === "" && l.remove();
              }
            }
            d.txt.eventHooks.enterDownEvents.push(p);
          }
          n.default = g;
        },
        /* 336 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3));
          function g(d) {
            var p = v.default("<blockquote></blockquote>");
            return (0, r.default)(d).call(d, function(f) {
              p.append(f.clone(true));
            }), p;
          }
          n.default = g;
        },
        /* 337 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(24)), g = o.__importDefault(t(3)), d = t(6), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                var l, m = this, c = g.default(`<div class="w-e-menu" data-title="背景色">
                <i class="w-e-icon-paint-brush"></i>
            </div>`), h = {
                  width: 120,
                  title: "背景颜色",
                  // droplist 内容以 block 形式展示
                  type: "inline-block",
                  list: (0, r.default)(l = u.config.colors).call(l, function(A) {
                    return {
                      $elem: g.default('<i style="color:' + A + ';" class="w-e-icon-paint-brush"></i>'),
                      value: A
                    };
                  }),
                  clickHandler: function(y) {
                    m.command(y);
                  }
                };
                return m = f.call(this, c, u, h) || this, m;
              }
              return i.prototype.command = function(u) {
                var l, m = this.editor, c = m.selection.isSelectionEmpty(), h = (l = m.selection.getSelectionContainerElem()) === null || l === void 0 ? void 0 : l.elems[0];
                if (h != null) {
                  var A = (h == null ? void 0 : h.nodeName.toLowerCase()) !== "p", y = h == null ? void 0 : h.style.backgroundColor, x = d.hexToRgb(u) === y;
                  if (c) {
                    if (A && !x) {
                      var S = m.selection.getSelectionRangeTopNodes();
                      m.selection.createRangeByElem(S[0]), m.selection.moveCursor(S[0].elems[0]);
                    }
                    m.selection.createEmptyRange();
                  }
                  m.cmd.do("backColor", u), c && (m.selection.collapseRange(), m.selection.restoreSelection());
                }
              }, i.prototype.tryChangeActive = function() {
              }, i;
            }(v.default)
          );
          n.default = p;
        },
        /* 338 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(24)), g = o.__importDefault(t(3)), d = (
            /** @class */
            function(p) {
              o.__extends(f, p);
              function f(i) {
                var u, l = this, m = g.default(`<div class="w-e-menu" data-title="文字颜色">
                <i class="w-e-icon-pencil2"></i>
            </div>`), c = {
                  width: 120,
                  title: "文字颜色",
                  // droplist 内容以 block 形式展示
                  type: "inline-block",
                  list: (0, r.default)(u = i.config.colors).call(u, function(h) {
                    return {
                      $elem: g.default('<i style="color:' + h + ';" class="w-e-icon-pencil2"></i>'),
                      value: h
                    };
                  }),
                  clickHandler: function(A) {
                    l.command(A);
                  }
                };
                return l = p.call(this, m, i, c) || this, l;
              }
              return f.prototype.command = function(i) {
                var u, l = this.editor, m = l.selection.isSelectionEmpty(), c = (u = l.selection.getSelectionContainerElem()) === null || u === void 0 ? void 0 : u.elems[0];
                if (c != null) {
                  var h = l.selection.getSelectionText();
                  if (c.nodeName === "A" && c.textContent === h) {
                    var A = g.default("<span>&#8203;</span>").getNode();
                    c.appendChild(A);
                  }
                  l.cmd.do("foreColor", i), m && (l.selection.collapseRange(), l.selection.restoreSelection());
                }
              }, f.prototype.tryChangeActive = function() {
              }, f;
            }(v.default)
          );
          n.default = d;
        },
        /* 339 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(33)), g = r.__importDefault(t(38)), d = r.__importDefault(t(340)), p = r.__importDefault(t(346)), f = (
            /** @class */
            function(i) {
              r.__extends(u, i);
              function u(l) {
                var m = this, c = o.default(`<div class="w-e-menu" data-title="视频">
                <i class="w-e-icon-play"></i>
            </div>`);
                return m = i.call(this, c, l) || this, p.default(l), m;
              }
              return u.prototype.clickHandler = function() {
                this.createPanel("");
              }, u.prototype.createPanel = function(l) {
                var m = d.default(this.editor, l), c = new v.default(this, m);
                c.create();
              }, u.prototype.tryChangeActive = function() {
              }, u;
            }(g.default)
          );
          n.default = f;
        },
        /* 340 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(6), g = o.__importDefault(t(3)), d = o.__importDefault(t(341)), p = t(7);
          function f(i, u) {
            var l = i.config, m = new d.default(i), c = v.getRandom("input-iframe"), h = v.getRandom("btn-ok"), A = v.getRandom("input-upload"), y = v.getRandom("btn-local-ok");
            function x(C) {
              i.cmd.do("insertHTML", C + p.EMPTY_P), i.config.onlineVideoCallback(C);
            }
            function S(C) {
              var E = i.config.onlineVideoCheck(C);
              return E === true ? true : (typeof E == "string" && i.config.customAlert(E, "error"), false);
            }
            var T = [{
              // tab 的标题
              title: i.i18next.t("menus.panelMenus.video.上传视频"),
              tpl: `<div class="w-e-up-video-container">
                    <div id="` + y + `" class="w-e-up-btn">
                        <i class="w-e-icon-upload2"></i>
                    </div>
                    <div style="display:none;">
                        <input id="` + A + `" type="file" accept="video/*"/>
                    </div>
                 </div>`,
              events: [
                // 触发选择视频
                {
                  selector: "#" + y,
                  type: "click",
                  fn: function() {
                    var E = g.default("#" + A), D = E.elems[0];
                    if (D)
                      D.click();
                    else
                      return true;
                  }
                },
                // 选择视频完毕
                {
                  selector: "#" + A,
                  type: "change",
                  fn: function() {
                    var E = g.default("#" + A), D = E.elems[0];
                    if (!D)
                      return true;
                    var P = D.files;
                    return P.length && m.uploadVideo(P), true;
                  }
                }
              ]
            }, {
              // tab 的标题
              title: i.i18next.t("menus.panelMenus.video.插入视频"),
              // 模板
              tpl: `<div>
                    <input 
                        id="` + c + `" 
                        type="text" 
                        class="block" 
                        placeholder="` + i.i18next.t("如") + `：<iframe src=... ></iframe>"/>
                    </td>
                    <div class="w-e-button-container">
                        <button type="button" id="` + h + `" class="right">
                            ` + i.i18next.t("插入") + `
                        </button>
                    </div>
                </div>`,
              // 事件绑定
              events: [
                // 插入视频
                {
                  selector: "#" + h,
                  type: "click",
                  fn: function() {
                    var E, D = g.default("#" + c), P = (0, r.default)(E = D.val()).call(E);
                    if (P && S(P))
                      return x(P), true;
                  },
                  bindEnter: true
                }
              ]
            }], I = {
              width: 300,
              height: 0,
              // panel 中可包含多个 tab
              tabs: []
            };
            return window.FileReader && (l.uploadVideoServer || l.customUploadVideo) && I.tabs.push(T[0]), l.showLinkVideo && I.tabs.push(T[1]), I;
          }
          n.default = f;
        },
        /* 341 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(133)), o = e2(t(57)), v = e2(t(4)), g = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var d = t(2), p = t(6), f = d.__importDefault(t(135)), i = d.__importDefault(t(136)), u = t(7), l = t(6), m = (
            /** @class */
            function() {
              function c(h) {
                this.editor = h;
              }
              return c.prototype.uploadVideo = function(h) {
                var A = this;
                if (h.length) {
                  var y = this.editor, x = y.config, S = "validate.", T = function(G) {
                    return y.i18next.t(S + G);
                  }, I = x.uploadVideoServer, C = x.uploadVideoMaxSize, E = C / 1024, D = x.uploadVideoName, P = x.uploadVideoParams, M = x.uploadVideoParamsWithUrl, R = x.uploadVideoHeaders, N = x.uploadVideoHooks, B = x.uploadVideoTimeout, F = x.withVideoCredentials, O = x.customUploadVideo, H = x.uploadVideoAccept, L = [], U = [];
                  if (p.arrForEach(h, function(w) {
                    var G = w.name, X = w.size / 1024 / 1024;
                    if (!(!G || !X)) {
                      if (!(H instanceof Array)) {
                        U.push("【" + H + "】" + T("uploadVideoAccept 不是Array"));
                        return;
                      }
                      if (!(0, r.default)(H).call(H, function(tt) {
                        return tt === G.split(".")[G.split(".").length - 1];
                      })) {
                        U.push("【" + G + "】" + T("不是视频"));
                        return;
                      }
                      if (E < X) {
                        U.push("【" + G + "】" + T("大于") + " " + E + "M");
                        return;
                      }
                      L.push(w);
                    }
                  }), U.length) {
                    x.customAlert(T("视频验证未通过") + `: 
` + U.join(`
`), "warning");
                    return;
                  }
                  if (L.length === 0) {
                    x.customAlert(T("传入的文件不合法"), "warning");
                    return;
                  }
                  if (O && typeof O == "function") {
                    var z;
                    O(L, (0, o.default)(z = this.insertVideo).call(z, this));
                    return;
                  }
                  var j = new FormData();
                  if ((0, v.default)(L).call(L, function(w, G) {
                    var X = D || w.name;
                    L.length > 1 && (X = X + (G + 1)), j.append(X, w);
                  }), I) {
                    var K = I.split("#");
                    I = K[0];
                    var V = K[1] || "";
                    (0, v.default)(p).call(p, P, function(w, G) {
                      M && ((0, g.default)(I).call(I, "?") > 0 ? I += "&" : I += "?", I = I + w + "=" + G), j.append(w, G);
                    }), V && (I += "#" + V);
                    var Q = f.default(I, {
                      timeout: B,
                      formData: j,
                      headers: R,
                      withCredentials: !!F,
                      beforeSend: function(G) {
                        if (N.before)
                          return N.before(G, y, L);
                      },
                      onTimeout: function(G) {
                        x.customAlert(T("上传视频超时"), "error"), N.timeout && N.timeout(G, y);
                      },
                      onProgress: function(G, X) {
                        var tt = new i.default(y);
                        X.lengthComputable && (G = X.loaded / X.total, tt.show(G));
                      },
                      onError: function(G) {
                        x.customAlert(T("上传视频错误"), "error", T("上传视频错误") + "，" + T("服务器返回状态") + ": " + G.status), N.error && N.error(G, y);
                      },
                      onFail: function(G, X) {
                        x.customAlert(T("上传视频失败"), "error", T("上传视频返回结果错误") + ("，" + T("返回结果") + ": ") + X), N.fail && N.fail(G, y, X);
                      },
                      onSuccess: function(G, X) {
                        if (N.customInsert) {
                          var tt;
                          N.customInsert((0, o.default)(tt = A.insertVideo).call(tt, A), X, y);
                          return;
                        }
                        if (X.errno != "0") {
                          x.customAlert(T("上传视频失败"), "error", T("上传视频返回结果错误") + "，" + T("返回结果") + " errno=" + X.errno), N.fail && N.fail(G, y, X);
                          return;
                        }
                        var nt = X.data;
                        A.insertVideo(nt.url), N.success && N.success(G, y, X);
                      }
                    });
                    typeof Q == "string" && x.customAlert(Q, "error");
                  }
                }
              }, c.prototype.insertVideo = function(h) {
                var A = this.editor, y = A.config, x = "validate.", S = function(C, E) {
                  return E === void 0 && (E = x), A.i18next.t(E + C);
                };
                if (!y.customInsertVideo)
                  l.UA.isFirefox ? A.cmd.do("insertHTML", '<p data-we-video-p="true"><video src="' + h + '" controls="controls" style="max-width:100%"></video></p><p>&#8203</p>') : A.cmd.do("insertHTML", '<video src="' + h + '" controls="controls" style="max-width:100%"></video>' + u.EMPTY_P);
                else {
                  y.customInsertVideo(h);
                  return;
                }
                var T = document.createElement("video");
                T.onload = function() {
                  T = null;
                }, T.onerror = function() {
                  y.customAlert(S("插入视频错误"), "error", "wangEditor: " + S("插入视频错误") + "，" + S("视频链接") + ' "' + h + '"，' + S("下载链接失败")), T = null;
                }, T.onabort = function() {
                  return T = null;
                }, T.src = h;
              }, c;
            }()
          );
          n.default = m;
        },
        /* 342 */
        /***/
        function(s, n, t) {
          s.exports = t(343);
        },
        /* 343 */
        /***/
        function(s, n, t) {
          var e2 = t(344);
          s.exports = e2;
        },
        /* 344 */
        /***/
        function(s, n, t) {
          t(345);
          var e2 = t(9);
          s.exports = e2.Date.now;
        },
        /* 345 */
        /***/
        function(s, n, t) {
          var e2 = t(5);
          e2({ target: "Date", stat: true }, {
            now: function() {
              return (/* @__PURE__ */ new Date()).getTime();
            }
          });
        },
        /* 346 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(347)), v = r.__importDefault(t(349));
          function g(d) {
            o.default(d), v.default(d);
          }
          n.default = g;
        },
        /* 347 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createShowHideFn = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(39)), g = r.__importDefault(t(348));
          function d(f) {
            var i, u = function(h, A) {
              return A === void 0 && (A = ""), f.i18next.t(A + h);
            };
            function l(c) {
              var h = [{
                $elem: o.default("<span class='w-e-icon-trash-o'></span>"),
                onClick: function(y, x) {
                  return x.remove(), true;
                }
              }, {
                $elem: o.default("<span>100%</span>"),
                onClick: function(y, x) {
                  return x.attr("width", "100%"), x.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>50%</span>"),
                onClick: function(y, x) {
                  return x.attr("width", "50%"), x.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>30%</span>"),
                onClick: function(y, x) {
                  return x.attr("width", "30%"), x.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>" + u("重置") + "</span>"),
                onClick: function(y, x) {
                  return x.removeAttr("width"), x.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>" + u("menus.justify.靠左") + "</span>"),
                onClick: function(y, x) {
                  return g.default(x, "left"), true;
                }
              }, {
                $elem: o.default("<span>" + u("menus.justify.居中") + "</span>"),
                onClick: function(y, x) {
                  return g.default(x, "center"), true;
                }
              }, {
                $elem: o.default("<span>" + u("menus.justify.靠右") + "</span>"),
                onClick: function(y, x) {
                  return g.default(x, "right"), true;
                }
              }];
              i = new v.default(f, c, h), i.create();
            }
            function m() {
              i && (i.remove(), i = null);
            }
            return {
              showVideoTooltip: l,
              hideVideoTooltip: m
            };
          }
          n.createShowHideFn = d;
          function p(f) {
            var i = d(f), u = i.showVideoTooltip, l = i.hideVideoTooltip;
            f.txt.eventHooks.videoClickEvents.push(u), f.txt.eventHooks.clickEvents.push(l), f.txt.eventHooks.keyupEvents.push(l), f.txt.eventHooks.toolbarClickEvents.push(l), f.txt.eventHooks.menuClickEvents.push(l), f.txt.eventHooks.textScrollEvents.push(l), f.txt.eventHooks.changeEvents.push(l);
          }
          n.default = p;
        },
        /* 348 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(28));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3));
          function g(p, f) {
            var i = ["P"], u = d(p, i);
            u && v.default(u).css("text-align", f);
          }
          n.default = g;
          function d(p, f) {
            for (var i, u = p.elems[0]; u != null; ) {
              if ((0, r.default)(f).call(f, u == null ? void 0 : u.nodeName))
                return u;
              if (((i = u == null ? void 0 : u.parentNode) === null || i === void 0 ? void 0 : i.nodeName) === "BODY")
                return null;
              u = u.parentNode;
            }
            return u;
          }
        },
        /* 349 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(6);
          function o(v) {
            if (r.UA.isFirefox) {
              var g = v.txt, d = v.selection, p = g.eventHooks.keydownEvents;
              p.push(function(f) {
                var i = d.getSelectionContainerElem();
                if (i) {
                  var u = i.getNodeTop(v), l = u.length && u.prev().length ? u.prev() : null;
                  l && l.attr("data-we-video-p") && d.getCursorPos() === 0 && f.keyCode === 8 && l.remove();
                }
              });
            }
          }
          n.default = o;
        },
        /* 350 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(7), g = o.__importDefault(t(3)), d = o.__importDefault(t(33)), p = o.__importDefault(t(38)), f = o.__importDefault(t(351)), i = o.__importDefault(t(364)), u = (
            /** @class */
            function(l) {
              o.__extends(m, l);
              function m(c) {
                var h = this, A = g.default('<div class="w-e-menu" data-title="图片"><i class="w-e-icon-image"></i></div>'), y = i.default(c);
                if (y.onlyUploadConf) {
                  var x;
                  A = y.onlyUploadConf.$elem, (0, r.default)(x = y.onlyUploadConf.events).call(x, function(S) {
                    var T = S.type, I = S.fn || v.EMPTY_FN;
                    A.on(T, function(C) {
                      C.stopPropagation(), I(C);
                    });
                  });
                }
                return h = l.call(this, A, c) || this, h.imgPanelConfig = y, f.default(c), h;
              }
              return m.prototype.clickHandler = function() {
                this.imgPanelConfig.onlyUploadConf || this.createPanel();
              }, m.prototype.createPanel = function() {
                var c = this.imgPanelConfig, h = new d.default(this, c);
                this.setPanel(h), h.create();
              }, m.prototype.tryChangeActive = function() {
              }, m;
            }(p.default)
          );
          n.default = u;
        },
        /* 351 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(352)), v = r.__importDefault(t(353)), g = r.__importDefault(t(354)), d = r.__importDefault(t(362)), p = r.__importDefault(t(363));
          function f(i) {
            o.default(i), v.default(i), g.default(i), d.default(i), p.default(i);
          }
          n.default = f;
        },
        /* 352 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = t(131), v = r.__importDefault(t(97));
          function g(i, u) {
            var l = i.config, m = l.pasteFilterStyle, c = l.pasteIgnoreImg, h = o.getPasteHtml(u, m, c);
            if (h)
              return true;
            var A = o.getPasteText(u);
            return !!A;
          }
          function d(i, u) {
            for (var l, m = ((l = u.clipboardData) === null || l === void 0 ? void 0 : l.types) || [], c = 0; c < m.length; c++) {
              var h = m[c];
              if (h === "Files")
                return true;
            }
            return false;
          }
          function p(i, u) {
            if (!(!d(u, i) && g(u, i))) {
              var l = o.getPasteImgs(i);
              if (l.length) {
                var m = new v.default(u);
                m.uploadImg(l);
              }
            }
          }
          function f(i) {
            i.txt.eventHooks.pasteEvents.unshift(function(u) {
              p(u, i);
            });
          }
          n.default = f;
        },
        /* 353 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(97));
          function v(g) {
            function d(p) {
              var f = p.dataTransfer && p.dataTransfer.files;
              if (!(!f || !f.length)) {
                var i = new o.default(g);
                i.uploadImg(f);
              }
            }
            g.txt.eventHooks.dropEvents.push(d);
          }
          n.default = v;
        },
        /* 354 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29)), o = e2(t(355));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createShowHideFn = void 0;
          var v = t(2), g = v.__importDefault(t(3));
          t(360);
          var d = t(6);
          function p(m, c, h, A, y) {
            m.attr("style", "width:" + c + "px; height:" + h + "px; left:" + A + "px; top:" + y + "px;");
          }
          function f(m, c) {
            var h = g.default(`<div class="w-e-img-drag-mask">
            <div class="w-e-img-drag-show-size"></div>
            <div class="w-e-img-drag-rb"></div>
         </div>`);
            return h.hide(), c.append(h), h;
          }
          function i(m, c, h) {
            var A = m.getBoundingClientRect(), y = h.getBoundingClientRect(), x = y.width.toFixed(2), S = y.height.toFixed(2);
            (0, r.default)(c).call(c, ".w-e-img-drag-show-size").text(x + "px * " + S + "px"), p(c, (0, o.default)(x), (0, o.default)(S), y.left - A.left, y.top - A.top), c.show();
          }
          function u(m) {
            var c = m.$textContainerElem, h, A = f(m, c);
            function y(T, I) {
              T.on("click", function(C) {
                C.stopPropagation();
              }), T.on("mousedown", ".w-e-img-drag-rb", function(C) {
                if (C.preventDefault(), !h)
                  return;
                var E = C.clientX, D = C.clientY, P = I.getBoundingClientRect(), M = h.getBoundingClientRect(), R = M.width, N = M.height, B = M.left - P.left, F = M.top - P.top, O = R / N, H = R, L = N, U = g.default(document);
                function z() {
                  U.off("mousemove", j), U.off("mouseup", K);
                }
                function j(V) {
                  V.stopPropagation(), V.preventDefault(), H = R + (V.clientX - E), L = N + (V.clientY - D), H / L != O && (L = H / O), H = (0, o.default)(H.toFixed(2)), L = (0, o.default)(L.toFixed(2)), (0, r.default)(T).call(T, ".w-e-img-drag-show-size").text(H.toFixed(2).replace(".00", "") + "px * " + L.toFixed(2).replace(".00", "") + "px"), p(T, H, L, B, F);
                }
                U.on("mousemove", j);
                function K() {
                  h.attr("width", H + ""), h.attr("height", L + "");
                  var V = h.getBoundingClientRect();
                  p(T, H, L, V.left - P.left, V.top - P.top), z();
                }
                U.on("mouseup", K), U.on("mouseleave", z);
              });
            }
            function x(T) {
              if (d.UA.isIE())
                return false;
              T && (h = T, i(c, A, h));
            }
            function S() {
              (0, r.default)(c).call(c, ".w-e-img-drag-mask").hide();
            }
            return y(A, c), g.default(document).on("click", S), m.beforeDestroy(function() {
              g.default(document).off("click", S);
            }), {
              showDrag: x,
              hideDrag: S
            };
          }
          n.createShowHideFn = u;
          function l(m) {
            var c = u(m), h = c.showDrag, A = c.hideDrag;
            m.txt.eventHooks.imgClickEvents.push(h), m.txt.eventHooks.textScrollEvents.push(A), m.txt.eventHooks.keyupEvents.push(A), m.txt.eventHooks.toolbarClickEvents.push(A), m.txt.eventHooks.menuClickEvents.push(A), m.txt.eventHooks.changeEvents.push(A);
          }
          n.default = l;
        },
        /* 355 */
        /***/
        function(s, n, t) {
          s.exports = t(356);
        },
        /* 356 */
        /***/
        function(s, n, t) {
          var e2 = t(357);
          s.exports = e2;
        },
        /* 357 */
        /***/
        function(s, n, t) {
          t(358);
          var e2 = t(9);
          s.exports = e2.parseFloat;
        },
        /* 358 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(359);
          e2({ global: true, forced: parseFloat != a }, {
            parseFloat: a
          });
        },
        /* 359 */
        /***/
        function(s, n, t) {
          var e2 = t(8), a = t(90).trim, r = t(68), o = e2.parseFloat, v = 1 / o(r + "-0") !== -1 / 0;
          s.exports = v ? function(d) {
            var p = a(String(d)), f = o(p);
            return f === 0 && p.charAt(0) == "-" ? -0 : f;
          } : o;
        },
        /* 360 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(361);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 361 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-text-container {
  overflow: hidden;
}
.w-e-img-drag-mask {
  position: absolute;
  z-index: 1;
  border: 1px dashed #ccc;
  box-sizing: border-box;
}
.w-e-img-drag-mask .w-e-img-drag-rb {
  position: absolute;
  right: -5px;
  bottom: -5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ccc;
  cursor: se-resize;
}
.w-e-img-drag-mask .w-e-img-drag-show-size {
  min-width: 110px;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #999;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #999;
  color: #fff;
  border-radius: 2px;
  padding: 0 5px;
}
`, ""]), s.exports = n;
        },
        /* 362 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createShowHideFn = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(39));
          function g(p) {
            var f, i = function(c, h) {
              return h === void 0 && (h = ""), p.i18next.t(h + c);
            };
            function u(m) {
              var c = [{
                $elem: o.default("<span class='w-e-icon-trash-o'></span>"),
                onClick: function(A, y) {
                  return A.selection.createRangeByElem(y), A.selection.restoreSelection(), A.cmd.do("delete"), true;
                }
              }, {
                $elem: o.default("<span>30%</span>"),
                onClick: function(A, y) {
                  return y.attr("width", "30%"), y.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>50%</span>"),
                onClick: function(A, y) {
                  return y.attr("width", "50%"), y.removeAttr("height"), true;
                }
              }, {
                $elem: o.default("<span>100%</span>"),
                onClick: function(A, y) {
                  return y.attr("width", "100%"), y.removeAttr("height"), true;
                }
              }];
              c.push({
                $elem: o.default("<span>" + i("重置") + "</span>"),
                onClick: function(A, y) {
                  return y.removeAttr("width"), y.removeAttr("height"), true;
                }
              }), m.attr("data-href") && c.push({
                $elem: o.default("<span>" + i("查看链接") + "</span>"),
                onClick: function(A, y) {
                  var x = y.attr("data-href");
                  return x && (x = decodeURIComponent(x), window.open(x, "_target")), true;
                }
              }), f = new v.default(p, m, c), f.create();
            }
            function l() {
              f && (f.remove(), f = null);
            }
            return {
              showImgTooltip: u,
              hideImgTooltip: l
            };
          }
          n.createShowHideFn = g;
          function d(p) {
            var f = g(p), i = f.showImgTooltip, u = f.hideImgTooltip;
            p.txt.eventHooks.imgClickEvents.push(i), p.txt.eventHooks.clickEvents.push(u), p.txt.eventHooks.keyupEvents.push(u), p.txt.eventHooks.toolbarClickEvents.push(u), p.txt.eventHooks.menuClickEvents.push(u), p.txt.eventHooks.textScrollEvents.push(u), p.txt.eventHooks.imgDragBarMouseDownEvents.push(u), p.txt.eventHooks.changeEvents.push(u);
          }
          n.default = d;
        },
        /* 363 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function r(o) {
            var v = o.txt, g = o.selection, d = v.eventHooks.keydownEvents;
            d.push(function(p) {
              var f = g.getSelectionContainerElem(), i = g.getRange();
              if (!(!i || !f || p.keyCode !== 8 || !g.isSelectionEmpty())) {
                var u = i.startContainer, l = i.startOffset, m = null;
                if (l === 0)
                  for (; u !== f.elems[0] && f.elems[0].contains(u) && u.parentNode && !m; ) {
                    if (u.previousSibling) {
                      m = u.previousSibling;
                      break;
                    }
                    u = u.parentNode;
                  }
                else
                  u.nodeType !== 3 && (m = u.childNodes[l - 1]);
                if (m) {
                  for (var c = m; c.childNodes.length; )
                    c = c.childNodes[c.childNodes.length - 1];
                  c instanceof HTMLElement && c.tagName === "IMG" && (c.remove(), p.preventDefault());
                }
              }
            });
          }
          n.default = r;
        },
        /* 364 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26)), o = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = t(6), p = v.__importDefault(t(97));
          function f(i) {
            var u, l = i.config, m = new p.default(i), c = d.getRandom("up-trigger-id"), h = d.getRandom("up-file-id"), A = d.getRandom("input-link-url"), y = d.getRandom("input-link-url-alt"), x = d.getRandom("input-link-url-href"), S = d.getRandom("btn-link"), T = "menus.panelMenus.image.", I = function(O, H) {
              return H === void 0 && (H = T), i.i18next.t(H + O);
            };
            function C(F, O, H) {
              var L = l.linkImgCheck(F);
              return L === true ? true : (typeof L == "string" && l.customAlert(L, "error"), false);
            }
            var E = l.uploadImgMaxLength === 1 ? "" : 'multiple="multiple"', D = (0, r.default)(u = l.uploadImgAccept).call(u, function(F) {
              return "image/" + F;
            }).join(","), P = function(O, H, L) {
              return '<div class="' + O + '" data-title="' + L + `">
            <div id="` + c + `" class="w-e-up-btn">
                <i class="` + H + `"></i>
            </div>
            <div style="display:none;">
                <input id="` + h + '" type="file" ' + E + ' accept="' + D + `"/>
            </div>
        </div>`;
            }, M = [
              // 触发选择图片
              {
                selector: "#" + c,
                type: "click",
                fn: function() {
                  var O = l.uploadImgFromMedia;
                  if (O && typeof O == "function")
                    return O(), true;
                  var H = g.default("#" + h), L = H.elems[0];
                  if (L)
                    L.click();
                  else
                    return true;
                }
              },
              // 选择图片完毕
              {
                selector: "#" + h,
                type: "change",
                fn: function() {
                  var O = g.default("#" + h), H = O.elems[0];
                  if (!H)
                    return true;
                  var L = H.files;
                  return L != null && L.length && m.uploadImg(L), H && (H.value = ""), true;
                }
              }
            ], R = [`<input
            id="` + A + `"
            type="text"
            class="block"
            placeholder="` + I("图片地址") + '"/>'];
            l.showLinkImgAlt && R.push(`
        <input
            id="` + y + `"
            type="text"
            class="block"
            placeholder="` + I("图片文字说明") + '"/>'), l.showLinkImgHref && R.push(`
        <input
            id="` + x + `"
            type="text"
            class="block"
            placeholder="` + I("跳转链接") + '"/>');
            var N = [
              // first tab
              {
                // 标题
                title: I("上传图片"),
                // 模板
                tpl: P("w-e-up-img-container", "w-e-icon-upload2", ""),
                // 事件绑定
                events: M
              },
              // second tab
              {
                title: I("网络图片"),
                tpl: `<div>
                    ` + R.join("") + `
                    <div class="w-e-button-container">
                        <button type="button" id="` + S + '" class="right">' + I("插入", "") + `</button>
                    </div>
                </div>`,
                events: [{
                  selector: "#" + S,
                  type: "click",
                  fn: function() {
                    var O, H = g.default("#" + A), L = (0, o.default)(O = H.val()).call(O);
                    if (L) {
                      var U;
                      if (l.showLinkImgAlt) {
                        var z;
                        U = (0, o.default)(z = g.default("#" + y).val()).call(z);
                      }
                      var j;
                      if (l.showLinkImgHref) {
                        var K;
                        j = (0, o.default)(K = g.default("#" + x).val()).call(K);
                      }
                      if (C(L))
                        return m.insertImg(L, U, j), true;
                    }
                  },
                  bindEnter: true
                }]
              }
            ], B = {
              width: 300,
              height: 0,
              tabs: [],
              onlyUploadConf: {
                $elem: g.default(P("w-e-menu", "w-e-icon-image", "图片")),
                events: M
              }
            };
            return window.FileReader && (l.uploadImgShowBase64 || l.uploadImgServer || l.customUploadImg || l.uploadImgFromMedia) && B.tabs.push(N[0]), l.showLinkImg && (B.tabs.push(N[1]), B.onlyUploadConf = void 0), B;
          }
          n.default = f;
        },
        /* 365 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = o.__importDefault(t(24)), d = o.__importDefault(t(366)), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                var l = this, m = v.default(`<div class="w-e-menu" data-title="缩进">
                <i class="w-e-icon-indent-increase"></i>
            </div>`), c = {
                  width: 130,
                  title: "设置缩进",
                  type: "list",
                  list: [{
                    $elem: v.default(`<p>
                            <i class="w-e-icon-indent-increase w-e-drop-list-item"></i>
                            ` + u.i18next.t("menus.dropListMenu.indent.增加缩进") + `
                        <p>`),
                    value: "increase"
                  }, {
                    $elem: v.default(`<p>
                            <i class="w-e-icon-indent-decrease w-e-drop-list-item"></i>
                            ` + u.i18next.t("menus.dropListMenu.indent.减少缩进") + `
                        <p>`),
                    value: "decrease"
                  }],
                  clickHandler: function(A) {
                    l.command(A);
                  }
                };
                return l = f.call(this, m, u, c) || this, l;
              }
              return i.prototype.command = function(u) {
                var l = this.editor, m = l.selection.getSelectionContainerElem();
                if (m && l.$textElem.equal(m)) {
                  var c = l.selection.getSelectionRangeTopNodes();
                  c.length > 0 && (0, r.default)(c).call(c, function(h) {
                    d.default(v.default(h), u, l);
                  });
                } else
                  m && m.length > 0 && (0, r.default)(m).call(m, function(h) {
                    d.default(v.default(h), u, l);
                  });
                l.selection.restoreSelection(), this.tryChangeActive();
              }, i.prototype.tryChangeActive = function() {
                var u = this.editor, l = u.selection.getSelectionStartElem(), m = v.default(l).getNodeTop(u);
                m.length <= 0 || (m.elems[0].style.paddingLeft != "" ? this.active() : this.unActive());
              }, i;
            }(g.default)
          );
          n.default = p;
        },
        /* 366 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(45)), o = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(367)), d = v.__importDefault(t(368)), p = /^(\d+)(\w+)$/, f = /^(\d+)%$/;
          function i(l) {
            var m = l.config.indentation;
            if (typeof m == "string") {
              if (p.test(m)) {
                var c, h = (0, r.default)(c = (0, o.default)(m).call(m).match(p)).call(c, 1, 3), A = h[0], y = h[1];
                return {
                  value: Number(A),
                  unit: y
                };
              } else if (f.test(m))
                return {
                  value: Number((0, o.default)(m).call(m).match(f)[1]),
                  unit: "%"
                };
            } else if (m.value !== void 0 && m.unit)
              return m;
            return {
              value: 2,
              unit: "em"
            };
          }
          function u(l, m, c) {
            var h = l.getNodeTop(c), A = /^(P|H[0-9]*)$/;
            A.test(h.getNodeName()) && (m === "increase" ? g.default(h, i(c)) : m === "decrease" && d.default(h, i(c)));
          }
          n.default = u;
        },
        /* 367 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(45));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function o(v, g) {
            var d = v.elems[0];
            if (d.style.paddingLeft === "")
              v.css("padding-left", g.value + g.unit);
            else {
              var p = d.style.paddingLeft, f = (0, r.default)(p).call(p, 0, p.length - g.unit.length), i = Number(f) + g.value;
              v.css("padding-left", "" + i + g.unit);
            }
          }
          n.default = o;
        },
        /* 368 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(45));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function o(v, g) {
            var d = v.elems[0];
            if (d.style.paddingLeft !== "") {
              var p = d.style.paddingLeft, f = (0, r.default)(p).call(p, 0, p.length - g.unit.length), i = Number(f) - g.value;
              i > 0 ? v.css("padding-left", "" + i + g.unit) : v.css("padding-left", "");
            }
          }
          n.default = o;
        },
        /* 369 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(38)), g = r.__importDefault(t(33)), d = r.__importDefault(t(370)), p = (
            /** @class */
            function(f) {
              r.__extends(i, f);
              function i(u) {
                var l = this, m = o.default(`<div class="w-e-menu" data-title="表情">
                <i class="w-e-icon-happy"></i>
            </div>`);
                return l = f.call(this, m, u) || this, l;
              }
              return i.prototype.createPanel = function() {
                var u = d.default(this.editor), l = new g.default(this, u);
                l.create();
              }, i.prototype.clickHandler = function() {
                this.createPanel();
              }, i.prototype.tryChangeActive = function() {
              }, i;
            }(v.default)
          );
          n.default = p;
        },
        /* 370 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26)), o = e2(t(70)), v = e2(t(17));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var g = t(2), d = g.__importDefault(t(3));
          function p(f) {
            var i = f.config.emotions;
            function u(c) {
              var h = [];
              if (c.type == "image") {
                var A;
                h = (0, r.default)(A = c.content).call(A, function(x) {
                  return typeof x == "string" ? "" : '<span  title="' + x.alt + `">
                    <img class="eleImg" data-emoji="` + x.alt + '" style src="' + x.src + '" alt="[' + x.alt + `]">
                </span>`;
                }), h = (0, o.default)(h).call(h, function(x) {
                  return x !== "";
                });
              } else {
                var y;
                h = (0, r.default)(y = c.content).call(y, function(x) {
                  return '<span class="eleImg" title="' + x + '">' + x + "</span>";
                });
              }
              return h.join("").replace(/&nbsp;/g, "");
            }
            var l = (0, r.default)(i).call(i, function(c) {
              return {
                title: f.i18next.t("menus.panelMenus.emoticon." + c.title),
                // 判断type类型如果是image则以img的形式插入否则以内容
                tpl: "<div>" + u(c) + "</div>",
                events: [{
                  selector: ".eleImg",
                  type: "click",
                  fn: function(A) {
                    var y = d.default(A.target), x = y.getNodeName(), S;
                    if (x === "IMG") {
                      var T;
                      S = (0, v.default)(T = y.parent().html()).call(T);
                    } else
                      S = "<span>" + y.html() + "</span>";
                    return f.cmd.do("insertHTML", S), true;
                  }
                }]
              };
            }), m = {
              width: 300,
              height: 230,
              tabs: l
            };
            return m;
          }
          n.default = p;
        },
        /* 371 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createListHandle = n.ClassType = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(372)), g = r.__importDefault(t(374)), d = r.__importDefault(t(375)), p = r.__importDefault(t(376)), f = r.__importDefault(t(377)), i;
          (function(c) {
            c.Wrap = "WrapListHandle", c.Join = "JoinListHandle", c.StartJoin = "StartJoinListHandle", c.EndJoin = "EndJoinListHandle", c.Other = "OtherListHandle";
          })(i = n.ClassType || (n.ClassType = {}));
          var u = {
            WrapListHandle: v.default,
            JoinListHandle: g.default,
            StartJoinListHandle: d.default,
            EndJoinListHandle: p.default,
            OtherListHandle: f.default
          };
          function l(c, h, A) {
            if (c === i.Other && A === void 0)
              throw new Error("other 类需要传入 range");
            return c !== i.Other ? new u[c](h) : new u[c](h, A);
          }
          n.createListHandle = l;
          var m = (
            /** @class */
            function() {
              function c(h) {
                this.handle = h, this.handle.exec();
              }
              return c.prototype.getSelectionRangeElem = function() {
                return o.default(this.handle.selectionRangeElem.get());
              }, c;
            }()
          );
          n.default = m;
        },
        /* 372 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(58), d = t(47), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                return f.call(this, u) || this;
              }
              return i.prototype.exec = function() {
                var u = this.options, l = u.listType, m = u.listTarget, c = u.$selectionElem, h = u.$startElem, A = u.$endElem, y, x = [], S = c == null ? void 0 : c.getNodeName(), T = h.prior, I = A.prior;
                if (!h.prior && !A.prior || !(T != null && T.prev().length) && !(I != null && I.next().length)) {
                  var C;
                  (0, r.default)(C = c == null ? void 0 : c.children()).call(C, function(B) {
                    x.push(v.default(B));
                  }), S === l ? y = d.createElementFragment(
                    x,
                    d.createDocumentFragment(),
                    // 创建 文档片段
                    "p"
                  ) : (y = d.createElement(m), (0, r.default)(x).call(x, function(B) {
                    y.appendChild(B.elems[0]);
                  })), this.selectionRangeElem.set(y), d.insertBefore(c, y, c.elems[0]), c.remove();
                } else {
                  for (var E = T; E.length; )
                    x.push(E), I != null && I.equal(E) ? E = v.default(void 0) : (
                      // 结束
                      E = E.next()
                    );
                  var D = T.prev(), P = I.next();
                  if (S === l ? y = d.createElementFragment(
                    x,
                    d.createDocumentFragment(),
                    // 创建 文档片段
                    "p"
                  ) : (y = d.createElement(m), (0, r.default)(x).call(x, function(B) {
                    y.append(B.elems[0]);
                  })), D.length && P.length) {
                    for (var M = []; P.length; )
                      M.push(P), P = P.next();
                    var R = d.createElement(S);
                    (0, r.default)(M).call(M, function(B) {
                      R.append(B.elems[0]);
                    }), v.default(R).insertAfter(c), this.selectionRangeElem.set(y);
                    var N = c.next();
                    N.length ? d.insertBefore(c, y, N.elems[0]) : c.parent().elems[0].append(y);
                  } else if (!D.length)
                    this.selectionRangeElem.set(y), d.insertBefore(c, y, c.elems[0]);
                  else {
                    this.selectionRangeElem.set(y);
                    var N = c.next();
                    N.length ? d.insertBefore(c, y, N.elems[0]) : c.parent().elems[0].append(y);
                  }
                }
              }, i;
            }(g.ListHandle)
          );
          n.default = p;
        },
        /* 373 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = (
            /** @class */
            function() {
              function v() {
                this._element = null;
              }
              return v.prototype.set = function(g) {
                if (g instanceof DocumentFragment) {
                  var d, p = [];
                  (0, r.default)(d = g.childNodes).call(d, function(f) {
                    p.push(f);
                  }), g = p;
                }
                this._element = g;
              }, v.prototype.get = function() {
                return this._element;
              }, v.prototype.clear = function() {
                this._element = null;
              }, v;
            }()
          );
          n.default = o;
        },
        /* 374 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(58), d = t(47), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                return f.call(this, u) || this;
              }
              return i.prototype.exec = function() {
                var u, l, m, c, h, A, y, x = this.options, S = x.editor, T = x.listType, I = x.listTarget, C = x.$startElem, E = x.$endElem, D, P = S.selection.getSelectionRangeTopNodes(), M = C == null ? void 0 : C.getNodeName(), R = E == null ? void 0 : E.getNodeName();
                if (M === R)
                  if (P.length > 2)
                    if (P.shift(), P.pop(), D = d.createElementFragment(
                      d.filterSelectionNodes(P),
                      // 过滤 $nodes 获取到符合要求的选中元素节点
                      d.createDocumentFragment()
                      // 创建 文档片段
                    ), M === T)
                      (u = E.children()) === null || u === void 0 || (0, r.default)(u).call(u, function(V) {
                        D.append(V);
                      }), E.remove(), this.selectionRangeElem.set(D), C.elems[0].append(D);
                    else {
                      for (var N = document.createDocumentFragment(), B = document.createDocumentFragment(), F = d.getStartPoint(C); F.length; ) {
                        var O = F.elems[0];
                        F = F.next(), N.append(O);
                      }
                      for (var H = d.getEndPoint(E), L = []; H.length; )
                        L.unshift(H.elems[0]), H = H.prev();
                      (0, r.default)(L).call(L, function(V) {
                        B.append(V);
                      });
                      var U = d.createElement(I);
                      U.append(N), U.append(D), U.append(B), D = U, this.selectionRangeElem.set(D), v.default(U).insertAfter(C), !(!((l = C.children()) === null || l === void 0) && l.length) && C.remove(), !(!((m = E.children()) === null || m === void 0) && m.length) && E.remove();
                    }
                  else {
                    P.length = 0;
                    for (var F = d.getStartPoint(C); F.length; )
                      P.push(F), F = F.next();
                    for (var H = d.getEndPoint(E), L = []; H.length; )
                      L.unshift(H), H = H.prev();
                    P.push.apply(P, L), M === T ? (D = d.createElementFragment(P, d.createDocumentFragment(), "p"), this.selectionRangeElem.set(D), d.insertBefore(C, D, E.elems[0])) : (D = d.createElement(I), (0, r.default)(P).call(P, function(G) {
                      D.append(G.elems[0]);
                    }), this.selectionRangeElem.set(D), v.default(D).insertAfter(C)), !(!((c = C.children()) === null || c === void 0) && c.length) && E.remove(), !(!((h = E.children()) === null || h === void 0) && h.length) && E.remove();
                  }
                else {
                  for (var z = [], H = d.getEndPoint(E); H.length; )
                    z.unshift(H), H = H.prev();
                  for (var j = [], F = d.getStartPoint(C); F.length; )
                    j.push(F), F = F.next();
                  if (D = d.createDocumentFragment(), P.shift(), P.pop(), (0, r.default)(j).call(j, function(w) {
                    return D.append(w.elems[0]);
                  }), D = d.createElementFragment(
                    d.filterSelectionNodes(P),
                    // 序列中间的数据 - 进行数据过滤
                    D
                  ), (0, r.default)(z).call(z, function(w) {
                    return D.append(w.elems[0]);
                  }), this.selectionRangeElem.set(D), M === T)
                    C.elems[0].append(D), !(!((A = E.children()) === null || A === void 0) && A.length) && E.remove();
                  else if (!((y = E.children()) === null || y === void 0) && y.length) {
                    var K = E.children();
                    d.insertBefore(K, D, K.elems[0]);
                  } else
                    E.elems[0].append(D);
                }
              }, i;
            }(g.ListHandle)
          );
          n.default = p;
        },
        /* 375 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(58), d = t(47), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                return f.call(this, u) || this;
              }
              return i.prototype.exec = function() {
                var u, l = this.options, m = l.editor, c = l.listType, h = l.listTarget, A = l.$startElem, y, x = m.selection.getSelectionRangeTopNodes(), S = A == null ? void 0 : A.getNodeName();
                x.shift();
                for (var T = [], I = d.getStartPoint(A); I.length; )
                  T.push(I), I = I.next();
                S === c ? (y = d.createDocumentFragment(), (0, r.default)(T).call(T, function(C) {
                  return y.append(C.elems[0]);
                }), y = d.createElementFragment(
                  d.filterSelectionNodes(x),
                  // 过滤元素节点数据
                  y
                ), this.selectionRangeElem.set(y), A.elems[0].append(y)) : (y = d.createElement(h), (0, r.default)(T).call(T, function(C) {
                  return y.append(C.elems[0]);
                }), y = d.createElementFragment(
                  d.filterSelectionNodes(x),
                  // 过滤普通节点
                  y
                ), this.selectionRangeElem.set(y), v.default(y).insertAfter(A), !(!((u = A.children()) === null || u === void 0) && u.length) && A.remove());
              }, i;
            }(g.ListHandle)
          );
          n.default = p;
        },
        /* 376 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(58), d = t(47), p = (
            /** @class */
            function(f) {
              o.__extends(i, f);
              function i(u) {
                return f.call(this, u) || this;
              }
              return i.prototype.exec = function() {
                var u, l, m = this.options, c = m.editor, h = m.listType, A = m.listTarget, y = m.$endElem, x, S = c.selection.getSelectionRangeTopNodes(), T = y == null ? void 0 : y.getNodeName();
                S.pop();
                for (var I = [], C = d.getEndPoint(y); C.length; )
                  I.unshift(C), C = C.prev();
                if (T === h)
                  if (x = d.createElementFragment(
                    d.filterSelectionNodes(S),
                    // 过滤元素节点数据
                    d.createDocumentFragment()
                    // 创建 文档片段
                  ), (0, r.default)(I).call(I, function(P) {
                    return x.append(P.elems[0]);
                  }), this.selectionRangeElem.set(x), !((u = y.children()) === null || u === void 0) && u.length) {
                    var E = y.children();
                    d.insertBefore(E, x, E.elems[0]);
                  } else
                    y.elems[0].append(x);
                else {
                  var D = d.filterSelectionNodes(S);
                  D.push.apply(D, I), x = d.createElementFragment(
                    D,
                    d.createElement(A)
                    // 创建 序列节点
                  ), this.selectionRangeElem.set(x), v.default(x).insertBefore(y), !(!((l = y.children()) === null || l === void 0) && l.length) && y.remove();
                }
              }, i;
            }(g.ListHandle)
          );
          n.default = p;
        },
        /* 377 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = t(58), v = t(47), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f, i) {
                var u = d.call(this, f) || this;
                return u.range = i, u;
              }
              return p.prototype.exec = function() {
                var f = this.options, i = f.editor, u = f.listTarget, l = i.selection.getSelectionRangeTopNodes(), m = v.createElementFragment(
                  v.filterSelectionNodes(l),
                  // 过滤选取的元素
                  v.createElement(u)
                  // 创建 序列节点
                );
                this.selectionRangeElem.set(m), this.range.insertNode(m);
              }, p;
            }(o.ListHandle)
          );
          n.default = g;
        },
        /* 378 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(24)), d = v.__importDefault(t(3)), p = v.__importDefault(t(379)), f = (
            /** @class */
            function(i) {
              v.__extends(u, i);
              function u(l) {
                var m = this, c = d.default(`<div class="w-e-menu" data-title="行高">
                    <i class="w-e-icon-row-height"></i>
                </div>`), h = new p.default(l, l.config.lineHeights), A = {
                  width: 100,
                  title: "设置行高",
                  type: "list",
                  list: h.getItemList(),
                  clickHandler: function(x) {
                    l.selection.saveRange(), m.command(x);
                  }
                };
                return m = i.call(this, c, l, A) || this, m;
              }
              return u.prototype.command = function(l) {
                var m = this.editor;
                m.selection.restoreSelection();
                var c = d.default(m.selection.getSelectionContainerElem());
                if (c.elems.length) {
                  if (c && m.$textElem.equal(c)) {
                    for (var h = false, A = d.default(m.selection.getSelectionStartElem()).elems[0], y = d.default(m.selection.getSelectionEndElem()).elems[0], x = this.getDom(A), S = this.getDom(y), T = c.elems[0].children, I = 0; I < T.length; I++) {
                      var C = T[I];
                      if (d.default(C).getNodeName() === "P" && (C === x && (h = true), h && (d.default(C).css("line-height", l), C === S))) {
                        h = false;
                        return;
                      }
                    }
                    m.selection.createRangeByElems(A, y);
                    return;
                  }
                  var E = c.elems[0], D = this.getDom(E);
                  d.default(D).getNodeName() === "P" && (d.default(D).css("line-height", l), m.selection.createRangeByElems(D, D));
                }
              }, u.prototype.getDom = function(l) {
                var m = d.default(l).elems[0];
                if (!m.parentNode)
                  return m;
                function c(h, A) {
                  var y = d.default(h.parentNode);
                  return A.$textElem.equal(y) ? h : c(y.elems[0], A);
                }
                return m = c(m, this.editor), m;
              }, u.prototype.styleProcessing = function(l) {
                var m = "";
                return (0, r.default)(l).call(l, function(c) {
                  c !== "" && (0, o.default)(c).call(c, "line-height") === -1 && (m = m + c + ";");
                }), m;
              }, u.prototype.setRange = function(l, m) {
                var c = this.editor, h = window.getSelection ? window.getSelection() : document.getSelection();
                h == null || h.removeAllRanges();
                var A = document.createRange(), y = l, x = m;
                A.setStart(y, 0), A.setEnd(x, 1), h == null || h.addRange(A), c.selection.saveRange(), h == null || h.removeAllRanges(), c.selection.restoreSelection();
              }, u.prototype.tryChangeActive = function() {
                var l = this.editor, m = l.selection.getSelectionContainerElem();
                if (!(m && l.$textElem.equal(m))) {
                  var c = d.default(l.selection.getSelectionStartElem());
                  if (c.length !== 0) {
                    c = this.getDom(c.elems[0]);
                    var h = c.getAttribute("style") ? c.getAttribute("style") : "";
                    h && (0, o.default)(h).call(h, "line-height") !== -1 ? this.active() : this.unActive();
                  }
                }
              }, u;
            }(g.default)
          );
          n.default = f;
        },
        /* 379 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = (
            /** @class */
            function() {
              function d(p, f) {
                var i = this;
                this.itemList = [{
                  $elem: v.default("<span>" + p.i18next.t("默认") + "</span>"),
                  value: ""
                }], (0, r.default)(f).call(f, function(u) {
                  i.itemList.push({
                    $elem: v.default("<span>" + u + "</span>"),
                    value: u
                  });
                });
              }
              return d.prototype.getItemList = function() {
                return this.itemList;
              }, d;
            }()
          );
          n.default = g;
        },
        /* 380 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(23)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = o.default(`<div class="w-e-menu" data-title="撤销">
                <i class="w-e-icon-undo"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor;
                f.history.revoke();
                var i = f.$textElem.children();
                if (i != null && i.length) {
                  var u = i.last();
                  f.selection.createRangeByElem(u, false, true), f.selection.restoreSelection();
                }
              }, p.prototype.tryChangeActive = function() {
                this.editor.isCompatibleMode || (this.editor.history.size[0] ? this.active() : this.unActive());
              }, p;
            }(v.default)
          );
          n.default = g;
        },
        /* 381 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(23)), g = (
            /** @class */
            function(d) {
              r.__extends(p, d);
              function p(f) {
                var i = this, u = o.default(`<div class="w-e-menu" data-title="恢复">
                <i class="w-e-icon-redo"></i>
            </div>`);
                return i = d.call(this, u, f) || this, i;
              }
              return p.prototype.clickHandler = function() {
                var f = this.editor;
                f.history.restore();
                var i = f.$textElem.children();
                if (i != null && i.length) {
                  var u = i.last();
                  f.selection.createRangeByElem(u, false, true), f.selection.restoreSelection();
                }
              }, p.prototype.tryChangeActive = function() {
                this.editor.isCompatibleMode || (this.editor.history.size[1] ? this.active() : this.unActive());
              }, p;
            }(v.default)
          );
          n.default = g;
        },
        /* 382 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(38)), v = r.__importDefault(t(3)), g = r.__importDefault(t(383)), d = r.__importDefault(t(33)), p = r.__importDefault(t(392)), f = (
            /** @class */
            function(i) {
              r.__extends(u, i);
              function u(l) {
                var m = this, c = v.default('<div class="w-e-menu" data-title="表格"><i class="w-e-icon-table2"></i></div>');
                return m = i.call(this, c, l) || this, p.default(l), m;
              }
              return u.prototype.clickHandler = function() {
                this.createPanel();
              }, u.prototype.createPanel = function() {
                var l = g.default(this.editor), m = new d.default(this, l);
                m.create();
              }, u.prototype.tryChangeActive = function() {
              }, u;
            }(o.default)
          );
          n.default = f;
        },
        /* 383 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(384));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(6), g = o.__importDefault(t(3));
          t(389);
          var d = o.__importDefault(t(391));
          function p(i) {
            return i > 0 && (0, r.default)(i);
          }
          function f(i) {
            var u = new d.default(i), l = v.getRandom("w-col-id"), m = v.getRandom("w-row-id"), c = v.getRandom("btn-link"), h = "menus.panelMenus.table.", A = function(T) {
              return i.i18next.t(T);
            }, y = [{
              title: A(h + "插入表格"),
              tpl: `<div>
                    <div class="w-e-table">
                        <span>` + A("创建") + `</span>
                        <input id="` + m + `"  type="text" class="w-e-table-input" value="5"/></td>
                        <span>` + A(h + "行") + `</span>
                        <input id="` + l + `" type="text" class="w-e-table-input" value="5"/></td>
                        <span>` + (A(h + "列") + A(h + "的") + A(h + "表格")) + `</span>
                    </div>
                    <div class="w-e-button-container">
                        <button type="button" id="` + c + '" class="right">' + A("插入") + `</button>
                    </div>
                </div>`,
              events: [{
                selector: "#" + c,
                type: "click",
                fn: function() {
                  var T = Number(g.default("#" + l).val()), I = Number(g.default("#" + m).val());
                  return p(I) && p(T) ? (u.createAction(I, T), true) : (i.config.customAlert("表格行列请输入正整数", "warning"), false);
                },
                bindEnter: true
              }]
            }], x = {
              width: 330,
              height: 0,
              tabs: []
            };
            return x.tabs.push(y[0]), x;
          }
          n.default = f;
        },
        /* 384 */
        /***/
        function(s, n, t) {
          s.exports = t(385);
        },
        /* 385 */
        /***/
        function(s, n, t) {
          var e2 = t(386);
          s.exports = e2;
        },
        /* 386 */
        /***/
        function(s, n, t) {
          t(387);
          var e2 = t(9);
          s.exports = e2.Number.isInteger;
        },
        /* 387 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(388);
          e2({ target: "Number", stat: true }, {
            isInteger: a
          });
        },
        /* 388 */
        /***/
        function(s, n, t) {
          var e2 = t(13), a = Math.floor;
          s.exports = function(o) {
            return !e2(o) && isFinite(o) && a(o) === o;
          };
        },
        /* 389 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(390);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 390 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-table {
  display: flex;
}
.w-e-table .w-e-table-input {
  width: 40px;
  text-align: center!important;
  margin: 0 5px;
}
`, ""]), s.exports = n;
        },
        /* 391 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = t(7), v = r.__importDefault(t(3)), g = (
            /** @class */
            function() {
              function d(p) {
                this.editor = p;
              }
              return d.prototype.createAction = function(p, f) {
                var i = this.editor, u = v.default(i.selection.getSelectionContainerElem()), l = v.default(u.elems[0]).parentUntilEditor("UL", i), m = v.default(u.elems[0]).parentUntilEditor("OL", i);
                if (!(l || m)) {
                  var c = this.createTableHtml(p, f);
                  i.cmd.do("insertHTML", c);
                }
              }, d.prototype.createTableHtml = function(p, f) {
                for (var i = "", u = "", l = 0; l < p; l++) {
                  u = "";
                  for (var m = 0; m < f; m++)
                    l === 0 ? u = u + "<th></th>" : u = u + "<td></td>";
                  i = i + "<tr>" + u + "</tr>";
                }
                var c = '<table border="0" width="100%" cellpadding="0" cellspacing="0"><tbody>' + i + ("</tbody></table>" + o.EMPTY_P);
                return c;
              }, d;
            }()
          );
          n.default = g;
        },
        /* 392 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(393)), v = t(400);
          function g(d) {
            o.default(d), v.bindEventKeyboardEvent(d), v.bindClickEvent(d);
          }
          n.default = g;
        },
        /* 393 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(39)), g = r.__importDefault(t(394)), d = r.__importDefault(t(399)), p = t(7);
          function f(m) {
            var c;
            function h(y) {
              var x = new d.default(m), S = "menus.panelMenus.table.", T = function(E, D) {
                return D === void 0 && (D = S), m.i18next.t(D + E);
              }, I = [{
                // $elem: $("<span class='w-e-icon-trash-o'></span>"),
                $elem: o.default("<span>" + T("删除表格") + "</span>"),
                onClick: function(E, D) {
                  return E.selection.createRangeByElem(D), E.selection.restoreSelection(), E.cmd.do("insertHTML", p.EMPTY_P), true;
                }
              }, {
                $elem: o.default("<span>" + T("添加行") + "</span>"),
                onClick: function(E, D) {
                  var P = i(E);
                  if (P)
                    return true;
                  var M = o.default(E.selection.getSelectionStartElem()), R = x.getRowNode(M.elems[0]);
                  if (!R)
                    return true;
                  var N = Number(x.getCurrentRowIndex(D.elems[0], R)), B = x.getTableHtml(D.elems[0]), F = x.getTableHtml(g.default.ProcessingRow(o.default(B), N).elems[0]);
                  return F = l(D, F), E.selection.createRangeByElem(D), E.selection.restoreSelection(), E.cmd.do("insertHTML", F), true;
                }
              }, {
                $elem: o.default("<span>" + T("删除行") + "</span>"),
                onClick: function(E, D) {
                  var P = i(E);
                  if (P)
                    return true;
                  var M = o.default(E.selection.getSelectionStartElem()), R = x.getRowNode(M.elems[0]);
                  if (!R)
                    return true;
                  var N = Number(x.getCurrentRowIndex(D.elems[0], R)), B = x.getTableHtml(D.elems[0]), F = g.default.DeleteRow(o.default(B), N).elems[0].children[0].children.length, O = "";
                  return E.selection.createRangeByElem(D), E.selection.restoreSelection(), F === 0 ? O = p.EMPTY_P : O = x.getTableHtml(g.default.DeleteRow(o.default(B), N).elems[0]), O = l(D, O), E.cmd.do("insertHTML", O), true;
                }
              }, {
                $elem: o.default("<span>" + T("添加列") + "</span>"),
                onClick: function(E, D) {
                  var P = i(E);
                  if (P)
                    return true;
                  var M = o.default(E.selection.getSelectionStartElem()), R = x.getCurrentColIndex(M.elems[0]), N = x.getTableHtml(D.elems[0]), B = x.getTableHtml(g.default.ProcessingCol(o.default(N), R).elems[0]);
                  return B = l(D, B), E.selection.createRangeByElem(D), E.selection.restoreSelection(), E.cmd.do("insertHTML", B), true;
                }
              }, {
                $elem: o.default("<span>" + T("删除列") + "</span>"),
                onClick: function(E, D) {
                  var P = i(E);
                  if (P)
                    return true;
                  var M = o.default(E.selection.getSelectionStartElem()), R = x.getCurrentColIndex(M.elems[0]), N = x.getTableHtml(D.elems[0]), B = g.default.DeleteCol(o.default(N), R), F = B.elems[0].children[0].children[0].children.length, O = "";
                  return E.selection.createRangeByElem(D), E.selection.restoreSelection(), F === 0 ? O = p.EMPTY_P : O = x.getTableHtml(B.elems[0]), O = l(D, O), E.cmd.do("insertHTML", O), true;
                }
              }, {
                $elem: o.default("<span>" + T("设置表头") + "</span>"),
                onClick: function(E, D) {
                  var P = i(E);
                  if (P)
                    return true;
                  var M = o.default(E.selection.getSelectionStartElem()), R = x.getRowNode(M.elems[0]);
                  if (!R)
                    return true;
                  var N = Number(x.getCurrentRowIndex(D.elems[0], R));
                  N !== 0 && (N = 0);
                  var B = x.getTableHtml(D.elems[0]), F = x.getTableHtml(g.default.setTheHeader(o.default(B), N, "th").elems[0]);
                  return F = l(D, F), E.selection.createRangeByElem(D), E.selection.restoreSelection(), E.cmd.do("insertHTML", F), true;
                }
              }, {
                $elem: o.default("<span>" + T("取消表头") + "</span>"),
                onClick: function(E, D) {
                  var P = o.default(E.selection.getSelectionStartElem()), M = x.getRowNode(P.elems[0]);
                  if (!M)
                    return true;
                  var R = Number(x.getCurrentRowIndex(D.elems[0], M));
                  R !== 0 && (R = 0);
                  var N = x.getTableHtml(D.elems[0]), B = x.getTableHtml(g.default.setTheHeader(o.default(N), R, "td").elems[0]);
                  return B = l(D, B), E.selection.createRangeByElem(D), E.selection.restoreSelection(), E.cmd.do("insertHTML", B), true;
                }
              }];
              c = new v.default(m, y, I), c.create();
            }
            function A() {
              c && (c.remove(), c = null);
            }
            return {
              showTableTooltip: h,
              hideTableTooltip: A
            };
          }
          function i(m) {
            var c = m.selection.getSelectionStartElem(), h = m.selection.getSelectionEndElem();
            return (c == null ? void 0 : c.elems[0]) !== (h == null ? void 0 : h.elems[0]);
          }
          function u(m) {
            var c = f(m), h = c.showTableTooltip, A = c.hideTableTooltip;
            m.txt.eventHooks.tableClickEvents.push(h), m.txt.eventHooks.clickEvents.push(A), m.txt.eventHooks.keyupEvents.push(A), m.txt.eventHooks.toolbarClickEvents.push(A), m.txt.eventHooks.menuClickEvents.push(A), m.txt.eventHooks.textScrollEvents.push(A);
          }
          n.default = u;
          function l(m, c) {
            var h = m.elems[0].nextSibling;
            return (!h || h.innerHTML === "<br>") && (c += "" + p.EMPTY_P), c;
          }
        },
        /* 394 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(45)), o = e2(t(91)), v = e2(t(4)), g = e2(t(138));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var d = t(2), p = d.__importDefault(t(3));
          function f(A, y) {
            for (var x = h(A), S = (0, r.default)(Array.prototype).apply(x.children), T = S[0].children.length, I = document.createElement("tr"), C = 0; C < T; C++) {
              var E = document.createElement("td");
              I.appendChild(E);
            }
            return (0, o.default)(S).call(S, y + 1, 0, I), c(x, S), p.default(x.parentNode);
          }
          function i(A, y) {
            for (var x = h(A), S = (0, r.default)(Array.prototype).apply(x.children), T = function(E) {
              var D, P = [];
              for ((0, v.default)(D = (0, g.default)(S[E].children)).call(D, function(N) {
                P.push(N);
              }); S[E].children.length !== 0; )
                S[E].removeChild(S[E].children[0]);
              var M = p.default(P[0]).getNodeName() !== "TH" ? document.createElement("td") : document.createElement("th");
              (0, o.default)(P).call(P, y + 1, 0, M);
              for (var R = 0; R < P.length; R++)
                S[E].appendChild(P[R]);
            }, I = 0; I < S.length; I++)
              T(I);
            return c(x, S), p.default(x.parentNode);
          }
          function u(A, y) {
            var x = h(A), S = (0, r.default)(Array.prototype).apply(x.children);
            return (0, o.default)(S).call(S, y, 1), c(x, S), p.default(x.parentNode);
          }
          function l(A, y) {
            for (var x = h(A), S = (0, r.default)(Array.prototype).apply(x.children), T = function(E) {
              var D, P = [];
              for ((0, v.default)(D = (0, g.default)(S[E].children)).call(D, function(R) {
                P.push(R);
              }); S[E].children.length !== 0; )
                S[E].removeChild(S[E].children[0]);
              (0, o.default)(P).call(P, y, 1);
              for (var M = 0; M < P.length; M++)
                S[E].appendChild(P[M]);
            }, I = 0; I < S.length; I++)
              T(I);
            return c(x, S), p.default(x.parentNode);
          }
          function m(A, y, x) {
            for (var S = h(A), T = (0, r.default)(Array.prototype).apply(S.children), I = T[y].children, C = document.createElement("tr"), E = function(M) {
              var R, N = document.createElement(x), B = I[M];
              (0, v.default)(R = (0, g.default)(B.childNodes)).call(R, function(F) {
                N.appendChild(F);
              }), C.appendChild(N);
            }, D = 0; D < I.length; D++)
              E(D);
            return (0, o.default)(T).call(T, y, 1, C), c(S, T), p.default(S.parentNode);
          }
          function c(A, y) {
            for (; A.children.length !== 0; )
              A.removeChild(A.children[0]);
            for (var x = 0; x < y.length; x++)
              A.appendChild(y[x]);
          }
          function h(A) {
            var y = A.elems[0].children[0];
            return y.nodeName === "COLGROUP" && (y = A.elems[0].children[A.elems[0].children.length - 1]), y;
          }
          n.default = {
            ProcessingRow: f,
            ProcessingCol: i,
            DeleteRow: u,
            DeleteCol: l,
            setTheHeader: m
          };
        },
        /* 395 */
        /***/
        function(s, n, t) {
          var e2 = t(396);
          s.exports = e2;
        },
        /* 396 */
        /***/
        function(s, n, t) {
          t(50), t(397);
          var e2 = t(9);
          s.exports = e2.Array.from;
        },
        /* 397 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(398), r = t(115), o = !r(function(v) {
            Array.from(v);
          });
          e2({ target: "Array", stat: true, forced: o }, {
            from: a
          });
        },
        /* 398 */
        /***/
        function(s, n, t) {
          var e2 = t(40), a = t(31), r = t(114), o = t(112), v = t(35), g = t(69), d = t(113);
          s.exports = function(f) {
            var i = a(f), u = typeof this == "function" ? this : Array, l = arguments.length, m = l > 1 ? arguments[1] : void 0, c = m !== void 0, h = d(i), A = 0, y, x, S, T, I, C;
            if (c && (m = e2(m, l > 2 ? arguments[2] : void 0, 2)), h != null && !(u == Array && o(h)))
              for (T = h.call(i), I = T.next, x = new u(); !(S = I.call(T)).done; A++)
                C = c ? r(T, m, [S.value, A], true) : S.value, g(x, A, C);
            else
              for (y = v(i.length), x = new u(y); y > A; A++)
                C = c ? m(i[A], A) : i[A], g(x, A, C);
            return x.length = A, x;
          };
        },
        /* 399 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(138));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(3)), d = (
            /** @class */
            function() {
              function p(f) {
                this.editor = f;
              }
              return p.prototype.getRowNode = function(f) {
                var i, u = g.default(f).elems[0];
                return u.parentNode && (u = (i = g.default(u).parentUntil("TR", u)) === null || i === void 0 ? void 0 : i.elems[0]), u;
              }, p.prototype.getCurrentRowIndex = function(f, i) {
                var u, l = 0, m = f.children[0];
                return m.nodeName === "COLGROUP" && (m = f.children[f.children.length - 1]), (0, r.default)(u = (0, o.default)(m.children)).call(u, function(c, h) {
                  c === i && (l = h);
                }), l;
              }, p.prototype.getCurrentColIndex = function(f) {
                var i, u, l = 0, m = g.default(f).getNodeName() === "TD" || g.default(f).getNodeName() === "TH" ? f : (u = g.default(f).parentUntil("TD", f)) === null || u === void 0 ? void 0 : u.elems[0], c = g.default(m).parent();
                return (0, r.default)(i = (0, o.default)(c.elems[0].children)).call(i, function(h, A) {
                  h === m && (l = A);
                }), l;
              }, p.prototype.getTableHtml = function(f) {
                var i = '<table border="0" width="100%" cellpadding="0" cellspacing="0">' + g.default(f).html() + "</table>";
                return i;
              }, p;
            }()
          );
          n.default = d;
        },
        /* 400 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.bindEventKeyboardEvent = n.bindClickEvent = void 0;
          var r = t(2), o = r.__importDefault(t(3));
          function v(p) {
            if (!p.length)
              return false;
            var f = p.elems[0];
            return f.nodeName === "P" && f.innerHTML === "<br>";
          }
          function g(p) {
            function f(i, u) {
              if (u.detail >= 3) {
                var l = window.getSelection();
                if (l) {
                  var m = l.focusNode, c = l.anchorNode, h = o.default(c == null ? void 0 : c.parentElement);
                  if (!i.isContain(o.default(m))) {
                    var A = h.elems[0].tagName === "TD" ? h : h.parentUntilEditor("td", p);
                    if (A) {
                      var y = p.selection.getRange();
                      y == null || y.setEnd(A.elems[0], A.elems[0].childNodes.length), p.selection.restoreSelection();
                    }
                  }
                }
              }
            }
            p.txt.eventHooks.tableClickEvents.push(f);
          }
          n.bindClickEvent = g;
          function d(p) {
            var f = p.txt, i = p.selection, u = f.eventHooks.keydownEvents;
            u.push(function(l) {
              p.selection.saveRange();
              var m = i.getSelectionContainerElem();
              if (m) {
                var c = m.getNodeTop(p), h = c.length && c.prev().length ? c.prev() : null;
                if (h && h.getNodeName() === "TABLE" && i.isSelectionEmpty() && i.getCursorPos() === 0 && l.keyCode === 8) {
                  var A = c.next(), y = !!A.length;
                  y && v(c) && (c.remove(), p.selection.setRangeToElem(A.elems[0])), l.preventDefault();
                }
              }
            });
          }
          n.bindEventKeyboardEvent = d;
        },
        /* 401 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.formatCodeHtml = void 0;
          var o = t(2), v = o.__importDefault(t(38)), g = o.__importDefault(t(3)), d = t(6), p = o.__importDefault(t(402)), f = o.__importDefault(t(139)), i = o.__importDefault(t(33)), u = o.__importDefault(t(403));
          function l(c, h) {
            if (!h)
              return h;
            return h = y(h), h = A(h), h = d.replaceSpecialSymbol(h), h;
            function A(x) {
              var S = x.match(/<pre[\s|\S]+?\/pre>/g);
              return S === null || (0, r.default)(S).call(S, function(T) {
                x = x.replace(T, T.replace(/<\/code><code>/g, `
`).replace(/<br>/g, ""));
              }), x;
            }
            function y(x) {
              var S, T = x.match(/<span\sclass="hljs[\s|\S]+?\/span>/gm);
              if (!T || !T.length)
                return x;
              for (var I = (0, r.default)(S = d.deepClone(T)).call(S, function(E) {
                return E = E.replace(/<span\sclass="hljs[^>]+>/, ""), E.replace(/<\/span>/, "");
              }), C = 0; C < T.length; C++)
                x = x.replace(T[C], I[C]);
              return y(x);
            }
          }
          n.formatCodeHtml = l;
          var m = (
            /** @class */
            function(c) {
              o.__extends(h, c);
              function h(A) {
                var y = this, x = g.default('<div class="w-e-menu" data-title="代码"><i class="w-e-icon-terminal"></i></div>');
                return y = c.call(this, x, A) || this, u.default(A), y;
              }
              return h.prototype.insertLineCode = function(A) {
                var y = this.editor, x = g.default("<code>" + A + "</code>");
                y.cmd.do("insertElem", x), y.selection.createRangeByElem(x, false), y.selection.restoreSelection();
              }, h.prototype.clickHandler = function() {
                var A = this.editor, y = A.selection.getSelectionText();
                this.isActive || (A.selection.isSelectionEmpty() ? this.createPanel("", "") : this.insertLineCode(y));
              }, h.prototype.createPanel = function(A, y) {
                var x = p.default(this.editor, A, y), S = new i.default(this, x);
                S.create();
              }, h.prototype.tryChangeActive = function() {
                var A = this.editor;
                f.default(A) ? this.active() : this.unActive();
              }, h;
            }(v.default)
          );
          n.default = m;
        },
        /* 402 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(26));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = t(6), g = o.__importDefault(t(3)), d = o.__importDefault(t(139)), p = t(7);
          function f(i, u, l) {
            var m, c = v.getRandom("input-iframe"), h = v.getRandom("select"), A = v.getRandom("btn-ok");
            function y(I, C) {
              var E, D = d.default(i);
              D && x();
              var P = (E = i.selection.getSelectionStartElem()) === null || E === void 0 ? void 0 : E.elems[0].innerHTML;
              P && i.cmd.do("insertHTML", p.EMPTY_P);
              var M = C.replace(/</g, "&lt;").replace(/>/g, "&gt;");
              i.highlight && (M = i.highlight.highlightAuto(M).value), i.cmd.do("insertHTML", '<pre><code class="' + I + '">' + M + "</code></pre>");
              var R = i.selection.getSelectionStartElem(), N = R == null ? void 0 : R.getNodeTop(i);
              (N == null ? void 0 : N.getNextSibling().elems.length) === 0 && g.default(p.EMPTY_P).insertAfter(N);
            }
            function x() {
              if (d.default(i)) {
                var I = i.selection.getSelectionStartElem(), C = I == null ? void 0 : I.getNodeTop(i);
                C && (i.selection.createRangeByElem(C), i.selection.restoreSelection());
              }
            }
            var S = function(C) {
              return i.i18next.t(C);
            }, T = {
              width: 500,
              height: 0,
              // panel 中可包含多个 tab
              tabs: [{
                // tab 的标题
                title: S("menus.panelMenus.code.插入代码"),
                // 模板
                tpl: `<div>
                        <select name="" id="` + h + `">
                            ` + (0, r.default)(m = i.config.languageType).call(m, function(I) {
                  return "<option " + (l == I ? "selected" : "") + ' value ="' + I + '">' + I + "</option>";
                }) + `
                        </select>
                        <textarea id="` + c + '" type="text" class="wang-code-textarea" placeholder="" style="height: 160px">' + u.replace(/&quot;/g, '"') + `</textarea>
                        <div class="w-e-button-container">
                            <button type="button" id="` + A + '" class="right">' + (d.default(i) ? S("修改") : S("插入")) + `</button>
                        </div>
                    </div>`,
                // 事件绑定
                events: [
                  // 插入链接
                  {
                    selector: "#" + A,
                    type: "click",
                    fn: function() {
                      var C = document.getElementById(c), E = g.default("#" + h), D = E.val(), P = C.value;
                      if (P)
                        return d.default(i) ? false : (y(D, P), true);
                    }
                  }
                ]
              }]
            };
            return T;
          }
          n.default = f;
        },
        /* 403 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(404)), v = r.__importDefault(t(405));
          function g(d) {
            o.default(d), v.default(d);
          }
          n.default = g;
        },
        /* 404 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.createShowHideFn = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(39));
          function g(p) {
            var f;
            function i(l) {
              var m = "menus.panelMenus.code.", c = function(y, x) {
                return x === void 0 && (x = m), p.i18next.t(x + y);
              }, h = [{
                $elem: o.default("<span>" + c("删除代码") + "</span>"),
                onClick: function(y, x) {
                  return x.remove(), true;
                }
              }];
              f = new v.default(p, l, h), f.create();
            }
            function u() {
              f && (f.remove(), f = null);
            }
            return {
              showCodeTooltip: i,
              hideCodeTooltip: u
            };
          }
          n.createShowHideFn = g;
          function d(p) {
            var f = g(p), i = f.showCodeTooltip, u = f.hideCodeTooltip;
            p.txt.eventHooks.codeClickEvents.push(i), p.txt.eventHooks.clickEvents.push(u), p.txt.eventHooks.toolbarClickEvents.push(u), p.txt.eventHooks.menuClickEvents.push(u), p.txt.eventHooks.textScrollEvents.push(u);
          }
          n.default = d;
        },
        /* 405 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = t(7), v = r.__importDefault(t(3));
          function g(d) {
            var p = d.$textElem, f = d.selection, i = d.txt, u = i.eventHooks.keydownEvents;
            u.push(function(l) {
              var m;
              if (l.keyCode === 40) {
                var c = f.getSelectionContainerElem(), h = (m = p.children()) === null || m === void 0 ? void 0 : m.last();
                if ((c == null ? void 0 : c.elems[0].tagName) === "XMP" && (h == null ? void 0 : h.elems[0].tagName) === "PRE") {
                  var A = v.default(o.EMPTY_P);
                  p.append(A);
                }
              }
            }), u.push(function(l) {
              d.selection.saveRange();
              var m = f.getSelectionContainerElem();
              if (m) {
                var c = m.getNodeTop(d), h = c == null ? void 0 : c.prev(), A = c == null ? void 0 : c.getNextSibling();
                if (h.length && (h == null ? void 0 : h.getNodeName()) === "PRE" && A.length === 0 && f.getCursorPos() === 0 && l.keyCode === 8) {
                  var y = v.default(o.EMPTY_P);
                  p.append(y);
                }
              }
            });
          }
          n.default = g;
        },
        /* 406 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(23)), v = r.__importDefault(t(3)), g = r.__importDefault(t(407)), d = t(6), p = t(7), f = (
            /** @class */
            function(i) {
              r.__extends(u, i);
              function u(l) {
                var m = this, c = v.default('<div class="w-e-menu" data-title="分割线"><i class="w-e-icon-split-line"></i></div>');
                return m = i.call(this, c, l) || this, g.default(l), m;
              }
              return u.prototype.clickHandler = function() {
                var l = this.editor, m = l.selection.getRange(), c = l.selection.getSelectionContainerElem();
                if (c != null && c.length) {
                  var h = v.default(c.elems[0]), A = h.parentUntil("TABLE", c.elems[0]), y = h.children();
                  h.getNodeName() !== "CODE" && (A && v.default(A.elems[0]).getNodeName() === "TABLE" || y && y.length !== 0 && v.default(y.elems[0]).getNodeName() === "IMG" && !(m != null && m.collapsed) || this.createSplitLine());
                }
              }, u.prototype.createSplitLine = function() {
                var l = "<hr/>" + p.EMPTY_P;
                d.UA.isFirefox && (l = "<hr/><p></p>"), this.editor.cmd.do("insertHTML", l);
              }, u.prototype.tryChangeActive = function() {
              }, u;
            }(o.default)
          );
          n.default = f;
        },
        /* 407 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(408));
          function v(g) {
            o.default(g);
          }
          n.default = v;
        },
        /* 408 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = r.__importDefault(t(39));
          function g(p) {
            var f;
            function i(l) {
              var m = [{
                $elem: o.default("<span>" + p.i18next.t("menus.panelMenus.删除") + "</span>"),
                onClick: function(h, A) {
                  return h.selection.createRangeByElem(A), h.selection.restoreSelection(), h.cmd.do("delete"), true;
                }
              }];
              f = new v.default(p, l, m), f.create();
            }
            function u() {
              f && (f.remove(), f = null);
            }
            return {
              showSplitLineTooltip: i,
              hideSplitLineTooltip: u
            };
          }
          function d(p) {
            var f = g(p), i = f.showSplitLineTooltip, u = f.hideSplitLineTooltip;
            p.txt.eventHooks.splitLineEvents.push(i), p.txt.eventHooks.clickEvents.push(u), p.txt.eventHooks.keyupEvents.push(u), p.txt.eventHooks.toolbarClickEvents.push(u), p.txt.eventHooks.menuClickEvents.push(u), p.txt.eventHooks.textScrollEvents.push(u);
          }
          n.default = d;
        },
        /* 409 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = o.__importDefault(t(23)), d = t(98), p = o.__importDefault(t(415)), f = o.__importDefault(t(140)), i = (
            /** @class */
            function(u) {
              o.__extends(l, u);
              function l(m) {
                var c = this, h = v.default(`<div class="w-e-menu" data-title="待办事项">
                    <i class="w-e-icon-checkbox-checked"></i>
                </div>`);
                return c = u.call(this, h, m) || this, p.default(m), c;
              }
              return l.prototype.clickHandler = function() {
                var m = this.editor;
                d.isAllTodo(m) ? (this.cancelTodo(), this.tryChangeActive()) : this.setTodo();
              }, l.prototype.tryChangeActive = function() {
                d.isAllTodo(this.editor) ? this.active() : this.unActive();
              }, l.prototype.setTodo = function() {
                var m = this.editor, c = m.selection.getSelectionRangeTopNodes();
                (0, r.default)(c).call(c, function(h) {
                  var A, y = h == null ? void 0 : h.getNodeName();
                  if (y === "P") {
                    var x = f.default(h), S = x.getTodo(), T = (A = S.children()) === null || A === void 0 ? void 0 : A.getNode();
                    S.insertAfter(h), m.selection.moveCursor(T), h.remove();
                  }
                }), this.tryChangeActive();
              }, l.prototype.cancelTodo = function() {
                var m = this.editor, c = m.selection.getSelectionRangeTopNodes();
                (0, r.default)(c).call(c, function(h) {
                  var A, y, x, S = (y = (A = h.childNodes()) === null || A === void 0 ? void 0 : A.childNodes()) === null || y === void 0 ? void 0 : y.clone(true), T = v.default("<p></p>");
                  T.append(S), T.insertAfter(h), (x = T.childNodes()) === null || x === void 0 || x.get(0).remove(), m.selection.moveCursor(T.getNode()), h.remove();
                });
              }, l;
            }(g.default)
          );
          n.default = i;
        },
        /* 410 */
        /***/
        function(s, n, t) {
          s.exports = t(411);
        },
        /* 411 */
        /***/
        function(s, n, t) {
          var e2 = t(412);
          s.exports = e2;
        },
        /* 412 */
        /***/
        function(s, n, t) {
          var e2 = t(413), a = Array.prototype;
          s.exports = function(r) {
            var o = r.every;
            return r === a || r instanceof Array && o === a.every ? e2 : o;
          };
        },
        /* 413 */
        /***/
        function(s, n, t) {
          t(414);
          var e2 = t(15);
          s.exports = e2("Array").every;
        },
        /* 414 */
        /***/
        function(s, n, t) {
          var e2 = t(5), a = t(32).every, r = t(67), o = t(22), v = r("every"), g = o("every");
          e2({ target: "Array", proto: true, forced: !v || !g }, {
            every: function(p) {
              return a(this, p, arguments.length > 1 ? arguments[1] : void 0);
            }
          });
        },
        /* 415 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3)), g = t(98), d = o.__importDefault(t(140)), p = t(98), f = t(7);
          function i(u) {
            function l(A) {
              var y, x;
              if (g.isAllTodo(u)) {
                A.preventDefault();
                var S = u.selection, T = S.getSelectionRangeTopNodes()[0], I = (y = T.childNodes()) === null || y === void 0 ? void 0 : y.get(0), C = (x = window.getSelection()) === null || x === void 0 ? void 0 : x.anchorNode, E = S.getRange();
                if (!(E != null && E.collapsed)) {
                  var D = E == null ? void 0 : E.commonAncestorContainer.childNodes, P = E == null ? void 0 : E.startContainer, M = E == null ? void 0 : E.endContainer, R = E == null ? void 0 : E.startOffset, N = E == null ? void 0 : E.endOffset, B = 0, F = 0, O = [];
                  D == null || (0, r.default)(D).call(D, function(G, X) {
                    G.contains(P) && (B = X), G.contains(M) && (F = X);
                  }), F - B > 1 && (D == null || (0, r.default)(D).call(D, function(G, X) {
                    X <= B || X >= F || O.push(G);
                  }), (0, r.default)(O).call(O, function(G) {
                    G.remove();
                  })), p.dealTextNode(P, R), p.dealTextNode(M, N, false), u.selection.moveCursor(M, 0);
                }
                if (T.text() === "") {
                  var H = v.default(f.EMPTY_P);
                  H.insertAfter(T), S.moveCursor(H.getNode()), T.remove();
                  return;
                }
                var L = S.getCursorPos(), U = g.getCursorNextNode(I == null ? void 0 : I.getNode(), C, L), z = d.default(v.default(U)), j = z.getInputContainer(), K = j.parent().getNode(), V = z.getTodo(), Q = j.getNode().nextSibling;
                if ((I == null ? void 0 : I.text()) === "" && (I == null || I.append(v.default("<br>"))), V.insertAfter(T), !Q || (Q == null ? void 0 : Q.textContent) === "") {
                  if ((Q == null ? void 0 : Q.nodeName) !== "BR") {
                    var w = v.default("<br>");
                    w.insertAfter(j);
                  }
                  S.moveCursor(K, 1);
                } else
                  S.moveCursor(K);
              }
            }
            function m(A) {
              var y, x;
              if (g.isAllTodo(u)) {
                var S = u.selection, T = S.getSelectionRangeTopNodes()[0], I = (y = T.childNodes()) === null || y === void 0 ? void 0 : y.getNode(), C = v.default("<p></p>"), E = C.getNode(), D = (x = window.getSelection()) === null || x === void 0 ? void 0 : x.anchorNode, P = S.getCursorPos(), M = D.previousSibling;
                if (T.text() === "") {
                  A.preventDefault();
                  var R = v.default(f.EMPTY_P);
                  R.insertAfter(T), T.remove(), S.moveCursor(R.getNode(), 0);
                  return;
                }
                if ((M == null ? void 0 : M.nodeName) === "SPAN" && M.childNodes[0].nodeName === "INPUT" && P === 0) {
                  var N;
                  A.preventDefault(), I == null || (0, r.default)(N = I.childNodes).call(N, function(B, F) {
                    F !== 0 && E.appendChild(B.cloneNode(true));
                  }), C.insertAfter(T), T.remove();
                }
              }
            }
            function c() {
              var A = u.selection, y = A.getSelectionRangeTopNodes()[0];
              y && p.isTodo(y) && y.text() === "" && (v.default(f.EMPTY_P).insertAfter(y), y.remove());
            }
            function h(A) {
              A && A.target instanceof HTMLInputElement && A.target.type === "checkbox" && (A.target.checked ? A.target.setAttribute("checked", "true") : A.target.removeAttribute("checked"));
            }
            u.txt.eventHooks.enterDownEvents.push(l), u.txt.eventHooks.deleteUpEvents.push(c), u.txt.eventHooks.deleteDownEvents.push(m), u.txt.eventHooks.clickEvents.push(h);
          }
          n.default = i;
        },
        /* 416 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.selectorValidator = void 0;
          var r = t(2), o = r.__importDefault(t(3)), v = t(6), g = t(7), d = r.__importDefault(t(130)), p = {
            border: "1px solid #c9d8db",
            toolbarBgColor: "#FFF",
            toolbarBottomBorder: "1px solid #EEE"
          };
          function f(u) {
            var l = u.toolbarSelector, m = o.default(l), c = u.textSelector, h = u.config, A = h.height, y = u.i18next, x = o.default("<div></div>"), S = o.default("<div></div>"), T, I, C = null;
            c == null ? (I = m.children(), m.append(x).append(S), x.css("background-color", p.toolbarBgColor).css("border", p.border).css("border-bottom", p.toolbarBottomBorder), S.css("border", p.border).css("border-top", "none").css("height", A + "px")) : (m.append(x), C = o.default(c).children(), o.default(c).append(S), I = S.children()), T = o.default("<div></div>"), T.attr("contenteditable", "true").css("width", "100%").css("height", "100%");
            var E, D = u.config.placeholder;
            D !== d.default.placeholder ? E = o.default("<div>" + D + "</div>") : E = o.default("<div>" + y.t(D) + "</div>"), E.addClass("placeholder"), I && I.length ? (T.append(I), E.hide()) : T.append(o.default(g.EMPTY_P)), C && C.length && (T.append(C), E.hide()), S.append(T), S.append(E), x.addClass("w-e-toolbar").css("z-index", u.zIndex.get("toolbar")), S.addClass("w-e-text-container"), S.css("z-index", u.zIndex.get()), T.addClass("w-e-text");
            var P = v.getRandom("toolbar-elem");
            x.attr("id", P);
            var M = v.getRandom("text-elem");
            T.attr("id", M);
            var R = S.getBoundingClientRect().height, N = T.getBoundingClientRect().height;
            R !== N && T.css("min-height", R + "px"), u.$toolbarElem = x, u.$textContainerElem = S, u.$textElem = T, u.toolbarElemId = P, u.textElemId = M;
          }
          n.default = f;
          function i(u) {
            var l = "data-we-id", m = /^wangEditor-\d+$/, c = u.textSelector, h = u.toolbarSelector, A = {
              bar: o.default("<div></div>"),
              text: o.default("<div></div>")
            };
            if (h == null)
              throw new Error("错误：初始化编辑器时候未传入任何参数，请查阅文档");
            if (A.bar = o.default(h), !A.bar.elems.length)
              throw new Error("无效的节点选择器：" + h);
            if (m.test(A.bar.attr(l)))
              throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器");
            if (c) {
              if (A.text = o.default(c), !A.text.elems.length)
                throw new Error("无效的节点选择器：" + c);
              if (m.test(A.text.attr(l)))
                throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器");
            }
            A.bar.attr(l, u.id), A.text.attr(l, u.id), u.beforeDestroy(function() {
              A.bar.removeAttr(l), A.text.removeAttr(l);
            });
          }
          n.selectorValidator = i;
        },
        /* 417 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(3)), v = t(7);
          function g(d, p) {
            var f = d.$textElem, i = f.children();
            if (!i || !i.length) {
              f.append(o.default(v.EMPTY_P)), g(d);
              return;
            }
            var u = i.last();
            if (p) {
              var l = u.html().toLowerCase(), m = u.getNodeName();
              if (l !== "<br>" && l !== "<br/>" || m !== "P") {
                f.append(o.default(v.EMPTY_P)), g(d);
                return;
              }
            }
            d.selection.createRangeByElem(u, false, true), d.config.focus ? d.selection.restoreSelection() : d.selection.clearWindowSelectionRange();
          }
          n.default = g;
        },
        /* 418 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3));
          function g(l) {
            d(l), p(l), f(l);
          }
          function d(l) {
            l.txt.eventHooks.changeEvents.push(function() {
              var m = l.config.onchange;
              if (m) {
                var c = l.txt.html() || "";
                l.isFocus = true, m(c);
              }
              l.txt.togglePlaceholder();
            });
          }
          function p(l) {
            l.isFocus = false;
            function m(c) {
              var h = c.target, A = v.default(h), y = l.$textElem, x = l.$toolbarElem, S = y.isContain(A), T = x.isContain(A), I = x.elems[0] == c.target;
              if (S)
                l.isFocus || u(l), l.isFocus = true;
              else {
                if (T && !I || !l.isFocus)
                  return;
                i(l), l.isFocus = false;
              }
            }
            document.activeElement === l.$textElem.elems[0] && l.config.focus && (u(l), l.isFocus = true), v.default(document).on("click", m), l.beforeDestroy(function() {
              v.default(document).off("click", m);
            });
          }
          function f(l) {
            l.$textElem.on("compositionstart", function() {
              l.isComposing = true, l.txt.togglePlaceholder();
            }).on("compositionend", function() {
              l.isComposing = false, l.txt.togglePlaceholder();
            });
          }
          function i(l) {
            var m, c = l.config, h = c.onblur, A = l.txt.html() || "";
            (0, r.default)(m = l.txt.eventHooks.onBlurEvents).call(m, function(y) {
              return y();
            }), h(A);
          }
          function u(l) {
            var m = l.config, c = m.onfocus, h = l.txt.html() || "";
            c(h);
          }
          n.default = g;
        },
        /* 419 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          function r(o) {
            var v = o.config, g = v.lang, d = v.languages;
            if (o.i18next != null) {
              try {
                o.i18next.init({
                  ns: "wangEditor",
                  lng: g,
                  defaultNS: "wangEditor",
                  resources: d
                });
              } catch (p) {
                throw new Error("i18next:" + p);
              }
              return;
            }
            o.i18next = {
              t: function(f) {
                var i = f.split(".");
                return i[i.length - 1];
              }
            };
          }
          n.default = r;
        },
        /* 420 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.setUnFullScreen = n.setFullScreen = void 0;
          var o = t(2), v = o.__importDefault(t(3));
          t(421);
          var g = "w-e-icon-fullscreen", d = "w-e-icon-fullscreen_exit", p = "w-e-full-screen-editor";
          n.setFullScreen = function(i) {
            var u = v.default(i.toolbarSelector), l = i.$textContainerElem, m = i.$toolbarElem, c = (0, r.default)(m).call(m, "i." + g), h = i.config;
            c.removeClass(g), c.addClass(d), u.addClass(p), u.css("z-index", h.zIndexFullScreen);
            var A = m.getBoundingClientRect();
            l.css("height", "calc(100% - " + A.height + "px)");
          }, n.setUnFullScreen = function(i) {
            var u = v.default(i.toolbarSelector), l = i.$textContainerElem, m = i.$toolbarElem, c = (0, r.default)(m).call(m, "i." + d), h = i.config;
            c.removeClass(d), c.addClass(g), u.removeClass(p), u.css("z-index", "auto"), l.css("height", h.height + "px");
          };
          var f = function(u) {
            if (!u.textSelector && u.config.showFullScreen) {
              var l = u.$toolbarElem, m = v.default(`<div class="w-e-menu" data-title="全屏">
            <i class="` + g + `"></i>
        </div>`);
              m.on("click", function(c) {
                var h, A = (0, r.default)(h = v.default(c.currentTarget)).call(h, "i");
                A.hasClass(g) ? (m.attr("data-title", "取消全屏"), n.setFullScreen(u)) : (m.attr("data-title", "全屏"), n.setUnFullScreen(u));
              }), l.append(m);
            }
          };
          n.default = f;
        },
        /* 421 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(422);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 422 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-full-screen-editor {
  position: fixed;
  width: 100%!important;
  height: 100%!important;
  left: 0;
  top: 0;
}
`, ""]), s.exports = n;
        },
        /* 423 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = function(g, d) {
            var p, f = g.isEnable ? g.$textElem : (0, r.default)(p = g.$textContainerElem).call(p, ".w-e-content-mantle"), i = (0, r.default)(f).call(f, "[id='" + d + "']"), u = i.getOffsetData().top;
            f.scrollTop(u);
          };
          n.default = o;
        },
        /* 424 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(129)), v = {
            menu: 2,
            panel: 2,
            toolbar: 1,
            tooltip: 1,
            textContainer: 1
          }, g = (
            /** @class */
            function() {
              function d() {
                this.tier = v, this.baseZIndex = o.default.zIndex;
              }
              return d.prototype.get = function(p) {
                return p && this.tier[p] ? this.baseZIndex + this.tier[p] : this.baseZIndex;
              }, d.prototype.init = function(p) {
                this.baseZIndex == o.default.zIndex && (this.baseZIndex = p.config.zIndex);
              }, d;
            }()
          );
          n.default = g;
        },
        /* 425 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(70)), o = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var v = t(2), g = v.__importDefault(t(426)), d = t(6), p = t(7);
          function f(u, l) {
            return (0, r.default)(u).call(u, function(m) {
              var c = m.type, h = m.target, A = m.attributeName;
              return c != "attributes" || c == "attributes" && (A == "contenteditable" || h != l);
            });
          }
          var i = (
            /** @class */
            function(u) {
              v.__extends(l, u);
              function l(m) {
                var c = u.call(this, function(h, A) {
                  var y;
                  if (h = f(h, A.target), (y = c.data).push.apply(y, h), m.isCompatibleMode)
                    c.asyncSave();
                  else if (!m.isComposing)
                    return c.asyncSave();
                }) || this;
                return c.editor = m, c.data = [], c.asyncSave = p.EMPTY_FN, c;
              }
              return l.prototype.save = function() {
                this.data.length && (this.editor.history.save(this.data), this.data.length = 0, this.emit());
              }, l.prototype.emit = function() {
                var m;
                (0, o.default)(m = this.editor.txt.eventHooks.changeEvents).call(m, function(c) {
                  return c();
                });
              }, l.prototype.observe = function() {
                var m = this;
                u.prototype.observe.call(this, this.editor.$textElem.elems[0]);
                var c = this.editor.config.onchangeTimeout;
                this.asyncSave = d.debounce(function() {
                  m.save();
                }, c), this.editor.isCompatibleMode || this.editor.$textElem.on("compositionend", function() {
                  m.asyncSave();
                });
              }, l;
            }(g.default)
          );
          n.default = i;
        },
        /* 426 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = (
            /** @class */
            function() {
              function o(v, g) {
                var d = this;
                this.options = {
                  subtree: true,
                  childList: true,
                  attributes: true,
                  attributeOldValue: true,
                  characterData: true,
                  characterDataOldValue: true
                }, this.callback = function(p) {
                  v(p, d);
                }, this.observer = new MutationObserver(this.callback), g && (this.options = g);
              }
              return (0, a.default)(o.prototype, "target", {
                get: function() {
                  return this.node;
                },
                enumerable: false,
                configurable: true
              }), o.prototype.observe = function(v) {
                this.node instanceof Node || (this.node = v, this.connect());
              }, o.prototype.connect = function() {
                if (this.node)
                  return this.observer.observe(this.node, this.options), this;
                throw new Error("还未初始化绑定，请您先绑定有效的 Node 节点");
              }, o.prototype.disconnect = function() {
                var v = this.observer.takeRecords();
                v.length && this.callback(v), this.observer.disconnect();
              }, o;
            }()
          );
          n.default = r;
        },
        /* 427 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(428)), v = r.__importDefault(t(435)), g = r.__importDefault(t(436)), d = (
            /** @class */
            function() {
              function p(f) {
                this.editor = f, this.content = new o.default(f), this.scroll = new v.default(f), this.range = new g.default(f);
              }
              return (0, a.default)(p.prototype, "size", {
                /**
                 *  获取缓存中的数据长度。格式为：[正常的数据的条数，被撤销的数据的条数]
                 */
                get: function() {
                  return this.scroll.size;
                },
                enumerable: false,
                configurable: true
              }), p.prototype.observe = function() {
                this.content.observe(), this.scroll.observe(), !this.editor.isCompatibleMode && this.range.observe();
              }, p.prototype.save = function(f) {
                f.length && (this.content.save(f), this.scroll.save(), !this.editor.isCompatibleMode && this.range.save());
              }, p.prototype.revoke = function() {
                this.editor.change.disconnect();
                var f = this.content.revoke();
                f && (this.scroll.revoke(), this.editor.isCompatibleMode || (this.range.revoke(), this.editor.$textElem.focus())), this.editor.change.connect(), f && this.editor.change.emit();
              }, p.prototype.restore = function() {
                this.editor.change.disconnect();
                var f = this.content.restore();
                f && (this.scroll.restore(), this.editor.isCompatibleMode || (this.range.restore(), this.editor.$textElem.focus())), this.editor.change.connect(), f && this.editor.change.emit();
              }, p;
            }()
          );
          n.default = d;
        },
        /* 428 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(429)), v = r.__importDefault(t(433)), g = (
            /** @class */
            function() {
              function d(p) {
                this.editor = p;
              }
              return d.prototype.observe = function() {
                this.editor.isCompatibleMode ? this.cache = new v.default(this.editor) : this.cache = new o.default(this.editor), this.cache.observe();
              }, d.prototype.save = function(p) {
                this.editor.isCompatibleMode ? this.cache.save() : this.cache.compile(p);
              }, d.prototype.revoke = function() {
                var p;
                return (p = this.cache) === null || p === void 0 ? void 0 : p.revoke();
              }, d.prototype.restore = function() {
                var p;
                return (p = this.cache) === null || p === void 0 ? void 0 : p.restore();
              }, d;
            }()
          );
          n.default = g;
        },
        /* 429 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(99)), v = r.__importDefault(t(431)), g = t(432), d = (
            /** @class */
            function(p) {
              r.__extends(f, p);
              function f(i) {
                var u = p.call(this, i.config.historyMaxSize) || this;
                return u.editor = i, u;
              }
              return f.prototype.observe = function() {
                this.resetMaxSize(this.editor.config.historyMaxSize);
              }, f.prototype.compile = function(i) {
                return this.save(v.default(i)), this;
              }, f.prototype.revoke = function() {
                return p.prototype.revoke.call(this, function(i) {
                  g.revoke(i);
                });
              }, f.prototype.restore = function() {
                return p.prototype.restore.call(this, function(i) {
                  g.restore(i);
                });
              }, f;
            }(o.default)
          );
          n.default = d;
        },
        /* 430 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.CeilStack = void 0;
          var r = (
            /** @class */
            function() {
              function o(v) {
                v === void 0 && (v = 0), this.data = [], this.max = 0, this.reset = false, v = Math.abs(v), v && (this.max = v);
              }
              return o.prototype.resetMax = function(v) {
                v = Math.abs(v), !this.reset && !isNaN(v) && (this.max = v, this.reset = true);
              }, (0, a.default)(o.prototype, "size", {
                /**
                 * 当前栈中的数据条数
                 */
                get: function() {
                  return this.data.length;
                },
                enumerable: false,
                configurable: true
              }), o.prototype.instack = function(v) {
                return this.data.unshift(v), this.max && this.size > this.max && (this.data.length = this.max), this;
              }, o.prototype.outstack = function() {
                return this.data.shift();
              }, o.prototype.clear = function() {
                return this.data.length = 0, this;
              }, o;
            }()
          );
          n.CeilStack = r;
        },
        /* 431 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(27));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.compliePosition = n.complieNodes = n.compileValue = n.compileType = void 0;
          var v = t(6);
          function g(m) {
            switch (m) {
              case "childList":
                return "node";
              case "attributes":
                return "attr";
              default:
                return "text";
            }
          }
          n.compileType = g;
          function d(m) {
            switch (m.type) {
              case "attributes":
                return m.target.getAttribute(m.attributeName) || "";
              case "characterData":
                return m.target.textContent;
              default:
                return "";
            }
          }
          n.compileValue = d;
          function p(m) {
            var c = {};
            return m.addedNodes.length && (c.add = v.toArray(m.addedNodes)), m.removedNodes.length && (c.remove = v.toArray(m.removedNodes)), c;
          }
          n.complieNodes = p;
          function f(m) {
            var c;
            return m.previousSibling ? c = {
              type: "before",
              target: m.previousSibling
            } : m.nextSibling ? c = {
              type: "after",
              target: m.nextSibling
            } : c = {
              type: "parent",
              target: m.target
            }, c;
          }
          n.compliePosition = f;
          var i = ["UL", "OL", "H1", "H2", "H3", "H4", "H5", "H6"];
          function u(m) {
            var c = [], h = false, A = [];
            return (0, r.default)(m).call(m, function(y, x) {
              var S = {
                type: g(y.type),
                target: y.target,
                attr: y.attributeName || "",
                value: d(y) || "",
                oldValue: y.oldValue || "",
                nodes: p(y),
                position: f(y)
              };
              if (c.push(S), !!v.UA.isFirefox) {
                if (h && y.addedNodes.length && y.addedNodes[0].nodeType == 1) {
                  var T = y.addedNodes[0], I = {
                    type: "node",
                    target: T,
                    attr: "",
                    value: "",
                    oldValue: "",
                    nodes: {
                      add: [h]
                    },
                    position: {
                      type: "parent",
                      target: T
                    }
                  };
                  (0, o.default)(i).call(i, T.nodeName) != -1 ? (I.nodes.add = v.toArray(T.childNodes), c.push(I)) : h.nodeType == 3 ? (l(T, A) && (I.nodes.add = v.toArray(T.childNodes)), c.push(I)) : (0, o.default)(i).call(i, y.target.nodeName) == -1 && l(T, A) && (I.nodes.add = v.toArray(T.childNodes), c.push(I));
                }
                S.type == "node" && y.removedNodes.length == 1 ? (h = y.removedNodes[0], A.push(h)) : (h = false, A.length = 0);
              }
            }), c;
          }
          n.default = u;
          function l(m, c) {
            for (var h = 0, A = c.length - 1; A > 0 && m.contains(c[A]); A--)
              h++;
            return h;
          }
        },
        /* 432 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(4)), o = e2(t(94));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.restore = n.revoke = void 0;
          function v(A, y) {
            var x = A.position.target;
            switch (A.position.type) {
              case "before":
                x.nextSibling ? (x = x.nextSibling, (0, r.default)(y).call(y, function(S) {
                  A.target.insertBefore(S, x);
                })) : (0, r.default)(y).call(y, function(S) {
                  A.target.appendChild(S);
                });
                break;
              case "after":
                (0, r.default)(y).call(y, function(S) {
                  A.target.insertBefore(S, x);
                });
                break;
              default:
                (0, r.default)(y).call(y, function(S) {
                  x.appendChild(S);
                });
                break;
            }
          }
          function g(A) {
            for (var y = 0, x = (0, o.default)(A.nodes); y < x.length; y++) {
              var S = x[y], T = S[0], I = S[1];
              switch (T) {
                case "add":
                  (0, r.default)(I).call(I, function(C) {
                    A.target.removeChild(C);
                  });
                  break;
                default: {
                  v(A, I);
                  break;
                }
              }
            }
          }
          function d(A) {
            var y = A.target;
            A.oldValue == null ? y.removeAttribute(A.attr) : y.setAttribute(A.attr, A.oldValue);
          }
          function p(A) {
            A.target.textContent = A.oldValue;
          }
          var f = {
            node: g,
            text: p,
            attr: d
          };
          function i(A) {
            for (var y = A.length - 1; y > -1; y--) {
              var x = A[y];
              f[x.type](x);
            }
          }
          n.revoke = i;
          function u(A) {
            for (var y = 0, x = (0, o.default)(A.nodes); y < x.length; y++) {
              var S = x[y], T = S[0], I = S[1];
              switch (T) {
                case "add": {
                  v(A, I);
                  break;
                }
                default: {
                  (0, r.default)(I).call(I, function(C) {
                    C.parentNode.removeChild(C);
                  });
                  break;
                }
              }
            }
          }
          function l(A) {
            A.target.textContent = A.value;
          }
          function m(A) {
            A.target.setAttribute(A.attr, A.value);
          }
          var c = {
            node: u,
            text: l,
            attr: m
          };
          function h(A) {
            for (var y = 0, x = A; y < x.length; y++) {
              var S = x[y];
              c[S.type](S);
            }
          }
          n.restore = h;
        },
        /* 433 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(434), o = (
            /** @class */
            function() {
              function v(g) {
                this.editor = g, this.data = new r.TailChain();
              }
              return v.prototype.observe = function() {
                this.data.resetMax(this.editor.config.historyMaxSize), this.data.insertLast(this.editor.$textElem.html());
              }, v.prototype.save = function() {
                return this.data.insertLast(this.editor.$textElem.html()), this;
              }, v.prototype.revoke = function() {
                var g = this.data.prev();
                return g ? (this.editor.$textElem.html(g), true) : false;
              }, v.prototype.restore = function() {
                var g = this.data.next();
                return g ? (this.editor.$textElem.html(g), true) : false;
              }, v;
            }()
          );
          n.default = o;
        },
        /* 434 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(91));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.TailChain = void 0;
          var o = (
            /** @class */
            function() {
              function v() {
                this.data = [], this.max = 0, this.point = 0, this.isRe = false;
              }
              return v.prototype.resetMax = function(g) {
                g = Math.abs(g), g && (this.max = g);
              }, (0, a.default)(v.prototype, "size", {
                /**
                 * 当前链表的长度
                 */
                get: function() {
                  return this.data.length;
                },
                enumerable: false,
                configurable: true
              }), v.prototype.insertLast = function(g) {
                if (this.isRe) {
                  var d;
                  (0, r.default)(d = this.data).call(d, this.point + 1), this.isRe = false;
                }
                for (this.data.push(g); this.max && this.size > this.max; )
                  this.data.shift();
                return this.point = this.size - 1, this;
              }, v.prototype.current = function() {
                return this.data[this.point];
              }, v.prototype.prev = function() {
                if (!this.isRe && (this.isRe = true), this.point--, this.point < 0) {
                  this.point = 0;
                  return;
                }
                return this.current();
              }, v.prototype.next = function() {
                if (!this.isRe && (this.isRe = true), this.point++, this.point >= this.size) {
                  this.point = this.size - 1;
                  return;
                }
                return this.current();
              }, v;
            }()
          );
          n.TailChain = o;
        },
        /* 435 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(99)), v = (
            /** @class */
            function(g) {
              r.__extends(d, g);
              function d(p) {
                var f = g.call(this, p.config.historyMaxSize) || this;
                return f.editor = p, f.last = 0, f.target = p.$textElem.elems[0], f;
              }
              return d.prototype.observe = function() {
                var p = this;
                this.target = this.editor.$textElem.elems[0], this.editor.$textElem.on("scroll", function() {
                  p.last = p.target.scrollTop;
                }), this.resetMaxSize(this.editor.config.historyMaxSize);
              }, d.prototype.save = function() {
                return g.prototype.save.call(this, [this.last, this.target.scrollTop]), this;
              }, d.prototype.revoke = function() {
                var p = this;
                return g.prototype.revoke.call(this, function(f) {
                  p.target.scrollTop = f[0];
                });
              }, d.prototype.restore = function() {
                var p = this;
                return g.prototype.restore.call(this, function(f) {
                  p.target.scrollTop = f[1];
                });
              }, d;
            }(o.default)
          );
          n.default = v;
        },
        /* 436 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = t(2), o = r.__importDefault(t(99)), v = r.__importDefault(t(3)), g = t(6);
          function d(f) {
            return {
              start: [f.startContainer, f.startOffset],
              end: [f.endContainer, f.endOffset],
              root: f.commonAncestorContainer,
              collapsed: f.collapsed
            };
          }
          var p = (
            /** @class */
            function(f) {
              r.__extends(i, f);
              function i(u) {
                var l = f.call(this, u.config.historyMaxSize) || this;
                return l.editor = u, l.lastRange = d(document.createRange()), l.root = u.$textElem.elems[0], l.updateLastRange = g.debounce(function() {
                  l.lastRange = d(l.rangeHandle);
                }, u.config.onchangeTimeout), l;
              }
              return (0, a.default)(i.prototype, "rangeHandle", {
                /**
                 * 获取 Range 对象
                 */
                get: function() {
                  var l = document.getSelection();
                  return l && l.rangeCount ? l.getRangeAt(0) : document.createRange();
                },
                enumerable: false,
                configurable: true
              }), i.prototype.observe = function() {
                var u = this;
                this.root = this.editor.$textElem.elems[0], this.resetMaxSize(this.editor.config.historyMaxSize);
                function l() {
                  var c = u.rangeHandle;
                  (u.root === c.commonAncestorContainer || u.root.contains(c.commonAncestorContainer)) && (u.editor.isComposing || u.updateLastRange());
                }
                function m(c) {
                  (c.key == "Backspace" || c.key == "Delete") && u.updateLastRange();
                }
                v.default(document).on("selectionchange", l), this.editor.beforeDestroy(function() {
                  v.default(document).off("selectionchange", l);
                }), u.editor.$textElem.on("keydown", m);
              }, i.prototype.save = function() {
                var u = d(this.rangeHandle);
                return f.prototype.save.call(this, [this.lastRange, u]), this.lastRange = u, this;
              }, i.prototype.set = function(u) {
                try {
                  if (u) {
                    var l = this.rangeHandle;
                    return l.setStart.apply(l, u.start), l.setEnd.apply(l, u.end), this.editor.menus.changeActive(), true;
                  }
                } catch {
                  return false;
                }
                return false;
              }, i.prototype.revoke = function() {
                var u = this;
                return f.prototype.revoke.call(this, function(l) {
                  u.set(l[0]);
                });
              }, i.prototype.restore = function() {
                var u = this;
                return f.prototype.restore.call(this, function(l) {
                  u.set(l[1]);
                });
              }, i;
            }(o.default)
          );
          n.default = p;
        },
        /* 437 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(29));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var o = t(2), v = o.__importDefault(t(3));
          t(438);
          function g(d) {
            var p = false, f, i;
            d.txt.eventHooks.changeEvents.push(function() {
              p && (0, r.default)(f).call(f, ".w-e-content-preview").html(d.$textElem.html());
            });
            function u() {
              if (!p) {
                d.$textElem.hide();
                var m = d.zIndex.get("textContainer"), c = d.txt.html();
                f = v.default('<div class="w-e-content-mantle" style="z-index:' + m + `">
                <div class="w-e-content-preview w-e-text">` + c + `</div>
            </div>`), d.$textContainerElem.append(f);
                var h = d.zIndex.get("menu");
                i = v.default('<div class="w-e-menue-mantle" style="z-index:' + h + '"></div>'), d.$toolbarElem.append(i), p = true, d.isEnable = false;
              }
            }
            function l() {
              p && (f.remove(), i.remove(), d.$textElem.show(), p = false, d.isEnable = true);
            }
            return {
              disable: u,
              enable: l
            };
          }
          n.default = g;
        },
        /* 438 */
        /***/
        function(s, n, t) {
          var e2 = t(20), a = t(439);
          a = a.__esModule ? a.default : a, typeof a == "string" && (a = [[s.i, a, ""]]);
          var r = {};
          r.insert = "head", r.singleton = false, e2(a, r), s.exports = a.locals || {};
        },
        /* 439 */
        /***/
        function(s, n, t) {
          var e2 = t(21);
          n = e2(false), n.push([s.i, `.w-e-content-mantle {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
.w-e-content-mantle .w-e-content-preview {
  width: 100%;
  min-height: 100%;
  padding: 0 10px;
  line-height: 1.5;
}
.w-e-content-mantle .w-e-content-preview img {
  cursor: default;
}
.w-e-content-mantle .w-e-content-preview img:hover {
  box-shadow: none;
}
.w-e-menue-mantle {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
}
`, ""]), s.exports = n;
        },
        /* 440 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
          var r = (
            /** @class */
            function() {
              function o(v) {
                var g = this;
                this.editor = v;
                var d = function() {
                  var f = document.activeElement;
                  f === v.$textElem.elems[0] && g.emit();
                };
                window.document.addEventListener("selectionchange", d), this.editor.beforeDestroy(function() {
                  window.document.removeEventListener("selectionchange", d);
                });
              }
              return o.prototype.emit = function() {
                var v, g = this.editor.config.onSelectionChange;
                if (g) {
                  var d = this.editor.selection;
                  d.saveRange(), d.isSelectionEmpty() || g({
                    // 当前文本
                    text: d.getSelectionText(),
                    // 当前的html
                    html: (v = d.getSelectionContainerElem()) === null || v === void 0 ? void 0 : v.elems[0].innerHTML,
                    // select对象
                    selection: d
                  });
                }
              }, o;
            }()
          );
          n.default = r;
        },
        /* 441 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1)), r = e2(t(128)), o = e2(t(94)), v = e2(t(4));
          (0, a.default)(n, "__esModule", {
            value: true
          }), n.registerPlugin = void 0;
          var g = t(2), d = g.__importDefault(t(87)), p = t(6);
          function f(u, l, m) {
            if (!u)
              throw new TypeError("name is not define");
            if (!l)
              throw new TypeError("options is not define");
            if (!l.intention)
              throw new TypeError("options.intention is not define");
            if (l.intention && typeof l.intention != "function")
              throw new TypeError("options.intention is not function");
            m[u], m[u] = l;
          }
          n.registerPlugin = f;
          function i(u) {
            var l = (0, r.default)({}, p.deepClone(d.default.globalPluginsFunctionList), p.deepClone(u.pluginsFunctionList)), m = (0, o.default)(l);
            (0, v.default)(m).call(m, function(c) {
              c[0];
              var h = c[1], A = h.intention, y = h.config;
              A(u, y);
            });
          }
          n.default = i;
        },
        /* 442 */
        /***/
        function(s, n, t) {
          var e2 = t(0), a = e2(t(1));
          (0, a.default)(n, "__esModule", {
            value: true
          });
        }
        /******/
      ]).default
    );
  });
})(Nt);
var zt = Nt.exports;
var Vt = e(zt);
var Gt = {
  menus: [
    "head",
    "bold",
    "fontSize",
    "fontName",
    "italic",
    "underline",
    "strikeThrough",
    "indent",
    "lineHeight",
    "foreColor",
    "backColor",
    "link",
    "list",
    "todo",
    "justify",
    "quote",
    "emoticon",
    "image",
    "video",
    "table",
    "code",
    "splitLine",
    "undo",
    "redo"
  ],
  // 不显示的菜单
  excludeMenus: [],
  // 隐藏菜单栏提示
  showMenuTooltips: false,
  // 颜色
  colors: ["#000000", "#eeece0", "#1c487f", "#4d80bf"],
  // 配置字体
  fontNames: [
    "黑体",
    "仿宋",
    "楷体",
    "标楷体",
    "华文仿宋",
    "华文楷体",
    "宋体",
    "微软雅黑",
    "Arial",
    "Tahoma",
    "Verdana",
    "Times New Roman",
    "Courier New"
  ],
  // 字号
  fontSizes: {
    "x-small": { name: "10px", value: "1" },
    small: { name: "13px", value: "2" },
    normal: { name: "16px", value: "3" },
    large: { name: "18px", value: "4" },
    "x-large": { name: "24px", value: "5" },
    "xx-large": { name: "32px", value: "6" },
    "xxx-large": { name: "48px", value: "7" }
  },
  // 行高
  lineHeights: ["1", "1.15", "1.6", "2", "2.5", "3"],
  // 粘贴过滤
  pasteFilterStyle: true,
  // 忽略粘贴内容中的图片
  pasteIgnoreImg: true,
  // 是否带cookie
  withCredentials: true,
  // 上传图片接口 如果不配置这个设置，那么就不会开启远程上传功能
  // uploadImgServer = '',
  // 上传图片字段名
  uploadFileName: "file",
  // 最多可以上传2M的图
  uploadImgMaxSize: 2 * 1024 * 1024,
  // 图片类型
  uploadImgAccept: ["jpg", "jpeg", "png", "gif", "bmp"],
  // 一次最多上传 1 个图片
  uploadImgMaxLength: 5,
  // 超时时间
  uploadImgTimeout: 5 * 1e3
};
var Kt = defineComponent({
  name: "FsEditorWang",
  props: {
    modelValue: {
      type: String,
      required: false,
      default: ""
    },
    config: {
      type: Object
    },
    /**
     * 同一个页面多个edit时，需要配置不同的id
     */
    id: {
      default: "1"
    },
    /**
     * uploader参数，配置则开启上传
     */
    uploader: {
      type: Object
    },
    disabled: {
      type: Boolean
    }
  },
  emits: ["update:modelValue", "change", "ready"],
  data() {
    return {
      editor: null,
      currentValue: "",
      options: {}
    };
  },
  computed: {
    uniqueId() {
      return "fs-wang-editor-" + this.id;
    }
  },
  watch: {
    modelValue: {
      handler(et) {
        et !== this.currentValue && (this.currentValue = et, this.editor && this.editor.txt.html(et));
      },
      immediate: true
    },
    disabled: {
      handler(et) {
        this.setDisabled(et);
      },
      immediate: true
    }
  },
  mounted() {
    this.init();
  },
  beforeUnmount() {
    this.editor.destroy(), this.editor = null;
  },
  methods: {
    init() {
      let et = null;
      try {
        et = new Vt("#" + this.uniqueId);
      } catch (ot) {
        console.error(ot);
        return;
      }
      if (merge_default(et.config, Gt, Me.wangEditor, this.config), et.config.onchange = (ot) => {
        this.$emit("update:modelValue", ot), this.$emit("change", ot), this.currentValue = ot;
      }, this.uploader) {
        const ot = async (s, n) => {
          var g;
          const t = {
            status: "uploading",
            progress: 0
          }, e2 = (d) => {
            t.progress = d.percent;
          }, a = (d) => {
            t.status = "error", t.message = "文件上传出错:" + d.message, console.error(t.message, d);
          }, r = {
            file: s,
            fileName: s.name,
            onProgress: e2,
            onError: a
          }, o = await this.doUpload(r);
          let v = o == null ? void 0 : o.url;
          (g = this.uploader) != null && g.buildUrl && (v = await this.uploader.buildUrl(o)), n(v);
        };
        et.config.customUploadImg = async (s, n) => {
          forEach_default(s, (t) => {
            ot(t, n);
          });
        };
      }
      et.create(), et.txt.html(this.currentValue), this.editor = et, this.setDisabled(this.disabled), this.$emit("ready", { editor: et });
    },
    async doUpload(et) {
      et.options = this.uploader;
      const { getUploaderImpl: ot } = bt();
      let s = await ot(et.options.type);
      if (s == null)
        throw new Error("Sorry，The component is not ready yet");
      return await (s == null ? void 0 : s.upload(et));
    },
    setDisabled(et = false) {
      this.editor && (et === true ? this.editor.disable() : this.editor.enable());
    }
  }
});
var Jt = { class: "fs-editor-wang" };
var Wt = ["id"];
function Xt(et, ot, s, n, t, e2) {
  return openBlock(), createElementBlock("div", Jt, [
    createBaseVNode("div", { id: et.uniqueId }, null, 8, Wt),
    withDirectives(createBaseVNode("textarea", {
      "onUpdate:modelValue": ot[0] || (ot[0] = (a) => et.currentValue = a),
      class: "fs-editor-wang-preview",
      readonly: ""
    }, null, 512), [
      [vModelText, et.currentValue]
    ])
  ]);
}
var ee = he(Kt, [["render", Xt]]);
export {
  ee as default
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/index-47ee27c0.mjs:
  (*! *****************************************************************************
  	Copyright (c) Microsoft Corporation.
  
  	Permission to use, copy, modify, and/or distribute this software for any
  	purpose with or without fee is hereby granted.
  
  	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  	PERFORMANCE OF THIS SOFTWARE.
  	***************************************************************************** *)
*/
//# sourceMappingURL=index-47ee27c0-OWBWK5H4.js.map
