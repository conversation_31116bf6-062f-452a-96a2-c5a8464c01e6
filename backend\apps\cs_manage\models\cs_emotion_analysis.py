from apps.kcs.models import Game, Link
from dvadmin.system.models import Users
from dvadmin.utils.models import CoreModel, table_prefix
from django.db import models

table_prefix = table_prefix + "cs_manage_"

class CsEmotionAnalysis(CoreModel):
    """客服会话情绪分析主表"""
    session_id = models.CharField(max_length=512, verbose_name="会话ID", help_text="七鱼会话ID", db_index=True, unique=True)
    
    # 客服信息
    service_id = models.CharField(max_length=255, verbose_name="客服ID", help_text="客服ID", db_index=True)
    service_name = models.CharField(max_length=255, verbose_name="客服姓名", help_text="客服姓名")
    service_account = models.CharField(max_length=255, verbose_name="客服账号", help_text="客服账号", null=True, blank=True)
    service_user = models.ForeignKey(
        Users,
        on_delete=models.SET_NULL,
        verbose_name="关联系统用户",
        help_text="通过姓名关联的系统用户",
        null=True, blank=True,
        related_name='emotion_analyses'
    )
    
    # 关联信息
    game = models.ForeignKey(Game, on_delete=models.SET_NULL, verbose_name="游戏", help_text="关联游戏", null=True, blank=True)
    link = models.ForeignKey(Link, on_delete=models.SET_NULL, verbose_name="关联Link", help_text="关联的文章链接", null=True, blank=True, related_name='emotion_analysis')
    
    # 情绪分析结果
    initial_emotion_score = models.FloatField(verbose_name="初始情绪分数", help_text="用户初始情绪分数(-10到10)", null=True, blank=True)
    initial_emotion_justification = models.TextField(verbose_name="初始情绪分析理由", help_text="LLM分析初始情绪的理由", blank=True)
    
    final_emotion_score = models.FloatField(verbose_name="最终情绪分数", help_text="用户最终情绪分数(-10到10)", null=True, blank=True)
    final_emotion_justification = models.TextField(verbose_name="最终情绪分析理由", help_text="LLM分析最终情绪的理由", blank=True)
    
    emotion_change_score = models.FloatField(verbose_name="情绪变化分数", help_text="最终情绪分数 - 初始情绪分数", null=True, blank=True)
    overall_assessment = models.TextField(verbose_name="整体评估", help_text="LLM对客服表现的整体评估", blank=True)
    
    # 会话统计信息
    session_start_time = models.DateTimeField(null=True, blank=True, verbose_name="会话开始时间")
    session_end_time = models.DateTimeField(null=True, blank=True, verbose_name="会话结束时间")
    session_duration = models.IntegerField(default=0, verbose_name="会话时长", help_text="会话时长(秒)")
    message_count = models.IntegerField(default=0, verbose_name="消息总数", help_text="会话中的消息总数")
    user_message_count = models.IntegerField(default=0, verbose_name="用户消息数", help_text="用户发送的消息数")
    service_message_count = models.IntegerField(default=0, verbose_name="客服消息数", help_text="客服发送的消息数")
    
    # 分析详情和元数据
    analysis_details = models.JSONField(null=True, blank=True, verbose_name="完整分析结果", help_text="LLM返回的完整分析结果")
    conversation_summary = models.TextField(verbose_name="会话摘要", help_text="会话内容摘要", blank=True)
    emotion_keywords = models.JSONField(default=list, blank=True, verbose_name="情绪关键词", help_text="提取的情绪相关关键词")
    structured_conversation = models.JSONField(null=True, blank=True, verbose_name="结构化会话数据", help_text="用户问题、客服行为、解决方案等结构化数据")
    
    # 处理状态
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '处理失败'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="处理状态")
    error_message = models.TextField(verbose_name="错误信息", help_text="处理失败时的错误信息", blank=True)
    group_name = models.CharField(max_length=128, blank=True, verbose_name="客服组名称", help_text="七鱼fromGroup字段")
    
    class Meta:
        db_table = table_prefix + "cs_emotion_analysis"
        verbose_name = "客服会话情绪分析"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['service_id']),
            models.Index(fields=['emotion_change_score']),
            models.Index(fields=['create_datetime']),
            models.Index(fields=['game']),
            models.Index(fields=['status']),
            models.Index(fields=['session_start_time']),
        ]

    def get_current_link(self):
        """
        获取当前关联的Link（软关联）
        优先返回硬关联，无则动态查找
        """
        if self.link:
            return self.link
        
        try:
            return Link.objects.filter(ticket_id=self.session_id).first()
        except Exception:
            return None


    def get_all_related_links(self):
        """
        获取所有相关的Link记录
        """
        return Link.objects.filter(ticket_id=self.session_id)

    def refresh_associations(self):
        """
        刷新关联关系（将软关联的记录转为硬关联）
        当有新的Link或质检记录创建后，可调用此方法更新硬关联
        
        Returns:
            bool: 是否有更新
        """
        updated = False
        update_fields = []
        
        # 更新Link关联
        if not self.link:
            current_link = self.get_current_link()
            if current_link and current_link != self.link:
                self.link = current_link
                update_fields.append('link')
                updated = True
        

        # 保存更新
        if updated:
            self.save(update_fields=update_fields)
            
        return updated

    @property 
    def related_link(self):
        """
        软关联Link属性（优先硬关联，无则动态查找）
        """
        return self.get_current_link()



class CsEmotionAnalysisDetail(CoreModel):
    """客服会话情绪分析详情表 - 存储分阶段的情绪分析"""
    emotion_analysis = models.ForeignKey(
        CsEmotionAnalysis,
        on_delete=models.CASCADE,
        related_name='details',
        verbose_name="情绪分析记录"
    )
    
    # 分析阶段
    PHASE_CHOICES = [
        ('initial', '初始阶段'),
        ('middle', '中间阶段'),
        ('final', '最终阶段'),
    ]
    phase = models.CharField(max_length=20, choices=PHASE_CHOICES, verbose_name="分析阶段")
    
    # 时间范围
    start_message_index = models.IntegerField(verbose_name="起始消息索引", help_text="该阶段分析的起始消息索引")
    end_message_index = models.IntegerField(verbose_name="结束消息索引", help_text="该阶段分析的结束消息索引")
    
    # 情绪分析结果
    emotion_score = models.FloatField(verbose_name="情绪分数", help_text="该阶段的情绪分数(-10到10)")
    emotion_description = models.CharField(max_length=100, verbose_name="情绪描述", help_text="情绪的文字描述")
    confidence_level = models.FloatField(verbose_name="置信度", help_text="分析结果的置信度(0-1)", default=0.5)
    
    # 关键证据
    key_messages = models.JSONField(default=list, verbose_name="关键消息", help_text="影响情绪判断的关键消息")
    emotion_triggers = models.JSONField(default=list, verbose_name="情绪触发因素", help_text="导致情绪变化的触发因素")
    
    class Meta:
        db_table = table_prefix + "cs_emotion_analysis_detail"
        verbose_name = "客服会话情绪分析详情"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['emotion_analysis', 'phase']),
            models.Index(fields=['emotion_score']),
        ]
        unique_together = ('emotion_analysis', 'phase')