import "./chunk-6PRCX2O7.js";
import {
  Fragment,
  Teleport,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentScope,
  inject,
  isRef,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  onScopeDispose,
  onUpdated,
  openBlock,
  provide,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  shallowRef,
  toDisplayString,
  toRef,
  toRefs,
  unref,
  vShow,
  watch,
  watchEffect,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/e-icon-picker/index.mjs
var Dt;
var te = typeof window < "u";
var jn = (e) => typeof e == "string";
var Fn = () => {
};
te && ((Dt = window == null ? void 0 : window.navigator) != null && Dt.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);
function qn(e) {
  return typeof e == "function" ? e() : unref(e);
}
function Yn(e) {
  return e;
}
function Un(e) {
  return getCurrentScope() ? (onScopeDispose(e), true) : false;
}
function Xn(e) {
  var t;
  const n = qn(e);
  return (t = n == null ? void 0 : n.$el) != null ? t : n;
}
var Gn = te ? window : void 0;
function Bt(...e) {
  let t, n, o, r;
  if (jn(e[0]) || Array.isArray(e[0]) ? ([n, o, r] = e, t = Gn) : [t, n, o, r] = e, !t)
    return Fn;
  Array.isArray(n) || (n = [n]), Array.isArray(o) || (o = [o]);
  const i = [], l = () => {
    i.forEach((c) => c()), i.length = 0;
  }, s = (c, h, b) => (c.addEventListener(h, b, r), () => c.removeEventListener(h, b, r)), a = watch(() => Xn(t), (c) => {
    l(), c && i.push(...n.flatMap((h) => o.map((b) => s(c, h, b))));
  }, { immediate: true, flush: "post" }), f = () => {
    a(), l();
  };
  return Un(f), f;
}
var it = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
var st = "__vueuse_ssr_handlers__";
it[st] = it[st] || {};
it[st];
var Rt;
(function(e) {
  e.UP = "UP", e.RIGHT = "RIGHT", e.DOWN = "DOWN", e.LEFT = "LEFT", e.NONE = "NONE";
})(Rt || (Rt = {}));
var Kn = Object.defineProperty;
var zt = Object.getOwnPropertySymbols;
var Qn = Object.prototype.hasOwnProperty;
var Zn = Object.prototype.propertyIsEnumerable;
var Mt = (e, t, n) => t in e ? Kn(e, t, { enumerable: true, configurable: true, writable: true, value: n }) : e[t] = n;
var Jn = (e, t) => {
  for (var n in t || (t = {}))
    Qn.call(t, n) && Mt(e, n, t[n]);
  if (zt)
    for (var n of zt(t))
      Zn.call(t, n) && Mt(e, n, t[n]);
  return e;
};
var eo = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
Jn({
  linear: Yn
}, eo);
var la = (e) => {
  let t = "", n = "", o = [];
  return e && (e.font_family && (t = e.font_family), e.css_prefix_text && (n = e.css_prefix_text), e.glyphs && (o = e.glyphs.map((r) => t + " " + n + r.font_class))), {
    font_family: t,
    css_prefix_text: n,
    list: o
  };
};
var ca = (e) => {
  let t = "", n = "", o = [];
  return e && (e.font_family && (t = e.font_family), e.css_prefix_text && (n = e.css_prefix_text), e.glyphs && (o = e.glyphs.map((r) => "#" + n + r.font_class))), {
    font_family: t,
    css_prefix_text: n,
    list: o
  };
};
function He(e) {
  return /^(https?:|data:|\/\/?)/.test(e);
}
var to = function() {
  return te && document && document.addEventListener ? (e, t, n) => {
    e && t && n && e.addEventListener(t, n, false);
  } : (e, t, n) => {
    e && t && n && e.attachEvent("on" + t, n);
  };
}();
var Vt = function() {
  return te && document && document.removeEventListener ? function(e, t, n) {
    e && t && e.removeEventListener(t, n, false);
  } : function(e, t, n) {
    e && t && e.detachEvent("on" + t, n);
  };
}();
var cn = (e) => typeof e == "object" && e.constructor === Array;
var Ze = (e) => typeof e == "string" && e.constructor === String;
var $e = (e) => typeof e == "number" && e.constructor === Number;
var no = (e) => typeof e == "object" && e.constructor === Object;
var oo = (e, t) => {
  let n = [];
  return t && cn(t) ? n = e.concat(t) : t && Ze(t) && (n = n.concat(e), typeof t == "string" && n.push(t)), n;
};
var ro = function(e, t) {
  if (t && cn(t))
    for (let n = 0; n < t.length; n++)
      for (let o = 0; o < e.length; o++)
        e[o] === t[n] && (e.splice(o, 1), o--);
  else
    t && Ze(t) && (e = e.filter((n) => n !== t));
  return e;
};
var lt = {
  list: [],
  addIcon: function(e) {
    this.list = oo(this.list, e);
  },
  removeIcon: function(e) {
    this.list = ro(this.list, e);
  }
};
te && function(e, t, n) {
  !e.composedPath && n && (e.composedPath = function() {
    if (this.path)
      return this.path;
    let o = this.target;
    for (this.path = []; o.parentNode !== null; )
      this.path.push(o), o = o.parentNode;
    return this.path.push(t, n), this.path;
  }), String.prototype.startsWith || Object.defineProperty(String.prototype, "startsWith", {
    value: function(o, r) {
      return r = !r || r < 0 ? 0 : +r, this.substring(r, r + o.length) === o;
    }
  });
}(Event.prototype, document, window);
var qe = "update:modelValue";
var Ye = "change";
var Ue = "input";
var ao = "clear";
var io = "focus";
var so = "blur";
var lo = "mouseleave";
var co = "mouseenter";
var uo = "scroll";
var _t = "click";
var fo = "close:popper";
var po = "open:popper";
var vo = "2.1.1";
var Wt = Symbol("INSTALLED_KEY");
var mo = defineComponent({
  name: "e-icon",
  props: {
    iconName: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ""
    }
  },
  emits: [_t],
  setup(e, t) {
    return {
      click: (o, r) => {
        r && r.preventDefault(), t.emit(_t, o);
      }
    };
  },
  computed: {
    fontClass() {
      return this.iconName && this.iconName.trim().length > 2 && !He(this.iconName) && !this.iconName.startsWith("#") && !this.iconName.startsWith("component ");
    },
    svg() {
      return this.iconName && this.iconName.trim().length > 2 && !He(this.iconName) && this.iconName.startsWith("#");
    },
    isComponent() {
      return this.iconName && this.iconName.trim().length > 2 && !He(this.iconName) && this.iconName.startsWith("component ");
    },
    component() {
      return this.iconName.replace("component ", "");
    },
    isExternal() {
      return He(this.iconName);
    },
    svgClass() {
      return this.className ? "icon " + this.className : "icon";
    },
    styleExternalIcon() {
      return {
        "background-image": `url(${this.iconName})`,
        "background-repeat": "no-repeat",
        "background-size": "100% 100%",
        "-moz-background-size": "100% 100%"
      };
    }
  }
});
var ce = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
};
var ho = ["xlink:href"];
function yo(e, t, n, o, r, i) {
  return e.fontClass ? (openBlock(), createElementBlock("i", {
    key: 0,
    class: normalizeClass(["e-icon", [e.iconName, e.className]]),
    onClick: t[0] || (t[0] = (l) => e.click(e.iconName, l))
  }, null, 2)) : e.svg ? (openBlock(), createElementBlock("svg", {
    key: 1,
    class: normalizeClass([e.svgClass, "e-icon e-icon-svg"]),
    "aria-hidden": "true",
    onClick: t[1] || (t[1] = (l) => e.click(e.iconName, l))
  }, [
    createBaseVNode("use", { "xlink:href": e.iconName }, null, 8, ho)
  ], 2)) : e.isComponent ? (openBlock(), createBlock(resolveDynamicComponent(e.component), {
    key: 2,
    class: "e-icon icon e-icon-svg",
    onClick: t[2] || (t[2] = (l) => e.click(e.iconName, l))
  })) : e.isExternal ? (openBlock(), createElementBlock("div", {
    key: 3,
    style: normalizeStyle(e.styleExternalIcon),
    class: normalizeClass([e.className, "e-icon icon external-icon"]),
    onClick: t[3] || (t[3] = (l) => e.click(e.iconName, l))
  }, null, 6)) : createCommentVNode("", true);
}
var Xe = ce(mo, [["render", yo], ["__scopeId", "data-v-8e177972"]]);
var go = {
  install(e) {
    e.component(Xe.name, Xe);
  }
};
var bo = defineComponent({
  name: "e-input",
  components: {
    eIcon: Xe
  },
  props: {
    prefixIcon: {
      type: String,
      default: "eiconfont e-icon-bi"
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ""
    },
    style: {
      type: Object,
      default: {}
    },
    clearable: {
      type: Boolean,
      default: true
    },
    modelValue: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "default",
      required: false
    }
  },
  setup(e, { emit: t }) {
    const n = reactive({
      prefixIcon: e.prefixIcon,
      focused: false,
      hovering: false
    }), o = shallowRef(), r = computed(() => o.value), i = computed(
      () => e.modelValue ? String(e.modelValue) : ""
    ), l = (u) => {
      let { value: v } = u.target;
      t(qe, v), t(Ue, v), d();
    }, s = (u) => {
      n.focused = true, t(io, u);
    }, a = (u) => {
      n.focused = false, t(so, u);
    }, f = (u) => {
      t(Ye, u.target.value);
    }, c = computed(
      () => e.clearable && !e.disabled && !e.readonly && !!i.value && (n.focused || n.hovering)
    ), h = (u) => {
      n.hovering = false, t(lo, u);
    }, b = (u) => {
      n.hovering = true, t(co, u);
    }, d = () => {
      const u = r.value;
      !u || u.value === i.value || (u.value = i.value);
    };
    return watch(i, () => d()), onMounted(async () => {
      d();
    }), {
      state: n,
      handleInput: l,
      handleFocus: s,
      handleBlur: a,
      handleChange: f,
      showClear: c,
      handleMouseLeave: h,
      handleMouseEnter: b,
      input: o,
      clear: () => {
        t(qe, ""), t(Ye, ""), t(ao), t(Ue, "");
      }
    };
  }
});
var wo = { class: "prefix-icon" };
var Eo = ["disabled", "readonly", "placeholder"];
var Oo = {
  t: "1657525825723",
  class: "icon",
  viewBox: "0 0 1024 1024",
  version: "1.1",
  xmlns: "http://www.w3.org/2000/svg"
};
var Io = ["fill"];
function So(e, t, n, o, r, i) {
  const l = resolveComponent("e-icon");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["e-input", `e-input--${e.size}`]),
    onMouseenter: t[5] || (t[5] = (...s) => e.handleMouseEnter && e.handleMouseEnter(...s)),
    onMouseleave: t[6] || (t[6] = (...s) => e.handleMouseLeave && e.handleMouseLeave(...s))
  }, [
    createBaseVNode("div", wo, [
      renderSlot(e.$slots, "prepend", {
        icon: e.state.prefixIcon
      }, () => [
        createVNode(l, {
          "icon-name": e.state.prefixIcon,
          class: "e-icon"
        }, null, 8, ["icon-name"])
      ], true)
    ]),
    createBaseVNode("input", {
      type: "text",
      ref: "input",
      class: normalizeClass(["e-input-inner", e.disabled ? "is-disabled" : ""]),
      disabled: e.disabled,
      readonly: e.readonly,
      placeholder: e.placeholder,
      style: normalizeStyle(e.style),
      onInput: t[0] || (t[0] = (...s) => e.handleInput && e.handleInput(...s)),
      onFocus: t[1] || (t[1] = (...s) => e.handleFocus && e.handleFocus(...s)),
      onBlur: t[2] || (t[2] = (...s) => e.handleBlur && e.handleBlur(...s)),
      onChange: t[3] || (t[3] = (...s) => e.handleChange && e.handleChange(...s))
    }, null, 46, Eo),
    e.showClear ? (openBlock(), createElementBlock("div", {
      key: 0,
      class: "suffix-icon",
      onClick: t[4] || (t[4] = withModifiers((...s) => e.clear && e.clear(...s), ["stop"]))
    }, [
      (openBlock(), createElementBlock("svg", Oo, [
        createTextVNode(' p-id="1823" width="200" height="200"> '),
        createBaseVNode("path", {
          d: "M466.986667 512L376.021333 421.973333a33.450667 33.450667 0 0 1-8.96-22.997333 30.72 30.72 0 0 1 9.514667-22.485333 30.72 30.72 0 0 1 22.485333-9.514667c8.661333 0 16.341333 2.986667 22.997334 8.96l90.026666 91.050667 90.026667-91.008c9.301333-8.661333 19.797333-11.349333 31.445333-8.021334a30.890667 30.890667 0 0 1 22.528 22.485334c3.328 11.690667 0.682667 22.186667-8.021333 31.530666L557.013333 512l91.008 89.984c8.661333 9.344 11.349333 19.84 8.021334 31.488a30.890667 30.890667 0 0 1-22.485334 22.485333c-11.690667 3.370667-22.186667 0.682667-31.530666-7.978666L512 556.970667l-89.984 91.008a33.450667 33.450667 0 0 1-23.04 8.96 30.72 30.72 0 0 1-22.485333-9.472 30.72 30.72 0 0 1-9.472-22.485334c0-8.704 2.986667-16.341333 8.96-23.04L466.986667 512zM512 896c108.672-2.688 199.168-40.192 271.488-112.512C855.808 711.168 893.312 620.672 896 512c-2.688-108.672-40.192-199.168-112.512-271.488C711.168 168.192 620.672 130.688 512 128c-108.672 2.688-199.168 40.192-271.488 112.512C168.192 312.874667 130.688 403.370667 128 512c2.688 108.672 40.192 199.168 112.512 271.488C312.874667 855.808 403.370667 893.312 512 896z m0 64c-126.677333-3.328-232.192-47.146667-316.501333-131.498667C111.146667 744.192 67.328 638.72 64 512c3.328-126.677333 47.146667-232.192 131.498667-316.501333C279.808 111.146667 385.28 67.328 512 64c126.677333 3.328 232.192 47.146667 316.501333 131.498667C912.853333 279.808 956.672 385.28 960 512c-3.328 126.677333-47.146667 232.192-131.498667 316.501333C744.192 912.853333 638.72 956.672 512 960z",
          "p-id": "1824",
          fill: e.state.focused ? "#606266" : "#C0C4CC"
        }, null, 8, Io)
      ]))
    ])) : createCommentVNode("", true)
  ], 34);
}
var ct = ce(bo, [["render", So], ["__scopeId", "data-v-d2b0f76c"]]);
var Po = {
  install(e) {
    e.component(ct.name, ct);
  }
};
var No = defineComponent({
  name: "e-arrow"
});
var Co = {
  ref: "arrowRef",
  class: "e-arrow",
  "data-popper-arrow": ""
};
function Lo(e, t, n, o, r, i) {
  return openBlock(), createElementBlock("span", Co, null, 512);
}
var $o = ce(No, [["render", Lo], ["__scopeId", "data-v-ce01e648"]]);
function xo(e, t, n) {
  e && isRef(e) ? watch(e, (o, r) => {
    r == null || r.removeEventListener(t, n), o == null || o.addEventListener(t, n);
  }) : onMounted(() => {
    e.addEventListener(t, n);
  }), onBeforeUnmount(() => {
    var o;
    (o = unref(e)) == null || o.removeEventListener(t, n);
  });
}
function ko(e, t) {
  const n = "pointerdown";
  return typeof window > "u" || !window ? void 0 : xo(window, n, (r) => {
    const i = unref(e);
    i && (i === r.target || r.composedPath().includes(i) || t(r));
  });
}
function To(e, t, n) {
  let o = null;
  const r = ref(false);
  onMounted(() => {
    (e.content !== void 0 || n.value) && (r.value = true), o = new MutationObserver(i), o.observe(t.value, {
      childList: true,
      subtree: true
    });
  }), onBeforeUnmount(() => o.disconnect()), watch(n, (l) => {
    r.value = !!l;
  });
  const i = () => {
    r.value = !!e.content;
  };
  return {
    hasContent: r
  };
}
var R = "top";
var _ = "bottom";
var W = "right";
var z = "left";
var Je = "auto";
var ze = [R, _, W, z];
var Ee = "start";
var Ae = "end";
var Ao = "clippingParents";
var un = "viewport";
var Le = "popper";
var Do = "reference";
var Ht = ze.reduce(function(e, t) {
  return e.concat([t + "-" + Ee, t + "-" + Ae]);
}, []);
var fn = [].concat(ze, [Je]).reduce(function(e, t) {
  return e.concat([t, t + "-" + Ee, t + "-" + Ae]);
}, []);
var Bo = "beforeRead";
var Ro = "read";
var zo = "afterRead";
var Mo = "beforeMain";
var Vo = "main";
var _o = "afterMain";
var Wo = "beforeWrite";
var Ho = "write";
var jo = "afterWrite";
var ut = [Bo, Ro, zo, Mo, Vo, _o, Wo, Ho, jo];
function K(e) {
  return e ? (e.nodeName || "").toLowerCase() : null;
}
function H(e) {
  if (e == null)
    return window;
  if (e.toString() !== "[object Window]") {
    var t = e.ownerDocument;
    return t && t.defaultView || window;
  }
  return e;
}
function he(e) {
  var t = H(e).Element;
  return e instanceof t || e instanceof Element;
}
function M(e) {
  var t = H(e).HTMLElement;
  return e instanceof t || e instanceof HTMLElement;
}
function yt(e) {
  if (typeof ShadowRoot > "u")
    return false;
  var t = H(e).ShadowRoot;
  return e instanceof t || e instanceof ShadowRoot;
}
function Fo(e) {
  var t = e.state;
  Object.keys(t.elements).forEach(function(n) {
    var o = t.styles[n] || {}, r = t.attributes[n] || {}, i = t.elements[n];
    !M(i) || !K(i) || (Object.assign(i.style, o), Object.keys(r).forEach(function(l) {
      var s = r[l];
      s === false ? i.removeAttribute(l) : i.setAttribute(l, s === true ? "" : s);
    }));
  });
}
function qo(e) {
  var t = e.state, n = {
    popper: {
      position: t.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  return Object.assign(t.elements.popper.style, n.popper), t.styles = n, t.elements.arrow && Object.assign(t.elements.arrow.style, n.arrow), function() {
    Object.keys(t.elements).forEach(function(o) {
      var r = t.elements[o], i = t.attributes[o] || {}, l = Object.keys(t.styles.hasOwnProperty(o) ? t.styles[o] : n[o]), s = l.reduce(function(a, f) {
        return a[f] = "", a;
      }, {});
      !M(r) || !K(r) || (Object.assign(r.style, s), Object.keys(i).forEach(function(a) {
        r.removeAttribute(a);
      }));
    });
  };
}
var Yo = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: Fo,
  effect: qo,
  requires: ["computeStyles"]
};
function Y(e) {
  return e.split("-")[0];
}
var me = Math.max;
var Ge = Math.min;
var Oe = Math.round;
function ft() {
  var e = navigator.userAgentData;
  return e != null && e.brands ? e.brands.map(function(t) {
    return t.brand + "/" + t.version;
  }).join(" ") : navigator.userAgent;
}
function pn() {
  return !/^((?!chrome|android).)*safari/i.test(ft());
}
function Ie(e, t, n) {
  t === void 0 && (t = false), n === void 0 && (n = false);
  var o = e.getBoundingClientRect(), r = 1, i = 1;
  t && M(e) && (r = e.offsetWidth > 0 && Oe(o.width) / e.offsetWidth || 1, i = e.offsetHeight > 0 && Oe(o.height) / e.offsetHeight || 1);
  var l = he(e) ? H(e) : window, s = l.visualViewport, a = !pn() && n, f = (o.left + (a && s ? s.offsetLeft : 0)) / r, c = (o.top + (a && s ? s.offsetTop : 0)) / i, h = o.width / r, b = o.height / i;
  return {
    width: h,
    height: b,
    top: c,
    right: f + h,
    bottom: c + b,
    left: f,
    x: f,
    y: c
  };
}
function gt(e) {
  var t = Ie(e), n = e.offsetWidth, o = e.offsetHeight;
  return Math.abs(t.width - n) <= 1 && (n = t.width), Math.abs(t.height - o) <= 1 && (o = t.height), {
    x: e.offsetLeft,
    y: e.offsetTop,
    width: n,
    height: o
  };
}
function dn(e, t) {
  var n = t.getRootNode && t.getRootNode();
  if (e.contains(t))
    return true;
  if (n && yt(n)) {
    var o = t;
    do {
      if (o && e.isSameNode(o))
        return true;
      o = o.parentNode || o.host;
    } while (o);
  }
  return false;
}
function U(e) {
  return H(e).getComputedStyle(e);
}
function Uo(e) {
  return ["table", "td", "th"].indexOf(K(e)) >= 0;
}
function ue(e) {
  return ((he(e) ? e.ownerDocument : e.document) || window.document).documentElement;
}
function et(e) {
  return K(e) === "html" ? e : e.assignedSlot || e.parentNode || (yt(e) ? e.host : null) || ue(e);
}
function jt(e) {
  return !M(e) || U(e).position === "fixed" ? null : e.offsetParent;
}
function Xo(e) {
  var t = /firefox/i.test(ft()), n = /Trident/i.test(ft());
  if (n && M(e)) {
    var o = U(e);
    if (o.position === "fixed")
      return null;
  }
  var r = et(e);
  for (yt(r) && (r = r.host); M(r) && ["html", "body"].indexOf(K(r)) < 0; ) {
    var i = U(r);
    if (i.transform !== "none" || i.perspective !== "none" || i.contain === "paint" || ["transform", "perspective"].indexOf(i.willChange) !== -1 || t && i.willChange === "filter" || t && i.filter && i.filter !== "none")
      return r;
    r = r.parentNode;
  }
  return null;
}
function Me(e) {
  for (var t = H(e), n = jt(e); n && Uo(n) && U(n).position === "static"; )
    n = jt(n);
  return n && (K(n) === "html" || K(n) === "body" && U(n).position === "static") ? t : n || Xo(e) || t;
}
function bt(e) {
  return ["top", "bottom"].indexOf(e) >= 0 ? "x" : "y";
}
function xe(e, t, n) {
  return me(e, Ge(t, n));
}
function Go(e, t, n) {
  var o = xe(e, t, n);
  return o > n ? n : o;
}
function vn() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}
function mn(e) {
  return Object.assign({}, vn(), e);
}
function hn(e, t) {
  return t.reduce(function(n, o) {
    return n[o] = e, n;
  }, {});
}
var Ko = function(t, n) {
  return t = typeof t == "function" ? t(Object.assign({}, n.rects, {
    placement: n.placement
  })) : t, mn(typeof t != "number" ? t : hn(t, ze));
};
function Qo(e) {
  var t, n = e.state, o = e.name, r = e.options, i = n.elements.arrow, l = n.modifiersData.popperOffsets, s = Y(n.placement), a = bt(s), f = [z, W].indexOf(s) >= 0, c = f ? "height" : "width";
  if (!(!i || !l)) {
    var h = Ko(r.padding, n), b = gt(i), d = a === "y" ? R : z, O = a === "y" ? _ : W, u = n.rects.reference[c] + n.rects.reference[a] - l[a] - n.rects.popper[c], v = l[a] - n.rects.reference[a], E = Me(i), S = E ? a === "y" ? E.clientHeight || 0 : E.clientWidth || 0 : 0, y = u / 2 - v / 2, m = h[d], w = S - b[c] - h[O], g = S / 2 - b[c] / 2 + y, p = xe(m, g, w), I = a;
    n.modifiersData[o] = (t = {}, t[I] = p, t.centerOffset = p - g, t);
  }
}
function Zo(e) {
  var t = e.state, n = e.options, o = n.element, r = o === void 0 ? "[data-popper-arrow]" : o;
  if (r != null && !(typeof r == "string" && (r = t.elements.popper.querySelector(r), !r))) {
    if (M(r) || console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).', "To use an SVG arrow, wrap it in an HTMLElement that will be used as", "the arrow."].join(" ")), !dn(t.elements.popper, r)) {
      console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper', "element."].join(" "));
      return;
    }
    t.elements.arrow = r;
  }
}
var yn = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: Qo,
  effect: Zo,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};
function Se(e) {
  return e.split("-")[1];
}
var Jo = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function er(e) {
  var t = e.x, n = e.y, o = window, r = o.devicePixelRatio || 1;
  return {
    x: Oe(t * r) / r || 0,
    y: Oe(n * r) / r || 0
  };
}
function Ft(e) {
  var t, n = e.popper, o = e.popperRect, r = e.placement, i = e.variation, l = e.offsets, s = e.position, a = e.gpuAcceleration, f = e.adaptive, c = e.roundOffsets, h = e.isFixed, b = l.x, d = b === void 0 ? 0 : b, O = l.y, u = O === void 0 ? 0 : O, v = typeof c == "function" ? c({
    x: d,
    y: u
  }) : {
    x: d,
    y: u
  };
  d = v.x, u = v.y;
  var E = l.hasOwnProperty("x"), S = l.hasOwnProperty("y"), y = z, m = R, w = window;
  if (f) {
    var g = Me(n), p = "clientHeight", I = "clientWidth";
    if (g === H(n) && (g = ue(n), U(g).position !== "static" && s === "absolute" && (p = "scrollHeight", I = "scrollWidth")), g = g, r === R || (r === z || r === W) && i === Ae) {
      m = _;
      var C = h && g === w && w.visualViewport ? w.visualViewport.height : g[p];
      u -= C - o.height, u *= a ? 1 : -1;
    }
    if (r === z || (r === R || r === _) && i === Ae) {
      y = W;
      var N = h && g === w && w.visualViewport ? w.visualViewport.width : g[I];
      d -= N - o.width, d *= a ? 1 : -1;
    }
  }
  var P = Object.assign({
    position: s
  }, f && Jo), $ = c === true ? er({
    x: d,
    y: u
  }) : {
    x: d,
    y: u
  };
  if (d = $.x, u = $.y, a) {
    var x;
    return Object.assign({}, P, (x = {}, x[m] = S ? "0" : "", x[y] = E ? "0" : "", x.transform = (w.devicePixelRatio || 1) <= 1 ? "translate(" + d + "px, " + u + "px)" : "translate3d(" + d + "px, " + u + "px, 0)", x));
  }
  return Object.assign({}, P, (t = {}, t[m] = S ? u + "px" : "", t[y] = E ? d + "px" : "", t.transform = "", t));
}
function tr(e) {
  var t = e.state, n = e.options, o = n.gpuAcceleration, r = o === void 0 ? true : o, i = n.adaptive, l = i === void 0 ? true : i, s = n.roundOffsets, a = s === void 0 ? true : s;
  if (true) {
    var f = U(t.elements.popper).transitionProperty || "";
    l && ["transform", "top", "right", "bottom", "left"].some(function(h) {
      return f.indexOf(h) >= 0;
    }) && console.warn(["Popper: Detected CSS transitions on at least one of the following", 'CSS properties: "transform", "top", "right", "bottom", "left".', `

`, 'Disable the "computeStyles" modifier\'s `adaptive` option to allow', "for smooth transitions, or remove these properties from the CSS", "transition declaration on the popper element if only transitioning", "opacity or background-color for example.", `

`, "We recommend using the popper element as a wrapper around an inner", "element that can have any CSS property transitioned for animations."].join(" "));
  }
  var c = {
    placement: Y(t.placement),
    variation: Se(t.placement),
    popper: t.elements.popper,
    popperRect: t.rects.popper,
    gpuAcceleration: r,
    isFixed: t.options.strategy === "fixed"
  };
  t.modifiersData.popperOffsets != null && (t.styles.popper = Object.assign({}, t.styles.popper, Ft(Object.assign({}, c, {
    offsets: t.modifiersData.popperOffsets,
    position: t.options.strategy,
    adaptive: l,
    roundOffsets: a
  })))), t.modifiersData.arrow != null && (t.styles.arrow = Object.assign({}, t.styles.arrow, Ft(Object.assign({}, c, {
    offsets: t.modifiersData.arrow,
    position: "absolute",
    adaptive: false,
    roundOffsets: a
  })))), t.attributes.popper = Object.assign({}, t.attributes.popper, {
    "data-popper-placement": t.placement
  });
}
var nr = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: tr,
  data: {}
};
var je = {
  passive: true
};
function or(e) {
  var t = e.state, n = e.instance, o = e.options, r = o.scroll, i = r === void 0 ? true : r, l = o.resize, s = l === void 0 ? true : l, a = H(t.elements.popper), f = [].concat(t.scrollParents.reference, t.scrollParents.popper);
  return i && f.forEach(function(c) {
    c.addEventListener("scroll", n.update, je);
  }), s && a.addEventListener("resize", n.update, je), function() {
    i && f.forEach(function(c) {
      c.removeEventListener("scroll", n.update, je);
    }), s && a.removeEventListener("resize", n.update, je);
  };
}
var rr = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function() {
  },
  effect: or,
  data: {}
};
var ar = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function Fe(e) {
  return e.replace(/left|right|bottom|top/g, function(t) {
    return ar[t];
  });
}
var ir = {
  start: "end",
  end: "start"
};
function qt(e) {
  return e.replace(/start|end/g, function(t) {
    return ir[t];
  });
}
function wt(e) {
  var t = H(e), n = t.pageXOffset, o = t.pageYOffset;
  return {
    scrollLeft: n,
    scrollTop: o
  };
}
function Et(e) {
  return Ie(ue(e)).left + wt(e).scrollLeft;
}
function sr(e, t) {
  var n = H(e), o = ue(e), r = n.visualViewport, i = o.clientWidth, l = o.clientHeight, s = 0, a = 0;
  if (r) {
    i = r.width, l = r.height;
    var f = pn();
    (f || !f && t === "fixed") && (s = r.offsetLeft, a = r.offsetTop);
  }
  return {
    width: i,
    height: l,
    x: s + Et(e),
    y: a
  };
}
function lr(e) {
  var t, n = ue(e), o = wt(e), r = (t = e.ownerDocument) == null ? void 0 : t.body, i = me(n.scrollWidth, n.clientWidth, r ? r.scrollWidth : 0, r ? r.clientWidth : 0), l = me(n.scrollHeight, n.clientHeight, r ? r.scrollHeight : 0, r ? r.clientHeight : 0), s = -o.scrollLeft + Et(e), a = -o.scrollTop;
  return U(r || n).direction === "rtl" && (s += me(n.clientWidth, r ? r.clientWidth : 0) - i), {
    width: i,
    height: l,
    x: s,
    y: a
  };
}
function Ot(e) {
  var t = U(e), n = t.overflow, o = t.overflowX, r = t.overflowY;
  return /auto|scroll|overlay|hidden/.test(n + r + o);
}
function gn(e) {
  return ["html", "body", "#document"].indexOf(K(e)) >= 0 ? e.ownerDocument.body : M(e) && Ot(e) ? e : gn(et(e));
}
function ke(e, t) {
  var n;
  t === void 0 && (t = []);
  var o = gn(e), r = o === ((n = e.ownerDocument) == null ? void 0 : n.body), i = H(o), l = r ? [i].concat(i.visualViewport || [], Ot(o) ? o : []) : o, s = t.concat(l);
  return r ? s : s.concat(ke(et(l)));
}
function pt(e) {
  return Object.assign({}, e, {
    left: e.x,
    top: e.y,
    right: e.x + e.width,
    bottom: e.y + e.height
  });
}
function cr(e, t) {
  var n = Ie(e, false, t === "fixed");
  return n.top = n.top + e.clientTop, n.left = n.left + e.clientLeft, n.bottom = n.top + e.clientHeight, n.right = n.left + e.clientWidth, n.width = e.clientWidth, n.height = e.clientHeight, n.x = n.left, n.y = n.top, n;
}
function Yt(e, t, n) {
  return t === un ? pt(sr(e, n)) : he(t) ? cr(t, n) : pt(lr(ue(e)));
}
function ur(e) {
  var t = ke(et(e)), n = ["absolute", "fixed"].indexOf(U(e).position) >= 0, o = n && M(e) ? Me(e) : e;
  return he(o) ? t.filter(function(r) {
    return he(r) && dn(r, o) && K(r) !== "body";
  }) : [];
}
function fr(e, t, n, o) {
  var r = t === "clippingParents" ? ur(e) : [].concat(t), i = [].concat(r, [n]), l = i[0], s = i.reduce(function(a, f) {
    var c = Yt(e, f, o);
    return a.top = me(c.top, a.top), a.right = Ge(c.right, a.right), a.bottom = Ge(c.bottom, a.bottom), a.left = me(c.left, a.left), a;
  }, Yt(e, l, o));
  return s.width = s.right - s.left, s.height = s.bottom - s.top, s.x = s.left, s.y = s.top, s;
}
function bn(e) {
  var t = e.reference, n = e.element, o = e.placement, r = o ? Y(o) : null, i = o ? Se(o) : null, l = t.x + t.width / 2 - n.width / 2, s = t.y + t.height / 2 - n.height / 2, a;
  switch (r) {
    case R:
      a = {
        x: l,
        y: t.y - n.height
      };
      break;
    case _:
      a = {
        x: l,
        y: t.y + t.height
      };
      break;
    case W:
      a = {
        x: t.x + t.width,
        y: s
      };
      break;
    case z:
      a = {
        x: t.x - n.width,
        y: s
      };
      break;
    default:
      a = {
        x: t.x,
        y: t.y
      };
  }
  var f = r ? bt(r) : null;
  if (f != null) {
    var c = f === "y" ? "height" : "width";
    switch (i) {
      case Ee:
        a[f] = a[f] - (t[c] / 2 - n[c] / 2);
        break;
      case Ae:
        a[f] = a[f] + (t[c] / 2 - n[c] / 2);
        break;
    }
  }
  return a;
}
function De(e, t) {
  t === void 0 && (t = {});
  var n = t, o = n.placement, r = o === void 0 ? e.placement : o, i = n.strategy, l = i === void 0 ? e.strategy : i, s = n.boundary, a = s === void 0 ? Ao : s, f = n.rootBoundary, c = f === void 0 ? un : f, h = n.elementContext, b = h === void 0 ? Le : h, d = n.altBoundary, O = d === void 0 ? false : d, u = n.padding, v = u === void 0 ? 0 : u, E = mn(typeof v != "number" ? v : hn(v, ze)), S = b === Le ? Do : Le, y = e.rects.popper, m = e.elements[O ? S : b], w = fr(he(m) ? m : m.contextElement || ue(e.elements.popper), a, c, l), g = Ie(e.elements.reference), p = bn({
    reference: g,
    element: y,
    strategy: "absolute",
    placement: r
  }), I = pt(Object.assign({}, y, p)), C = b === Le ? I : g, N = {
    top: w.top - C.top + E.top,
    bottom: C.bottom - w.bottom + E.bottom,
    left: w.left - C.left + E.left,
    right: C.right - w.right + E.right
  }, P = e.modifiersData.offset;
  if (b === Le && P) {
    var $ = P[r];
    Object.keys(N).forEach(function(x) {
      var Q = [W, _].indexOf(x) >= 0 ? 1 : -1, j = [R, _].indexOf(x) >= 0 ? "y" : "x";
      N[x] += $[j] * Q;
    });
  }
  return N;
}
function pr(e, t) {
  t === void 0 && (t = {});
  var n = t, o = n.placement, r = n.boundary, i = n.rootBoundary, l = n.padding, s = n.flipVariations, a = n.allowedAutoPlacements, f = a === void 0 ? fn : a, c = Se(o), h = c ? s ? Ht : Ht.filter(function(O) {
    return Se(O) === c;
  }) : ze, b = h.filter(function(O) {
    return f.indexOf(O) >= 0;
  });
  b.length === 0 && (b = h, console.error(["Popper: The `allowedAutoPlacements` option did not allow any", "placements. Ensure the `placement` option matches the variation", "of the allowed placements.", 'For example, "auto" cannot be used to allow "bottom-start".', 'Use "auto-start" instead.'].join(" ")));
  var d = b.reduce(function(O, u) {
    return O[u] = De(e, {
      placement: u,
      boundary: r,
      rootBoundary: i,
      padding: l
    })[Y(u)], O;
  }, {});
  return Object.keys(d).sort(function(O, u) {
    return d[O] - d[u];
  });
}
function dr(e) {
  if (Y(e) === Je)
    return [];
  var t = Fe(e);
  return [qt(e), t, qt(t)];
}
function vr(e) {
  var t = e.state, n = e.options, o = e.name;
  if (!t.modifiersData[o]._skip) {
    for (var r = n.mainAxis, i = r === void 0 ? true : r, l = n.altAxis, s = l === void 0 ? true : l, a = n.fallbackPlacements, f = n.padding, c = n.boundary, h = n.rootBoundary, b = n.altBoundary, d = n.flipVariations, O = d === void 0 ? true : d, u = n.allowedAutoPlacements, v = t.options.placement, E = Y(v), S = E === v, y = a || (S || !O ? [Fe(v)] : dr(v)), m = [v].concat(y).reduce(function(ye, re) {
      return ye.concat(Y(re) === Je ? pr(t, {
        placement: re,
        boundary: c,
        rootBoundary: h,
        padding: f,
        flipVariations: O,
        allowedAutoPlacements: u
      }) : re);
    }, []), w = t.rects.reference, g = t.rects.popper, p = /* @__PURE__ */ new Map(), I = true, C = m[0], N = 0; N < m.length; N++) {
      var P = m[N], $ = Y(P), x = Se(P) === Ee, Q = [R, _].indexOf($) >= 0, j = Q ? "width" : "height", k = De(t, {
        placement: P,
        boundary: c,
        rootBoundary: h,
        altBoundary: b,
        padding: f
      }), D = Q ? x ? W : z : x ? _ : R;
      w[j] > g[j] && (D = Fe(D));
      var ne = Fe(D), Z = [];
      if (i && Z.push(k[$] <= 0), s && Z.push(k[D] <= 0, k[ne] <= 0), Z.every(function(ye) {
        return ye;
      })) {
        C = P, I = false;
        break;
      }
      p.set(P, Z);
    }
    if (I)
      for (var V = O ? 3 : 1, oe = function(re) {
        var Ce = m.find(function(_e) {
          var fe = p.get(_e);
          if (fe)
            return fe.slice(0, re).every(function(tt) {
              return tt;
            });
        });
        if (Ce)
          return C = Ce, "break";
      }, Ne = V; Ne > 0; Ne--) {
        var Ve = oe(Ne);
        if (Ve === "break")
          break;
      }
    t.placement !== C && (t.modifiersData[o]._skip = true, t.placement = C, t.reset = true);
  }
}
var wn = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: vr,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};
function Ut(e, t, n) {
  return n === void 0 && (n = {
    x: 0,
    y: 0
  }), {
    top: e.top - t.height - n.y,
    right: e.right - t.width + n.x,
    bottom: e.bottom - t.height + n.y,
    left: e.left - t.width - n.x
  };
}
function Xt(e) {
  return [R, W, _, z].some(function(t) {
    return e[t] >= 0;
  });
}
function mr(e) {
  var t = e.state, n = e.name, o = t.rects.reference, r = t.rects.popper, i = t.modifiersData.preventOverflow, l = De(t, {
    elementContext: "reference"
  }), s = De(t, {
    altBoundary: true
  }), a = Ut(l, o), f = Ut(s, r, i), c = Xt(a), h = Xt(f);
  t.modifiersData[n] = {
    referenceClippingOffsets: a,
    popperEscapeOffsets: f,
    isReferenceHidden: c,
    hasPopperEscaped: h
  }, t.attributes.popper = Object.assign({}, t.attributes.popper, {
    "data-popper-reference-hidden": c,
    "data-popper-escaped": h
  });
}
var hr = {
  name: "hide",
  enabled: true,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: mr
};
function yr(e, t, n) {
  var o = Y(e), r = [z, R].indexOf(o) >= 0 ? -1 : 1, i = typeof n == "function" ? n(Object.assign({}, t, {
    placement: e
  })) : n, l = i[0], s = i[1];
  return l = l || 0, s = (s || 0) * r, [z, W].indexOf(o) >= 0 ? {
    x: s,
    y: l
  } : {
    x: l,
    y: s
  };
}
function gr(e) {
  var t = e.state, n = e.options, o = e.name, r = n.offset, i = r === void 0 ? [0, 0] : r, l = fn.reduce(function(c, h) {
    return c[h] = yr(h, t.rects, i), c;
  }, {}), s = l[t.placement], a = s.x, f = s.y;
  t.modifiersData.popperOffsets != null && (t.modifiersData.popperOffsets.x += a, t.modifiersData.popperOffsets.y += f), t.modifiersData[o] = l;
}
var En = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: gr
};
function br(e) {
  var t = e.state, n = e.name;
  t.modifiersData[n] = bn({
    reference: t.rects.reference,
    element: t.rects.popper,
    strategy: "absolute",
    placement: t.placement
  });
}
var wr = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: br,
  data: {}
};
function Er(e) {
  return e === "x" ? "y" : "x";
}
function Or(e) {
  var t = e.state, n = e.options, o = e.name, r = n.mainAxis, i = r === void 0 ? true : r, l = n.altAxis, s = l === void 0 ? false : l, a = n.boundary, f = n.rootBoundary, c = n.altBoundary, h = n.padding, b = n.tether, d = b === void 0 ? true : b, O = n.tetherOffset, u = O === void 0 ? 0 : O, v = De(t, {
    boundary: a,
    rootBoundary: f,
    padding: h,
    altBoundary: c
  }), E = Y(t.placement), S = Se(t.placement), y = !S, m = bt(E), w = Er(m), g = t.modifiersData.popperOffsets, p = t.rects.reference, I = t.rects.popper, C = typeof u == "function" ? u(Object.assign({}, t.rects, {
    placement: t.placement
  })) : u, N = typeof C == "number" ? {
    mainAxis: C,
    altAxis: C
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, C), P = t.modifiersData.offset ? t.modifiersData.offset[t.placement] : null, $ = {
    x: 0,
    y: 0
  };
  if (g) {
    if (i) {
      var x, Q = m === "y" ? R : z, j = m === "y" ? _ : W, k = m === "y" ? "height" : "width", D = g[m], ne = D + v[Q], Z = D - v[j], V = d ? -I[k] / 2 : 0, oe = S === Ee ? p[k] : I[k], Ne = S === Ee ? -I[k] : -p[k], Ve = t.elements.arrow, ye = d && Ve ? gt(Ve) : {
        width: 0,
        height: 0
      }, re = t.modifiersData["arrow#persistent"] ? t.modifiersData["arrow#persistent"].padding : vn(), Ce = re[Q], _e = re[j], fe = xe(0, p[k], ye[k]), tt = y ? p[k] / 2 - V - fe - Ce - N.mainAxis : oe - fe - Ce - N.mainAxis, Pn = y ? -p[k] / 2 + V + fe + _e + N.mainAxis : Ne + fe + _e + N.mainAxis, nt = t.elements.arrow && Me(t.elements.arrow), Nn = nt ? m === "y" ? nt.clientTop || 0 : nt.clientLeft || 0 : 0, It = (x = P == null ? void 0 : P[m]) != null ? x : 0, Cn = D + tt - It - Nn, Ln = D + Pn - It, St = xe(d ? Ge(ne, Cn) : ne, D, d ? me(Z, Ln) : Z);
      g[m] = St, $[m] = St - D;
    }
    if (s) {
      var Pt, $n = m === "x" ? R : z, xn = m === "x" ? _ : W, pe = g[w], We = w === "y" ? "height" : "width", Nt = pe + v[$n], Ct = pe - v[xn], ot = [R, z].indexOf(E) !== -1, Lt = (Pt = P == null ? void 0 : P[w]) != null ? Pt : 0, $t = ot ? Nt : pe - p[We] - I[We] - Lt + N.altAxis, xt = ot ? pe + p[We] + I[We] - Lt - N.altAxis : Ct, kt = d && ot ? Go($t, pe, xt) : xe(d ? $t : Nt, pe, d ? xt : Ct);
      g[w] = kt, $[w] = kt - pe;
    }
    t.modifiersData[o] = $;
  }
}
var On = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: Or,
  requiresIfExists: ["offset"]
};
function Ir(e) {
  return {
    scrollLeft: e.scrollLeft,
    scrollTop: e.scrollTop
  };
}
function Sr(e) {
  return e === H(e) || !M(e) ? wt(e) : Ir(e);
}
function Pr(e) {
  var t = e.getBoundingClientRect(), n = Oe(t.width) / e.offsetWidth || 1, o = Oe(t.height) / e.offsetHeight || 1;
  return n !== 1 || o !== 1;
}
function Nr(e, t, n) {
  n === void 0 && (n = false);
  var o = M(t), r = M(t) && Pr(t), i = ue(t), l = Ie(e, r, n), s = {
    scrollLeft: 0,
    scrollTop: 0
  }, a = {
    x: 0,
    y: 0
  };
  return (o || !o && !n) && ((K(t) !== "body" || Ot(i)) && (s = Sr(t)), M(t) ? (a = Ie(t, true), a.x += t.clientLeft, a.y += t.clientTop) : i && (a.x = Et(i))), {
    x: l.left + s.scrollLeft - a.x,
    y: l.top + s.scrollTop - a.y,
    width: l.width,
    height: l.height
  };
}
function Cr(e) {
  var t = /* @__PURE__ */ new Map(), n = /* @__PURE__ */ new Set(), o = [];
  e.forEach(function(i) {
    t.set(i.name, i);
  });
  function r(i) {
    n.add(i.name);
    var l = [].concat(i.requires || [], i.requiresIfExists || []);
    l.forEach(function(s) {
      if (!n.has(s)) {
        var a = t.get(s);
        a && r(a);
      }
    }), o.push(i);
  }
  return e.forEach(function(i) {
    n.has(i.name) || r(i);
  }), o;
}
function Lr(e) {
  var t = Cr(e);
  return ut.reduce(function(n, o) {
    return n.concat(t.filter(function(r) {
      return r.phase === o;
    }));
  }, []);
}
function $r(e) {
  var t;
  return function() {
    return t || (t = new Promise(function(n) {
      Promise.resolve().then(function() {
        t = void 0, n(e());
      });
    })), t;
  };
}
function ae(e) {
  for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++)
    n[o - 1] = arguments[o];
  return [].concat(n).reduce(function(r, i) {
    return r.replace(/%s/, i);
  }, e);
}
var de = 'Popper: modifier "%s" provided an invalid %s property, expected %s but got %s';
var xr = 'Popper: modifier "%s" requires "%s", but "%s" modifier is not available';
var Gt = ["name", "enabled", "phase", "fn", "effect", "requires", "options"];
function kr(e) {
  e.forEach(function(t) {
    [].concat(Object.keys(t), Gt).filter(function(n, o, r) {
      return r.indexOf(n) === o;
    }).forEach(function(n) {
      switch (n) {
        case "name":
          typeof t.name != "string" && console.error(ae(de, String(t.name), '"name"', '"string"', '"' + String(t.name) + '"'));
          break;
        case "enabled":
          typeof t.enabled != "boolean" && console.error(ae(de, t.name, '"enabled"', '"boolean"', '"' + String(t.enabled) + '"'));
          break;
        case "phase":
          ut.indexOf(t.phase) < 0 && console.error(ae(de, t.name, '"phase"', "either " + ut.join(", "), '"' + String(t.phase) + '"'));
          break;
        case "fn":
          typeof t.fn != "function" && console.error(ae(de, t.name, '"fn"', '"function"', '"' + String(t.fn) + '"'));
          break;
        case "effect":
          t.effect != null && typeof t.effect != "function" && console.error(ae(de, t.name, '"effect"', '"function"', '"' + String(t.fn) + '"'));
          break;
        case "requires":
          t.requires != null && !Array.isArray(t.requires) && console.error(ae(de, t.name, '"requires"', '"array"', '"' + String(t.requires) + '"'));
          break;
        case "requiresIfExists":
          Array.isArray(t.requiresIfExists) || console.error(ae(de, t.name, '"requiresIfExists"', '"array"', '"' + String(t.requiresIfExists) + '"'));
          break;
        case "options":
        case "data":
          break;
        default:
          console.error('PopperJS: an invalid property has been provided to the "' + t.name + '" modifier, valid properties are ' + Gt.map(function(o) {
            return '"' + o + '"';
          }).join(", ") + '; but "' + n + '" was provided.');
      }
      t.requires && t.requires.forEach(function(o) {
        e.find(function(r) {
          return r.name === o;
        }) == null && console.error(ae(xr, String(t.name), o, o));
      });
    });
  });
}
function Tr(e, t) {
  var n = /* @__PURE__ */ new Set();
  return e.filter(function(o) {
    var r = t(o);
    if (!n.has(r))
      return n.add(r), true;
  });
}
function Ar(e) {
  var t = e.reduce(function(n, o) {
    var r = n[o.name];
    return n[o.name] = r ? Object.assign({}, r, o, {
      options: Object.assign({}, r.options, o.options),
      data: Object.assign({}, r.data, o.data)
    }) : o, n;
  }, {});
  return Object.keys(t).map(function(n) {
    return t[n];
  });
}
var Kt = "Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.";
var Dr = "Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.";
var Qt = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function Zt() {
  for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
    t[n] = arguments[n];
  return !t.some(function(o) {
    return !(o && typeof o.getBoundingClientRect == "function");
  });
}
function Br(e) {
  e === void 0 && (e = {});
  var t = e, n = t.defaultModifiers, o = n === void 0 ? [] : n, r = t.defaultOptions, i = r === void 0 ? Qt : r;
  return function(s, a, f) {
    f === void 0 && (f = i);
    var c = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, Qt, i),
      modifiersData: {},
      elements: {
        reference: s,
        popper: a
      },
      attributes: {},
      styles: {}
    }, h = [], b = false, d = {
      state: c,
      setOptions: function(E) {
        var S = typeof E == "function" ? E(c.options) : E;
        u(), c.options = Object.assign({}, i, c.options, S), c.scrollParents = {
          reference: he(s) ? ke(s) : s.contextElement ? ke(s.contextElement) : [],
          popper: ke(a)
        };
        var y = Lr(Ar([].concat(o, c.options.modifiers)));
        if (c.orderedModifiers = y.filter(function(P) {
          return P.enabled;
        }), true) {
          var m = Tr([].concat(y, c.options.modifiers), function(P) {
            var $ = P.name;
            return $;
          });
          if (kr(m), Y(c.options.placement) === Je) {
            var w = c.orderedModifiers.find(function(P) {
              var $ = P.name;
              return $ === "flip";
            });
            w || console.error(['Popper: "auto" placements require the "flip" modifier be', "present and enabled to work."].join(" "));
          }
          var g = U(a), p = g.marginTop, I = g.marginRight, C = g.marginBottom, N = g.marginLeft;
          [p, I, C, N].some(function(P) {
            return parseFloat(P);
          }) && console.warn(['Popper: CSS "margin" styles cannot be used to apply padding', "between the popper and its reference element or boundary.", "To replicate margin, use the `offset` modifier, as well as", "the `padding` option in the `preventOverflow` and `flip`", "modifiers."].join(" "));
        }
        return O(), d.update();
      },
      forceUpdate: function() {
        if (!b) {
          var E = c.elements, S = E.reference, y = E.popper;
          if (!Zt(S, y)) {
            console.error(Kt);
            return;
          }
          c.rects = {
            reference: Nr(S, Me(y), c.options.strategy === "fixed"),
            popper: gt(y)
          }, c.reset = false, c.placement = c.options.placement, c.orderedModifiers.forEach(function(P) {
            return c.modifiersData[P.name] = Object.assign({}, P.data);
          });
          for (var m = 0, w = 0; w < c.orderedModifiers.length; w++) {
            if (m += 1, m > 100) {
              console.error(Dr);
              break;
            }
            if (c.reset === true) {
              c.reset = false, w = -1;
              continue;
            }
            var g = c.orderedModifiers[w], p = g.fn, I = g.options, C = I === void 0 ? {} : I, N = g.name;
            typeof p == "function" && (c = p({
              state: c,
              options: C,
              name: N,
              instance: d
            }) || c);
          }
        }
      },
      update: $r(function() {
        return new Promise(function(v) {
          d.forceUpdate(), v(c);
        });
      }),
      destroy: function() {
        u(), b = true;
      }
    };
    if (!Zt(s, a))
      return console.error(Kt), d;
    d.setOptions(f).then(function(v) {
      !b && f.onFirstUpdate && f.onFirstUpdate(v);
    });
    function O() {
      c.orderedModifiers.forEach(function(v) {
        var E = v.name, S = v.options, y = S === void 0 ? {} : S, m = v.effect;
        if (typeof m == "function") {
          var w = m({
            state: c,
            name: E,
            instance: d,
            options: y
          }), g = function() {
          };
          h.push(w || g);
        }
      });
    }
    function u() {
      h.forEach(function(v) {
        return v();
      }), h = [];
    }
    return d;
  };
}
var Rr = [rr, wr, nr, Yo, En, wn, On, yn, hr];
var zr = Br({
  defaultModifiers: Rr
});
var rt = (e) => parseInt(e, 10);
function Mr({
  arrowPadding: e,
  emit: t,
  locked: n,
  offsetDistance: o,
  offsetSkid: r,
  placement: i,
  popperNode: l,
  triggerNode: s
}) {
  const a = reactive({
    isOpen: false,
    popperInstance: null
  }), f = (u) => {
    var v;
    (v = a.popperInstance) == null || v.setOptions((E) => ({
      ...E,
      modifiers: [...E.modifiers, { name: "eventListeners", enabled: u }]
    }));
  }, c = () => f(true), h = () => f(false), b = () => {
    a.isOpen && (a.isOpen = false, t(fo));
  }, d = () => {
    a.isOpen || (a.isOpen = true, t(po));
  };
  watch([() => a.isOpen, i], async ([u]) => {
    u ? (await O(), c()) : h();
  });
  const O = async () => {
    var u;
    await nextTick(), a.popperInstance = zr(s.value, l.value, {
      placement: i.value,
      modifiers: [
        On,
        wn,
        {
          name: "flip",
          enabled: !n.value
        },
        yn,
        {
          name: "arrow",
          options: {
            padding: rt(e.value)
          }
        },
        En,
        {
          name: "offset",
          options: {
            offset: [rt(r.value), rt(o.value)]
          }
        }
      ]
    }), (u = a.popperInstance) == null || u.update();
  };
  return onBeforeUnmount(() => {
    var u;
    (u = a.popperInstance) == null || u.destroy();
  }), {
    ...toRefs(a),
    open: d,
    close: b
  };
}
function dt(e, t, n) {
  var o, r, i, l, s;
  t == null && (t = 100);
  function a() {
    var c = Date.now() - l;
    c < t && c >= 0 ? o = setTimeout(a, t - c) : (o = null, n || (s = e.apply(i, r), i = r = null));
  }
  var f = function() {
    i = this, r = arguments, l = Date.now();
    var c = n && !o;
    return o || (o = setTimeout(a, t)), c && (s = e.apply(i, r), i = r = null), s;
  };
  return f.clear = function() {
    o && (clearTimeout(o), o = null);
  }, f.flush = function() {
    o && (s = e.apply(i, r), i = r = null, clearTimeout(o), o = null);
  }, f;
}
dt.debounce = dt;
var at = dt;
var F = {
  addIconList: [],
  removeIconList: [],
  zIndex: 3e3
};
function Vr(e, t) {
  return F[e] || t;
}
var _r = (e) => {
  F = { ...F, ...e }, F.addIconList !== void 0 && F.addIconList && F.addIconList.length > 0 && lt.addIcon(F.addIconList), F.removeIconList !== void 0 && F.removeIconList && F.removeIconList.length > 0 && lt.removeIcon(F.removeIconList);
};
var Jt = ref(0);
var In = () => {
  const e = ref(Vr("zIndex", 3e3)), t = computed(() => e.value + Jt.value);
  return {
    initialZIndex: e,
    currentZIndex: t,
    nextZIndex: () => (Jt.value++, t.value)
  };
};
var Wr = defineComponent({
  name: "e-popover",
  components: {
    eArrow: $o
  },
  props: {
    placement: {
      type: String,
      default: "bottom",
      validator: (e) => [
        "auto",
        "auto-start",
        "auto-end",
        "top",
        "top-start",
        "top-end",
        "bottom",
        "bottom-start",
        "bottom-end",
        "right",
        "right-start",
        "right-end",
        "left",
        "left-start",
        "left-end"
      ].includes(e)
    },
    disableClickAway: {
      type: Boolean,
      default: false
    },
    offsetSkid: {
      type: String,
      default: "0"
    },
    offsetDistance: {
      type: String,
      default: "12"
    },
    hover: {
      type: Boolean,
      default: false
    },
    show: {
      type: Boolean,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    openDelay: {
      type: [Number],
      default: 0
    },
    closeDelay: {
      type: [Number],
      default: 0
    },
    zIndex: {
      type: [Number],
      default: 0
    },
    arrow: {
      type: Boolean,
      default: false
    },
    arrowPadding: {
      type: String,
      default: "0"
    },
    interactive: {
      type: Boolean,
      default: true
    },
    locked: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: null
    },
    height: {
      type: Number,
      default: 200
    },
    maxHeight: {
      type: Number,
      default: 400
    },
    width: {
      type: Number,
      default: 500
    },
    maxWidth: {
      type: Number,
      default: 800
    },
    container: {
      type: String,
      default: "body"
    },
    appendContainer: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default: ""
    },
    display: {
      type: String,
      default: "block"
    }
  },
  setup(e, { slots: t, attrs: n, emit: o }) {
    const r = ref(null), i = ref(null), l = ref(null), s = ref(false), { nextZIndex: a } = In();
    let f = e.zIndex || a();
    onMounted(() => {
      var oe;
      const V = ((oe = t.default) == null ? void 0 : oe.call(t)) ?? [];
      if (V && V.length > 1)
        return console.error(
          `[Popper]: The <Popper> component expects only one child element at its root. You passed ${V.length} child nodes.`
        );
    });
    const {
      arrowPadding: c,
      closeDelay: h,
      content: b,
      disableClickAway: d,
      disabled: O,
      interactive: u,
      locked: v,
      offsetDistance: E,
      offsetSkid: S,
      openDelay: y,
      placement: m,
      show: w
    } = toRefs(e), { isOpen: g, open: p, close: I } = Mr({
      arrowPadding: c,
      emit: o,
      locked: v,
      offsetDistance: E,
      offsetSkid: S,
      placement: m,
      popperNode: i,
      triggerNode: l
    }), { hasContent: C } = To(t, i, b), N = computed(() => w.value !== null), P = computed(() => O.value || !C.value), $ = computed(() => g.value && !P.value), x = computed(() => !d.value && !N.value), Q = computed(
      () => u.value ? `border: ${E.value}px solid transparent; margin: -${E.value}px;` : null
    ), j = at.debounce(p, y.value), k = at.debounce(I, h.value), D = async () => {
      P.value || N.value || (f = e.zIndex || a(), k.clear(), j());
    }, ne = async () => {
      N.value || (j.clear(), k());
    }, Z = () => {
      g.value ? ne() : D();
    };
    return watch([C, O], ([V, oe]) => {
      g.value && (!V || oe) && I();
    }), watch(g, (V) => {
      V ? (f = e.zIndex, s.value = true) : at.debounce(() => {
        s.value = false;
      }, 200);
    }), watchEffect(() => {
      N.value && (w.value ? j() : k());
    }), watchEffect(() => {
      x.value && ko(r, ne);
    }), {
      interactiveStyle: Q,
      closePopper: ne,
      openPopper: D,
      togglePopper: Z,
      popperContainerNode: r,
      triggerNode: l,
      shouldShowPopper: $,
      popperNode: i,
      modifiedIsOpen: s,
      close: I,
      zIndex: f
    };
  }
});
function Hr(e, t, n, o, r, i) {
  const l = resolveComponent("e-arrow");
  return openBlock(), createElementBlock("div", {
    class: "e-popover",
    style: normalizeStyle(e.interactiveStyle),
    onMouseleave: t[5] || (t[5] = (s) => e.hover && e.closePopper()),
    ref: "popperContainerNode"
  }, [
    createBaseVNode("div", {
      ref: "triggerNode",
      style: normalizeStyle({ display: e.display }),
      onMouseover: t[0] || (t[0] = (s) => e.hover && e.openPopper()),
      onClick: t[1] || (t[1] = (...s) => e.togglePopper && e.togglePopper(...s)),
      onFocus: t[2] || (t[2] = (...s) => e.openPopper && e.openPopper(...s)),
      onKeyup: t[3] || (t[3] = withKeys((...s) => e.closePopper && e.closePopper(...s), ["esc"]))
    }, [
      renderSlot(e.$slots, "default")
    ], 36),
    (openBlock(), createBlock(Teleport, {
      to: e.container,
      disabled: !e.appendContainer
    }, [
      createVNode(Transition, { name: "fade" }, {
        default: withCtx(() => [
          withDirectives(createBaseVNode("div", {
            onClick: t[4] || (t[4] = (s) => !e.interactive && e.closePopper()),
            class: normalizeClass(["popper", e.contentClass]),
            ref: "popperNode",
            style: normalizeStyle({ zIndex: e.zIndex, width: `${e.width}px`, height: `${e.height}px`, maxHeight: `${e.maxHeight}px`, maxWidth: `${e.maxWidth}px` })
          }, [
            renderSlot(e.$slots, "content", {
              close: e.close,
              isOpen: e.modifiedIsOpen
            }, () => [
              createTextVNode(toDisplayString(e.content), 1)
            ]),
            e.arrow ? (openBlock(), createBlock(l, { key: 0 })) : createCommentVNode("", true)
          ], 6), [
            [vShow, e.shouldShowPopper]
          ])
        ]),
        _: 3
      })
    ], 8, ["to", "disabled"]))
  ], 36);
}
var vt = ce(Wr, [["render", Hr]]);
var jr = {
  install(e) {
    e.component(vt.name, vt);
  }
};
function en(e, t = "px") {
  if (!e)
    return "";
  if (Ze(e))
    return e;
  if ($e(e))
    return `${e}${t}`;
  console.warn("binding value must be a string or number");
}
var we = 4;
var Sn = Symbol("scrollbarContextKey");
var Fr = {
  vertical: {
    offset: "offsetHeight",
    scroll: "scrollTop",
    scrollSize: "scrollHeight",
    size: "height",
    key: "vertical",
    axis: "Y",
    client: "clientY",
    direction: "top"
  },
  horizontal: {
    offset: "offsetWidth",
    scroll: "scrollLeft",
    scrollSize: "scrollWidth",
    size: "width",
    key: "horizontal",
    axis: "X",
    client: "clientX",
    direction: "left"
  }
};
var qr = defineComponent({
  name: "e-thumb",
  props: {
    always: {
      type: Boolean,
      default: false
    },
    vertical: {
      type: Boolean,
      default: false
    },
    size: {
      type: Number,
      default: 0
    },
    move: {
      type: Number,
      default: 0
    },
    ratio: {
      type: Number,
      default: 1
    }
  },
  setup(e) {
    const t = ref(false);
    let n = false, o = false;
    const r = ref(), i = ref(), l = computed(() => Fr[e.vertical ? "vertical" : "horizontal"]);
    let s = te ? document.onselectstart : null;
    const a = inject(Sn);
    if (!a)
      return;
    const f = (y) => {
      if (!r.value || !i.value || !a.wrapElement)
        return;
      const m = Math.abs(y.target.getBoundingClientRect()[l.value.direction] - y[l.value.client]), w = r.value[l.value.offset] / 2, g = (m - w) * 100 * h.value / i.value[l.value.offset];
      a.wrapElement[l.value.scroll] = g * a.wrapElement[l.value.scrollSize] / 100;
    }, c = computed(
      () => {
        let m = {
          transform: `translate${e.vertical ? "Y" : "X"}(${e.move}%)`
        };
        return e.vertical ? m.height = `${e.size}px` : m.width = `${e.size}px`, m;
      }
    ), h = computed(
      () => i.value[l.value.offset] ** 2 / a.wrapElement[l.value.scrollSize] / e.ratio / r.value[l.value.offset]
    ), b = (y) => {
      if (!i.value || !r.value || !n)
        return;
      const m = (i.value.getBoundingClientRect()[l.value.direction] - y[l.value.client]) * -1, w = r.value[l.value.offset], g = (m - w) * 100 * h.value / i.value[l.value.offset];
      a.wrapElement[l.value.scroll] = g * a.wrapElement[l.value.scrollSize] / 100;
    }, d = () => {
      document.onselectstart !== s && (document.onselectstart = s);
    }, O = () => {
      n = false, document.removeEventListener("mousemove", b), document.removeEventListener("mouseup", O), d(), o && (t.value = false);
    }, u = (y) => {
      y.stopImmediatePropagation(), n = true, document.addEventListener("mousemove", b), document.addEventListener("mouseup", O), s = document.onselectstart, document.onselectstart = () => false;
    }, v = (y) => {
      var w;
      y.stopPropagation(), y.ctrlKey || [1, 2].includes(y.button) || ((w = window == null ? void 0 : window.getSelection()) == null || w.removeAllRanges(), u(y), y.currentTarget);
    };
    onBeforeUnmount(() => {
      d(), document.removeEventListener("mouseup", O);
    });
    const E = () => {
      o = false, t.value = !!e.size;
    }, S = () => {
      o = true, t.value = n;
    };
    return Bt(
      toRef(a, "scrollbarElement"),
      "mousemove",
      E
    ), Bt(
      toRef(a, "scrollbarElement"),
      "mouseleave",
      S
    ), {
      visible: t,
      clickTrackHandler: f,
      clickThumbHandler: v,
      eThumb: r,
      thumbStyle: c,
      instance: i
    };
  }
});
function Yr(e, t, n, o, r, i) {
  return openBlock(), createBlock(Transition, { name: "fade" }, {
    default: withCtx(() => [
      withDirectives(createBaseVNode("div", {
        ref: "instance",
        class: normalizeClass(["e-thumb", e.vertical ? "is-vertical" : "is-horizontal"]),
        onMousedown: t[1] || (t[1] = (...l) => e.clickTrackHandler && e.clickTrackHandler(...l))
      }, [
        createBaseVNode("div", {
          ref: "eThumb",
          class: "e-thumb-inner",
          style: normalizeStyle(e.thumbStyle),
          onMousedown: t[0] || (t[0] = (...l) => e.clickThumbHandler && e.clickThumbHandler(...l))
        }, null, 36)
      ], 34), [
        [vShow, e.always || e.visible]
      ])
    ]),
    _: 1
  });
}
var Ur = ce(qr, [["render", Yr], ["__scopeId", "data-v-30bd9195"]]);
var Xr = defineComponent({
  name: "e-bar",
  props: {
    always: {
      type: Boolean,
      default: true
    },
    ratioY: {
      type: Number,
      default: 1
    },
    ratioX: {
      type: Number,
      default: 1
    },
    width: {
      type: Number,
      default: 0
    },
    height: {
      type: Number,
      default: 0
    }
  },
  components: {
    eThumb: Ur
  },
  setup(e) {
    const t = reactive({
      moveX: 0,
      moveY: 0
    }), n = (o) => {
      if (o) {
        const r = o.offsetHeight - we, i = o.offsetWidth - we;
        t.moveY = o.scrollTop * 100 / r * e.ratioY, t.moveX = o.scrollLeft * 100 / i * e.ratioX;
      }
    };
    return {
      ...toRefs(t),
      handleScroll: n
    };
  }
});
function Gr(e, t, n, o, r, i) {
  const l = resolveComponent("e-thumb");
  return openBlock(), createElementBlock(Fragment, null, [
    createVNode(l, {
      move: e.moveX,
      ratio: e.ratioX,
      size: e.width,
      always: e.always
    }, null, 8, ["move", "ratio", "size", "always"]),
    createVNode(l, {
      move: e.moveY,
      ratio: e.ratioY,
      size: e.height,
      vertical: "",
      always: e.always
    }, null, 8, ["move", "ratio", "size", "always"])
  ], 64);
}
var Kr = ce(Xr, [["render", Gr], ["__scopeId", "data-v-80bd0648"]]);
var Qr = defineComponent({
  name: "e-scrollbar",
  props: {
    height: {
      type: [String, Number],
      default: ""
    },
    maxHeight: {
      type: [String, Number],
      default: ""
    },
    wrapStyle: {
      type: Object,
      default: () => ({})
    },
    always: Boolean,
    noresize: Boolean,
    minSize: {
      type: Number,
      default: 20
    }
  },
  components: {
    eBar: Kr
  },
  setup(e, { emit: t }) {
    const n = ref(), o = ref();
    let r = ref(0), i = ref(0), l = ref(1), s = ref(1);
    const a = ref(), f = computed(() => {
      const u = {};
      return e.height && (u.height = en(e.height)), e.maxHeight && (u.maxHeight = en(e.maxHeight)), [e.wrapStyle, u];
    }), c = () => {
      if (!o.value)
        return;
      const u = o.value.offsetHeight - we, v = o.value.offsetWidth - we, E = u ** 2 / o.value.scrollHeight, S = v ** 2 / o.value.scrollWidth, y = Math.max(E, e.minSize), m = Math.max(S, e.minSize);
      l.value = E / (u - E) / (y / (u - y)), s.value = S / (v - S) / (m / (v - m)), i.value = y + we < u ? y : 0, r.value = m + we < v ? m : 0;
    };
    watch(
      () => [e.maxHeight, e.height],
      () => {
        nextTick(() => {
          var u;
          c(), o.value && ((u = a.value) == null || u.handleScroll(o.value));
        });
      }
    );
    const h = (u, v) => {
      no(u) ? o.value.scrollTo(u) : $e(u) && $e(v) && o.value.scrollTo(u, v);
    }, b = (u) => {
      if (!$e(u)) {
        console.warn("value must be a number");
        return;
      }
      nextTick(() => {
        o.value.scrollTop = u;
      });
    }, d = (u) => {
      if (!$e(u)) {
        console.warn("value must be a number");
        return;
      }
      nextTick(() => {
        o.value.scrollLeft = u;
      });
    };
    return onMounted(() => {
      nextTick(() => c());
    }), onUpdated(() => c()), provide(
      Sn,
      reactive({
        scrollbarElement: n,
        wrapElement: o
      })
    ), {
      eScrollbar: n,
      wrap: o,
      style: f,
      sizeWidth: r,
      sizeHeight: i,
      ratioX: s,
      ratioY: l,
      update: c,
      barRef: a,
      handleScroll: () => {
        var u;
        o.value && ((u = a.value) == null || u.handleScroll(o.value), t(uo, {
          scrollTop: o.value.scrollTop,
          scrollLeft: o.value.scrollLeft
        }));
      },
      setScrollTop: b,
      setScrollLeft: d,
      scrollTo: h
    };
  }
});
var Zr = {
  class: "e-scrollbar",
  ref: "eScrollbar"
};
function Jr(e, t, n, o, r, i) {
  const l = resolveComponent("e-bar");
  return openBlock(), createElementBlock("div", Zr, [
    createBaseVNode("div", {
      ref: "wrap",
      style: normalizeStyle(e.style),
      onScroll: t[0] || (t[0] = (...s) => e.handleScroll && e.handleScroll(...s)),
      class: "e-scrollbar-wrap"
    }, [
      renderSlot(e.$slots, "default", {}, void 0, true)
    ], 36),
    createVNode(l, {
      ref: "barRef",
      height: e.sizeHeight,
      width: e.sizeWidth,
      "ratio-x": e.ratioX,
      "ratio-y": e.ratioY,
      always: e.always
    }, null, 8, ["height", "width", "ratio-x", "ratio-y", "always"])
  ], 512);
}
var mt = ce(Qr, [["render", Jr], ["__scopeId", "data-v-1bb2aa3b"]]);
var ea = {
  install(e) {
    e.component(mt.name, mt);
  }
};
var ta = defineComponent({
  name: "eIconPicker",
  components: {
    eIcon: Xe,
    eInput: ct,
    ePopover: vt,
    eScrollbar: mt
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    styles: {
      type: Object,
      default() {
        return {};
      }
    },
    placement: {
      type: String,
      default: "bottom",
      validator: (e) => [
        "top",
        "bottom"
      ].includes(e)
    },
    modelValue: {
      type: String,
      default: ""
    },
    options: {
      type: Object,
      default: {}
    },
    width: {
      type: Number,
      default: -1
    },
    size: {
      type: String,
      default: "default",
      validator: (e) => [
        "default",
        "small",
        "large"
      ].includes(e)
    },
    placeholder: {
      type: String,
      default: "请选择图标"
    },
    defaultIcon: {
      type: String,
      default: "eiconfont e-icon-bi"
    },
    emptyText: {
      type: String,
      default() {
        return "暂无可选图标";
      }
    },
    highLightColor: {
      type: String,
      default() {
        return "";
      }
    },
    zIndex: {
      type: Number,
      default() {
        return null;
      }
    },
    appendBody: {
      type: Boolean,
      default: false
    },
    contentClass: {
      type: String,
      default() {
        return "";
      }
    }
  },
  emits: [Ye, qe, Ue],
  setup(e, t) {
    let n = shallowRef(), o = shallowRef(), r = shallowRef(), i = shallowRef(), l = shallowRef();
    const { nextZIndex: s } = In(), a = reactive({
      iconList: [],
      visible: false,
      prefixIcon: "eiconfont e-icon-bi",
      name: "",
      icon: {},
      myPlacement: "bottom",
      popoverWidth: 200,
      dataList: computed(() => {
        let p = [];
        for (let I = 0, C = a.iconList.length; I < C; I++)
          p.indexOf(a.iconList[I]) === -1 && p.push(a.iconList[I]);
        return p;
      }),
      destroy: false,
      id: (/* @__PURE__ */ new Date()).getTime(),
      zIndex: s(),
      display: "block"
    });
    onMounted(() => {
      O();
      let p = l.value.children[0];
      l.value.offsetWidth > (p == null ? void 0 : p.offsetWidth) ? a.display = "inline-block" : a.display = "block";
    }), onBeforeMount(() => {
      m(), c(true);
    }), onBeforeUnmount(() => {
      te && Vt(document, "mouseup", E), y();
    }), watch(() => e.modelValue, (p) => {
      a.name = p, a.prefixIcon = a.name ? a.name : e.defaultIcon;
    }, { deep: true }), watch(() => e.options, () => {
      c(true);
    }, { deep: true }), watch(() => a.visible, (p) => {
      p === false ? nextTick(() => {
        te && Vt(document, "mouseup", E);
      }) : nextTick(() => {
        m(), te && to(document, "mouseup", E);
      });
    }, { deep: true });
    const f = (p) => {
      Ze(p) && (a.iconList = a.icon.list.filter((I) => I.indexOf(p) !== -1));
    }, c = (p) => {
      a.prefixIcon = e.modelValue && p && p ? e.modelValue : e.defaultIcon, a.name = p === true ? e.modelValue : "", a.icon = Object.assign({}, lt), e.options && (e.options.addIconList && e.options.addIconList.length > 0 && (a.icon.list = [], a.icon.addIcon(e.options.addIconList)), e.options.removeIconList && e.options.removeIconList.length > 0 && a.icon.removeIcon(e.options.removeIconList)), a.iconList = a.icon.list, e.placement && (e.placement === "bottom" || e.placement === "top") && (a.myPlacement = e.placement), p === false && S("");
    }, h = (p = []) => {
      p && p.length > 0 && (a.icon.addIcon(p), a.iconList = a.icon.list);
    }, b = (p = []) => {
      p && p.length > 0 && (a.icon.removeIcon(p), a.iconList = a.icon.list);
    }, d = (p) => {
      a.visible = false, a.name = p, a.prefixIcon = a.name, S(a.prefixIcon);
    }, O = () => {
      nextTick(() => {
        e.width === -1 && n.value && n.value.$el ? a.popoverWidth = n.value.$el.getBoundingClientRect().width - 36 : a.popoverWidth = e.width, o && o.value && setTimeout(() => {
          var p, I;
          (p = o.value) == null || p.setScrollTop(0), (I = o.value) == null || I.update();
        }, 100);
      });
    }, u = (p) => {
      p && (a.zIndex = p), v(true);
    }, v = (p) => {
      e.readonly !== true && e.disabled !== true && (!p && e.zIndex ? a.zIndex = e.zIndex : a.zIndex = s(), a.iconList = a.icon.list, a.visible = true, O());
    }, E = (p) => {
      (p.path || p.composedPath && p.composedPath()).some((N) => N.className && (N.className.toString().indexOf("is-empty-" + a.id) !== -1 || N.className.toString().indexOf("e-icon-picker-" + a.id) !== -1)) || (a.visible = false);
    }, S = (p) => {
      t.emit(qe, p), t.emit(Ye, p), t.emit(Ue, p);
    }, y = () => {
      a.destroy = true;
    }, m = () => {
      a.destroy = false;
    }, w = () => {
      v(false);
    }, g = () => {
      a.visible = false;
    };
    return {
      popoverShowFun: v,
      change: f,
      initIcon: c,
      selectedIcon: d,
      addIcon: h,
      removeIcon: b,
      ...toRefs(a),
      input: n,
      eScrollbar: o,
      popover: r,
      fasIconList: i,
      updatePopper: u,
      createIconList: m,
      destroyIconList: y,
      show: w,
      hide: g,
      triggerWrapper: l
    };
  }
});
var na = {
  key: 0,
  class: "e-icon-picker-icon-list",
  ref: "fasIconList"
};
var oa = ["textContent"];
function ra(e, t, n, o, r, i) {
  const l = resolveComponent("e-icon"), s = resolveComponent("e-input"), a = resolveComponent("e-scrollbar"), f = resolveComponent("e-popover");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["e-icon-picker", `e-icon-picker-${e.id}`])
  }, [
    createVNode(f, {
      ref: "popover",
      placement: e.myPlacement,
      disabled: e.disabled,
      readonly: e.readonly,
      width: e.popoverWidth,
      "content-class": e.contentClass,
      "max-height": 400,
      "z-index": e.zIndex,
      arrow: "",
      "append-container": e.appendBody,
      show: e.visible,
      display: e.display
    }, {
      default: withCtx(() => [
        createBaseVNode("div", {
          onClick: t[2] || (t[2] = (c) => e.popoverShowFun(false)),
          style: normalizeStyle({ display: e.display }),
          ref: "triggerWrapper",
          class: "trigger-wrapper"
        }, [
          renderSlot(e.$slots, "default", {
            data: { prefixIcon: e.prefixIcon, visible: e.visible, placeholder: e.placeholder, disabled: e.disabled, clearable: e.clearable, readonly: e.readonly, size: e.size }
          }, () => [
            createVNode(s, {
              modelValue: e.name,
              "onUpdate:modelValue": t[0] || (t[0] = (c) => e.name = c),
              placeholder: e.placeholder,
              ref: "input",
              style: normalizeStyle(e.styles),
              clearable: e.clearable,
              disabled: e.disabled,
              readonly: e.readonly,
              size: e.size,
              onInput: e.change,
              onClear: t[1] || (t[1] = (c) => e.initIcon(false))
            }, {
              prepend: withCtx(() => [
                renderSlot(e.$slots, "prepend", { icon: e.prefixIcon }, () => [
                  createVNode(l, {
                    "icon-name": e.prefixIcon,
                    class: "e-icon"
                  }, null, 8, ["icon-name"])
                ], true)
              ]),
              _: 3
            }, 8, ["modelValue", "placeholder", "style", "clearable", "disabled", "readonly", "size", "onInput"])
          ], true)
        ], 4)
      ]),
      content: withCtx(() => [
        e.destroy ? createCommentVNode("", true) : (openBlock(), createBlock(a, {
          key: 0,
          ref: "eScrollbar",
          class: normalizeClass("is-empty-" + e.id)
        }, {
          default: withCtx(() => [
            e.dataList && e.dataList.length > 0 ? (openBlock(), createElementBlock("ul", na, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(e.dataList, (c, h) => (openBlock(), createElementBlock("li", {
                key: h,
                style: normalizeStyle(e.name === c && e.highLightColor !== "" ? { color: e.highLightColor, "--e-icon-color": e.highLightColor } : "")
              }, [
                renderSlot(e.$slots, "icon", { icon: c }, () => [
                  createVNode(l, {
                    "icon-name": c,
                    title: c,
                    onClick: e.selectedIcon,
                    class: "e-icon"
                  }, null, 8, ["icon-name", "title", "onClick"])
                ], true)
              ], 4))), 128))
            ], 512)) : (openBlock(), createElementBlock("span", {
              key: 1,
              class: "e-icon-picker-no-data",
              textContent: toDisplayString(e.emptyText)
            }, null, 8, oa))
          ]),
          _: 3
        }, 8, ["class"]))
      ]),
      _: 3
    }, 8, ["placement", "disabled", "readonly", "width", "content-class", "z-index", "append-container", "show", "display"])
  ], 2);
}
var tn = ce(ta, [["render", ra], ["__scopeId", "data-v-b181942e"]]);
var aa = {
  install(e) {
    e.component(tn.name, tn);
  }
};
var ia = [Po, go, jr, ea, aa];
var ua = {
  version: vo,
  install(e, t) {
    e[Wt] || (e[Wt] = true, ia.forEach((n) => e.use(n)), t && _r(t));
  }
};
export {
  la as analyzingIconForIconfont,
  ua as default,
  $o as eArrow,
  Kr as eBar,
  Xe as eIcon,
  tn as eIconPicker,
  ca as eIconSymbol,
  ct as eInput,
  vt as ePopover,
  mt as eScrollbar,
  Ur as eThumb,
  lt as iconList
};
/*! Bundled license information:

e-icon-picker/index.mjs:
  (**
    * e-icon-picker v2.1.1
    * (c) 2019 - 2022 cnovel.club
    * @license MIT
    *)
*/
//# sourceMappingURL=e-icon-picker.js.map
