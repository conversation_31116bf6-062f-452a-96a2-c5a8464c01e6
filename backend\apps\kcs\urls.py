from django.urls import path, include
from rest_framework import routers
from apps.kcs.views.settings import *
from apps.kcs.views.link import *
from apps.kcs.views.article import *
from apps.kcs.views.dashboard import (
    DashboardGameInfoViewSet, DashboardLinkTrendViewSet, DashboardTopArticleViewSet,
    GetAllStaffLinkRatioViewSet, DashboardOverallPerformanceViewSet,
    DashboardArticleValueAnalysisViewSet, DashboardHotTrendAnalysisViewSet,
    DashboardPotentialArticlesViewSet, DashboardGameComparisonViewSet
)
from apps.kcs.views.qiyu import *
from apps.kcs.views.articleComment import *
from apps.kcs.views.article_search import ArticleSearchViewSet
from apps.kcs.views.performance_analysis import AICsPerformanceAnalysisViewSet
from apps.kcs.views.ai_reply_adivce import AIReplyAdviceViewSet

router = routers.SimpleRouter()
router.register(r'article_category', ArticleCategoryViewSet)
router.register(r'link_category', LinkCategoryViewSet)
router.register(r'game', GameViewSet)
router.register(r'link', LinkViewSet)
router.register(r'article', ArticleViewSet)
router.register(r'article_comment', ArticleCommentViewSet)
# router.register(r'search', ArticleHaystackViewSet, basename='search')
router.register(r'search', ArticleSearchViewSet, basename='search')
router.register(r'performance_analysis', AICsPerformanceAnalysisViewSet)
router.register(r'ai_reply_advice', AIReplyAdviceViewSet, basename='ai_reply_advice')

urlpatterns = [
    path("article/withdraw", ArticleWithdrawViewSet.as_view(), name="article_withdraw"),
    path("article/merge", ArticleMergeViewSet.as_view(), name="article_merge"),
    path("article/pass", ArticlePassViewSet.as_view(), name="article_pass"),
    path("article/rebuild_index/", ArticleRebuildIndexViewSet.as_view(), name="article_rebuild_index"),
    path("article/rebuild_tags/", ArticleRebuildTagsViewSet.as_view(), name="article_rebuild_tags"),
    path("article/similar/", ArticleSimilarViewSet.as_view(), name="article_similar"),
    path("article/findsimilar/", FindSimilarArticleViewSet.as_view(), name="find_similar_article"),
    path("qiyu/category", QiyuCategoryViewSet.as_view(), name="qiyu_category"),
    path("link/handle", HandleLinkViewSet.as_view({'post':'post','delete':'delete'}), name="link_handle"),
    path("dashboard/game_info", DashboardGameInfoViewSet.as_view(), name="dashboard_game_info"),
    path("dashboard/link_trend", DashboardLinkTrendViewSet.as_view(), name="dashboard_link_trend"),
    path("dashboard/top_article", DashboardTopArticleViewSet.as_view(), name="dashboard_top_article"),
    path("dashboard/staff_link_data", GetAllStaffLinkRatioViewSet.as_view(), name="dashboard_staff_link_ratio"),
    path("dashboard/overall_performance", DashboardOverallPerformanceViewSet.as_view(), name="dashboard_overall_performance"),
    path("dashboard/article_value_analysis", DashboardArticleValueAnalysisViewSet.as_view(), name="dashboard_article_value_analysis"),
    path("dashboard/hot_trend_analysis", DashboardHotTrendAnalysisViewSet.as_view(), name="dashboard_hot_trend_analysis"),
    path("dashboard/potential_articles", DashboardPotentialArticlesViewSet.as_view(), name="dashboard_potential_articles"),
    path("dashboard/game_comparison", DashboardGameComparisonViewSet.as_view(), name="dashboard_game_comparison"),
    path("article/getSummary", GetQiyuSummary.as_view(), name="article_getSummary"),
    path("article/export", ArticleViewSet.as_view({"post": "export_data"}), name="article_export"),
    path("link/export", LinkViewSet.as_view({"post": "export_data"}), name="link_export"),
    path("qiyu/session_message", QiyuSessionMessageViewSet.as_view(), name="qiyu_session_message"),
    path("article_comment/toggle_like", ArticleToggleLikeViewSet.as_view(), name="article_comment_toggle_like"),
    path("article_comment/set_reward", ArticleCommentViewSet.as_view({"post": "comment_set_reward"}), name="article_comment_set_reward"),
    path("ai/analyzer/", ArticleAiAnalysisViewSet.as_view(), name="ai_analyzer")
]
urlpatterns += router.urls
