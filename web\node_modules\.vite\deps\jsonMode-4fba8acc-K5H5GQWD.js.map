{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/language/json/jsonMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `JSONWorker` instance\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker2) => {\n      return worker2.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia = false) {\n  const len = text.length;\n  let pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    let digits = 0;\n    let value2 = 0;\n    while (digits < count || !exact) {\n      let ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    let start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    let end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    let result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      const ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        const ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            const ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    let code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        const start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          const safeLength = len - 1;\n          let commentClosed = false;\n          while (pos < safeLength) {\n            const ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    let result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: () => pos,\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: () => token,\n    getTokenValue: () => value,\n    getTokenOffset: () => tokenOffset,\n    getTokenLength: () => pos - tokenOffset,\n    getTokenStartLine: () => lineStartOffset,\n    getTokenStartCharacter: () => tokenOffset - prevTokenLineStartOffset,\n    getTokenError: () => scanError\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function(CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\n\n// node_modules/jsonc-parser/lib/esm/impl/string-intern.js\nvar cachedSpaces = new Array(20).fill(0).map((_, index) => {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\nvar ScanError;\n(function(ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function(SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar ParseErrorCode;\n(function(ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class _ParentsStack {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new _ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class _JSONState {\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new _JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof _JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\n        \"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3)\n      );\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(\n      state.getStateData(),\n      scanner.getTokenError(),\n      lastWasColon,\n      parents\n    );\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar worker;\nfunction getWorker() {\n  return new Promise((resolve, reject) => {\n    if (!worker) {\n      return reject(\"JSON not registered!\");\n    }\n    resolve(worker);\n  });\n}\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker2, defaults) {\n    super(languageId, worker2, defaults.onDidChange);\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n        this._resetSchema(model.uri);\n      })\n    );\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        this._resetSchema(event.model.uri);\n      })\n    );\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker2) => {\n      worker2.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\" \", \":\", '\"'])\n        )\n      );\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\nexport {\n  CompletionAdapter,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentColorAdapter,\n  DocumentFormattingEditProvider,\n  DocumentHighlightAdapter,\n  DocumentLinkAdapter,\n  DocumentRangeFormattingEditProvider,\n  DocumentSymbolAdapter,\n  FoldingRangeAdapter,\n  HoverAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SelectionRangeAdapter,\n  WorkerManager,\n  fromPosition,\n  fromRange,\n  getWorker,\n  setupMode,\n  toRange,\n  toTextEdit\n};\n"], "mappings": ";;;;;;AAOA,IAAIA,KAAY,OAAO;AAAvB,IACIC,KAAmB,OAAO;AAD9B,IAEIC,KAAoB,OAAO;AAF/B,IAGIC,KAAe,OAAO,UAAU;AAHpC,IAIIC,KAAc,CAACC,GAAIC,GAAMC,GAAQC,MAAS;AAC5C,MAAIF,KAAQ,OAAOA,KAAS,YAAY,OAAOA,KAAS;AACtD,aAASG,KAAOP,GAAkBI,CAAI;AAChC,OAACH,GAAa,KAAKE,GAAII,CAAG,KAAKA,MAAQF,KACzCP,GAAUK,GAAII,GAAK,EAAE,KAAK,MAAMH,EAAKG,CAAG,GAAG,YAAY,EAAED,IAAOP,GAAiBK,GAAMG,CAAG,MAAMD,EAAK,WAAU,CAAE;AAEvH,SAAOH;AACT;AAXA,IAYIK,KAAa,CAACC,GAAQC,GAAKC,OAAkBT,GAAYO,GAAQC,GAAK,SAAS,GAAGC,KAAgBT,GAAYS,GAAcD,GAAK,SAAS;AAZ9I,IAeIE,IAA6B,CAAA;AACjCJ,GAAWI,GAA4BC,GAAuB;AAI9D,IAAIC,KAAqB,IAAI,KAAK;AAAlC,IACIC,KAAgB,MAAM;EACxB,YAAYC,GAAU;AACpB,SAAK,YAAYA,GACjB,KAAK,UAAU,MACf,KAAK,UAAU,MACf,KAAK,qBAAqB,OAAO,YAAY,MAAM,KAAK,aAAc,GAAE,KAAK,GAAG,GAChF,KAAK,gBAAgB,GACrB,KAAK,wBAAwB,KAAK,UAAU,YAAY,MAAM,KAAK,YAAW,CAAE;EACpF;EACE,cAAc;AACR,SAAK,YACP,KAAK,QAAQ,QAAA,GACb,KAAK,UAAU,OAEjB,KAAK,UAAU;EACnB;EACE,UAAU;AACR,kBAAc,KAAK,kBAAkB,GACrC,KAAK,sBAAsB,QAAA,GAC3B,KAAK,YAAW;EACpB;EACE,eAAe;AACb,QAAI,CAAC,KAAK;AACR;AAE4B,SAAK,IAAG,IAAK,KAAK,gBAClBF,MAC5B,KAAK,YAAW;EAEtB;EACE,aAAa;AACX,WAAA,KAAK,gBAAgB,KAAK,IAAA,GACrB,KAAK,YACR,KAAK,UAAUF,EAA2B,OAAO,gBAAgB;;MAE/D,UAAU;MACV,OAAO,KAAK,UAAU;;MAEtB,YAAY;QACV,kBAAkB,KAAK,UAAU;QACjC,YAAY,KAAK,UAAU;QAC3B,qBAAqB,KAAK,UAAU,mBAAmB;MACjE;IACA,CAAO,GACD,KAAK,UAAU,KAAK,QAAQ,SAAQ,IAE/B,KAAK;EAChB;EACE,4BAA4BK,GAAW;AACrC,QAAIC;AACJ,WAAO,KAAK,WAAU,EAAG,KAAK,CAACC,MAAW;AACxCD,UAAUC;IAChB,CAAK,EAAE,KAAK,CAACC,MAAM;AACb,UAAI,KAAK;AACP,eAAO,KAAK,QAAQ,oBAAoBH,CAAS;IAEpD,CAAA,EAAE,KAAK,CAACG,MAAMF,CAAO;EAC1B;AACA;AA3DA,IA8DIG;CACH,SAASC,GAAc;AACtB,WAASC,EAAGC,GAAO;AACjB,WAAO,OAAOA,KAAU;EAC5B;AACEF,IAAa,KAAKC;AACpB,GAAGF,OAAgBA,KAAc,CAAE,EAAC;AACpC,IAAII;CACH,SAASC,GAAM;AACd,WAASH,EAAGC,GAAO;AACjB,WAAO,OAAOA,KAAU;EAC5B;AACEE,IAAK,KAAKH;AACZ,GAAGE,MAAQA,IAAM,CAAE,EAAC;AACpB,IAAIE;CACH,SAASC,GAAU;AAClBA,IAAS,YAAY,aACrBA,EAAS,YAAY;AACrB,WAASL,EAAGC,GAAO;AACjB,WAAO,OAAOA,KAAU,YAAYI,EAAS,aAAaJ,KAASA,KAASI,EAAS;EACzF;AACEA,IAAS,KAAKL;AAChB,GAAGI,OAAYA,KAAU,CAAE,EAAC;AAC5B,IAAIE;CACH,SAASC,GAAW;AACnBA,IAAU,YAAY,GACtBA,EAAU,YAAY;AACtB,WAASP,EAAGC,GAAO;AACjB,WAAO,OAAOA,KAAU,YAAYM,EAAU,aAAaN,KAASA,KAASM,EAAU;EAC3F;AACEA,IAAU,KAAKP;AACjB,GAAGM,MAAaA,IAAW,CAAE,EAAC;AAC9B,IAAIE;CACH,SAASC,GAAW;AACnB,WAASC,EAAOC,GAAMC,GAAW;AAC/B,WAAID,MAAS,OAAO,cAClBA,IAAOL,EAAS,YAEdM,MAAc,OAAO,cACvBA,IAAYN,EAAS,YAEhB,EAAE,MAAAK,GAAM,WAAAC,EAAAA;EACnB;AACEH,IAAU,SAASC;AACnB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,SAASD,EAAU,IAAI,KAAKC,EAAG,SAASD,EAAU,SAAS;EACxG;AACEJ,IAAU,KAAKT;AACjB,GAAGQ,MAAaA,IAAW,CAAE,EAAC;AAC9B,IAAIO;CACH,SAASC,GAAQ;AAChB,WAASN,EAAOO,GAAKC,GAAKC,GAAOC,GAAM;AACrC,QAAIN,EAAG,SAASG,CAAG,KAAKH,EAAG,SAASI,CAAG,KAAKJ,EAAG,SAASK,CAAK,KAAKL,EAAG,SAASM,CAAI;AAChF,aAAO,EAAE,OAAOZ,EAAS,OAAOS,GAAKC,CAAG,GAAG,KAAKV,EAAS,OAAOW,GAAOC,CAAI,EAAC;AACvE,QAAIZ,EAAS,GAAGS,CAAG,KAAKT,EAAS,GAAGU,CAAG;AAC5C,aAAO,EAAE,OAAOD,GAAK,KAAKC,EAAG;AAE7B,UAAM,IAAI,MAAM,8CAA8CD,CAAG,KAAKC,CAAG,KAAKC,CAAK,KAAKC,CAAI,GAAG;EAErG;AACEJ,IAAO,SAASN;AAChB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,cAAcD,CAAS,KAAKL,EAAS,GAAGK,EAAU,KAAK,KAAKL,EAAS,GAAGK,EAAU,GAAG;EACnG;AACEG,IAAO,KAAKhB;AACd,GAAGe,MAAUA,IAAQ,CAAE,EAAC;AACxB,IAAIM;CACH,SAASC,GAAW;AACnB,WAASZ,EAAOa,GAAKC,GAAO;AAC1B,WAAO,EAAE,KAAAD,GAAK,OAAAC,EAAAA;EAClB;AACEF,IAAU,SAASZ;AACnB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,cAAcD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,MAAMC,EAAG,OAAOD,EAAU,GAAG,KAAKC,EAAG,UAAUD,EAAU,GAAG;EAC9H;AACES,IAAU,KAAKtB;AACjB,GAAGqB,MAAaA,IAAW,CAAE,EAAC;AAC9B,IAAII;CACH,SAASC,GAAe;AACvB,WAAShB,EAAOiB,GAAWC,GAAaC,GAAsBC,GAAsB;AAClF,WAAO,EAAE,WAAAH,GAAW,aAAAC,GAAa,sBAAAC,GAAsB,sBAAAC,EAAoB;EAC/E;AACEJ,IAAc,SAAShB;AACvB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,cAAcD,CAAS,KAAKE,EAAM,GAAGF,EAAU,WAAW,KAAKC,EAAG,OAAOD,EAAU,SAAS,KAAKE,EAAM,GAAGF,EAAU,oBAAoB,MAAME,EAAM,GAAGF,EAAU,oBAAoB,KAAKC,EAAG,UAAUD,EAAU,oBAAoB;EACnP;AACEa,IAAc,KAAK1B;AACrB,GAAGyB,OAAiBA,KAAe,CAAE,EAAC;AACtC,IAAIM;CACH,SAASC,GAAQ;AAChB,WAAStB,EAAOuB,GAAKC,GAAOC,GAAMC,GAAO;AACvC,WAAO;MACL,KAAAH;MACA,OAAAC;MACA,MAAAC;MACA,OAAAC;IACN;EACA;AACEJ,IAAO,SAAStB;AAChB,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,YAAYD,EAAU,KAAK,GAAG,CAAC,KAAKC,EAAG,YAAYD,EAAU,OAAO,GAAG,CAAC,KAAKC,EAAG,YAAYD,EAAU,MAAM,GAAG,CAAC,KAAKC,EAAG,YAAYD,EAAU,OAAO,GAAG,CAAC;EACtM;AACEmB,IAAO,KAAKhC;AACd,GAAG+B,MAAUA,IAAQ,CAAE,EAAC;AACxB,IAAIM;CACH,SAASC,GAAmB;AAC3B,WAAS5B,EAAOc,GAAOe,GAAO;AAC5B,WAAO;MACL,OAAAf;MACA,OAAAe;IACN;EACA;AACED,IAAkB,SAAS5B;AAC3B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,KAAKkB,EAAM,GAAGlB,EAAU,KAAK;EAC/F;AACEyB,IAAkB,KAAKtC;AACzB,GAAGqC,OAAqBA,KAAmB,CAAE,EAAC;AAC9C,IAAIG;CACH,SAASC,GAAoB;AAC5B,WAAS/B,EAAOgC,GAAOC,GAAUC,GAAqB;AACpD,WAAO;MACL,OAAAF;MACA,UAAAC;MACA,qBAAAC;IACN;EACA;AACEH,IAAmB,SAAS/B;AAC5B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,OAAOD,EAAU,KAAK,MAAMC,EAAG,UAAUD,EAAU,QAAQ,KAAKgC,EAAS,GAAGhC,CAAS,OAAOC,EAAG,UAAUD,EAAU,mBAAmB,KAAKC,EAAG,WAAWD,EAAU,qBAAqBgC,EAAS,EAAE;EAChP;AACEJ,IAAmB,KAAKzC;AAC1B,GAAGwC,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIM;CACH,SAASC,GAAmB;AAC3BA,IAAkB,UAAU,WAC5BA,EAAkB,UAAU,WAC5BA,EAAkB,SAAS;AAC7B,GAAGD,MAAqBA,IAAmB,CAAE,EAAC;AAC9C,IAAIE;CACH,SAASC,GAAe;AACvB,WAASvC,EAAOwC,GAAWC,GAASC,GAAgBC,GAAcC,GAAMC,GAAe;AACrF,UAAMC,IAAS;MACb,WAAAN;MACA,SAAAC;IACN;AACI,WAAIrC,EAAG,QAAQsC,CAAc,MAC3BI,EAAO,iBAAiBJ,IAEtBtC,EAAG,QAAQuC,CAAY,MACzBG,EAAO,eAAeH,IAEpBvC,EAAG,QAAQwC,CAAI,MACjBE,EAAO,OAAOF,IAEZxC,EAAG,QAAQyC,CAAa,MAC1BC,EAAO,gBAAgBD,IAElBC;EACX;AACEP,IAAc,SAASvC;AACvB,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,SAASD,EAAU,SAAS,KAAKC,EAAG,SAASD,EAAU,SAAS,MAAMC,EAAG,UAAUD,EAAU,cAAc,KAAKC,EAAG,SAASD,EAAU,cAAc,OAAOC,EAAG,UAAUD,EAAU,YAAY,KAAKC,EAAG,SAASD,EAAU,YAAY,OAAOC,EAAG,UAAUD,EAAU,IAAI,KAAKC,EAAG,OAAOD,EAAU,IAAI;EACjV;AACEoC,IAAc,KAAKjD;AACrB,GAAGgD,OAAiBA,KAAe,CAAE,EAAC;AACtC,IAAIS;CACH,SAASC,GAA+B;AACvC,WAAShD,EAAOiD,GAAUC,GAAS;AACjC,WAAO;MACL,UAAAD;MACA,SAAAC;IACN;EACA;AACEF,IAA8B,SAAShD;AACvC,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKQ,EAAS,GAAGR,EAAU,QAAQ,KAAKC,EAAG,OAAOD,EAAU,OAAO;EAClG;AACE6C,IAA8B,KAAK1D;AACrC,GAAGyD,MAAiCA,IAA+B,CAAE,EAAC;AACtE,IAAII;CACH,SAASC,GAAqB;AAC7BA,IAAoB,QAAQ,GAC5BA,EAAoB,UAAU,GAC9BA,EAAoB,cAAc,GAClCA,EAAoB,OAAO;AAC7B,GAAGD,MAAuBA,IAAqB,CAAE,EAAC;AAClD,IAAIE;CACH,SAASC,GAAgB;AACxBA,IAAe,cAAc,GAC7BA,EAAe,aAAa;AAC9B,GAAGD,OAAkBA,KAAgB,CAAE,EAAC;AACxC,IAAIE;CACH,SAASC,GAAkB;AAC1B,WAASlE,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,OAAOD,EAAU,IAAI;EAClE;AACEqD,IAAiB,KAAKlE;AACxB,GAAGiE,OAAoBA,KAAkB,CAAE,EAAC;AAC5C,IAAIE;CACH,SAASC,GAAa;AACrB,WAAS1D,EAAOc,GAAOoC,GAASS,GAAUC,GAAMC,GAAQC,GAAoB;AAC1E,QAAIhB,IAAS,EAAE,OAAAhC,GAAO,SAAAoC,EAAAA;AACtB,WAAI9C,EAAG,QAAQuD,CAAQ,MACrBb,EAAO,WAAWa,IAEhBvD,EAAG,QAAQwD,CAAI,MACjBd,EAAO,OAAOc,IAEZxD,EAAG,QAAQyD,CAAM,MACnBf,EAAO,SAASe,IAEdzD,EAAG,QAAQ0D,CAAkB,MAC/BhB,EAAO,qBAAqBgB,IAEvBhB;EACX;AACEY,IAAY,SAAS1D;AACrB,WAASV,EAAGC,GAAO;AACjB,QAAIwE;AACJ,QAAI5D,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,KAAKC,EAAG,OAAOD,EAAU,OAAO,MAAMC,EAAG,OAAOD,EAAU,QAAQ,KAAKC,EAAG,UAAUD,EAAU,QAAQ,OAAOC,EAAG,QAAQD,EAAU,IAAI,KAAKC,EAAG,OAAOD,EAAU,IAAI,KAAKC,EAAG,UAAUD,EAAU,IAAI,OAAOC,EAAG,UAAUD,EAAU,eAAe,KAAKC,EAAG,QAAQ2D,IAAK5D,EAAU,qBAAqB,QAAQ4D,MAAO,SAAS,SAASA,EAAG,IAAI,OAAO3D,EAAG,OAAOD,EAAU,MAAM,KAAKC,EAAG,UAAUD,EAAU,MAAM,OAAOC,EAAG,UAAUD,EAAU,kBAAkB,KAAKC,EAAG,WAAWD,EAAU,oBAAoB4C,EAA6B,EAAE;EAC3kB;AACEW,IAAY,KAAKpE;AACnB,GAAGmE,MAAeA,IAAa,CAAE,EAAC;AAClC,IAAIO;CACH,SAASC,GAAU;AAClB,WAASjE,EAAOkE,GAAOC,MAAYC,GAAM;AACvC,QAAItB,IAAS,EAAE,OAAAoB,GAAO,SAAAC,EAAAA;AACtB,WAAI/D,EAAG,QAAQgE,CAAI,KAAKA,EAAK,SAAS,MACpCtB,EAAO,YAAYsB,IAEdtB;EACX;AACEmB,IAAS,SAASjE;AAClB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,KAAK,KAAKC,EAAG,OAAOD,EAAU,OAAO;EAC7F;AACE8D,IAAS,KAAK3E;AAChB,GAAG0E,MAAYA,IAAU,CAAE,EAAC;AAC5B,IAAI7B;CACH,SAASkC,GAAW;AACnB,WAASC,EAAQxD,GAAOyD,GAAS;AAC/B,WAAO,EAAE,OAAAzD,GAAO,SAAAyD,EAAAA;EACpB;AACEF,IAAU,UAAUC;AACpB,WAASE,EAAOC,GAAUF,GAAS;AACjC,WAAO,EAAE,OAAO,EAAE,OAAOE,GAAU,KAAKA,EAAQ,GAAI,SAAAF,EAAAA;EACxD;AACEF,IAAU,SAASG;AACnB,WAASE,EAAI5D,GAAO;AAClB,WAAO,EAAE,OAAAA,GAAO,SAAS,GAAA;EAC7B;AACEuD,IAAU,MAAMK;AAChB,WAASpF,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,OAAOD,EAAU,OAAO,KAAKE,EAAM,GAAGF,EAAU,KAAK;EAClG;AACEkE,IAAU,KAAK/E;AACjB,GAAG6C,MAAaA,IAAW,CAAE,EAAC;AAC9B,IAAIwC;CACH,SAASC,GAAmB;AAC3B,WAAS5E,EAAOgC,GAAO6C,GAAmBC,GAAa;AACrD,UAAMhC,IAAS,EAAE,OAAAd,EAAAA;AACjB,WAAI6C,MAAsB,WACxB/B,EAAO,oBAAoB+B,IAEzBC,MAAgB,WAClBhC,EAAO,cAAcgC,IAEhBhC;EACX;AACE8B,IAAkB,SAAS5E;AAC3B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKC,EAAG,OAAOD,EAAU,KAAK,MAAMC,EAAG,QAAQD,EAAU,iBAAiB,KAAKA,EAAU,sBAAsB,YAAYC,EAAG,OAAOD,EAAU,WAAW,KAAKA,EAAU,gBAAgB;EAC9N;AACEyE,IAAkB,KAAKtF;AACzB,GAAGqF,MAAqBA,IAAmB,CAAE,EAAC;AAC9C,IAAII;CACH,SAASC,GAA6B;AACrC,WAAS1F,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,OAAOD,CAAS;EAC9B;AACE6E,IAA4B,KAAK1F;AACnC,GAAGyF,MAA+BA,IAA6B,CAAE,EAAC;AAClE,IAAIE;CACH,SAASC,GAAoB;AAC5B,WAASZ,EAAQxD,GAAOyD,GAASY,GAAY;AAC3C,WAAO,EAAE,OAAArE,GAAO,SAAAyD,GAAS,cAAcY,EAAU;EACrD;AACED,IAAmB,UAAUZ;AAC7B,WAASE,EAAOC,GAAUF,GAASY,GAAY;AAC7C,WAAO,EAAE,OAAO,EAAE,OAAOV,GAAU,KAAKA,EAAQ,GAAI,SAAAF,GAAS,cAAcY,EAAAA;EAC/E;AACED,IAAmB,SAASV;AAC5B,WAASE,EAAI5D,GAAOqE,GAAY;AAC9B,WAAO,EAAE,OAAArE,GAAO,SAAS,IAAI,cAAcqE,EAAU;EACzD;AACED,IAAmB,MAAMR;AACzB,WAASpF,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAO4C,EAAS,GAAGhC,CAAS,MAAMwE,EAAiB,GAAGxE,EAAU,YAAY,KAAK4E,EAA2B,GAAG5E,EAAU,YAAY;EACzI;AACE+E,IAAmB,KAAK5F;AAC1B,GAAG2F,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIG;CACH,SAASC,GAAmB;AAC3B,WAASrF,EAAOsF,GAAcC,GAAO;AACnC,WAAO,EAAE,cAAAD,GAAc,OAAAC,EAAAA;EAC3B;AACEF,IAAkB,SAASrF;AAC3B,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKqF,GAAwC,GAAGrF,EAAU,YAAY,KAAK,MAAM,QAAQA,EAAU,KAAK;EACvI;AACEkF,IAAkB,KAAK/F;AACzB,GAAG8F,MAAqBA,IAAmB,CAAE,EAAC;AAC9C,IAAIK;CACH,SAASC,GAAa;AACrB,WAAS1F,EAAOa,GAAK8E,GAASR,GAAY;AACxC,QAAIrC,IAAS;MACX,MAAM;MACN,KAAAjC;IACN;AACI,WAAI8E,MAAY,WAAWA,EAAQ,cAAc,UAAUA,EAAQ,mBAAmB,YACpF7C,EAAO,UAAU6C,IAEfR,MAAe,WACjBrC,EAAO,eAAeqC,IAEjBrC;EACX;AACE4C,IAAY,SAAS1F;AACrB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,KAAaA,EAAU,SAAS,YAAYC,EAAG,OAAOD,EAAU,GAAG,MAAMA,EAAU,YAAY,WAAWA,EAAU,QAAQ,cAAc,UAAUC,EAAG,QAAQD,EAAU,QAAQ,SAAS,OAAOA,EAAU,QAAQ,mBAAmB,UAAUC,EAAG,QAAQD,EAAU,QAAQ,cAAc,QAAQA,EAAU,iBAAiB,UAAU4E,EAA2B,GAAG5E,EAAU,YAAY;EACvY;AACEuF,IAAY,KAAKpG;AACnB,GAAGmG,MAAeA,IAAa,CAAE,EAAC;AAClC,IAAIG;CACH,SAASC,GAAa;AACrB,WAAS7F,EAAO8F,GAAQC,GAAQJ,GAASR,GAAY;AACnD,QAAIrC,IAAS;MACX,MAAM;MACN,QAAAgD;MACA,QAAAC;IACN;AACI,WAAIJ,MAAY,WAAWA,EAAQ,cAAc,UAAUA,EAAQ,mBAAmB,YACpF7C,EAAO,UAAU6C,IAEfR,MAAe,WACjBrC,EAAO,eAAeqC,IAEjBrC;EACX;AACE+C,IAAY,SAAS7F;AACrB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,KAAaA,EAAU,SAAS,YAAYC,EAAG,OAAOD,EAAU,MAAM,KAAKC,EAAG,OAAOD,EAAU,MAAM,MAAMA,EAAU,YAAY,WAAWA,EAAU,QAAQ,cAAc,UAAUC,EAAG,QAAQD,EAAU,QAAQ,SAAS,OAAOA,EAAU,QAAQ,mBAAmB,UAAUC,EAAG,QAAQD,EAAU,QAAQ,cAAc,QAAQA,EAAU,iBAAiB,UAAU4E,EAA2B,GAAG5E,EAAU,YAAY;EACza;AACE0F,IAAY,KAAKvG;AACnB,GAAGsG,OAAeA,KAAa,CAAE,EAAC;AAClC,IAAII;CACH,SAASC,GAAa;AACrB,WAASjG,EAAOa,GAAK8E,GAASR,GAAY;AACxC,QAAIrC,IAAS;MACX,MAAM;MACN,KAAAjC;IACN;AACI,WAAI8E,MAAY,WAAWA,EAAQ,cAAc,UAAUA,EAAQ,sBAAsB,YACvF7C,EAAO,UAAU6C,IAEfR,MAAe,WACjBrC,EAAO,eAAeqC,IAEjBrC;EACX;AACEmD,IAAY,SAASjG;AACrB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,KAAaA,EAAU,SAAS,YAAYC,EAAG,OAAOD,EAAU,GAAG,MAAMA,EAAU,YAAY,WAAWA,EAAU,QAAQ,cAAc,UAAUC,EAAG,QAAQD,EAAU,QAAQ,SAAS,OAAOA,EAAU,QAAQ,sBAAsB,UAAUC,EAAG,QAAQD,EAAU,QAAQ,iBAAiB,QAAQA,EAAU,iBAAiB,UAAU4E,EAA2B,GAAG5E,EAAU,YAAY;EAC7Y;AACE8F,IAAY,KAAK3G;AACnB,GAAG0G,OAAeA,KAAa,CAAE,EAAC;AAClC,IAAIE;CACH,SAASC,GAAgB;AACxB,WAAS7G,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,MAAcA,EAAU,YAAY,UAAUA,EAAU,oBAAoB,YAAYA,EAAU,oBAAoB,UAAUA,EAAU,gBAAgB,MAAM,CAACiG,MAClKhG,EAAG,OAAOgG,EAAO,IAAI,IAChBX,EAAW,GAAGW,CAAM,KAAKR,GAAW,GAAGQ,CAAM,KAAKJ,GAAW,GAAGI,CAAM,IAEtEhB,EAAiB,GAAGgB,CAAM,CAEpC;EACL;AACED,IAAe,KAAK7G;AACtB,GAAG4G,OAAkBA,KAAgB,CAAE,EAAC;AACxC,IAAIG;CACH,SAASC,GAAyB;AACjC,WAAStG,EAAOa,GAAK;AACnB,WAAO,EAAE,KAAAA,EAAG;EAChB;AACEyF,IAAwB,SAAStG;AACjC,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,GAAG;EAC3D;AACEmG,IAAwB,KAAKhH;AAC/B,GAAG+G,OAA2BA,KAAyB,CAAE,EAAC;AAC1D,IAAIE;CACH,SAASC,GAAkC;AAC1C,WAASxG,EAAOa,GAAK4F,GAAS;AAC5B,WAAO,EAAE,KAAA5F,GAAK,SAAA4F,EAAAA;EAClB;AACED,IAAiC,SAASxG;AAC1C,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,GAAG,KAAKC,EAAG,QAAQD,EAAU,OAAO;EAC5F;AACEqG,IAAiC,KAAKlH;AACxC,GAAGiH,OAAoCA,KAAkC,CAAE,EAAC;AAC5E,IAAIf;CACH,SAASkB,GAA0C;AAClD,WAAS1G,EAAOa,GAAK4F,GAAS;AAC5B,WAAO,EAAE,KAAA5F,GAAK,SAAA4F,EAAAA;EAClB;AACEC,IAAyC,SAAS1G;AAClD,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,GAAG,MAAMA,EAAU,YAAY,QAAQC,EAAG,QAAQD,EAAU,OAAO;EAC3H;AACEuG,IAAyC,KAAKpH;AAChD,GAAGkG,OAA4CA,KAA0C,CAAE,EAAC;AAC5F,IAAImB;CACH,SAASC,GAAmB;AAC3B,WAAS5G,EAAOa,GAAKgG,GAAYJ,GAASK,GAAM;AAC9C,WAAO,EAAE,KAAAjG,GAAK,YAAAgG,GAAY,SAAAJ,GAAS,MAAAK,EAAI;EAC3C;AACEF,IAAkB,SAAS5G;AAC3B,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,GAAG,KAAKC,EAAG,OAAOD,EAAU,UAAU,KAAKC,EAAG,QAAQD,EAAU,OAAO,KAAKC,EAAG,OAAOD,EAAU,IAAI;EAC5J;AACEyG,IAAkB,KAAKtH;AACzB,GAAGqH,OAAqBA,KAAmB,CAAE,EAAC;AAC9C,IAAII;CACH,SAASC,GAAa;AACrBA,IAAY,YAAY,aACxBA,EAAY,WAAW;AACvB,WAAS1H,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOY,MAAc6G,EAAY,aAAa7G,MAAc6G,EAAY;EAC5E;AACEA,IAAY,KAAK1H;AACnB,GAAGyH,OAAeA,KAAa,CAAE,EAAC;AAClC,IAAIE;CACH,SAASC,GAAgB;AACxB,WAAS5H,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcb,CAAK,KAAKwH,GAAW,GAAG5G,EAAU,IAAI,KAAKC,EAAG,OAAOD,EAAU,KAAK;EAChG;AACE+G,IAAe,KAAK5H;AACtB,GAAG2H,MAAkBA,IAAgB,CAAE,EAAC;AACxC,IAAIE;CACH,SAASC,GAAqB;AAC7BA,IAAoB,OAAO,GAC3BA,EAAoB,SAAS,GAC7BA,EAAoB,WAAW,GAC/BA,EAAoB,cAAc,GAClCA,EAAoB,QAAQ,GAC5BA,EAAoB,WAAW,GAC/BA,EAAoB,QAAQ,GAC5BA,EAAoB,YAAY,GAChCA,EAAoB,SAAS,GAC7BA,EAAoB,WAAW,IAC/BA,EAAoB,OAAO,IAC3BA,EAAoB,QAAQ,IAC5BA,EAAoB,OAAO,IAC3BA,EAAoB,UAAU,IAC9BA,EAAoB,UAAU,IAC9BA,EAAoB,QAAQ,IAC5BA,EAAoB,OAAO,IAC3BA,EAAoB,YAAY,IAChCA,EAAoB,SAAS,IAC7BA,EAAoB,aAAa,IACjCA,EAAoB,WAAW,IAC/BA,EAAoB,SAAS,IAC7BA,EAAoB,QAAQ,IAC5BA,EAAoB,WAAW,IAC/BA,EAAoB,gBAAgB;AACtC,GAAGD,MAAuBA,IAAqB,CAAE,EAAC;AAClD,IAAIE;CACH,SAASC,GAAmB;AAC3BA,IAAkB,YAAY,GAC9BA,EAAkB,UAAU;AAC9B,GAAGD,OAAqBA,KAAmB,CAAE,EAAC;AAC9C,IAAIE;CACH,SAASC,GAAoB;AAC5BA,IAAmB,aAAa;AAClC,GAAGD,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIE;CACH,SAASC,GAAoB;AAC5B,WAAS1H,EAAOuE,GAASC,GAAQF,GAAS;AACxC,WAAO,EAAE,SAAAC,GAAS,QAAAC,GAAQ,SAAAF,EAAAA;EAC9B;AACEoD,IAAmB,SAAS1H;AAC5B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOY,KAAaC,EAAG,OAAOD,EAAU,OAAO,KAAKE,EAAM,GAAGF,EAAU,MAAM,KAAKE,EAAM,GAAGF,EAAU,OAAO;EAChH;AACEuH,IAAmB,KAAKpI;AAC1B,GAAGmI,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIE;CACH,SAASC,GAAiB;AACzBA,IAAgB,OAAO,GACvBA,EAAgB,oBAAoB;AACtC,GAAGD,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIE;CACH,SAASC,GAA6B;AACrC,WAASxI,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOY,MAAcC,EAAG,OAAOD,EAAU,MAAM,KAAKA,EAAU,WAAW,YAAYC,EAAG,OAAOD,EAAU,WAAW,KAAKA,EAAU,gBAAgB;EACvJ;AACE2H,IAA4B,KAAKxI;AACnC,GAAGuI,OAA+BA,KAA6B,CAAE,EAAC;AAClE,IAAIE;CACH,SAASC,GAAiB;AACzB,WAAShI,EAAOgC,GAAO;AACrB,WAAO,EAAE,OAAAA,EAAK;EAClB;AACEgG,IAAgB,SAAShI;AAC3B,GAAG+H,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIE;CACH,SAASC,GAAiB;AACzB,WAASlI,EAAOmI,GAAOC,GAAc;AACnC,WAAO,EAAE,OAAOD,KAAgB,CAAE,GAAE,cAAc,CAAC,CAACC,EAAAA;EACxD;AACEF,IAAgB,SAASlI;AAC3B,GAAGiI,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAII;CACH,SAASC,GAAe;AACvB,WAASC,EAAcC,GAAW;AAChC,WAAOA,EAAU,QAAQ,yBAAyB,MAAM;EAC5D;AACEF,IAAc,gBAAgBC;AAC9B,WAASjJ,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,OAAOD,CAAS,KAAKC,EAAG,cAAcD,CAAS,KAAKC,EAAG,OAAOD,EAAU,QAAQ,KAAKC,EAAG,OAAOD,EAAU,KAAK;EAC5H;AACEmI,IAAc,KAAKhJ;AACrB,GAAG+I,MAAiBA,IAAe,CAAE,EAAC;AACtC,IAAII;CACH,SAASC,GAAQ;AAChB,WAASpJ,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAO,CAAC,CAACY,KAAaC,EAAG,cAAcD,CAAS,MAAM8G,EAAc,GAAG9G,EAAU,QAAQ,KAAKkI,EAAa,GAAGlI,EAAU,QAAQ,KAAKC,EAAG,WAAWD,EAAU,UAAUkI,EAAa,EAAE,OAAO9I,EAAM,UAAU,UAAUc,EAAM,GAAGd,EAAM,KAAK;EAC/O;AACEmJ,IAAO,KAAKpJ;AACd,GAAGmJ,OAAUA,KAAQ,CAAE,EAAC;AACxB,IAAIE;CACH,SAASC,GAAuB;AAC/B,WAAS5I,EAAOgC,GAAO6G,GAAe;AACpC,WAAOA,IAAgB,EAAE,OAAA7G,GAAO,eAAA6G,EAAa,IAAK,EAAE,OAAA7G,EAAK;EAC7D;AACE4G,IAAsB,SAAS5I;AACjC,GAAG2I,OAAyBA,KAAuB,CAAE,EAAC;AACtD,IAAIG;CACH,SAASC,GAAuB;AAC/B,WAAS/I,EAAOgC,GAAO6G,MAAkBG,GAAY;AACnD,QAAIlG,IAAS,EAAE,OAAAd,EAAAA;AACf,WAAI5B,EAAG,QAAQyI,CAAa,MAC1B/F,EAAO,gBAAgB+F,IAErBzI,EAAG,QAAQ4I,CAAU,IACvBlG,EAAO,aAAakG,IAEpBlG,EAAO,aAAa,CAAA,GAEfA;EACX;AACEiG,IAAsB,SAAS/I;AACjC,GAAG8I,OAAyBA,KAAuB,CAAE,EAAC;AACtD,IAAIG;CACH,SAASC,GAAwB;AAChCA,IAAuB,OAAO,GAC9BA,EAAuB,OAAO,GAC9BA,EAAuB,QAAQ;AACjC,GAAGD,MAA0BA,IAAwB,CAAE,EAAC;AACxD,IAAIE;CACH,SAASC,GAAoB;AAC5B,WAASpJ,EAAOc,GAAO8B,GAAM;AAC3B,QAAIE,IAAS,EAAE,OAAAhC,EAAAA;AACf,WAAIV,EAAG,OAAOwC,CAAI,MAChBE,EAAO,OAAOF,IAETE;EACX;AACEsG,IAAmB,SAASpJ;AAC9B,GAAGmJ,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIE;CACH,SAASC,GAAa;AACrBA,IAAY,OAAO,GACnBA,EAAY,SAAS,GACrBA,EAAY,YAAY,GACxBA,EAAY,UAAU,GACtBA,EAAY,QAAQ,GACpBA,EAAY,SAAS,GACrBA,EAAY,WAAW,GACvBA,EAAY,QAAQ,GACpBA,EAAY,cAAc,GAC1BA,EAAY,OAAO,IACnBA,EAAY,YAAY,IACxBA,EAAY,WAAW,IACvBA,EAAY,WAAW,IACvBA,EAAY,WAAW,IACvBA,EAAY,SAAS,IACrBA,EAAY,SAAS,IACrBA,EAAY,UAAU,IACtBA,EAAY,QAAQ,IACpBA,EAAY,SAAS,IACrBA,EAAY,MAAM,IAClBA,EAAY,OAAO,IACnBA,EAAY,aAAa,IACzBA,EAAY,SAAS,IACrBA,EAAY,QAAQ,IACpBA,EAAY,WAAW,IACvBA,EAAY,gBAAgB;AAC9B,GAAGD,MAAeA,IAAa,CAAE,EAAC;AAClC,IAAIE;CACH,SAASC,GAAY;AACpBA,IAAW,aAAa;AAC1B,GAAGD,OAAcA,KAAY,CAAE,EAAC;AAChC,IAAIE;CACH,SAASC,GAAoB;AAC5B,WAAS1J,EAAO2J,GAAM/G,GAAM9B,GAAOD,GAAK+I,GAAe;AACrD,QAAI9G,IAAS;MACX,MAAA6G;MACA,MAAA/G;MACA,UAAU,EAAE,KAAA/B,GAAK,OAAAC,EAAK;IAC5B;AACI,WAAI8I,MACF9G,EAAO,gBAAgB8G,IAElB9G;EACX;AACE4G,IAAmB,SAAS1J;AAC9B,GAAGyJ,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAII;CACH,SAASC,GAAkB;AAC1B,WAAS9J,EAAO2J,GAAM/G,GAAM/B,GAAKC,GAAO;AACtC,WAAOA,MAAU,SAAS,EAAE,MAAA6I,GAAM,MAAA/G,GAAM,UAAU,EAAE,KAAA/B,GAAK,OAAAC,EAAO,EAAA,IAAK,EAAE,MAAA6I,GAAM,MAAA/G,GAAM,UAAU,EAAE,KAAA/B,EAAG,EAAA;EACtG;AACEiJ,IAAiB,SAAS9J;AAC5B,GAAG6J,OAAoBA,KAAkB,CAAE,EAAC;AAC5C,IAAIE;CACH,SAASC,GAAiB;AACzB,WAAShK,EAAO2J,GAAMM,GAAQrH,GAAM9B,GAAOoJ,GAAgBC,GAAU;AACnE,QAAIrH,IAAS;MACX,MAAA6G;MACA,QAAAM;MACA,MAAArH;MACA,OAAA9B;MACA,gBAAAoJ;IACN;AACI,WAAIC,MAAa,WACfrH,EAAO,WAAWqH,IAEbrH;EACX;AACEkH,IAAgB,SAAShK;AACzB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,KAAaC,EAAG,OAAOD,EAAU,IAAI,KAAKC,EAAG,OAAOD,EAAU,IAAI,KAAKE,EAAM,GAAGF,EAAU,KAAK,KAAKE,EAAM,GAAGF,EAAU,cAAc,MAAMA,EAAU,WAAW,UAAUC,EAAG,OAAOD,EAAU,MAAM,OAAOA,EAAU,eAAe,UAAUC,EAAG,QAAQD,EAAU,UAAU,OAAOA,EAAU,aAAa,UAAU,MAAM,QAAQA,EAAU,QAAQ,OAAOA,EAAU,SAAS,UAAU,MAAM,QAAQA,EAAU,IAAI;EACha;AACE6J,IAAgB,KAAK1K;AACvB,GAAGyK,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIK;CACH,SAASC,GAAiB;AACzBA,IAAgB,QAAQ,IACxBA,EAAgB,WAAW,YAC3BA,EAAgB,WAAW,YAC3BA,EAAgB,kBAAkB,oBAClCA,EAAgB,iBAAiB,mBACjCA,EAAgB,kBAAkB,oBAClCA,EAAgB,SAAS,UACzBA,EAAgB,wBAAwB,0BACxCA,EAAgB,eAAe;AACjC,GAAGD,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIE;CACH,SAASC,GAAwB;AAChCA,IAAuB,UAAU,GACjCA,EAAuB,YAAY;AACrC,GAAGD,MAA0BA,IAAwB,CAAE,EAAC;AACxD,IAAIE;CACH,SAASC,GAAoB;AAC5B,WAASzK,EAAO0K,GAAaC,GAAMC,GAAa;AAC9C,QAAI9H,IAAS,EAAE,aAAA4H,EAAAA;AACf,WAAuBC,KAAS,SAC9B7H,EAAO,OAAO6H,IAEcC,KAAgB,SAC5C9H,EAAO,cAAc8H,IAEhB9H;EACX;AACE2H,IAAmB,SAASzK;AAC5B,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,WAAWD,EAAU,aAAasD,EAAW,EAAE,MAAMtD,EAAU,SAAS,UAAUC,EAAG,WAAWD,EAAU,MAAMC,EAAG,MAAM,OAAOD,EAAU,gBAAgB,UAAUA,EAAU,gBAAgBmK,EAAsB,WAAWnK,EAAU,gBAAgBmK,EAAsB;EACtT;AACEG,IAAmB,KAAKnL;AAC1B,GAAGkL,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAIK;CACH,SAASC,GAAa;AACrB,WAAS9K,EAAOkE,GAAO6G,GAAqBnI,GAAM;AAChD,QAAIE,IAAS,EAAE,OAAAoB,EAAAA,GACX8G,IAAY;AAChB,WAAI,OAAOD,KAAwB,YACjCC,IAAY,OACZlI,EAAO,OAAOiI,KACL/G,EAAQ,GAAG+G,CAAmB,IACvCjI,EAAO,UAAUiI,IAEjBjI,EAAO,OAAOiI,GAEZC,KAAapI,MAAS,WACxBE,EAAO,OAAOF,IAETE;EACX;AACEgI,IAAY,SAAS9K;AACrB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOY,KAAaC,EAAG,OAAOD,EAAU,KAAK,MAAMA,EAAU,gBAAgB,UAAUC,EAAG,WAAWD,EAAU,aAAasD,EAAW,EAAE,OAAOtD,EAAU,SAAS,UAAUC,EAAG,OAAOD,EAAU,IAAI,OAAOA,EAAU,SAAS,UAAUA,EAAU,YAAY,YAAYA,EAAU,YAAY,UAAU6D,EAAQ,GAAG7D,EAAU,OAAO,OAAOA,EAAU,gBAAgB,UAAUC,EAAG,QAAQD,EAAU,WAAW,OAAOA,EAAU,SAAS,UAAU+F,GAAc,GAAG/F,EAAU,IAAI;EACzd;AACE2K,IAAY,KAAKxL;AACnB,GAAGuL,OAAeA,KAAa,CAAE,EAAC;AAClC,IAAII;CACH,SAASC,GAAW;AACnB,WAASlL,EAAOc,GAAOqK,GAAM;AAC3B,QAAIrI,IAAS,EAAE,OAAAhC,EAAAA;AACf,WAAIV,EAAG,QAAQ+K,CAAI,MACjBrI,EAAO,OAAOqI,IAETrI;EACX;AACEoI,IAAU,SAASlL;AACnB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,MAAMC,EAAG,UAAUD,EAAU,OAAO,KAAK6D,EAAQ,GAAG7D,EAAU,OAAO;EACjI;AACE+K,IAAU,KAAK5L;AACjB,GAAG2L,OAAaA,KAAW,CAAE,EAAC;AAC9B,IAAIG;CACH,SAASC,GAAoB;AAC5B,WAASrL,EAAOsL,GAASC,GAAc;AACrC,WAAO,EAAE,SAAAD,GAAS,cAAAC,EAAAA;EACtB;AACEF,IAAmB,SAASrL;AAC5B,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKC,EAAG,SAASD,EAAU,OAAO,KAAKC,EAAG,QAAQD,EAAU,YAAY;EACvG;AACEkL,IAAmB,KAAK/L;AAC1B,GAAG8L,OAAsBA,KAAoB,CAAE,EAAC;AAChD,IAAII;CACH,SAASC,GAAe;AACvB,WAASzL,EAAOc,GAAOtC,GAAQ2M,GAAM;AACnC,WAAO,EAAE,OAAArK,GAAO,QAAAtC,GAAQ,MAAA2M,EAAAA;EAC5B;AACEM,IAAc,SAASzL;AACvB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,QAAQD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,MAAMC,EAAG,UAAUD,EAAU,MAAM,KAAKC,EAAG,OAAOD,EAAU,MAAM;EAC9H;AACEsL,IAAc,KAAKnM;AACrB,GAAGkM,OAAiBA,KAAe,CAAE,EAAC;AACtC,IAAIE;CACH,SAASC,GAAiB;AACzB,WAAS3L,EAAOc,GAAO8K,GAAQ;AAC7B,WAAO,EAAE,OAAA9K,GAAO,QAAA8K,EAAAA;EACpB;AACED,IAAgB,SAAS3L;AACzB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAOa,EAAG,cAAcD,CAAS,KAAKE,EAAM,GAAGF,EAAU,KAAK,MAAMA,EAAU,WAAW,UAAUwL,EAAgB,GAAGxL,EAAU,MAAM;EAC1I;AACEwL,IAAgB,KAAKrM;AACvB,GAAGoM,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIG;CACH,SAASC,GAAqB;AAC7BA,IAAoB,YAAe,aACnCA,EAAoB,OAAU,QAC9BA,EAAoB,QAAW,SAC/BA,EAAoB,OAAU,QAC9BA,EAAoB,YAAe,aACnCA,EAAoB,SAAY,UAChCA,EAAoB,gBAAmB,iBACvCA,EAAoB,YAAe,aACnCA,EAAoB,WAAc,YAClCA,EAAoB,WAAc,YAClCA,EAAoB,aAAgB,cACpCA,EAAoB,QAAW,SAC/BA,EAAoB,WAAc,YAClCA,EAAoB,SAAY,UAChCA,EAAoB,QAAW,SAC/BA,EAAoB,UAAa,WACjCA,EAAoB,WAAc,YAClCA,EAAoB,UAAa,WACjCA,EAAoB,SAAY,UAChCA,EAAoB,SAAY,UAChCA,EAAoB,SAAY,UAChCA,EAAoB,WAAc,YAClCA,EAAoB,YAAe;AACrC,GAAGD,OAAuBA,KAAqB,CAAE,EAAC;AAClD,IAAIE;CACH,SAASC,GAAyB;AACjCA,IAAwB,cAAiB,eACzCA,EAAwB,aAAgB,cACxCA,EAAwB,WAAc,YACtCA,EAAwB,SAAY,UACpCA,EAAwB,aAAgB,cACxCA,EAAwB,WAAc,YACtCA,EAAwB,QAAW,SACnCA,EAAwB,eAAkB,gBAC1CA,EAAwB,gBAAmB,iBAC3CA,EAAwB,iBAAoB;AAC9C,GAAGD,OAA2BA,KAAyB,CAAE,EAAC;AAC1D,IAAIE;CACH,SAASC,GAAiB;AACzB,WAAS5M,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,MAAMA,EAAU,aAAa,UAAU,OAAOA,EAAU,YAAa,aAAa,MAAM,QAAQA,EAAU,IAAI,MAAMA,EAAU,KAAK,WAAW,KAAK,OAAOA,EAAU,KAAK,CAAC,KAAM;EACrN;AACE+L,IAAgB,KAAK5M;AACvB,GAAG2M,OAAmBA,KAAiB,CAAE,EAAC;AAC1C,IAAIE;CACH,SAASC,GAAkB;AAC1B,WAASpM,EAAOc,GAAOgG,GAAM;AAC3B,WAAO,EAAE,OAAAhG,GAAO,MAAAgG,EAAAA;EACpB;AACEsF,IAAiB,SAASpM;AAC1B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAA+BY,KAAc,QAAQE,EAAM,GAAGF,EAAU,KAAK,KAAKC,EAAG,OAAOD,EAAU,IAAI;EAC9G;AACEiM,IAAiB,KAAK9M;AACxB,GAAG6M,OAAoBA,KAAkB,CAAE,EAAC;AAC5C,IAAIE;CACH,SAASC,GAA4B;AACpC,WAAStM,EAAOc,GAAOyL,GAAcC,GAAqB;AACxD,WAAO,EAAE,OAAA1L,GAAO,cAAAyL,GAAc,qBAAAC,EAAAA;EAClC;AACEF,IAA2B,SAAStM;AACpC,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAA+BY,KAAc,QAAQE,EAAM,GAAGF,EAAU,KAAK,KAAKC,EAAG,QAAQD,EAAU,mBAAmB,MAAMC,EAAG,OAAOD,EAAU,YAAY,KAAKA,EAAU,iBAAiB;EACpM;AACEmM,IAA2B,KAAKhN;AAClC,GAAG+M,OAA8BA,KAA4B,CAAE,EAAC;AAChE,IAAII;CACH,SAASC,GAAmC;AAC3C,WAAS1M,EAAOc,GAAO6L,GAAY;AACjC,WAAO,EAAE,OAAA7L,GAAO,YAAA6L,EAAAA;EACpB;AACED,IAAkC,SAAS1M;AAC3C,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAA+BY,KAAc,QAAQE,EAAM,GAAGF,EAAU,KAAK,MAAMC,EAAG,OAAOD,EAAU,UAAU,KAAKA,EAAU,eAAe;EACnJ;AACEuM,IAAkC,KAAKpN;AACzC,GAAGmN,OAAqCA,KAAmC,CAAE,EAAC;AAC9E,IAAIG;CACH,SAASC,GAAqB;AAC7B,WAAS7M,EAAO8M,GAASC,GAAiB;AACxC,WAAO,EAAE,SAAAD,GAAS,iBAAAC,EAAAA;EACtB;AACEF,IAAoB,SAAS7M;AAC7B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,QAAQD,CAAS,KAAKE,EAAM,GAAGd,EAAM,eAAe;EAClE;AACEsN,IAAoB,KAAKvN;AAC3B,GAAGsN,OAAuBA,KAAqB,CAAE,EAAC;AAClD,IAAII;CACH,SAASC,GAAgB;AACxBA,IAAe,OAAO,GACtBA,EAAe,YAAY;AAC3B,WAAS3N,EAAGC,GAAO;AACjB,WAAOA,MAAU,KAAKA,MAAU;EACpC;AACE0N,IAAe,KAAK3N;AACtB,GAAG0N,OAAkBA,KAAgB,CAAE,EAAC;AACxC,IAAIE;CACH,SAASC,GAAqB;AAC7B,WAASnN,EAAOT,GAAO;AACrB,WAAO,EAAE,OAAAA,EAAK;EAClB;AACE4N,IAAoB,SAASnN;AAC7B,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,MAAMA,EAAU,YAAY,UAAUC,EAAG,OAAOD,EAAU,OAAO,KAAK8G,EAAc,GAAG9G,EAAU,OAAO,OAAOA,EAAU,aAAa,UAAUQ,EAAS,GAAGR,EAAU,QAAQ,OAAOA,EAAU,YAAY,UAAU6D,EAAQ,GAAG7D,EAAU,OAAO;EACtR;AACEgN,IAAoB,KAAK7N;AAC3B,GAAG4N,OAAuBA,KAAqB,CAAE,EAAC;AAClD,IAAIE;CACH,SAASC,GAAY;AACpB,WAASrN,EAAOyE,GAAUzC,GAAOY,GAAM;AACrC,UAAME,IAAS,EAAE,UAAA2B,GAAU,OAAAzC,EAAAA;AAC3B,WAAIY,MAAS,WACXE,EAAO,OAAOF,IAETE;EACX;AACEuK,IAAW,SAASrN;AACpB,WAASV,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKL,EAAS,GAAGK,EAAU,QAAQ,MAAMC,EAAG,OAAOD,EAAU,KAAK,KAAKC,EAAG,WAAWD,EAAU,OAAO+M,GAAmB,EAAE,OAAO/M,EAAU,SAAS,UAAU6M,GAAc,GAAG7M,EAAU,IAAI,MAAMA,EAAU,cAAc,UAAUC,EAAG,WAAWD,EAAU,WAAWgC,EAAS,EAAE,MAAMhC,EAAU,YAAY,UAAUC,EAAG,OAAOD,EAAU,OAAO,KAAK8G,EAAc,GAAG9G,EAAU,OAAO,OAAOA,EAAU,gBAAgB,UAAUC,EAAG,QAAQD,EAAU,WAAW,OAAOA,EAAU,iBAAiB,UAAUC,EAAG,QAAQD,EAAU,YAAY;EACzjB;AACEkN,IAAW,KAAK/N;AAClB,GAAG8N,OAAcA,KAAY,CAAE,EAAC;AAChC,IAAIE;CACH,SAASC,GAAc;AACtB,WAASC,EAAcjO,GAAO;AAC5B,WAAO,EAAE,MAAM,WAAW,OAAAA,EAAAA;EAC9B;AACEgO,IAAa,gBAAgBC;AAC/B,GAAGF,OAAgBA,KAAc,CAAE,EAAC;AACpC,IAAIG;CACH,SAASC,GAAuB;AAC/B,WAAS1N,EAAO2N,GAAYC,GAAY9M,GAAOqD,GAAS;AACtD,WAAO,EAAE,YAAAwJ,GAAY,YAAAC,GAAY,OAAA9M,GAAO,SAAAqD,EAAO;EACnD;AACEuJ,IAAsB,SAAS1N;AACjC,GAAGyN,OAAyBA,KAAuB,CAAE,EAAC;AACtD,IAAII;CACH,SAASC,GAAuB;AAC/B,WAAS9N,EAAOmI,GAAO;AACrB,WAAO,EAAE,OAAAA,EAAK;EAClB;AACE2F,IAAsB,SAAS9N;AACjC,GAAG6N,OAAyBA,KAAuB,CAAE,EAAC;AACtD,IAAIE;CACH,SAASC,GAA8B;AACtCA,IAA6B,UAAU,GACvCA,EAA6B,YAAY;AAC3C,GAAGD,OAAgCA,KAA8B,CAAE,EAAC;AACpE,IAAIE;CACH,SAASC,GAAyB;AACjC,WAASlO,EAAOc,GAAOgG,GAAM;AAC3B,WAAO,EAAE,OAAAhG,GAAO,MAAAgG,EAAAA;EACpB;AACEoH,IAAwB,SAASlO;AACnC,GAAGiO,OAA2BA,KAAyB,CAAE,EAAC;AAC1D,IAAIE;CACH,SAASC,GAA0B;AAClC,WAASpO,EAAO4K,GAAayD,GAAwB;AACnD,WAAO,EAAE,aAAAzD,GAAa,wBAAAyD,EAAAA;EAC1B;AACED,IAAyB,SAASpO;AACpC,GAAGmO,OAA4BA,KAA0B,CAAE,EAAC;AAC5D,IAAIG;CACH,SAASC,GAAkB;AAC1B,WAASjP,EAAGC,GAAO;AACjB,UAAMY,IAAYZ;AAClB,WAAOa,EAAG,cAAcD,CAAS,KAAKX,EAAI,GAAGW,EAAU,GAAG,KAAKC,EAAG,OAAOD,EAAU,IAAI;EAC3F;AACEoO,IAAiB,KAAKjP;AACxB,GAAGgP,OAAoBA,KAAkB,CAAE,EAAC;AAC5C,IAAIE;CACH,SAASC,GAAe;AACvB,WAASzO,EAAOa,GAAKgG,GAAYJ,GAASiI,GAAS;AACjD,WAAO,IAAIC,GAAiB9N,GAAKgG,GAAYJ,GAASiI,CAAO;EACjE;AACED,IAAc,SAASzO;AACvB,WAASV,EAAGC,GAAO;AACjB,QAAIY,IAAYZ;AAChB,WAAO,CAAA,EAAAa,EAAG,QAAQD,CAAS,KAAKC,EAAG,OAAOD,EAAU,GAAG,MAAMC,EAAG,UAAUD,EAAU,UAAU,KAAKC,EAAG,OAAOD,EAAU,UAAU,MAAMC,EAAG,SAASD,EAAU,SAAS,KAAKC,EAAG,KAAKD,EAAU,OAAO,KAAKC,EAAG,KAAKD,EAAU,UAAU,KAAKC,EAAG,KAAKD,EAAU,QAAQ;EACxQ;AACEsO,IAAc,KAAKnP;AACnB,WAASsP,EAAWC,GAAUtJ,GAAO;AACnC,QAAIuB,IAAO+H,EAAS,QAAA,GAChBC,IAAcC,EAAUxJ,GAAO,CAACyJ,GAAGC,MAAM;AAC3C,UAAIC,IAAOF,EAAE,MAAM,MAAM,OAAOC,EAAE,MAAM,MAAM;AAC9C,aAAIC,MAAS,IACJF,EAAE,MAAM,MAAM,YAAYC,EAAE,MAAM,MAAM,YAE1CC;IACb,CAAK,GACGC,IAAqBrI,EAAK;AAC9B,aAASsI,IAAIN,EAAY,SAAS,GAAGM,KAAK,GAAGA,KAAK;AAChD,UAAIC,IAAIP,EAAYM,CAAC,GACjBE,IAAcT,EAAS,SAASQ,EAAE,MAAM,KAAK,GAC7CE,IAAYV,EAAS,SAASQ,EAAE,MAAM,GAAG;AAC7C,UAAIE,KAAaJ;AACfrI,YAAOA,EAAK,UAAU,GAAGwI,CAAW,IAAID,EAAE,UAAUvI,EAAK,UAAUyI,GAAWzI,EAAK,MAAM;;AAEzF,cAAM,IAAI,MAAM,kBAAkB;AAEpCqI,UAAqBG;IAC3B;AACI,WAAOxI;EACX;AACE2H,IAAc,aAAaG;AAC3B,WAASG,EAAU5D,GAAMqE,GAAS;AAChC,QAAIrE,EAAK,UAAU;AACjB,aAAOA;AAET,UAAMsE,IAAItE,EAAK,SAAS,IAAI,GACtBuE,IAAOvE,EAAK,MAAM,GAAGsE,CAAC,GACtBE,IAAQxE,EAAK,MAAMsE,CAAC;AAC1BV,MAAUW,GAAMF,CAAO,GACvBT,EAAUY,GAAOH,CAAO;AACxB,QAAII,IAAU,GACVC,IAAW,GACXT,IAAI;AACR,WAAOQ,IAAUF,EAAK,UAAUG,IAAWF,EAAM;AACrCH,QAAQE,EAAKE,CAAO,GAAGD,EAAME,CAAQ,CAAC,KACrC,IACT1E,EAAKiE,GAAG,IAAIM,EAAKE,GAAS,IAE1BzE,EAAKiE,GAAG,IAAIO,EAAME,GAAU;AAGhC,WAAOD,IAAUF,EAAK;AACpBvE,QAAKiE,GAAG,IAAIM,EAAKE,GAAS;AAE5B,WAAOC,IAAWF,EAAM;AACtBxE,QAAKiE,GAAG,IAAIO,EAAME,GAAU;AAE9B,WAAO1E;EACX;AACA,GAAGqD,OAAiBA,KAAe,CAAE,EAAC;AACtC,IAAIG,KAAmB,MAAM;EAC3B,YAAY9N,GAAKgG,GAAYJ,GAASiI,GAAS;AAC7C,SAAK,OAAO7N,GACZ,KAAK,cAAcgG,GACnB,KAAK,WAAWJ,GAChB,KAAK,WAAWiI,GAChB,KAAK,eAAe;EACxB;EACE,IAAI,MAAM;AACR,WAAO,KAAK;EAChB;EACE,IAAI,aAAa;AACf,WAAO,KAAK;EAChB;EACE,IAAI,UAAU;AACZ,WAAO,KAAK;EAChB;EACE,QAAQ5N,GAAO;AACb,QAAIA,GAAO;AACT,UAAIgP,IAAQ,KAAK,SAAShP,EAAM,KAAK,GACjCiP,IAAM,KAAK,SAASjP,EAAM,GAAG;AACjC,aAAO,KAAK,SAAS,UAAUgP,GAAOC,CAAG;IAC/C;AACI,WAAO,KAAK;EAChB;EACE,OAAOC,GAAOvJ,GAAS;AACrB,SAAK,WAAWuJ,EAAM,MACtB,KAAK,WAAWvJ,GAChB,KAAK,eAAe;EACxB;EACE,iBAAiB;AACf,QAAI,KAAK,iBAAiB,QAAQ;AAChC,UAAIwJ,IAAc,CAAA,GACdnJ,IAAO,KAAK,UACZoJ,IAAc;AAClB,eAASd,IAAI,GAAGA,IAAItI,EAAK,QAAQsI,KAAK;AAChCc,cACFD,EAAY,KAAKb,CAAC,GAClBc,IAAc;AAEhB,YAAIC,IAAKrJ,EAAK,OAAOsI,CAAC;AACtBc,YAAcC,MAAO,QAAQA,MAAO;GAChCA,MAAO,QAAQf,IAAI,IAAItI,EAAK,UAAUA,EAAK,OAAOsI,IAAI,CAAC,MAAM;KAC/DA;MAEV;AACUc,WAAepJ,EAAK,SAAS,KAC/BmJ,EAAY,KAAKnJ,EAAK,MAAM,GAE9B,KAAK,eAAemJ;IAC1B;AACI,WAAO,KAAK;EAChB;EACE,WAAWG,GAAQ;AACjBA,QAAS,KAAK,IAAI,KAAK,IAAIA,GAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,QAAIH,IAAc,KAAK,eAAA,GACnBI,IAAM,GAAGC,IAAOL,EAAY;AAChC,QAAIK,MAAS;AACX,aAAOxQ,EAAS,OAAO,GAAGsQ,CAAM;AAElC,WAAOC,IAAMC,KAAM;AACjB,UAAIC,IAAM,KAAK,OAAOF,IAAMC,KAAQ,CAAC;AACjCL,QAAYM,CAAG,IAAIH,IACrBE,IAAOC,IAEPF,IAAME,IAAM;IAEpB;AACI,QAAItQ,IAAOoQ,IAAM;AACjB,WAAOvQ,EAAS,OAAOG,GAAMmQ,IAASH,EAAYhQ,CAAI,CAAC;EAC3D;EACE,SAASwE,GAAU;AACjB,QAAIwL,IAAc,KAAK,eAAA;AACvB,QAAIxL,EAAS,QAAQwL,EAAY;AAC/B,aAAO,KAAK,SAAS;AAChB,QAAIxL,EAAS,OAAO;AACzB,aAAO;AAET,QAAI+L,IAAaP,EAAYxL,EAAS,IAAI,GACtCgM,IAAiBhM,EAAS,OAAO,IAAIwL,EAAY,SAASA,EAAYxL,EAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC7G,WAAO,KAAK,IAAI,KAAK,IAAI+L,IAAa/L,EAAS,WAAWgM,CAAc,GAAGD,CAAU;EACzF;EACE,IAAI,YAAY;AACd,WAAO,KAAK,eAAgB,EAAC;EACjC;AACA;AArFA,IAsFIpQ;CACH,SAASsQ,GAAK;AACb,QAAMC,IAAW,OAAO,UAAU;AAClC,WAASC,EAAQrR,GAAO;AACtB,WAAO,OAAOA,IAAU;EAC5B;AACEmR,IAAI,UAAUE;AACd,WAASC,EAAWtR,GAAO;AACzB,WAAO,OAAOA,IAAU;EAC5B;AACEmR,IAAI,YAAYG;AAChB,WAASC,EAAQvR,GAAO;AACtB,WAAOA,MAAU,QAAQA,MAAU;EACvC;AACEmR,IAAI,UAAUI;AACd,WAASC,EAAOxR,GAAO;AACrB,WAAOoR,EAAS,KAAKpR,CAAK,MAAM;EACpC;AACEmR,IAAI,SAASK;AACb,WAASC,EAAOzR,GAAO;AACrB,WAAOoR,EAAS,KAAKpR,CAAK,MAAM;EACpC;AACEmR,IAAI,SAASM;AACb,WAASC,EAAY1R,GAAO2R,GAAKC,GAAK;AACpC,WAAOR,EAAS,KAAKpR,CAAK,MAAM,qBAAqB2R,KAAO3R,KAASA,KAAS4R;EAClF;AACET,IAAI,cAAcO;AAClB,WAAStR,EAASJ,GAAO;AACvB,WAAOoR,EAAS,KAAKpR,CAAK,MAAM,qBAAqB,eAAeA,KAASA,KAAS;EAC1F;AACEmR,IAAI,UAAU/Q;AACd,WAASE,EAAUN,GAAO;AACxB,WAAOoR,EAAS,KAAKpR,CAAK,MAAM,qBAAqB,KAAKA,KAASA,KAAS;EAChF;AACEmR,IAAI,WAAW7Q;AACf,WAASuR,EAAK7R,GAAO;AACnB,WAAOoR,EAAS,KAAKpR,CAAK,MAAM;EACpC;AACEmR,IAAI,OAAOU;AACX,WAASC,EAAc9R,GAAO;AAC5B,WAAOA,MAAU,QAAQ,OAAOA,KAAU;EAC9C;AACEmR,IAAI,gBAAgBW;AACpB,WAASC,EAAW/R,GAAOgS,GAAO;AAChC,WAAO,MAAM,QAAQhS,CAAK,KAAKA,EAAM,MAAMgS,CAAK;EACpD;AACEb,IAAI,aAAaY;AACnB,GAAGlR,MAAOA,IAAK,CAAE,EAAC;AAGd,IAAAoR,KAAqB,MAAM;EAC7B,YAAYC,GAAaC,GAASC,GAAmB;AACnD,SAAK,cAAcF,GACnB,KAAK,UAAUC,GACf,KAAK,eAAe,CAAA,GACpB,KAAK,YAA4B,uBAAO,OAAO,IAAI;AACnD,UAAME,IAAa,CAACC,MAAU;AAC5B,UAAIC,IAASD,EAAM,cAAA;AACnB,UAAIC,MAAW,KAAK;AAClB;AAEF,UAAIC;AACJ,WAAK,UAAUF,EAAM,IAAI,SAAQ,CAAE,IAAIA,EAAM,mBAAmB,MAAM;AACpE,eAAO,aAAaE,CAAM,GAC1BA,IAAS,OAAO,WAAW,MAAM,KAAK,YAAYF,EAAM,KAAKC,CAAM,GAAG,GAAG;MACjF,CAAO,GACD,KAAK,YAAYD,EAAM,KAAKC,CAAM;IACxC,GACUE,IAAiB,CAACH,MAAU;AAChClT,QAA2B,OAAO,gBAAgBkT,GAAO,KAAK,aAAa,CAAA,CAAE;AAC7E,UAAII,IAASJ,EAAM,IAAI,SAAQ,GAC3BK,IAAW,KAAK,UAAUD,CAAM;AAChCC,YACFA,EAAS,QAAO,GAChB,OAAO,KAAK,UAAUD,CAAM;IAEpC;AACI,SAAK,aAAa,KAAKtT,EAA2B,OAAO,iBAAiBiT,CAAU,CAAC,GACrF,KAAK,aAAa,KAAKjT,EAA2B,OAAO,mBAAmBqT,CAAc,CAAC,GAC3F,KAAK,aAAa;MAChBrT,EAA2B,OAAO,yBAAyB,CAACqR,MAAU;AACpEgC,UAAehC,EAAM,KAAK,GAC1B4B,EAAW5B,EAAM,KAAK;MAC9B,CAAO;IACP,GACI,KAAK,aAAa;MAChB2B,EAAkB,CAACxS,MAAM;AACvBR,UAA2B,OAAO,UAAW,EAAC,QAAQ,CAACkT,MAAU;AAC3DA,YAAM,cAAA,MAAoB,KAAK,gBACjCG,EAAeH,CAAK,GACpBD,EAAWC,CAAK;QAE5B,CAAS;MACT,CAAO;IACP,GACI,KAAK,aAAa,KAAK;MACrB,SAAS,MAAM;AACblT,UAA2B,OAAO,UAAW,EAAC,QAAQqT,CAAc;AACpE,iBAAS1T,KAAO,KAAK;AACnB,eAAK,UAAUA,CAAG,EAAE,QAAO;MAErC;IACA,CAAK,GACDK,EAA2B,OAAO,UAAW,EAAC,QAAQiT,CAAU;EACpE;EACE,UAAU;AACR,SAAK,aAAa,QAAQ,CAACO,MAAMA,KAAKA,EAAE,QAAO,CAAE,GACjD,KAAK,aAAa,SAAS;EAC/B;EACE,YAAYC,GAAUvL,GAAY;AAChC,SAAK,QAAQuL,CAAQ,EAAE,KAAK,CAACC,MACpBA,EAAQ,aAAaD,EAAS,SAAU,CAAA,CAChD,EAAE,KAAK,CAAC1H,MAAgB;AACvB,YAAM4H,IAAU5H,EAAY,IAAI,CAACyH,MAAMI,GAAcH,GAAUD,CAAC,CAAC;AACjE,UAAIN,IAAQlT,EAA2B,OAAO,SAASyT,CAAQ;AAC3DP,WAASA,EAAM,cAAa,MAAOhL,KACrClI,EAA2B,OAAO,gBAAgBkT,GAAOhL,GAAYyL,CAAO;IAE/E,CAAA,EAAE,KAAK,QAAQ,CAACE,MAAQ;IAE7B,CAAK;EACL;AACA;AACA,SAASC,GAAWC,GAAY;AAC9B,UAAQA,GAAU;IAChB,KAAKvP,EAAmB;AACtB,aAAOxE,EAA2B,eAAe;IACnD,KAAKwE,EAAmB;AACtB,aAAOxE,EAA2B,eAAe;IACnD,KAAKwE,EAAmB;AACtB,aAAOxE,EAA2B,eAAe;IACnD,KAAKwE,EAAmB;AACtB,aAAOxE,EAA2B,eAAe;IACnD;AACE,aAAOA,EAA2B,eAAe;EACvD;AACA;AACA,SAAS4T,GAAcH,GAAUO,GAAM;AACrC,MAAI/O,IAAO,OAAO+O,EAAK,QAAS,WAAW,OAAOA,EAAK,IAAI,IAAIA,EAAK;AACpE,SAAO;IACL,UAAUF,GAAWE,EAAK,QAAQ;IAClC,iBAAiBA,EAAK,MAAM,MAAM,OAAO;IACzC,aAAaA,EAAK,MAAM,MAAM,YAAY;IAC1C,eAAeA,EAAK,MAAM,IAAI,OAAO;IACrC,WAAWA,EAAK,MAAM,IAAI,YAAY;IACtC,SAASA,EAAK;IACd,MAAA/O;IACA,QAAQ+O,EAAK;EACjB;AACA;AACI,IAAAC,KAAoB,MAAM;EAC5B,YAAYlB,GAASmB,GAAoB;AACvC,SAAK,UAAUnB,GACf,KAAK,qBAAqBmB;EAC9B;EACE,IAAI,oBAAoB;AACtB,WAAO,KAAK;EAChB;EACE,uBAAuBhB,GAAOpN,GAAUqO,GAASC,GAAO;AACtD,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,WAAWD,EAAS,SAAQ,GAAIY,EAAavO,CAAQ,CAAC,CACtE,EAAE,KAAK,CAACwO,MAAS;AAChB,UAAI,CAACA;AACH;AAEF,YAAMC,IAAWrB,EAAM,qBAAqBpN,CAAQ,GAC9C0O,IAAY,IAAIxU,EAA2B;QAC/C8F,EAAS;QACTyO,EAAS;QACTzO,EAAS;QACTyO,EAAS;MACjB,GACY/K,IAAQ8K,EAAK,MAAM,IAAI,CAACG,MAAU;AACtC,cAAMC,IAAO;UACX,OAAOD,EAAM;UACb,YAAYA,EAAM,cAAcA,EAAM;UACtC,UAAUA,EAAM;UAChB,YAAYA,EAAM;UAClB,eAAeA,EAAM;UACrB,QAAQA,EAAM;UACd,SAASE,GAAUF,EAAM,OAAO;UAChC,OAAOD;UACP,MAAMI,GAAqBH,EAAM,IAAI;QAC/C;AACQ,eAAIA,EAAM,aACJI,GAAoBJ,EAAM,QAAQ,IACpCC,EAAK,QAAQ;UACX,QAAQI,EAAQL,EAAM,SAAS,MAAM;UACrC,SAASK,EAAQL,EAAM,SAAS,OAAO;QACrD,IAEYC,EAAK,QAAQI,EAAQL,EAAM,SAAS,KAAK,GAE3CC,EAAK,aAAaD,EAAM,SAAS,UAE/BA,EAAM,wBACRC,EAAK,sBAAsBD,EAAM,oBAAoB,IAAIM,CAAU,IAEjEN,EAAM,qBAAqB/L,GAAiB,YAC9CgM,EAAK,kBAAkB1U,EAA2B,UAAU,6BAA6B,kBAEpF0U;MACf,CAAO;AACD,aAAO;QACL,cAAcJ,EAAK;QACnB,aAAa9K;MACrB;IACA,CAAK;EACL;AACA;AACA,SAAS6K,EAAavO,GAAU;AAC9B,MAAKA;AAGL,WAAO,EAAE,WAAWA,EAAS,SAAS,GAAG,MAAMA,EAAS,aAAa,EAAA;AACvE;AACA,SAASkP,GAAU7S,GAAO;AACxB,MAAKA;AAGL,WAAO;MACL,OAAO;QACL,MAAMA,EAAM,kBAAkB;QAC9B,WAAWA,EAAM,cAAc;MAChC;MACD,KAAK,EAAE,MAAMA,EAAM,gBAAgB,GAAG,WAAWA,EAAM,YAAY,EAAC;IACxE;AACA;AACA,SAAS2S,EAAQ3S,GAAO;AACtB,MAAKA;AAGL,WAAO,IAAInC,EAA2B;MACpCmC,EAAM,MAAM,OAAO;MACnBA,EAAM,MAAM,YAAY;MACxBA,EAAM,IAAI,OAAO;MACjBA,EAAM,IAAI,YAAY;IAC1B;AACA;AACA,SAAS0S,GAAoBI,GAAM;AACjC,SAAO,OAAOA,EAAK,SAAW,OAAe,OAAOA,EAAK,UAAY;AACvE;AACA,SAASL,GAAqB3Q,GAAM;AAClC,QAAMiR,IAAYlV,EAA2B,UAAU;AACvD,UAAQiE,GAAI;IACV,KAAKuE,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;IACnB,KAAK1M,EAAmB;AACtB,aAAO0M,EAAU;EACvB;AACE,SAAOA,EAAU;AACnB;AACA,SAASH,EAAWzR,GAAU;AAC5B,MAAKA;AAGL,WAAO;MACL,OAAOwR,EAAQxR,EAAS,KAAK;MAC7B,MAAMA,EAAS;IACnB;AACA;AACA,SAASqR,GAAUQ,GAAG;AACpB,SAAOA,KAAKA,EAAE,YAAY,iCAAiC,EAAE,IAAIA,EAAE,SAAS,OAAOA,EAAE,OAAO,WAAWA,EAAE,UAAS,IAAK;AACzH;AACI,IAAAC,KAAe,MAAM;EACvB,YAAYrC,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,aAAaG,GAAOpN,GAAUsO,GAAO;AACnC,QAAIX,IAAWP,EAAM;AACrB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,QAAQD,EAAS,SAAQ,GAAIY,EAAavO,CAAQ,CAAC,CACnE,EAAE,KAAK,CAACwO,MAAS;AAChB,UAAKA;AAGL,eAAO;UACL,OAAOQ,EAAQR,EAAK,KAAK;UACzB,UAAUe,GAAoBf,EAAK,QAAQ;QACnD;IACA,CAAK;EACL;AACA;AACA,SAASgB,GAAgBC,GAAO;AAC9B,SAAOA,KAAS,OAAOA,KAAU,YAAY,OAAOA,EAAM,QAAS;AACrE;AACA,SAASC,GAAiBf,GAAO;AAC/B,SAAI,OAAOA,KAAU,WACZ;IACL,OAAOA;EACb,IAEMa,GAAgBb,CAAK,IACnBA,EAAM,SAAS,cACV;IACL,OAAOA,EAAM,MAAM,QAAQ,yBAAyB,MAAM;EAClE,IAEW;IACL,OAAOA,EAAM;EACnB,IAES,EAAE,OAAO,QAAQA,EAAM,WAAW;IAAOA,EAAM,QAAQ,UAAA;AAChE;AACA,SAASY,GAAoBI,GAAU;AACrC,MAAKA;AAGL,WAAI,MAAM,QAAQA,CAAQ,IACjBA,EAAS,IAAID,EAAgB,IAE/B,CAACA,GAAiBC,CAAQ,CAAC;AACpC;AACI,IAAAC,KAA2B,MAAM;EACnC,YAAY3C,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,0BAA0BG,GAAOpN,GAAUsO,GAAO;AAChD,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAAYA,EAAQ,uBAAuBD,EAAS,SAAU,GAAEY,EAAavO,CAAQ,CAAC,CAAC,EAAE,KAAK,CAAC6P,MAAY;AAC7I,UAAKA;AAGL,eAAOA,EAAQ,IAAI,CAAClB,OACX;UACL,OAAOK,EAAQL,EAAM,KAAK;UAC1B,MAAMmB,GAAwBnB,EAAM,IAAI;QAClD,EACO;IACP,CAAK;EACL;AACA;AACA,SAASmB,GAAwB3R,GAAM;AACrC,UAAQA,GAAI;IACV,KAAKqG,EAAsB;AACzB,aAAOtK,EAA2B,UAAU,sBAAsB;IACpE,KAAKsK,EAAsB;AACzB,aAAOtK,EAA2B,UAAU,sBAAsB;IACpE,KAAKsK,EAAsB;AACzB,aAAOtK,EAA2B,UAAU,sBAAsB;EACxE;AACE,SAAOA,EAA2B,UAAU,sBAAsB;AACpE;AACI,IAAA6V,KAAoB,MAAM;EAC5B,YAAY9C,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,kBAAkBG,GAAOpN,GAAUsO,GAAO;AACxC,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,eAAeD,EAAS,SAAQ,GAAIY,EAAavO,CAAQ,CAAC,CAC1E,EAAE,KAAK,CAACgQ,MAAe;AACtB,UAAKA;AAGL,eAAO,CAACC,GAAWD,CAAU,CAAC;IACpC,CAAK;EACL;AACA;AACA,SAASC,GAAWzR,GAAU;AAC5B,SAAO;IACL,KAAKtE,EAA2B,IAAI,MAAMsE,EAAS,GAAG;IACtD,OAAOwQ,EAAQxQ,EAAS,KAAK;EACjC;AACA;AACI,IAAA0R,KAAmB,MAAM;EAC3B,YAAYjD,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,kBAAkBG,GAAOpN,GAAUqO,GAASC,GAAO;AACjD,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,eAAeD,EAAS,SAAQ,GAAIY,EAAavO,CAAQ,CAAC,CAC1E,EAAE,KAAK,CAAC6P,MAAY;AACnB,UAAKA;AAGL,eAAOA,EAAQ,IAAII,EAAU;IACnC,CAAK;EACL;AACA;AAfI,IAgBAE,KAAgB,MAAM;EACxB,YAAYlD,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,mBAAmBG,GAAOpN,GAAUoQ,GAAS9B,GAAO;AAClD,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,SAASD,EAAS,SAAU,GAAEY,EAAavO,CAAQ,GAAGoQ,CAAO,CAC7E,EAAE,KAAK,CAACjB,MACAkB,GAAgBlB,CAAI,CAC5B;EACL;AACA;AACA,SAASkB,GAAgBlB,GAAM;AAC7B,MAAI,CAACA,KAAQ,CAACA,EAAK;AACjB;AAEF,MAAImB,IAAgB,CAAA;AACpB,WAASlU,KAAO+S,EAAK,SAAS;AAC5B,UAAMoB,IAAOrW,EAA2B,IAAI,MAAMkC,CAAG;AACrD,aAASwO,KAAKuE,EAAK,QAAQ/S,CAAG;AAC5BkU,QAAc,KAAK;QACjB,UAAUC;QACV,WAAW;QACX,UAAU;UACR,OAAOvB,EAAQpE,EAAE,KAAK;UACtB,MAAMA,EAAE;QAClB;MACA,CAAO;EAEP;AACE,SAAO;IACL,OAAO0F;EACX;AACA;AACI,IAAAE,KAAwB,MAAM;EAChC,YAAYvD,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,uBAAuBG,GAAOkB,GAAO;AACnC,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAAYA,EAAQ,oBAAoBD,EAAS,SAAU,CAAA,CAAC,EAAE,KAAK,CAACjK,MAAU;AAChH,UAAKA;AAGL,eAAOA,EAAM,IAAI,CAACkL,MACZ6B,GAAiB7B,CAAI,IAChB8B,GAAiB9B,CAAI,IAEvB;UACL,MAAMA,EAAK;UACX,QAAQ;UACR,eAAeA,EAAK;UACpB,MAAM+B,GAAa/B,EAAK,IAAI;UAC5B,OAAOI,EAAQJ,EAAK,SAAS,KAAK;UAClC,gBAAgBI,EAAQJ,EAAK,SAAS,KAAK;UAC3C,MAAM,CAAA;QAChB,CACO;IACP,CAAK;EACL;AACA;AACA,SAAS6B,GAAiBG,GAAQ;AAChC,SAAO,cAAcA;AACvB;AACA,SAASF,GAAiBE,GAAQ;AAChC,SAAO;IACL,MAAMA,EAAO;IACb,QAAQA,EAAO,UAAU;IACzB,MAAMD,GAAaC,EAAO,IAAI;IAC9B,OAAO5B,EAAQ4B,EAAO,KAAK;IAC3B,gBAAgB5B,EAAQ4B,EAAO,cAAc;IAC7C,MAAMA,EAAO,QAAQ,CAAE;IACvB,WAAWA,EAAO,YAAY,CAAE,GAAE,IAAI,CAAChC,MAAS8B,GAAiB9B,CAAI,CAAC;EAC1E;AACA;AACA,SAAS+B,GAAaxS,GAAM;AAC1B,MAAI0S,IAAQ3W,EAA2B,UAAU;AACjD,UAAQiE,GAAI;IACV,KAAKyG,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;IACf,KAAKjM,EAAW;AACd,aAAOiM,EAAM;EACnB;AACE,SAAOA,EAAM;AACf;AACI,IAAAC,KAAsB,MAAM;EAC9B,YAAY7D,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,aAAaG,GAAOkB,GAAO;AACzB,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAAYA,EAAQ,kBAAkBD,EAAS,SAAU,CAAA,CAAC,EAAE,KAAK,CAACjK,MAAU;AAC9G,UAAKA;AAGL,eAAO;UACL,OAAOA,EAAM,IAAI,CAACkL,OAAU;YAC1B,OAAOI,EAAQJ,EAAK,KAAK;YACzB,KAAKA,EAAK;UACpB,EAAU;QACV;IACA,CAAK;EACL;AACA;AAlBI,IAmBAmC,KAAiC,MAAM;EACzC,YAAY9D,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,+BAA+BG,GAAOlM,GAASoN,GAAO;AACpD,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,OAAOD,EAAS,SAAQ,GAAI,MAAMqD,GAAsB9P,CAAO,CAAC,EAAE,KAAK,CAACJ,MAAU;AAC/F,UAAI,EAAA,CAACA,KAASA,EAAM,WAAW;AAG/B,eAAOA,EAAM,IAAImO,CAAU;IACnC,CAAO,CACF;EACL;AACA;AAlCI,IAmCAgC,KAAsC,MAAM;EAC9C,YAAYhE,GAAS;AACnB,SAAK,UAAUA,GACf,KAAK,0BAA0B;EACnC;EACE,oCAAoCG,GAAO/Q,GAAO6E,GAASoN,GAAO;AAChE,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAC3BA,EAAQ,OAAOD,EAAS,SAAU,GAAEuB,GAAU7S,CAAK,GAAG2U,GAAsB9P,CAAO,CAAC,EAAE,KAAK,CAACJ,MAAU;AAC3G,UAAI,EAAA,CAACA,KAASA,EAAM,WAAW;AAG/B,eAAOA,EAAM,IAAImO,CAAU;IACnC,CAAO,CACF;EACL;AACA;AACA,SAAS+B,GAAsB9P,GAAS;AACtC,SAAO;IACL,SAASA,EAAQ;IACjB,cAAcA,EAAQ;EAC1B;AACA;AACI,IAAAgQ,KAAuB,MAAM;EAC/B,YAAYjE,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,sBAAsBG,GAAOkB,GAAO;AAClC,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAAYA,EAAQ,mBAAmBD,EAAS,SAAU,CAAA,CAAC,EAAE,KAAK,CAACwD,MAAU;AAC/G,UAAKA;AAGL,eAAOA,EAAM,IAAI,CAACvC,OAAU;UAC1B,OAAOA,EAAK;UACZ,OAAOI,EAAQJ,EAAK,KAAK;QAC1B,EAAC;IACR,CAAK;EACL;EACE,0BAA0BxB,GAAOoB,GAAMF,GAAO;AAC5C,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE;MAC5B,CAACC,MAAYA,EAAQ,sBAAsBD,EAAS,SAAQ,GAAIa,EAAK,OAAOU,GAAUV,EAAK,KAAK,CAAC;IACvG,EAAM,KAAK,CAAC4C,MAAkB;AACxB,UAAKA;AAGL,eAAOA,EAAc,IAAI,CAACC,MAAiB;AACzC,cAAIzC,IAAO;YACT,OAAOyC,EAAa;UAC9B;AACQ,iBAAIA,EAAa,aACfzC,EAAK,WAAWK,EAAWoC,EAAa,QAAQ,IAE9CA,EAAa,wBACfzC,EAAK,sBAAsByC,EAAa,oBAAoB,IAAIpC,CAAU,IAErEL;QACf,CAAO;IACP,CAAK;EACL;AACA;AAtCI,IAuCA0C,KAAsB,MAAM;EAC9B,YAAYrE,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,qBAAqBG,GAAOiB,GAASC,GAAO;AAC1C,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE,KAAK,CAACC,MAAYA,EAAQ,iBAAiBD,EAAS,SAAQ,GAAIU,CAAO,CAAC,EAAE,KAAK,CAACkD,MAAW;AACvH,UAAKA;AAGL,eAAOA,EAAO,IAAI,CAAClV,MAAU;AAC3B,gBAAMgC,IAAS;YACb,OAAOhC,EAAM,YAAY;YACzB,KAAKA,EAAM,UAAU;UAC/B;AACQ,iBAAI,OAAOA,EAAM,OAAS,QACxBgC,EAAO,OAAOmT,GAAmBnV,EAAM,IAAI,IAEtCgC;QACf,CAAO;IACP,CAAK;EACL;AACA;AACA,SAASmT,GAAmBrT,GAAM;AAChC,UAAQA,GAAI;IACV,KAAKR,EAAiB;AACpB,aAAOzD,EAA2B,UAAU,iBAAiB;IAC/D,KAAKyD,EAAiB;AACpB,aAAOzD,EAA2B,UAAU,iBAAiB;IAC/D,KAAKyD,EAAiB;AACpB,aAAOzD,EAA2B,UAAU,iBAAiB;EACnE;AAEA;AACI,IAAAuX,KAAwB,MAAM;EAChC,YAAYxE,GAAS;AACnB,SAAK,UAAUA;EACnB;EACE,uBAAuBG,GAAOsE,GAAWpD,GAAO;AAC9C,UAAMX,IAAWP,EAAM;AACvB,WAAO,KAAK,QAAQO,CAAQ,EAAE;MAC5B,CAACC,MAAYA,EAAQ;QACnBD,EAAS,SAAU;QACnB+D,EAAU,IAAInD,CAAY;MAClC;IACA,EAAM,KAAK,CAACoD,MAAoB;AAC1B,UAAKA;AAGL,eAAOA,EAAgB,IAAI,CAAClM,MAAmB;AAC7C,gBAAMpH,IAAS,CAAA;AACf,iBAAOoH;AACLpH,cAAO,KAAK,EAAE,OAAO2Q,EAAQvJ,EAAe,KAAK,EAAC,CAAE,GACpDA,IAAiBA,EAAe;AAElC,iBAAOpH;QACf,CAAO;IACP,CAAK;EACL;AACA;AAGA,SAASuT,GAAcvP,GAAMwP,IAAe,OAAO;AACjD,QAAMC,IAAMzP,EAAK;AACjB,MAAI0P,IAAM,GAAGjX,IAAQ,IAAIkX,IAAc,GAAG1D,IAAQ,IAAI2D,IAAa,GAAGC,IAAkB,GAAGC,IAAuB,GAAGC,IAA2B,GAAGC,IAAY;AAC/J,WAASC,EAAcC,GAAOC,GAAO;AACnC,QAAIC,IAAS,GACTC,IAAS;AACb,WAAOD,IAASF,KAAS,CAACC,KAAO;AAC/B,UAAI9G,IAAKrJ,EAAK,WAAW0P,CAAG;AAC5B,UAAIrG,KAAM,MAAMA,KAAM;AACpBgH,YAASA,IAAS,KAAKhH,IAAK;eACnBA,KAAM,MAAMA,KAAM;AAC3BgH,YAASA,IAAS,KAAKhH,IAAK,KAAK;eACxBA,KAAM,MAAMA,KAAM;AAC3BgH,YAASA,IAAS,KAAKhH,IAAK,KAAK;;AAEjC;AAEFqG,WACAU;IACN;AACI,WAAIA,IAASF,MACXG,IAAS,KAEJA;EACX;AACE,WAASC,EAAYC,GAAa;AAChCb,QAAMa,GACN9X,IAAQ,IACRkX,IAAc,GACd1D,IAAQ,IACR+D,IAAY;EAChB;AACE,WAASQ,IAAa;AACpB,QAAIxH,IAAQ0G;AACZ,QAAI1P,EAAK,WAAW0P,CAAG,MAAM;AAC3BA;;AAGA,WADAA,KACOA,IAAM1P,EAAK,UAAUyQ,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC;AACtDA;AAGJ,QAAIA,IAAM1P,EAAK,UAAUA,EAAK,WAAW0P,CAAG,MAAM;AAEhD,UADAA,KACIA,IAAM1P,EAAK,UAAUyQ,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC;AAEnD,aADAA,KACOA,IAAM1P,EAAK,UAAUyQ,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC;AACtDA;;AAGF,eAAAM,IAAY,GACLhQ,EAAK,UAAUgJ,GAAO0G,CAAG;AAGpC,QAAIzG,IAAMyG;AACV,QAAIA,IAAM1P,EAAK,WAAWA,EAAK,WAAW0P,CAAG,MAAM,MAAM1P,EAAK,WAAW0P,CAAG,MAAM;AAKhF,UAJAA,MACIA,IAAM1P,EAAK,UAAUA,EAAK,WAAW0P,CAAG,MAAM,MAAM1P,EAAK,WAAW0P,CAAG,MAAM,OAC/EA,KAEEA,IAAM1P,EAAK,UAAUyQ,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC,GAAG;AAEtD,aADAA,KACOA,IAAM1P,EAAK,UAAUyQ,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC;AACtDA;AAEFzG,YAAMyG;MACd;AACQM,YAAY;AAGhB,WAAOhQ,EAAK,UAAUgJ,GAAOC,CAAG;EACpC;AACE,WAASyH,IAAa;AACpB,QAAI1U,IAAS,IAAIgN,IAAQ0G;AACzB,eAAa;AACX,UAAIA,KAAOD,GAAK;AACdzT,aAAUgE,EAAK,UAAUgJ,GAAO0G,CAAG,GACnCM,IAAY;AACZ;MACR;AACM,YAAM3G,IAAKrJ,EAAK,WAAW0P,CAAG;AAC9B,UAAIrG,MAAO,IAAI;AACbrN,aAAUgE,EAAK,UAAUgJ,GAAO0G,CAAG,GACnCA;AACA;MACR;AACM,UAAIrG,MAAO,IAAI;AAGb,YAFArN,KAAUgE,EAAK,UAAUgJ,GAAO0G,CAAG,GACnCA,KACIA,KAAOD,GAAK;AACdO,cAAY;AACZ;QACV;AAEQ,gBADYhQ,EAAK,WAAW0P,GAAK,GACtB;UACT,KAAK;AACH1T,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACHA,iBAAU;AACV;UACF,KAAK;AACH,kBAAM2U,IAAMV,EAAc,GAAG,IAAI;AAC7BU,iBAAO,IACT3U,KAAU,OAAO,aAAa2U,CAAG,IAEjCX,IAAY;AAEd;UACF;AACEA,gBAAY;QACxB;AACQhH,YAAQ0G;AACR;MACR;AACM,UAAIrG,KAAM,KAAKA,KAAM;AACnB,YAAIuH,EAAYvH,CAAE,GAAG;AACnBrN,eAAUgE,EAAK,UAAUgJ,GAAO0G,CAAG,GACnCM,IAAY;AACZ;QACV;AACUA,cAAY;AAGhBN;IACN;AACI,WAAO1T;EACX;AACE,WAAS6U,KAAW;AAMlB,QALApY,IAAQ,IACRuX,IAAY,GACZL,IAAcD,GACdG,IAAkBD,GAClBG,IAA2BD,GACvBJ,KAAOD;AACT,aAAAE,IAAcF,GACPxD,IAAQ;AAEjB,QAAInP,IAAOkD,EAAK,WAAW0P,CAAG;AAC9B,QAAIoB,EAAahU,CAAI,GAAG;AACtB;AACE4S,aACAjX,KAAS,OAAO,aAAaqE,CAAI,GACjCA,IAAOkD,EAAK,WAAW0P,CAAG;aACnBoB,EAAahU,CAAI;AAC1B,aAAOmP,IAAQ;IACrB;AACI,QAAI2E,EAAY9T,CAAI;AAClB,aAAA4S,KACAjX,KAAS,OAAO,aAAaqE,CAAI,GAC7BA,MAAS,MAAMkD,EAAK,WAAW0P,CAAG,MAAM,OAC1CA,KACAjX,KAAS;IAEXmX,KACAE,IAAuBJ,GAChBzD,IAAQ;AAEjB,YAAQnP,GAAI;MACV,KAAK;AACH,eAAA4S,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACOzD,IAAQ;MACjB,KAAK;AACH,eAAAyD,KACAjX,IAAQiY,EAAU,GACXzE,IAAQ;MACjB,KAAK;AACH,cAAMjD,IAAQ0G,IAAM;AACpB,YAAI1P,EAAK,WAAW0P,IAAM,CAAC,MAAM,IAAI;AAEnC,eADAA,KAAO,GACAA,IAAMD,KACP,CAAAmB,EAAY5Q,EAAK,WAAW0P,CAAG,CAAC;AAGpCA;AAEF,iBAAAjX,IAAQuH,EAAK,UAAUgJ,GAAO0G,CAAG,GAC1BzD,IAAQ;QACzB;AACQ,YAAIjM,EAAK,WAAW0P,IAAM,CAAC,MAAM,IAAI;AACnCA,eAAO;AACP,gBAAMqB,IAAatB,IAAM;AACzB,cAAIuB,IAAgB;AACpB,iBAAOtB,IAAMqB,KAAY;AACvB,kBAAM1H,IAAKrJ,EAAK,WAAW0P,CAAG;AAC9B,gBAAIrG,MAAO,MAAMrJ,EAAK,WAAW0P,IAAM,CAAC,MAAM,IAAI;AAChDA,mBAAO,GACPsB,IAAgB;AAChB;YACd;AACYtB,iBACIkB,EAAYvH,CAAE,MACZA,MAAO,MAAMrJ,EAAK,WAAW0P,CAAG,MAAM,MACxCA,KAEFE,KACAE,IAAuBJ;UAErC;AACU,iBAAKsB,MACHtB,KACAM,IAAY,IAEdvX,IAAQuH,EAAK,UAAUgJ,GAAO0G,CAAG,GAC1BzD,IAAQ;QACzB;AACQ,eAAAxT,KAAS,OAAO,aAAaqE,CAAI,GACjC4S,KACOzD,IAAQ;MACjB,KAAK;AAGH,YAFAxT,KAAS,OAAO,aAAaqE,CAAI,GACjC4S,KACIA,MAAQD,KAAO,CAACgB,EAAQzQ,EAAK,WAAW0P,CAAG,CAAC;AAC9C,iBAAOzD,IAAQ;MAEnB,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAAxT,KAAS+X,EAAU,GACZvE,IAAQ;MACjB;AACE,eAAOyD,IAAMD,KAAOwB,GAA0BnU,CAAI;AAChD4S,eACA5S,IAAOkD,EAAK,WAAW0P,CAAG;AAE5B,YAAIC,MAAgBD,GAAK;AAEvB,kBADAjX,IAAQuH,EAAK,UAAU2P,GAAaD,CAAG,GAC/BjX,GAAK;YACX,KAAK;AACH,qBAAOwT,IAAQ;YACjB,KAAK;AACH,qBAAOA,IAAQ;YACjB,KAAK;AACH,qBAAOA,IAAQ;UAC7B;AACU,iBAAOA,IAAQ;QACzB;AACQ,eAAAxT,KAAS,OAAO,aAAaqE,CAAI,GACjC4S,KACOzD,IAAQ;IACvB;EACA;AACE,WAASgF,GAA0BnU,GAAM;AACvC,QAAIgU,EAAahU,CAAI,KAAK8T,EAAY9T,CAAI;AACxC,aAAO;AAET,YAAQA,GAAI;MACV,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO;IACf;AACI,WAAO;EACX;AACE,WAASoU,KAAoB;AAC3B,QAAIlV;AACJ;AACEA,UAAS6U,GAAQ;WACV7U,KAAU,MAAMA,KAAU;AACnC,WAAOA;EACX;AACE,SAAO;IACL,aAAAsU;IACA,aAAa,MAAMZ;IACnB,MAAMF,IAAe0B,KAAoBL;IACzC,UAAU,MAAM5E;IAChB,eAAe,MAAMxT;IACrB,gBAAgB,MAAMkX;IACtB,gBAAgB,MAAMD,IAAMC;IAC5B,mBAAmB,MAAME;IACzB,wBAAwB,MAAMF,IAAcI;IAC5C,eAAe,MAAMC;EACzB;AACA;AACA,SAASc,EAAazH,GAAI;AACxB,SAAOA,MAAO,MAAMA,MAAO;AAC7B;AACA,SAASuH,EAAYvH,GAAI;AACvB,SAAOA,MAAO,MAAMA,MAAO;AAC7B;AACA,SAASoH,EAAQpH,GAAI;AACnB,SAAOA,KAAM,MAAMA,KAAM;AAC3B;AACA,IAAI8H;CACH,SAASC,GAAiB;AACzBA,IAAgBA,EAAgB,WAAc,EAAE,IAAI,YACpDA,EAAgBA,EAAgB,iBAAoB,EAAE,IAAI,kBAC1DA,EAAgBA,EAAgB,QAAW,EAAE,IAAI,SACjDA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,KAAQ,EAAE,IAAI,MAC9CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,GAAG,IAAI,KAC9CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,IAAO,EAAE,IAAI,KAC7CA,EAAgBA,EAAgB,WAAc,EAAE,IAAI,YACpDA,EAAgBA,EAAgB,YAAe,EAAE,IAAI,aACrDA,EAAgBA,EAAgB,aAAgB,GAAG,IAAI,cACvDA,EAAgBA,EAAgB,eAAkB,EAAE,IAAI,gBACxDA,EAAgBA,EAAgB,QAAW,EAAE,IAAI,SACjDA,EAAgBA,EAAgB,QAAW,EAAE,IAAI,SACjDA,EAAgBA,EAAgB,MAAS,EAAE,IAAI,OAC/CA,EAAgBA,EAAgB,cAAiB,EAAE,IAAI,eACvDA,EAAgBA,EAAgB,QAAW,EAAE,IAAI,SACjDA,EAAgBA,EAAgB,YAAe,GAAG,IAAI,aACtDA,EAAgBA,EAAgB,cAAiB,EAAE,IAAI,eACvDA,EAAgBA,EAAgB,OAAU,EAAE,IAAI,QAChDA,EAAgBA,EAAgB,QAAW,EAAE,IAAI,SACjDA,EAAgBA,EAAgB,WAAc,EAAE,IAAI,YACpDA,EAAgBA,EAAgB,MAAS,CAAC,IAAI;AAChD,GAAGD,OAAmBA,KAAiB,CAAE,EAAC;AAGvB,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC9Y,GAAGgZ,MACxC,IAAI,OAAOA,CAAK,CACxB;AACD,IAAIC,IAAkB;AAGZ,IAAI,MAAMA,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MACxC;IAAO,IAAI,OAAOA,CAAK,CAC/B,GACK,IAAI,MAAMC,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MACxC,OAAO,IAAI,OAAOA,CAAK,CAC/B,GACO,IAAI,MAAMC,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MAC1C;IAAS,IAAI,OAAOA,CAAK,CACjC,GAGK,IAAI,MAAMC,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MACxC;IAAO,IAAI,OAAOA,CAAK,CAC/B,GACK,IAAI,MAAMC,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MACxC,OAAO,IAAI,OAAOA,CAAK,CAC/B,GACO,IAAI,MAAMC,CAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjZ,GAAGgZ,MAC1C;IAAS,IAAI,OAAOA,CAAK,CACjC;AAKL,IAAIE;CACH,SAASC,GAAe;AACvBA,IAAc,UAAU;IACtB,oBAAoB;EACxB;AACA,GAAGD,OAAiBA,KAAe,CAAE,EAAC;AAGtC,IAAIE,KAAiBlC;AAArB,IACImC;CACH,SAASC,GAAY;AACpBA,IAAWA,EAAW,OAAU,CAAC,IAAI,QACrCA,EAAWA,EAAW,yBAA4B,CAAC,IAAI,0BACvDA,EAAWA,EAAW,wBAA2B,CAAC,IAAI,yBACtDA,EAAWA,EAAW,wBAA2B,CAAC,IAAI,yBACtDA,EAAWA,EAAW,iBAAoB,CAAC,IAAI,kBAC/CA,EAAWA,EAAW,yBAA4B,CAAC,IAAI,0BACvDA,EAAWA,EAAW,mBAAsB,CAAC,IAAI;AACnD,GAAGD,OAAcA,KAAY,CAAE,EAAC;AAChC,IAAIE;CACH,SAASC,GAAa;AACrBA,IAAYA,EAAY,iBAAoB,CAAC,IAAI,kBACjDA,EAAYA,EAAY,kBAAqB,CAAC,IAAI,mBAClDA,EAAYA,EAAY,mBAAsB,CAAC,IAAI,oBACnDA,EAAYA,EAAY,oBAAuB,CAAC,IAAI,qBACpDA,EAAYA,EAAY,aAAgB,CAAC,IAAI,cAC7CA,EAAYA,EAAY,aAAgB,CAAC,IAAI,cAC7CA,EAAYA,EAAY,cAAiB,CAAC,IAAI,eAC9CA,EAAYA,EAAY,cAAiB,CAAC,IAAI,eAC9CA,EAAYA,EAAY,eAAkB,CAAC,IAAI,gBAC/CA,EAAYA,EAAY,gBAAmB,EAAE,IAAI,iBACjDA,EAAYA,EAAY,iBAAoB,EAAE,IAAI,kBAClDA,EAAYA,EAAY,oBAAuB,EAAE,IAAI,qBACrDA,EAAYA,EAAY,qBAAwB,EAAE,IAAI,sBACtDA,EAAYA,EAAY,kBAAqB,EAAE,IAAI,mBACnDA,EAAYA,EAAY,SAAY,EAAE,IAAI,UAC1CA,EAAYA,EAAY,UAAa,EAAE,IAAI,WAC3CA,EAAYA,EAAY,MAAS,EAAE,IAAI;AACzC,GAAGD,OAAeA,KAAa,CAAE,EAAC;AAClC,IAAIE;CACH,SAASC,GAAiB;AACzBA,IAAgBA,EAAgB,gBAAmB,CAAC,IAAI,iBACxDA,EAAgBA,EAAgB,sBAAyB,CAAC,IAAI,uBAC9DA,EAAgBA,EAAgB,uBAA0B,CAAC,IAAI,wBAC/DA,EAAgBA,EAAgB,gBAAmB,CAAC,IAAI,iBACxDA,EAAgBA,EAAgB,gBAAmB,CAAC,IAAI,iBACxDA,EAAgBA,EAAgB,gBAAmB,CAAC,IAAI,iBACxDA,EAAgBA,EAAgB,qBAAwB,CAAC,IAAI,sBAC7DA,EAAgBA,EAAgB,uBAA0B,CAAC,IAAI,wBAC/DA,EAAgBA,EAAgB,oBAAuB,CAAC,IAAI,qBAC5DA,EAAgBA,EAAgB,sBAAyB,EAAE,IAAI,uBAC/DA,EAAgBA,EAAgB,yBAA4B,EAAE,IAAI,0BAClEA,EAAgBA,EAAgB,wBAA2B,EAAE,IAAI,yBACjEA,EAAgBA,EAAgB,wBAA2B,EAAE,IAAI,yBACjEA,EAAgBA,EAAgB,iBAAoB,EAAE,IAAI,kBAC1DA,EAAgBA,EAAgB,yBAA4B,EAAE,IAAI,0BAClEA,EAAgBA,EAAgB,mBAAsB,EAAE,IAAI;AAC9D,GAAGD,OAAmBA,KAAiB,CAAE,EAAC;AAG1C,SAASE,GAA0BC,GAAiB;AAClD,SAAO;IACL,iBAAiB,MAAM,IAAIC,GAAU,MAAM,MAAM,OAAO,IAAI;IAC5D,UAAU,CAAC/Y,GAAMgZ,MAAUC,GAASH,GAAiB9Y,GAAMgZ,CAAK;EACpE;AACA;AACA,IAAIE,KAAqB;AAAzB,IACIC,KAAoB;AADxB,IAEIC,KAAoB;AAFxB,IAGIC,KAAoB;AAHxB,IAIIC,KAAsB;AAJ1B,IAKIC,KAAmB;AALvB,IAMIC,KAAqB;AANzB,IAOIC,KAAqB;AAPzB,IAQIC,KAAsB;AAR1B,IASIC,KAAsB;AAT1B,IAUIC,KAAqB;AAVzB,IAWIC,IAAe,MAAMC,GAAc;EACrC,YAAYnO,GAAQoO,GAAM;AACxB,SAAK,SAASpO,GACd,KAAK,OAAOoO;EAChB;EACE,OAAO,IAAIC,GAAS;AAClB,WAAIA,IACKA,EAAQ,SAEV;EACX;EACE,OAAO,KAAKA,GAASD,GAAM;AACzB,WAAO,IAAID,GAAcE,GAASD,CAAI;EAC1C;EACE,OAAO,OAAOhL,GAAGC,GAAG;AAClB,QAAI,CAACD,KAAK,CAACC;AACT,aAAO;AAET,QAAI,CAACD,KAAK,CAACC;AACT,aAAO;AAET,WAAOD,KAAKC,KAAG;AACb,UAAID,MAAMC;AACR,eAAO;AAET,UAAID,EAAE,SAASC,EAAE;AACf,eAAO;AAETD,UAAIA,EAAE,QACNC,IAAIA,EAAE;IACZ;AACI,WAAO;EACX;AACA;AA5CA,IA6CI+J,KAAY,MAAMkB,GAAW;EAC/B,YAAYjB,GAAOnC,GAAWqD,GAAcF,GAAS;AACnD,SAAK,SAAShB,GACd,KAAK,YAAYnC,GACjB,KAAK,eAAeqD,GACpB,KAAK,UAAUF;EACnB;EACE,QAAQ;AACN,WAAO,IAAIC,GAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO;EACtF;EACE,OAAOE,GAAO;AACZ,WAAIA,MAAU,OACL,OAEL,CAACA,KAAS,EAAEA,aAAiBF,MACxB,QAEF,KAAK,cAAcE,EAAM,aAAa,KAAK,iBAAiBA,EAAM,gBAAgBN,EAAa,OAAO,KAAK,SAASM,EAAM,OAAO;EAC5I;EACE,eAAe;AACb,WAAO,KAAK;EAChB;EACE,aAAanB,GAAO;AAClB,SAAK,SAASA;EAClB;AACA;AACA,SAASC,GAASmB,GAAUpa,GAAMgZ,GAAOqB,IAAc,GAAG;AACxD,MAAIC,IAA6B,GAC7BC,IAAe;AACnB,UAAQvB,EAAM,WAAS;IACrB,KAAK;AACHhZ,UAAO,MAAMA,GACbsa,IAA6B;AAC7B;IACF,KAAK;AACHta,UAAO,OAAOA,GACdsa,IAA6B;AAC7B;EACN;AACE,QAAME,IAAUlC,GAAetY,CAAI;AACnC,MAAIka,IAAelB,EAAM,cACrBgB,IAAUhB,EAAM;AACpB,QAAMyB,IAAM;IACV,QAAQ,CAAE;IACV,UAAUzB,EAAM,MAAK;EACzB;AACE,aAAa;AACX,QAAI7I,IAASkK,IAAcG,EAAQ,YAAW,GAC1CT,IAAO;AACX,UAAMpX,IAAO6X,EAAQ,KAAA;AACrB,QAAI7X,MAAS;AACX;AAEF,QAAIwN,MAAWkK,IAAcG,EAAQ,YAAW;AAC9C,YAAM,IAAI;QACR,qDAAqDxa,EAAK,OAAOwa,EAAQ,YAAW,GAAI,CAAC;MACjG;AAMI,YAJID,MACFpK,KAAUmK,IAEZC,IAAeD,IAA6B,GACpC3X,GAAI;MACV,KAAK;AACHqX,YAAUH,EAAa;UAAKG;UAAS;;QAAC,GACtCD,IAAOb,IACPgB,IAAe;AACf;MACF,KAAK;AACHF,YAAUH,EAAa,IAAIG,CAAO,GAClCD,IAAOb,IACPgB,IAAe;AACf;MACF,KAAK;AACHF,YAAUH,EAAa;UAAKG;UAAS;;QAAC,GACtCD,IAAOZ,IACPe,IAAe;AACf;MACF,KAAK;AACHF,YAAUH,EAAa,IAAIG,CAAO,GAClCD,IAAOZ,IACPe,IAAe;AACf;MACF,KAAK;AACHH,YAAOX,IACPc,IAAe;AACf;MACF,KAAK;AACHH,YAAOV,IACPa,IAAe;AACf;MACF,KAAK;MACL,KAAK;AACHH,YAAOT,IACPY,IAAe;AACf;MACF,KAAK;AACHH,YAAOR,IACPW,IAAe;AACf;MACF,KAAK;AAEH,cAAMQ,KADgBV,IAAUA,EAAQ,OAAO,OACb;AAClCD,YAAOG,KAAgBQ,IAAUlB,KAAqBE,IACtDQ,IAAe;AACf;MACF,KAAK;AACHH,YAAON,IACPS,IAAe;AACf;IACR;AACI,QAAIE;AACF,cAAQzX,GAAI;QACV,KAAK;AACHoX,cAAOH;AACP;QACF,KAAK;AACHG,cAAOJ;AACP;MACV;AAEIc,MAAI,WAAW,IAAI1B;MACjBC,EAAM,aAAc;MACpBwB,EAAQ,cAAe;MACvBN;MACAF;IACN,GACIS,EAAI,OAAO,KAAK;MACd,YAAYtK;MACZ,QAAQ4J;IACd,CAAK;EACL;AACE,SAAOU;AACT;AAGA,IAAIE;AACJ,SAASC,KAAY;AACnB,SAAO,IAAI,QAAQ,CAACC,GAASC,MAAW;AACtC,QAAI,CAACH;AACH,aAAOG,EAAO,sBAAsB;AAEtCD,MAAQF,CAAM;EAClB,CAAG;AACH;AACA,IAAII,KAAyB,cAAcxJ,GAAmB;EAC5D,YAAY3K,GAAYwL,GAAStT,GAAU;AACzC,UAAM8H,GAAYwL,GAAStT,EAAS,WAAW,GAC/C,KAAK,aAAa;MAChBJ,EAA2B,OAAO,mBAAmB,CAACkT,MAAU;AAC9D,aAAK,aAAaA,EAAM,GAAG;MACnC,CAAO;IACP,GACI,KAAK,aAAa;MAChBlT,EAA2B,OAAO,yBAAyB,CAACqR,MAAU;AACpE,aAAK,aAAaA,EAAM,MAAM,GAAG;MACzC,CAAO;IACP;EACA;EACE,aAAaoC,GAAU;AACrB,SAAK,QAAO,EAAG,KAAK,CAACC,MAAY;AAC/BA,QAAQ,YAAYD,EAAS,SAAU,CAAA;IAC7C,CAAK;EACL;AACA;AACA,SAAS6I,GAAUlc,GAAU;AAC3B,QAAMmc,IAAc,CAAA,GACdC,IAAY,CAAA,GACZjc,IAAS,IAAIJ,GAAcC,CAAQ;AACzCmc,IAAY,KAAKhc,CAAM,GACvB0b,IAAS,IAAIQ,MACJlc,EAAO,yBAAyB,GAAGkc,CAAI;AAEhD,WAASC,IAAoB;AAC3B,UAAM,EAAE,YAAAxU,GAAY,mBAAmByU,EAAkB,IAAKvc;AAC9Dwc,OAAWJ,CAAS,GAChBG,EAAmB,2BACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAI2O,GAA+BoF,CAAM;MACnD;IACA,GAEQU,EAAmB,gCACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAI6O,GAAoCkF,CAAM;MACxD;IACA,GAEQU,EAAmB,mBACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAI+L,GAAkBgI,GAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;MACvD;IACA,GAEQU,EAAmB,UACrBH,EAAU;MACRxc,EAA2B,UAAU,sBAAsBkI,GAAY,IAAIkN,GAAa6G,CAAM,CAAC;IACvG,GAEQU,EAAmB,mBACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAIoO,GAAsB2F,CAAM;MAC1C;IACA,GAEQU,EAAmB,UACrBH,EAAU,KAAKxc,EAA2B,UAAU,kBAAkBkI,GAAYiS,GAA0B,IAAI,CAAC,CAAC,GAEhHwC,EAAmB,UACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAI8O,GAAqBiF,CAAM;MACzC;IACA,GAEQU,EAAmB,iBACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAIkP,GAAoB6E,CAAM;MACxC;IACA,GAEQU,EAAmB,eACrBH,EAAU,KAAK,IAAIH,GAAuBnU,GAAY+T,GAAQ7b,CAAQ,CAAC,GAErEuc,EAAmB,mBACrBH,EAAU;MACRxc,EAA2B,UAAU;QACnCkI;QACA,IAAIqP,GAAsB0E,CAAM;MAC1C;IACA;EAEA;AACES,IAAAA,GACAH,EAAY,KAAKvc,EAA2B,UAAU,yBAAyBI,EAAS,YAAYyc,EAAqB,CAAC;AAC1H,MAAIC,IAAoB1c,EAAS;AACjC,SAAAA,EAAS,YAAY,CAAC2c,MAAgB;AAChCA,MAAY,sBAAsBD,MACpCA,IAAoBC,EAAY,mBAChCL,EAAAA;EAEN,CAAG,GACDH,EAAY,KAAKS,GAAaR,CAAS,CAAC,GACjCQ,GAAaT,CAAW;AACjC;AACA,SAASS,GAAaT,GAAa;AACjC,SAAO,EAAE,SAAS,MAAMK,GAAWL,CAAW,EAAC;AACjD;AACA,SAASK,GAAWL,GAAa;AAC/B,SAAOA,EAAY;AACjBA,MAAY,IAAA,EAAM,QAAA;AAEtB;AACA,IAAIM,KAAwB;EAC1B,aAAa;EACb,UAAU;IACR,aAAa;IACb,cAAc,CAAC,MAAM,IAAI;EAC1B;EACD,UAAU;IACR,CAAC,KAAK,GAAG;IACT,CAAC,KAAK,GAAG;EACV;EACD,kBAAkB;IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAG;IAC5C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAG;IAC5C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAC;EAC9C;AACA;", "names": ["__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "__reExport", "target", "mod", "second<PERSON><PERSON><PERSON>", "monaco_editor_core_exports", "monaco_editor_core_star", "STOP_WHEN_IDLE_FOR", "WorkerManager", "defaults", "resources", "_client", "client", "_", "DocumentUri", "DocumentUri2", "is", "value", "URI", "URI2", "integer", "integer2", "<PERSON><PERSON><PERSON><PERSON>", "uinteger2", "Position", "Position3", "create", "line", "character", "candidate", "Is", "Range", "Range3", "one", "two", "three", "four", "Location", "Location2", "uri", "range", "LocationLink", "LocationLink2", "targetUri", "targetRange", "targetSelectionRange", "originSelectionRange", "Color", "Color2", "red", "green", "blue", "alpha", "ColorInformation", "ColorInformation2", "color", "ColorPresentation", "ColorPresentation2", "label", "textEdit", "additionalTextEdits", "TextEdit", "FoldingRangeKind", "FoldingRangeKind2", "FoldingRange", "FoldingRange2", "startLine", "endLine", "startCharacter", "endCharacter", "kind", "collapsedText", "result", "DiagnosticRelatedInformation", "DiagnosticRelatedInformation2", "location", "message", "DiagnosticSeverity", "DiagnosticSeverity2", "DiagnosticTag", "DiagnosticTag2", "CodeDescription", "CodeDescription2", "Diagnostic", "Diagnostic2", "severity", "code", "source", "relatedInformation", "_a", "Command", "Command2", "title", "command", "args", "TextEdit2", "replace", "newText", "insert", "position", "del", "ChangeAnnotation", "ChangeAnnotation2", "needsConfirmation", "description", "ChangeAnnotationIdentifier", "ChangeAnnotationIdentifier2", "AnnotatedTextEdit", "AnnotatedTextEdit2", "annotation", "TextDocumentEdit", "TextDocumentEdit2", "textDocument", "edits", "OptionalVersionedTextDocumentIdentifier", "CreateFile", "CreateFile2", "options", "RenameFile", "RenameFile2", "old<PERSON><PERSON>", "newUri", "DeleteFile", "DeleteFile2", "WorkspaceEdit", "WorkspaceEdit2", "change", "TextDocumentIdentifier", "TextDocumentIdentifier2", "VersionedTextDocumentIdentifier", "VersionedTextDocumentIdentifier2", "version", "OptionalVersionedTextDocumentIdentifier2", "TextDocumentItem", "TextDocumentItem2", "languageId", "text", "<PERSON><PERSON><PERSON><PERSON>", "MarkupKind2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkupContent2", "CompletionItemKind", "CompletionItemKind2", "InsertTextFormat", "InsertTextFormat2", "CompletionItemTag", "CompletionItemTag2", "InsertReplaceEdit", "InsertReplaceEdit2", "InsertTextMode", "InsertTextMode2", "CompletionItemLabelDetails", "CompletionItemLabelDetails2", "CompletionItem", "CompletionItem2", "CompletionList", "CompletionList2", "items", "isIncomplete", "MarkedString", "MarkedString2", "fromPlainText", "plainText", "Hover", "Hover2", "ParameterInformation", "ParameterInformation2", "documentation", "SignatureInformation", "SignatureInformation2", "parameters", "DocumentHighlightKind", "DocumentHighlightKind2", "DocumentHighlight", "DocumentHighlight2", "SymbolKind", "SymbolKind2", "SymbolTag", "SymbolTag2", "SymbolInformation", "SymbolInformation2", "name", "containerName", "WorkspaceSymbol", "WorkspaceSymbol2", "DocumentSymbol", "DocumentSymbol2", "detail", "<PERSON><PERSON><PERSON><PERSON>", "children", "CodeActionKind", "CodeActionKind2", "CodeActionTriggerKind", "CodeActionTriggerKind2", "CodeActionContext", "CodeActionContext2", "diagnostics", "only", "trigger<PERSON>ind", "CodeAction", "CodeAction2", "kindOrCommandOrEdit", "checkKind", "CodeLens", "CodeLens2", "data", "FormattingOptions", "FormattingOptions2", "tabSize", "insertSpaces", "DocumentLink", "DocumentLink2", "SelectionRange", "SelectionRange2", "parent", "SemanticTokenTypes", "SemanticTokenTypes2", "SemanticTokenModifiers", "SemanticTokenModifiers2", "SemanticTokens", "SemanticTokens2", "InlineValueText", "InlineValueText2", "InlineValueVariableLookup", "InlineValueVariableLookup2", "variableName", "caseSensitiveLookup", "InlineValueEvaluatableExpression", "InlineValueEvaluatableExpression2", "expression", "InlineValueContext", "InlineValueContext2", "frameId", "stoppedLocation", "InlayHintKind", "InlayHintKind2", "InlayHintLabelPart", "InlayHintLabelPart2", "InlayHint", "InlayHint2", "StringValue", "StringValue2", "createSnippet", "InlineCompletionItem", "InlineCompletionItem2", "insertText", "filterText", "InlineCompletionList", "InlineCompletionList2", "InlineCompletionTriggerKind", "InlineCompletionTriggerKind2", "SelectedCompletionInfo", "SelectedCompletionInfo2", "InlineCompletionContext", "InlineCompletionContext2", "selectedCompletionInfo", "WorkspaceFolder", "WorkspaceFolder2", "TextDocument", "TextDocument2", "content", "FullTextDocument", "applyEdits", "document", "sortedEdits", "mergeSort", "a", "b", "diff", "lastModifiedOffset", "i", "e", "startOffset", "endOffset", "compare", "p", "left", "right", "leftIdx", "rightIdx", "start", "end", "event", "lineOffsets", "isLineStart", "ch", "offset", "low", "high", "mid", "lineOffset", "nextLineOffset", "Is2", "toString", "defined", "undefined2", "boolean", "string", "number", "numberRange", "min", "max", "func", "objectLiteral", "typedArray", "check", "DiagnosticsAdapter", "_languageId", "_worker", "configChangeEvent", "onModelAdd", "model", "modeId", "handle", "onModelRemoved", "uriStr", "listener", "d", "resource", "worker2", "markers", "toDiagnostics", "err", "toSeverity", "lsSeverity", "diag", "CompletionAdapter", "_triggerCharacters", "context", "token", "fromPosition", "info", "wordInfo", "wordRange", "entry", "item", "to<PERSON>ommand", "toCompletionItemKind", "isInsertReplaceEdit", "to<PERSON><PERSON><PERSON>", "toTextEdit", "fromRange", "edit", "mItemKind", "c", "HoverAdapter", "toMarkedStringArray", "isMarkupContent", "thing", "toMarkdownString", "contents", "DocumentHighlightAdapter", "entries", "toDocumentHighlightKind", "DefinitionAdapter", "definition", "toLocation", "ReferenceAdapter", "RenameAdapter", "newName", "toWorkspaceEdit", "resourceEdits", "_uri", "DocumentSymbolAdapter", "isDocumentSymbol", "toDocumentSymbol", "toSymbolKind", "symbol", "m<PERSON>ind", "DocumentLinkAdapter", "DocumentFormattingEditProvider", "fromFormattingOptions", "DocumentRangeFormattingEditProvider", "DocumentColorAdapter", "infos", "presentations", "presentation", "FoldingRangeAdapter", "ranges", "toFoldingRangeKind", "SelectionRangeAdapter", "positions", "<PERSON><PERSON><PERSON><PERSON>", "createScanner", "ignoreTrivia", "len", "pos", "tokenOffset", "lineNumber", "lineStartOffset", "tokenLineStartOffset", "prevTokenLineStartOffset", "scanError", "scanHexDigits", "count", "exact", "digits", "value2", "setPosition", "newPosition", "scanNumber", "isDigit", "scanString", "ch3", "isLineBreak", "scanNext", "isWhiteSpace", "safe<PERSON>ength", "commentClosed", "isUnknownContentCharacter", "scanNextNonTrivia", "CharacterCodes", "CharacterCodes2", "index", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParseOptions", "ParseOptions2", "createScanner2", "ScanError", "ScanError2", "SyntaxKind", "SyntaxKind2", "ParseErrorCode", "ParseErrorCode2", "createTokenizationSupport", "supportComments", "JSONState", "state", "tokenize", "TOKEN_DELIM_OBJECT", "TOKEN_DELIM_ARRAY", "TOKEN_DELIM_COLON", "TOKEN_DELIM_COMMA", "TOKEN_VALUE_BOOLEAN", "TOKEN_VALUE_NULL", "TOKEN_VALUE_STRING", "TOKEN_VALUE_NUMBER", "TOKEN_PROPERTY_NAME", "TOKEN_COMMENT_BLOCK", "TOKEN_COMMENT_LINE", "ParentsStack", "_ParentsStack", "type", "parents", "_JSONState", "lastWasColon", "other", "comments", "offsetDelta", "numberOfInsertedCharacters", "adjustOffset", "scanner", "ret", "inArray", "worker", "getWorker", "resolve", "reject", "JSONDiagnosticsAdapter", "setupMode", "disposables", "providers", "uris", "registerProviders", "modeConfiguration2", "disposeAll", "richEditConfiguration", "modeConfiguration", "newDefaults", "asDisposable"]}