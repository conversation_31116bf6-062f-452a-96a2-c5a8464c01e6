from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix

table_prefix = table_prefix + "cs_manage_"

class DailyMetricGroup(CoreModel):
    """每日客服指标按组别统计表"""
    
    date = models.DateField(verbose_name="统计日期", help_text="统计的日期", db_index=True)
    group_id = models.BigIntegerField(default=0, verbose_name="组别ID", help_text="客服组ID，0表示全部")
    group_name = models.CharField(max_length=128, blank=True, verbose_name="组别名称", help_text="客服组名称")
    
    # 基础会话指标
    total_sessions = models.IntegerField(default=0, verbose_name="总会话量", help_text="当日总会话数")
    ai_sessions = models.IntegerField(default=0, verbose_name="AI会话量", help_text="AI客服处理的会话数")
    manual_sessions = models.IntegerField(default=0, verbose_name="人工会话量", help_text="人工客服处理的会话数")
    ai_transfer = models.IntegerField(default=0, verbose_name="AI转人工量", help_text="从AI转到人工的会话数")
    
    # 工单相关
    worksheet_count = models.IntegerField(default=0, verbose_name="工单量", help_text="当日工单数量")

    # 情绪分析已移除，改为实时计算
    
    # 服务质量指标
    online_ratio = models.FloatField(default=0, verbose_name="在线接入率", help_text="在线接入率")
    manual_satisfaction = models.FloatField(default=0, verbose_name="人工满意度", help_text="人工客服满意度")
    fcr_ratio = models.FloatField(default=0, verbose_name="一次性解决率", help_text="First Call Resolution率")
    avg_first_resp = models.IntegerField(default=0, verbose_name="平均首响时间", help_text="平均首次响应时间（秒）")
    resp_30_ratio = models.FloatField(default=0, verbose_name="30秒应答率", help_text="30秒内应答的比例")
    
    # 计算字段
    ai_transfer_rate = models.FloatField(default=0, verbose_name="AI转人工率", help_text="AI转人工的比例")
    manual_handle_ai_ratio = models.FloatField(default=0, verbose_name="人工处理AI占比", help_text="人工处理AI转接的占比")

    # 新增headline数据字段
    eva_count = models.IntegerField(default=0, verbose_name="参评数", help_text="评价总数")
    eva_ratio = models.FloatField(default=0, verbose_name="参评率", help_text="参评率百分比")
    invite_count = models.IntegerField(default=0, verbose_name="邀评数", help_text="总邀评数")

    # 七鱼原始数据存储
    qiyu_raw_data = models.JSONField(
        null=True, blank=True,
        verbose_name="七鱼原始数据",
        help_text="完整的七鱼API响应数据，用于扩展性和数据完整性"
    )
    data_version = models.CharField(
        max_length=20, default="v1.0",
        verbose_name="数据版本",
        help_text="数据结构版本，用于兼容性管理"
    )
    
    class Meta:
        db_table = table_prefix + "daily_metric_group"
        verbose_name = "每日客服指标统计"
        verbose_name_plural = verbose_name
        unique_together = ("date", "group_id")
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['group_id']),
            models.Index(fields=['date', 'group_id']),
        ]
        
    def __str__(self):
        group_display = self.group_name if self.group_name else f"组别{self.group_id}"
        if self.group_id == 0:
            group_display = "全部"
        return f"{self.date} - {group_display}"
    
    @property
    def ai_sessions_ratio(self):
        """AI会话占比"""
        if self.total_sessions == 0:
            return 0
        return round(self.ai_sessions / self.total_sessions * 100, 2)
    
    @property
    def manual_sessions_ratio(self):
        """人工会话占比"""
        if self.total_sessions == 0:
            return 0
        return round(self.manual_sessions / self.total_sessions * 100, 2)
    
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        # AI转人工率
        if self.ai_sessions > 0:
            self.ai_transfer_rate = round(self.ai_transfer / self.ai_sessions * 100, 2)
        else:
            self.ai_transfer_rate = 0
            
        # 人工处理AI占比
        if self.manual_sessions > 0:
            self.manual_handle_ai_ratio = round(self.ai_transfer / self.manual_sessions * 100, 2)
        else:
            self.manual_handle_ai_ratio = 0
