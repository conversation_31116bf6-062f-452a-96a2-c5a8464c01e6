"""
CS_MANAGE 模块的 Celery 任务

所有任务函数以 task__ 开头，以便前端自动发现
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from application.celery import app
from apps.cs_manage.services.data_processor import DataProcessor

logger = logging.getLogger(__name__)


# 修改：改为 *args, **kwargs 形式接收参数
@app.task(bind=True, max_retries=3)
def task__cs_manage_daily_data_collection(self, *args, **kwargs):
    """
    每日客服数据收集任务
    
    Args:
        target_date: 目标日期，格式 YYYY-MM-DD，默认为昨天
    """
    try:
        # 解析目标日期（通过 kwargs 取值）
        target_date: Optional[str] = kwargs.get('target_date')  # 示例：{"target_date": "2023-09-01"}

        # 解析目标日期，统一将时间截断到当日 00:00:00
        if target_date:
            target_datetime = datetime.strptime(target_date, '%Y-%m-%d').replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        else:
            # 默认取昨日，并固定到昨日 00:00:00
            target_datetime = (datetime.now() - timedelta(days=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        
        logger.info(f"[CS_MANAGE] 开始执行 {target_datetime.date()} 的数据收集任务")
        
        # 执行数据处理
        processor = DataProcessor()
        success = processor.process_daily_data(target_datetime)
        
        if success:
            logger.info(f"[CS_MANAGE] {target_datetime.date()} 数据收集任务执行成功")
            return {
                'status': 'success',
                'date': target_datetime.date().isoformat(),
                'message': '数据收集完成'
            }
        else:
            logger.error(f"[CS_MANAGE] {target_datetime.date()} 数据收集任务执行失败")
            raise Exception("数据收集失败")
            
    except Exception as e:
        logger.error(f"[CS_MANAGE] 数据收集任务执行异常: {str(e)}")
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"[CS_MANAGE] 任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60 * (self.request.retries + 1))
        else:
            return {
                'status': 'failed',
                'date': target_date or 'yesterday',
                'error': str(e)
            }


# 修改：改为 *args, **kwargs 形式接收参数
@app.task(bind=True, max_retries=2)
def task__cs_manage_data_backfill(self, *args, **kwargs):
    """
    客服数据回填任务
    
    Args:
        start_date: 开始日期，格式 YYYY-MM-DD
        end_date: 结束日期，格式 YYYY-MM-DD
    """
    try:
        # 解析日期（通过 kwargs 取值）
        start_date: Optional[str] = kwargs.get('start_date')
        end_date: Optional[str] = kwargs.get('end_date')

        if not start_date or not end_date:
            raise ValueError("start_date 和 end_date 参数为必填")

        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
        
        logger.info(f"[CS_MANAGE] 开始执行数据回填任务: {start_date} 到 {end_date}")
        
        processor = DataProcessor()
        current_date = start_datetime
        success_count = 0
        total_days = (end_datetime - start_datetime).days + 1
        
        while current_date <= end_datetime:
            try:
                success = processor.process_daily_data(current_date)
                if success:
                    success_count += 1
                    logger.info(f"[CS_MANAGE] {current_date.date()} 数据回填成功")
                else:
                    logger.warning(f"[CS_MANAGE] {current_date.date()} 数据回填失败")
                    
            except Exception as e:
                logger.error(f"[CS_MANAGE] {current_date.date()} 数据回填异常: {str(e)}")
            
            current_date += timedelta(days=1)
        
        logger.info(f"[CS_MANAGE] 数据回填任务完成: 成功 {success_count}/{total_days} 天")
        
        return {
            'status': 'completed',
            'start_date': start_date,
            'end_date': end_date,
            'success_count': success_count,
            'total_days': total_days
        }
        
    except Exception as e:
        logger.error(f"[CS_MANAGE] 数据回填任务异常: {str(e)}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=300)
        else:
            return {
                'status': 'failed',
                'start_date': start_date,
                'end_date': end_date,
                'error': str(e)
            }


@app.task
def task__cs_manage_data_cleanup(days_to_keep: int = 90):
    """
    清理过期的客服数据
    
    Args:
        days_to_keep: 保留天数，默认90天
    """
    try:
        from apps.cs_manage.models import DailyMetricGroup
        from django.utils import timezone
        
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)
        
        logger.info(f"[CS_MANAGE] 开始清理 {cutoff_date} 之前的数据")
        
        # 删除过期数据
        deleted_count, _ = DailyMetricGroup.objects.filter(
            date__lt=cutoff_date
        ).delete()
        
        logger.info(f"[CS_MANAGE] 数据清理完成，删除了 {deleted_count} 条记录")
        
        return {
            'status': 'success',
            'cutoff_date': cutoff_date.isoformat(),
            'deleted_count': deleted_count
        }
        
    except Exception as e:
        logger.error(f"[CS_MANAGE] 数据清理任务异常: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


@app.task
def task__cs_manage_health_check():
    """
    CS_MANAGE 模块健康检查任务
    """
    try:
        from apps.cs_manage.models import DailyMetricGroup
        from apps.cs_manage.services.qiyu_client import QiyuDataClient
        
        logger.info("[CS_MANAGE] 开始健康检查")
        
        # 检查数据库连接
        recent_data_count = DailyMetricGroup.objects.filter(
            date__gte=datetime.now().date() - timedelta(days=7)
        ).count()
        
        # 检查七鱼API连接
        client = QiyuDataClient()
        groups = client.get_staff_groups()
        
        health_status = {
            'status': 'healthy',
            'database_connection': True,
            'recent_data_count': recent_data_count,
            'qiyu_api_connection': len(groups) > 0,
            'staff_groups_count': len(groups),
            'check_time': datetime.now().isoformat()
        }
        
        logger.info(f"[CS_MANAGE] 健康检查完成: {health_status}")
        return health_status
        
    except Exception as e:
        logger.error(f"[CS_MANAGE] 健康检查失败: {str(e)}")
        return {
            'status': 'unhealthy',
            'error': str(e),
            'check_time': datetime.now().isoformat()
        }


@app.task
def LLM_auto_qc(*args, **kwargs):   # 客服智能质检异步任务
    """
    客服智能质检异步任务

    Args:
        session_id: 会话ID
    """
    import logging
    from apps.cs_manage.utils.LLM_auto_qc import process_session_for_qc

    logger = logging.getLogger(__name__)

    try:
        logger.info(f"[Task] 开始处理会话的质检任务")
        session_id = kwargs.get('session_id')

        if not session_id:
            logger.info(f"[Task] 会话ID为空，跳过质检任务")
            return
        
        # 调用质检处理函数
        success = process_session_for_qc(session_id)

        if success:
            logger.info(f"[Task] 会话质检任务完成")
        else:
            logger.info(f"[Task] 会话质检任务未执行（可能已处理或数据不足）")

    except Exception as e:
        logger.error(f"[Task] 会话质检任务失败: {str(e)}", exc_info=True)

@app.task
def LLM_cs_emotion_analysis(*args, **kwargs):
    """
    客服会话情绪分析异步任务

    Args:
        session_id: 会话ID
        force_reprocess: 是否强制重新处理
    """
    import logging
    from apps.cs_manage.utils.LLM_cs_emotion_analysis import process_session_for_emotion_analysis

    logger = logging.getLogger(__name__)

    try:
        logger.info(f"[Task] 开始处理会话的情绪分析任务")
        session_id = kwargs.get('session_id')
        force_reprocess = kwargs.get('force_reprocess', False)

        if not session_id:
            logger.info(f"[Task] 会话ID为空，跳过情绪分析任务")
            return
        
        # 调用情绪分析处理函数
        success = process_session_for_emotion_analysis(session_id, force_reprocess)

        if success:
            logger.info(f"[Task] 会话情绪分析任务完成: {session_id}")
        else:
            logger.info(f"[Task] 会话情绪分析任务未执行（可能已处理或数据不足）: {session_id}")

    except Exception as e:
        logger.error(f"[Task] 会话情绪分析任务失败: {str(e)}", exc_info=True)