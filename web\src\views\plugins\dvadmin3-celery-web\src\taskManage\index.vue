<template>
    <el-main class="bg-gray-100">
        <el-form class="search-header mb-4" :model="searchForm" inline>
            <el-form-item label="任务名称">
                <el-input v-model="searchForm.name" placeholder="请输入任务名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="任务函数">
                <el-input v-model="searchForm.task" placeholder="请输入任务函数" clearable></el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="searchForm.enabled" placeholder="请选择状态" clearable>
                    <el-option :value="true" label="已启用"></el-option>
                    <el-option :value="false" label="已停用"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchTasks">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
                <el-button type="success" @click="showAllTaskLogs">
                    <el-icon>
                        <List />
                    </el-icon>
                    查看全部任务日志
                </el-button>
            </el-form-item>
        </el-form>

        <!-- 添加正在执行任务列表 -->
        <active-task-list class="mb-4" @view-task-logs="viewTaskLogs"></active-task-list>

        <el-row :gutter="15">
            <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24" v-for="item in taskList" :key="item.id">
                <el-card class="task task-item" shadow="hover">
                    <h2>{{ item.name }}</h2>
                    <ul>
                        <li>
                            <h4>执行任务</h4>
                            <p>{{ item.task }}</p>
                        </li>
                        <li>
                            <h4>定时规则</h4>
                            <p v-if="item.interval">
                                <span v-if="item.interval.every && item.interval.period">
                                    {{ getIntervalDescription(item.interval) }}
                                </span>
                                <span v-else>{{ item.interval }}</span>
                            </p>
                            <p v-else-if="item.crontab">
                                <span v-if="item.crontab.minute">
                                    {{ getCrontabDescription(item.crontab) }}
                                </span>
                                <span v-else>{{ item.crontab }}</span>
                            </p>
                            <p v-else>--</p>
                        </li>
                        <li>
                            <h4>最后运行时间</h4>
                            <p>{{ item.last_run_at || '--' }}</p>
                        </li>
                    </ul>
                    <div class="bottom w-full">
                        <div class="state flex flex-wrap items-center">
                            <div>
                                <el-popconfirm width="180" confirm-button-text="确定" @confirm="setTaskStatus(item)"
                                    cancel-button-text="取消" :title="item.enabled ? '确认停用该任务？' : '确认启用该任务？'">
                                    <template #reference>
                                        <el-tag v-if="item.enabled == true" type="success" effect="dark">已启用</el-tag>
                                        <el-tag v-else type="danger" effect="dark">已停用</el-tag>
                                    </template>
                                </el-popconfirm>
                            </div>
                            <div class="ml-2">
                                <el-popconfirm width="180" confirm-button-text="确定" @confirm="runTask(item)"
                                    cancel-button-text="取消" title="立即运行该任务？">
                                    <template #reference>
                                        <el-button size="small" circle>
                                            <el-icon>
                                                <CaretRight />
                                            </el-icon>
                                        </el-button>
                                    </template>
                                </el-popconfirm>
                            </div>
                        </div>
                        <div class="taskName">
                            <el-dropdown trigger="hover" class="ml-2">
                                <el-button type="primary" size="small" circle effect>
                                    <el-icon>
                                        <Edit />
                                    </el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item :icon="Edit" @click="editTask(item)">编辑</el-dropdown-item>
                                        <el-dropdown-item :icon="Delete" @click="del(item)" divided>删除
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>

                            <el-button type="primary" size="small" circle plain @click="taskLogs(item)" class="ml-2">
                                <el-icon>
                                    <Monitor />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-card class="task task-add" shadow="never" @click="addTaskShow">
                    <el-icon>
                        <Plus />
                    </el-icon>
                    <p>添加计划任务</p>
                </el-card>
                <el-dialog v-model="addTaskDialogVisible" title="添加任务" width="50%" class="el-dialog">
                    <el-form ref="addTaskFormRef" :rules="rules" :model="addTaskForm" label-width="120px"
                        label-position="left">
                        <el-row>
                            <el-col>
                                <el-form-item label="任务名称" required prop="name">
                                    <el-input v-model="addTaskForm.name" />
                                </el-form-item>
                            </el-col>
                            <el-col>
                                <el-form-item label="任务" required prop="task">
                                    <el-select v-model="addTaskForm.task" @focus="loadTaskList" class="w-full"
                                        filterable>
                                        <el-option v-for="item in selectedTaskList" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col>
                                <el-form-item label="使用调度模型" required prop="schedule">
                                    <el-switch active-text="CrontabSchedule" inactive-text="IntervalSchedule"
                                        :active-value="1" :inactive-value="0" v-model="addTaskForm.scheduleType" />
                                </el-form-item>
                                <div v-show="addTaskForm.scheduleType == 0">
                                    <el-form-item label="调度模型" required>
                                        <el-select v-model="addTaskForm.schedule" @focus="loadIntervalScheduleList"
                                            class="w-full" clearable>
                                            <el-option v-for="item in intervalScheduleList" :key="item.value"
                                                :value="item.id" :label="getIntervalDescription(item)">
                                                <template #default>
                                                    <div class="flex justify-between">
                                                        <span>{{ getIntervalDescription(item) }}</span>
                                                    </div>
                                                </template>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <div v-show="addTaskForm.scheduleType == 1">
                                    <el-form-item label="调度模型" required>
                                        <el-select v-model="addTaskForm.schedule" @focus="loadCrontabScheduleList"
                                            class="w-full" clearable>
                                            <el-option v-for="item in crontabScheduleList" :key="item.value"
                                                :value="item.id" :label="getCrontabDescription(item)">
                                                <template #default>
                                                    <div class="flex justify-between">
                                                        <span>{{ getCrontabDescription(item) }}</span>
                                                    </div>
                                                </template>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                            </el-col>
                            <el-col>
                                <el-form-item label="是否启用" required prop="enabled">
                                    <el-switch v-model="addTaskForm.enabled" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="addTaskDialogVisible = false">取消</el-button>
                            <el-button type="primary" @click="addTask(addTaskFormRef)">
                                确定
                            </el-button>
                        </span>
                    </template>

                </el-dialog>
                <el-dialog v-model="editTaskDialogVisible" title="编辑任务" width="80%" class="el-dialog">
                    <el-form ref="editTaskFormRef" :rules="rules" :model="editTaskForm" label-width="120px"
                        label-position="left">
                        <el-row>
                            <el-col>
                                <el-form-item label="任务名称" required prop="name">
                                    <el-input v-model="editTaskForm.name" />
                                </el-form-item>
                            </el-col>
                            <el-col>
                                <el-form-item label="任务" required prop="task">
                                    <el-select v-model="editTaskForm.task" @focus="loadTaskList" class="w-full">
                                        <el-option v-for="item in selectedTaskList" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col>
                                <el-form-item label="参数" required prop="kwargs">
                                    <div class="w-full"
                                        style="border: 1px solid #DCDFE6; border-radius: 4px; padding: 5px;">
                                        <el-tabs>
                                            <el-tab-pane label="JSON编辑器">
                                                <monaco-editor v-model="editTaskForm.kwargs" language="json"
                                                    :options="editorOptions"></monaco-editor>
                                            </el-tab-pane>
                                            <el-tab-pane label="文本编辑">
                                                <el-input v-model="editTaskForm.kwargs" class="w-full" type="textarea"
                                                    :rows="6" placeholder="请输入JSON格式参数"></el-input>
                                            </el-tab-pane>
                                        </el-tabs>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col>
                                <el-form-item label="使用调度模型" required prop="schedule">
                                    <el-switch active-text="CrontabSchedule" inactive-text="IntervalSchedule"
                                        :active-value="1" :inactive-value="0" v-model="editTaskForm.scheduleType" />
                                </el-form-item>
                                <div v-show="editTaskForm.scheduleType == 0">
                                    <el-form-item label="调度模型" required>
                                        <el-select v-model="editTaskForm.schedule" @focus="loadIntervalScheduleList"
                                            class="w-full" clearable>
                                            <el-option v-for="item in intervalScheduleList" :key="item.value"
                                                :value="item.id" :label="getIntervalDescription(item)">
                                                <template #default>
                                                    <div class="flex justify-between">
                                                        <span>{{ getIntervalDescription(item) }}</span>
                                                    </div>
                                                </template>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <div v-show="editTaskForm.scheduleType == 1">
                                    <el-form-item label="调度模型" required>
                                        <el-select v-model="editTaskForm.schedule" @focus="loadCrontabScheduleList"
                                            class="w-full" clearable>
                                            <el-option v-for="item in crontabScheduleList" :key="item.value"
                                                :value="item.id" :label="getCrontabDescription(item)">
                                                <template #default>
                                                    <div class="flex justify-between">
                                                        <span>{{ getCrontabDescription(item) }}</span>
                                                    </div>
                                                </template>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                            </el-col>
                            <el-col>
                                <el-form-item label="是否启用" required prop="enabled">
                                    <el-switch v-model="editTaskForm.enabled" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="editTaskDialogVisible = false">取消</el-button>
                            <el-button type="primary" @click="submitEditTask(editTaskFormRef)">
                                确定
                            </el-button>
                        </span>
                    </template>

                </el-dialog>
                <el-dialog v-model="showTaskDialogVisible" title="任务运行日志" width="90%" class="rounded-lg"
                    destroy-on-close>
                    <div style="height: 800px;  position: relative">
                        <taskLog :taskItem="selectedTask"></taskLog>
                    </div>
                </el-dialog>
                <el-dialog v-model="allTaskLogsVisible" title="全部任务运行日志" width="95%" class="rounded-lg"
                    destroy-on-close>
                    <div style="height: 800px; position: relative">
                        <allTaskLogs></allTaskLogs>
                    </div>
                </el-dialog>
                <el-dialog v-model="taskLogViewerVisible" title="任务详细日志" width="80%" class="rounded-lg"
                    destroy-on-close>
                    <div style="height: 600px;">
                        <task-log-viewer :task-id="selectedTaskLog.task_id"
                            :task-name="selectedTaskLog.task_name"></task-log-viewer>
                    </div>
                </el-dialog>
            </el-col>
        </el-row>
    </el-main>
</template>

<script setup lang="ts" name="taskManage">
import { Edit, Delete, ArrowDown, Plus, CaretRight, Monitor, List } from '@element-plus/icons-vue';
import * as api from './api'
import { ref, onMounted, reactive, defineAsyncComponent, toRaw, watch, computed } from 'vue';
import { errorMessage, successMessage } from '/@/utils/message';
import { FormInstance, FormRules } from "element-plus";
import MonacoEditor from '/@/components/monaco-editor/index.vue';

const taskLog = defineAsyncComponent(() => import('./component/taskLog/index.vue'));
const allTaskLogs = defineAsyncComponent(() => import('./component/allTaskLogs/index.vue'));
const ActiveTaskList = defineAsyncComponent(() => import('./component/activeTaskList/index.vue'));
const TaskLogViewer = defineAsyncComponent(() => import('./component/taskLogViewer/index.vue'));

// 编辑器配置
const editorOptions = {
    theme: 'vs-dark',
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    minimap: { enabled: false },
    lineHeight: 20,
    fontSize: 14,
    tabSize: 2,
    scrollBeyondLastLine: false,
    height: 200
};

// 添加搜索表单
const searchForm = reactive({
    name: '',
    task: '',
    enabled: '',
});

const addTaskDialogVisible = ref(false);

const editTaskDialogVisible = ref(false);

const showTaskDialogVisible = ref(false);

const showCron = ref(false);

const addTaskForm = reactive({
    name: '',
    task: '',
    scheduleType: 0, //0 IntervalSchedule 1 CrontabSchedule
    schedule: '',
    enabled: true,
})

let editTaskForm = reactive({
    id: '',
    name: '',
    task: '',
    scheduleType: 0, //0 IntervalSchedule 1 CrontabSchedule
    schedule: '',
    enabled: true,
    kwargs: '{}'
})

const addTaskFormRef = ref<FormInstance>()

const editTaskFormRef = ref<FormInstance>()

interface TaskForm {
    name: string,
    task: string,
    schedule: string,
    enabled: string,
}

const rules = reactive<FormRules<TaskForm>>({
    name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
    ], task: [
        { required: true, message: '请选择一个任务', trigger: 'blur' },
    ], schedule: [
        { required: true, message: '请选择schedule', trigger: 'blur' },
    ], enabled: [
        { required: true, message: '请选择状态', trigger: 'blur' },
    ],
})

const taskList = ref([]) as any;

const selectedTaskList = ref({}) as any;

const intervalScheduleList = ref({}) as any;

const crontabScheduleList = ref({}) as any;

let selectedTask = ref({}) as any;

const allTaskLogsVisible = ref(false);
const taskLogViewerVisible = ref(false);
const selectedTaskLog = ref({
    task_id: '',
    task_name: ''
});

// 添加搜索方法
const searchTasks = () => {
    // 使用筛选参数调用任务列表API
    api.getTaskList(searchForm).then((res: APIResponseData) => {
        if (res.code === 2000) {
            taskList.value = res.data;
        }
    });
}

// 重置搜索
const resetSearch = () => {
    searchForm.name = '';
    searchForm.task = '';
    searchForm.enabled = '';
    getOrUpdateTaskList();
}

/*
* 加载任务列表
* */
const loadTaskList = () => {
    api.getBackendTaskList({}).then((res: APIResponseData) => {
        if (res.code === 2000) {
            selectedTaskList.value = res.data
        }
    })
}

const loadIntervalScheduleList = () => {
    api.getIntervalScheduleList({}).then((res: APIResponseData) => {
        if (res.code === 2000) {
            intervalScheduleList.value = res.data
        }
    })
}

const loadCrontabScheduleList = () => {
    api.getCrontabScheduleList({}).then((res: APIResponseData) => {
        if (res.code === 2000) {
            crontabScheduleList.value = res.data
        }
    })
}

/**
 * 运行任务
 * @param item 任务
 */
const runTask = (item: any) => {
    api.RunTask(item).then((res: APIResponseData) => {
        if (res.code === 2000) {
            return successMessage(res.msg as string)
        }
    })
};

/**
 * 编辑任务 - 修复初始化问题
 * @param item 任务
 */
const editTask = (item: any) => {
    // 先清空表单，避免冲突
    for (const key in editTaskForm) {
        if (key !== 'scheduleType') {
            editTaskForm[key] = '';
        } else {
            editTaskForm[key] = 0;
        }
    }

    // 加载调度列表
    loadIntervalScheduleList();
    loadCrontabScheduleList();

    // 延迟执行，确保先重置再赋值
    setTimeout(() => {
        // 根据任务数据确定调度类型
        editTaskForm.scheduleType = item.crontab ? 1 : 0;

        // 初始化kwargs，确保是有效的JSON字符串
        try {
            if (item.kwargs) {
                // 如果已经是对象，转为字符串
                if (typeof item.kwargs === 'object') {
                    editTaskForm.kwargs = JSON.stringify(item.kwargs, null, 2);
                } else {
                    // 尝试解析，格式化并重新序列化为美观的JSON
                    const parsed = JSON.parse(item.kwargs);
                    editTaskForm.kwargs = JSON.stringify(parsed, null, 2);
                }
            } else {
                editTaskForm.kwargs = '{}';
            }
        } catch (e) {
            // 如果解析失败，使用原始值
            editTaskForm.kwargs = item.kwargs || '{}';
        }

        // 设置其他字段值
        editTaskForm.id = item.id;
        editTaskForm.name = item.name;
        editTaskForm.task = item.task;
        editTaskForm.enabled = item.enabled;

        // 根据调度类型设置schedule
        if (editTaskForm.scheduleType === 1) {
            editTaskForm.schedule = item.crontab;
        } else {
            editTaskForm.schedule = item.interval;
        }

        // 打开对话框
        editTaskDialogVisible.value = true;
    }, 100);
};

watch(() => editTaskDialogVisible.value, (val) => {
    if (!val) {
        showCron.value = false
        editTaskFormRef.value?.resetFields()
    }
})

const submitEditTask = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            // 尝试格式化JSON
            try {
                const parsed = JSON.parse(editTaskForm.kwargs);
                editTaskForm.kwargs = JSON.stringify(parsed);
            } catch (e) {
                errorMessage('参数格式不正确，请检查JSON格式');
                return;
            }

            // 构造符合后端预期的 payload
            const payload: any = {
                id: editTaskForm.id,
                name: editTaskForm.name,
                task: editTaskForm.task,
                enabled: editTaskForm.enabled,
                kwargs: editTaskForm.kwargs, // 确保 kwargs 仍然被传递
            };
            if (editTaskForm.scheduleType === 0) {
                payload.interval = editTaskForm.schedule;
                payload.crontab = null;
            } else {
                payload.interval = null;
                payload.crontab = editTaskForm.schedule;
            }

            api.EditTask(payload).then((res: APIResponseData) => {
                if (res.code === 2000) {
                    editTaskDialogVisible.value = false;
                    formEl.resetFields()
                    getOrUpdateTaskList()
                    return successMessage(res.msg as string)
                }
            })
        } else {
            errorMessage('请检查表单')
        }
    })
}

/**
 * 任务日志
 * @param item 任务
 */
const taskLogs = (item: any) => {
    selectedTask.value = toRaw(item)
    showTaskDialogVisible.value = true
};

/**
 * 删除任务
 * @param item 任务
 */
const del = (item: any) => {
    api.DelTask(item).then((res: APIResponseData) => {
        if (res.code === 2000) {
            getOrUpdateTaskList()
            return successMessage(res.msg as string)
        }
    })
};

/**
 * 添加任务弹窗
 */
const addTaskShow = () => {
    addTaskDialogVisible.value = true;
    selectedTask.value = {}
};

const addTask = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            api.AddTask(addTaskForm).then((res: addTaskForm) => {
                if (res.code === 2000) {
                    addTaskDialogVisible.value = false;
                    formEl.resetFields()
                    getOrUpdateTaskList()
                    return successMessage(res.msg as string)
                }
            })
        } else {
            errorMessage('请检查表单')
        }
    })
}

/**
 * 设置任务状态
 * @param item 任务
 */
const setTaskStatus = (item: any) => {
    item.enabled = !item.enabled;
    api.UpdateTask({ enabled: item.enabled, id: item.id }).then((res: APIResponseData) => {
        if (res.code === 2000) {
            return successMessage(res.msg as string)
        }
    });
}

/**
 *  加载或更新任务列表
 */
const getOrUpdateTaskList = () => {
    api.getTaskList({}).then((res: APIResponseData) => {
        taskList.value = res.data;
    });
}

const getIntervalDescription = (item) => {
    if (!item || !item.every || !item.period) return '未设置';

    const periodMap = {
        'days': '天',
        'hours': '小时',
        'minutes': '分钟',
        'seconds': '秒',
        'microseconds': '微秒',
    };

    const periodLabel = periodMap[item.period] || item.period;
    return `每 ${item.every} ${periodLabel}`;
}

const getCrontabDescription = (item) => {
    if (!item || !item.minute) return '未设置';

    // 星期描述
    const weekMap = {
        '*': '每天',
        '0': '周日',
        '1': '周一',
        '2': '周二',
        '3': '周三',
        '4': '周四',
        '5': '周五',
        '6': '周六',
    };

    let description = '';

    // 判断是否每天执行
    if (item.day_of_week === '*' && item.day_of_month === '*' &&
        item.month_of_year === '*') {
        description = '每天';
    }
    // 判断是否按星期执行
    else if (item.day_of_week !== '*' && item.day_of_month === '*') {
        const days = item.day_of_week.split(',');
        if (days.length === 1) {
            description = weekMap[days[0]] || `星期${days[0]}`;
        } else {
            description = days.map(d => weekMap[d] || `星期${d}`).join('、');
        }
    }
    // 判断是否按日期执行
    else if (item.day_of_month !== '*') {
        if (item.month_of_year === '*') {
            description = `每月${item.day_of_month}日`;
        } else {
            description = `${item.month_of_year}月${item.day_of_month}日`;
        }
    }

    // 添加时间
    if (item.hour !== '*' && item.minute !== '*') {
        description += ` ${item.hour}:${item.minute}`;
    } else if (item.hour !== '*') {
        description += ` ${item.hour}点每分钟`;
    } else if (item.minute !== '*') {
        description += ` 每小时${item.minute}分`;
    } else {
        description += ' 每分钟';
    }

    return description;
}

const showAllTaskLogs = () => {
    allTaskLogsVisible.value = true;
};

// 查看任务日志
const viewTaskLogs = (task) => {
    selectedTaskLog.value = {
        task_id: task.task_id || task.id,
        task_name: task.task_name || task.name
    };
    taskLogViewerVisible.value = true;
};

onMounted(() => {
    getOrUpdateTaskList()
});
</script>

<style lang="scss" scoped>
.search-header {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.task {
    height: 260px;
}

.task-item h2 {
    font-size: 15px;
    color: #3c4a54;
    padding-bottom: 10px;
}

.task-item li {
    list-style-type: none;
    margin-bottom: 10px;
}

.task-item li h4 {
    font-size: 12px;
    font-weight: normal;
    color: #999;
}

.task-item li p {
    margin-top: 5px;
}

.task-item .bottom {
    border-top: 1px solid #ebeef5;
    text-align: right;
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    color: #999;
}

.task-add:hover {
    color: #409eff;
}

.task-add i {
    font-size: 30px;
}

.task-add p {
    font-size: 12px;
    margin-top: 20px;
}

.dark .task-item .bottom {
    border-color: var(--el-border-color-light);
}

.el-card {
    border-radius: 3%;
    overflow: hidden;
}

.el-dialog {
    border-radius: 3%;
    overflow: hidden;
}

/* 编辑器样式 */
:deep(.monaco-editor) {
    min-height: 200px;
    border-radius: 4px;
    overflow: hidden;
}
</style>
