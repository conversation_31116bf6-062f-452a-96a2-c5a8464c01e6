"""
客服会话情绪分析 API 视图

提供情绪分析结果的查询、统计、导出等接口
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from django.utils import timezone
from django.db.models import Q, Avg, Count, Sum
from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework import serializers
from rest_framework.filters import SearchFilter, OrderingFilter

from dvadmin.utils.json_response import SuccessResponse, ErrorResponse
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.filters import CustomDjangoFilterBackend

from apps.cs_manage.models import CsEmotionAnalysis, CsEmotionAnalysisDetail
from apps.cs_manage.serializers.cs_emotion_analysis import CsEmotionAnalysisExportSerializer

logger = logging.getLogger(__name__)


class CsEmotionAnalysisDetailSerializer(CustomModelSerializer):
    """情绪分析详情序列化器"""
    
    class Meta:
        model = CsEmotionAnalysisDetail
        fields = '__all__'


class CsEmotionAnalysisSerializer(CustomModelSerializer):
    """情绪分析序列化器"""
    
    # 关联字段
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_user_name = serializers.CharField(source='service_user.name', read_only=True, default='')
    service_user_username = serializers.CharField(source='service_user.username', read_only=True, default='')
    creator_name = serializers.CharField(source='creator.name', read_only=True, default='')
    
    # 计算字段
    session_duration_minutes = serializers.SerializerMethodField(read_only=True)
    emotion_change_level = serializers.SerializerMethodField(read_only=True)
    dept_name = serializers.SerializerMethodField(read_only=True)
    
    # 分阶段详情
    details = CsEmotionAnalysisDetailSerializer(many=True, read_only=True)
    
    class Meta:
        model = CsEmotionAnalysis
        fields = [
            'id', 'session_id', 'service_id', 'service_name', 'service_account',
            'game', 'game_name', 'service_user', 'service_user_name', 'service_user_username', 'dept_name',
            'initial_emotion_score', 'initial_emotion_justification',
            'final_emotion_score', 'final_emotion_justification',
            'emotion_change_score', 'emotion_change_level', 'overall_assessment',
            'session_start_time', 'session_end_time', 'session_duration', 'session_duration_minutes',
            'message_count', 'user_message_count', 'service_message_count',
            'conversation_summary', 'emotion_keywords', 'structured_conversation',
            'status', 'create_datetime', 'update_datetime', 'creator', 'creator_name',
            'details'  # 包含分阶段详情
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime', 'creator']
    
    def get_session_duration_minutes(self, obj):
        """获取会话时长（分钟）"""
        if obj.session_duration:
            return round(obj.session_duration / 60, 2)
        return 0
    
    def get_emotion_change_level(self, obj):
        """获取情绪变化等级"""
        if obj.emotion_change_score is None:
            return '未知'
        elif obj.emotion_change_score >= 5:
            return '大幅改善'
        elif obj.emotion_change_score >= 1:
            return '改善'
        elif obj.emotion_change_score >= -1:
            return '稳定'
        elif obj.emotion_change_score >= -5:
            return '恶化'
        else:
            return '大幅恶化'
    
    def get_dept_name(self, obj):
        """获取用户所属部门名称"""
        if obj.service_user and hasattr(obj.service_user, 'dept') and obj.service_user.dept:
            return obj.service_user.dept.name
        return ''


class CsEmotionAnalysisViewSet(CustomModelViewSet):
    """
    客服会话情绪分析视图集
    
    提供情绪分析结果的CRUD操作、统计和导出功能
    """
    
    queryset = CsEmotionAnalysis.objects.all().select_related(
        'game', 'link', 'service_user', 'creator'
    ).prefetch_related('details', 'service_user__dept', 'creator__dept')
    serializer_class = CsEmotionAnalysisSerializer
    ordering = ['-create_datetime']
    
    # 筛选字段
    filterset_fields = {
        'service_id': ['exact', 'in'],
        'service_name': ['icontains'],
        'game': ['exact'],
        'emotion_change_score': ['gte', 'lte', 'range'],
        'initial_emotion_score': ['gte', 'lte', 'range'],
        'final_emotion_score': ['gte', 'lte', 'range'],
        'session_start_time': ['gte', 'lte', 'date', 'date__range'],
        'status': ['exact', 'in'],
        'create_datetime': ['gte', 'lte', 'date', 'date__range'],
        # 添加部门筛选支持
        'service_user__dept__name': ['icontains', 'exact'],
        'session_id': ['icontains', 'exact'],
        'conversation_summary': ['icontains', 'exact'],
        'overall_assessment': ['icontains', 'exact'],
    }
    
    # 配置导出功能
    export_field_label = {
        'session_id': '会话ID',
        'service_name': '客服姓名',
        'service_user_name': '系统用户姓名',
        'service_user_username': '系统用户名',
        'dept_name': '部门',
        'game_name': '游戏',
        'initial_emotion_score': '初始情绪分数',
        'final_emotion_score': '最终情绪分数',
        'emotion_change_score': '情绪变化分数',
        'emotion_change_level': '变化等级',
        'session_duration_minutes': '会话时长(分钟)',
        'message_count': '消息总数',
        'user_message_count': '用户消息数',
        'service_message_count': '客服消息数',
        'conversation_summary': '会话摘要',
        'overall_assessment': '整体评估',
        'session_start_time_str': '会话开始时间',
        'session_end_time_str': '会话结束时间',
        'create_datetime_str': '分析时间',
        'emotion_keywords_text': '情绪关键词',
        'status': '处理状态'
    }
    export_serializer_class = CsEmotionAnalysisExportSerializer
    
    def get_queryset(self):
        """自定义查询集"""
        queryset = super().get_queryset()
        
        
        # 按游戏筛选
        game_id = self.request.query_params.get('game_id')
        if game_id:
            queryset = queryset.filter(game_id=game_id)
            
        # 按时间范围筛选
        days = self.request.query_params.get('days')
        if days:
            try:
                days = int(days)
                start_date = timezone.now() - timedelta(days=days)
                queryset = queryset.filter(create_datetime__gte=start_date)
            except (ValueError, TypeError):
                pass
        
        # 处理分析时间范围筛选
        create_datetime_gte = self.request.query_params.get('create_datetime__gte')
        create_datetime_lte = self.request.query_params.get('create_datetime__lte')
        if create_datetime_gte:
            logger.info(f"时间筛选 gte: {create_datetime_gte}")
            queryset = queryset.filter(create_datetime__gte=create_datetime_gte)
        if create_datetime_lte:
            logger.info(f"时间筛选 lte: {create_datetime_lte}")
            queryset = queryset.filter(create_datetime__lte=create_datetime_lte)
        
        # 按情绪改善类型筛选
        improvement_type = self.request.query_params.get('improvement_type')
        if improvement_type == 'improved':
            queryset = queryset.filter(emotion_change_score__gt=0)
        elif improvement_type == 'worsened':
            queryset = queryset.filter(emotion_change_score__lt=0)
        elif improvement_type == 'stable':
            queryset = queryset.filter(emotion_change_score__gte=-1, emotion_change_score__lte=1)
        
        # 按部门名称筛选
        dept_name = self.request.query_params.get('dept_name')
        if dept_name:
            logger.info(f"部门筛选: {dept_name}")
            queryset = queryset.filter(service_user__dept__name__icontains=dept_name)
        
        # 按情绪变化等级筛选
        emotion_change_level = self.request.query_params.get('emotion_change_level')
        if emotion_change_level:
            logger.info(f"情绪等级筛选: {emotion_change_level}")
            if emotion_change_level == '大幅改善':
                queryset = queryset.filter(emotion_change_score__gte=5)
            elif emotion_change_level == '改善':
                queryset = queryset.filter(emotion_change_score__gte=1, emotion_change_score__lt=5)
            elif emotion_change_level == '稳定':
                queryset = queryset.filter(emotion_change_score__gte=-1, emotion_change_score__lte=1)
            elif emotion_change_level == '恶化':
                queryset = queryset.filter(emotion_change_score__gte=-5, emotion_change_score__lt=-1)
            elif emotion_change_level == '大幅恶化':
                queryset = queryset.filter(emotion_change_score__lt=-5)
            
        return queryset
    

    
    @action(detail=False, methods=['post'])
    def manual_analysis(self, request):
        """
        手动触发会话情绪分析
        
        支持单个会话或批量会话的手动分析
        """
        try:
            session_ids = request.data.get('session_ids', [])
            force_reprocess = request.data.get('force_reprocess', False)
            
            if isinstance(session_ids, str):
                session_ids = [session_ids]
            
            if not session_ids:
                return ErrorResponse(msg="缺少session_ids参数")
            
            # 触发异步任务
            from apps.kcs.tasks import task__ai_cs_emotion_analysis
            
            results = []
            for session_id in session_ids[:50]:  # 限制最多50个
                try:
                    task__ai_cs_emotion_analysis.delay(
                        session_id=session_id,
                        force_reprocess=force_reprocess
                    )
                    results.append({
                        'session_id': session_id,
                        'status': 'queued',
                        'message': '任务已加入队列'
                    })
                except Exception as e:
                    results.append({
                        'session_id': session_id,
                        'status': 'error',
                        'message': str(e)
                    })
            
            return SuccessResponse(
                data={'results': results}, 
                msg=f"已触发{len(results)}个会话的情绪分析任务"
            )
            
        except Exception as e:
            logger.error(f"手动触发情绪分析失败: {str(e)}", exc_info=True)
            return ErrorResponse(msg=f"手动触发情绪分析失败: {str(e)}")
    
    @action(detail=True, methods=['get'])
    def conversation_detail(self, request, pk=None):
        """
        获取会话的详细对话内容和分析结果
        """
        try:
            analysis = self.get_object()
            
            # 获取会话消息详情
            from apps.common.qiyu_service import get_qiyu_service
            service = get_qiyu_service()
            messages = service.get_session_messages(analysis.session_id)
            
            # 格式化消息
            formatted_messages = []
            if messages:
                for msg in messages:
                    if msg.get("autoReply") == 1:
                        continue
                    
                    content = msg.get("msg", "")
                    if isinstance(content, str) and content.startswith("{"):
                        try:
                            msg_dict = json.loads(content)
                            if "text" in msg_dict:
                                content = msg_dict["text"]
                            elif "url" in msg_dict:
                                content = f"[链接] {msg_dict['url']}"
                        except json.JSONDecodeError:
                            pass
                    
                    if content and content.strip():
                        formatted_messages.append({
                            'id': msg.get('id'),
                            'sender': 'service' if msg.get('from') == 0 else 'user',
                            'content': content.strip(),
                            'timestamp': msg.get('time'),
                            'time_str': datetime.fromtimestamp(msg.get('time', 0) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        })
            
            # 组合返回数据
            result = {
                'analysis': CsEmotionAnalysisSerializer(analysis).data,
                'details': CsEmotionAnalysisDetailSerializer(analysis.details.all(), many=True).data,
                'conversation': formatted_messages
            }
            
            return SuccessResponse(data=result, msg="会话详情获取成功")
            
        except Exception as e:
            logger.error(f"获取会话详情失败: {str(e)}", exc_info=True)
            return ErrorResponse(msg=f"获取会话详情失败: {str(e)}")
    
 