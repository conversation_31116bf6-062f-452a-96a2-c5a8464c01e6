<template>
  <div class="emotion-analysis-detail">
    <!-- 头部信息卡片 -->
    <el-card class="header-card" shadow="hover">
      <div class="header-content">
        <div class="basic-info">
          <h3 class="session-title">
            <el-icon class="title-icon"><ChatDotRound /></el-icon>
            会话情绪分析详情
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">会话ID:</span>
              <span class="value">{{ data.session_id }}</span>
            </div>
            <div class="info-item">
              <span class="label">客服姓名:</span>
              <span class="value">{{ data.service_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">部门:</span>
              <span class="value">{{ data.dept_name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">分析时间:</span>
              <span class="value">{{ formatDateTime(data.create_datetime) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 情绪变化概览 -->
        <div class="emotion-overview">
          <div class="emotion-card initial">
            <div class="emotion-label">初始情绪</div>
            <div class="emotion-score" :class="getScoreClass(data.initial_emotion_score)">
              {{ data.initial_emotion_score?.toFixed(1) || '-' }}
            </div>
          </div>
          <div class="emotion-arrow">
            <el-icon size="24" color="#909399"><Right /></el-icon>
          </div>
          <div class="emotion-card final">
            <div class="emotion-label">最终情绪</div>
            <div class="emotion-score" :class="getScoreClass(data.final_emotion_score)">
              {{ data.final_emotion_score?.toFixed(1) || '-' }}
            </div>
          </div>
          <div class="emotion-change" :class="getChangeClass(data.emotion_change_score)">
            <div class="change-label">变化</div>
            <div class="change-score">
              {{ getChangeText(data.emotion_change_score) }}
            </div>
            <div class="change-level">{{ data.emotion_change_level }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <el-icon class="stat-icon messages"><ChatRound /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ data.message_count || 0 }}</div>
              <div class="stat-label">消息总数</div>
            </div>
          </div>
          <div class="stat-detail">
            <span>用户: {{ data.user_message_count || 0 }}</span>
            <span>客服: {{ data.service_message_count || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <el-icon class="stat-icon duration"><Timer /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ getDurationText(data.session_duration_minutes) }}</div>
              <div class="stat-label">会话时长</div>
            </div>
          </div>
          <div class="stat-detail">
            <span>{{ formatTimeRange(data.session_start_time, data.session_end_time) }}</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <el-icon class="stat-icon keywords"><Collection /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ (data.emotion_keywords || []).length }}</div>
              <div class="stat-label">情绪关键词</div>
            </div>
          </div>
          <div class="stat-detail">
            <span>关键特征词汇</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <el-icon class="stat-icon status"><CircleCheck /></el-icon>
            <div class="stat-info">
              <div class="stat-number">
                <el-tag :type="getStatusType(data.status)" size="small">
                  {{ getStatusText(data.status) }}
                </el-tag>
              </div>
              <div class="stat-label">分析状态</div>
            </div>
          </div>
          <div class="stat-detail">
            <span>处理完成</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分阶段详情 -->
    <el-card class="phase-card" shadow="hover" v-if="sortedPhaseDetails && sortedPhaseDetails.length">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><TrendCharts /></el-icon>
          <span>分阶段情绪分析</span>
        </div>
      </template>
      
      <div class="phase-timeline-horizontal">
        <div 
          v-for="(phase, index) in sortedPhaseDetails" 
          :key="phase.phase"
          class="phase-item-horizontal"
          :class="{ 'active': index === 1 }"
        >
          <!-- 阶段卡片 -->
          <div class="phase-card-item">
            <div class="phase-header-horizontal">
              <div class="phase-icon" :class="getPhaseIconClass(phase.phase)">
                <el-icon><component :is="getPhaseIcon(phase.phase)" /></el-icon>
              </div>
              <div class="phase-title">{{ getPhaseTitle(phase.phase) }}</div>
            </div>
            
            <div class="phase-score-large" :class="getScoreClass(phase.emotion_score)">
              {{ phase.emotion_score?.toFixed(1) || '-' }}
            </div>
            
            <div class="phase-content-horizontal">
              <div class="phase-description">{{ phase.emotion_description || '-' }}</div>
              
              <div class="phase-confidence">
                <span class="confidence-label">置信度:</span>
                <el-progress 
                  :percentage="Math.round((phase.confidence_level || 0) * 100)"
                  :stroke-width="4"
                  :show-text="false"
                  size="small"
                  :color="getConfidenceColor(phase.confidence_level)"
                />
                <span class="confidence-value">{{ Math.round((phase.confidence_level || 0) * 100) }}%</span>
              </div>
              
              <!-- 关键消息 -->
              <div class="key-messages" v-if="phase.key_messages && phase.key_messages.length">
                <div class="messages-title">关键消息:</div>
                <div class="messages-list">
                  <el-tag 
                    v-for="(msg, i) in phase.key_messages" 
                    :key="i"
                    size="small"
                    class="message-tag"
                    effect="plain"
                  >
                    {{ msg }}
                  </el-tag>
                  <span v-if="phase.key_messages.length > 2" class="more-count">
                    +{{ phase.key_messages.length - 2 }}
                  </span>
                </div>
              </div>
              
              <!-- 情绪触发因素 -->
              <div class="triggers" v-if="phase.emotion_triggers && phase.emotion_triggers.length">
                <div class="triggers-title">触发因素:</div>
                <div class="triggers-list">
                  <el-tag 
                    v-for="(trigger, i) in phase.emotion_triggers" 
                    :key="i"
                    size="small"
                    type="warning"
                    effect="light"
                    class="trigger-tag"
                  >
                    {{ trigger }}
                  </el-tag>
                  <span v-if="phase.emotion_triggers.length > 2" class="more-count">
                    +{{ phase.emotion_triggers.length - 2 }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 连接箭头 -->
          <div v-if="index < sortedPhaseDetails.length - 1" class="phase-arrow">
            <el-icon size="20" color="#409EFF"><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 会话摘要和评估 -->
    <el-row :gutter="16">
      <el-col :span="12">
        <el-card class="summary-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Document /></el-icon>
              <span>会话摘要</span>
            </div>
          </template>
          <div class="summary-content">
            <p class="summary-text">{{ data.conversation_summary || '暂无摘要' }}</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="assessment-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Medal /></el-icon>
              <span>整体评估</span>
            </div>
          </template>
          <div class="assessment-content">
            <p class="assessment-text">{{ data.overall_assessment || '暂无评估' }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 情绪关键词 -->
    <el-card class="keywords-card" shadow="hover" v-if="data.emotion_keywords && data.emotion_keywords.length">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><PriceTag /></el-icon>
          <span>情绪关键词</span>
        </div>
      </template>
      <div class="keywords-cloud">
        <el-tag
          v-for="(keyword, index) in data.emotion_keywords"
          :key="index"
          :type="getKeywordType(index)"
          effect="light"
          size="small"
          class="keyword-tag"
        >
          {{ keyword }}
        </el-tag>
      </div>
    </el-card>

    <!-- 结构化数据 -->
    <el-card class="structured-card" shadow="hover" v-if="data.structured_conversation">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><DataAnalysis /></el-icon>
          <span>结构化会话数据</span>
        </div>
      </template>
      <div class="structured-content">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item 
            v-for="(value, key) in structuredData" 
            :key="key"
            :title="getStructuredTitle(key)"
            :name="key"
          >
            <div class="structured-section">
              <div v-if="Array.isArray(value)" class="list-content">
                <el-tag 
                  v-for="(item, i) in value" 
                  :key="i"
                  size="small"
                  class="list-tag"
                  effect="plain"
                >
                  {{ item }}
                </el-tag>
              </div>
              <div v-else-if="typeof value === 'string'" class="text-content">
                <p>{{ value }}</p>
              </div>
              <div v-else class="object-content">
                <pre>{{ JSON.stringify(value, null, 2) }}</pre>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  ChatDotRound, Right, ChatRound, Timer, Collection, CircleCheck, 
  TrendCharts, Document, Medal, PriceTag, DataAnalysis,
  Sunrise, PartlyCloudy, Sunset, ArrowRight
} from '@element-plus/icons-vue';

interface EmotionAnalysisData {
  session_id: string;
  service_name: string;
  dept_name?: string;
  create_datetime: string;
  initial_emotion_score?: number;
  final_emotion_score?: number;
  emotion_change_score?: number;
  emotion_change_level?: string;
  message_count?: number;
  user_message_count?: number;
  service_message_count?: number;
  session_duration_minutes?: number;
  session_start_time?: string;
  session_end_time?: string;
  emotion_keywords?: string[];
  status?: string;
  conversation_summary?: string;
  overall_assessment?: string;
  structured_conversation?: any;
  details?: any[];
}

const props = defineProps<{
  data: EmotionAnalysisData;
}>();

const activeCollapse = ref<string[]>([]);

// 计算分阶段详情
const phaseDetails = computed(() => {
  return props.data.details || [];
});

// 排序后的阶段详情（按照时间顺序：初始 -> 中间 -> 最终）
const sortedPhaseDetails = computed(() => {
  const details = [...phaseDetails.value];
  const phaseOrder = { 'initial': 1, 'middle': 2, 'final': 3 };
  return details.sort((a, b) => {
    const orderA = phaseOrder[a.phase] || 999;
    const orderB = phaseOrder[b.phase] || 999;
    return orderA - orderB;
  });
});

// 结构化数据
const structuredData = computed(() => {
  return props.data.structured_conversation || {};
});

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 获取分数样式类
const getScoreClass = (score?: number) => {
  if (score === null || score === undefined) return 'score-unknown';
  if (score >= 5) return 'score-positive';
  if (score >= 0) return 'score-neutral';
  return 'score-negative';
};

// 获取变化样式类
const getChangeClass = (change?: number) => {
  if (change === null || change === undefined) return 'change-unknown';
  if (change > 0) return 'change-positive';
  if (change < 0) return 'change-negative';
  return 'change-neutral';
};

// 获取变化文本
const getChangeText = (change?: number) => {
  if (change === null || change === undefined) return '-';
  const prefix = change >= 0 ? '+' : '';
  return `${prefix}${change.toFixed(1)}`;
};

// 获取时长文本
const getDurationText = (minutes?: number) => {
  if (!minutes) return '-';
  if (minutes < 60) return `${minutes}分钟`;
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hours}小时${mins}分钟`;
};

// 格式化时间范围
const formatTimeRange = (start?: string, end?: string) => {
  if (!start || !end) return '-';
  const startTime = new Date(start).toLocaleTimeString('zh-CN');
  const endTime = new Date(end).toLocaleTimeString('zh-CN');
  return `${startTime} - ${endTime}`;
};

// 获取状态类型
const getStatusType = (status?: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'processing': 'warning',
    'failed': 'danger',
    'pending': 'info'
  };
  return typeMap[status || ''] || 'info';
};

// 获取状态文本
const getStatusText = (status?: string) => {
  const textMap: Record<string, string> = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '处理失败',
    'pending': '待处理'
  };
  return textMap[status || ''] || '未知';
};

// 获取阶段标题
const getPhaseTitle = (phase: string) => {
  const titleMap: Record<string, string> = {
    'initial': '初始阶段',
    'middle': '中间阶段',
    'final': '最终阶段'
  };
  return titleMap[phase] || phase;
};

// 获取阶段图标
const getPhaseIcon = (phase: string) => {
  const iconMap: Record<string, any> = {
    'initial': Sunrise,
    'middle': PartlyCloudy,
    'final': Sunset
  };
  return iconMap[phase] || PartlyCloudy;
};

// 获取阶段图标样式类
const getPhaseIconClass = (phase: string) => {
  const classMap: Record<string, string> = {
    'initial': 'phase-initial',
    'middle': 'phase-middle',
    'final': 'phase-final'
  };
  return classMap[phase] || '';
};

// 获取置信度颜色
const getConfidenceColor = (confidence?: number) => {
  if (!confidence) return '#F56C6C';
  if (confidence >= 0.8) return '#67C23A';
  if (confidence >= 0.6) return '#E6A23C';
  return '#F56C6C';
};

// 获取关键词类型
const getKeywordType = (index: number) => {
  const types = ['primary', 'success', 'warning', 'danger', 'info'];
  return types[index % types.length];
};

// 获取结构化数据标题
const getStructuredTitle = (key: string) => {
  const titleMap: Record<string, string> = {
    'user_issues': '用户问题',
    'service_actions': '客服行为',
    'resolution_steps': '解决步骤',
    'emotion_flow': '情绪流向',
    'key_moments': '关键时刻'
  };
  return titleMap[key] || key;
};
</script>

<style scoped>
.emotion-analysis-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 头部卡片 */
.header-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.basic-info {
  flex: 1;
}

.session-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.title-icon {
  color: #409EFF;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 600;
}

/* 情绪概览 */
.emotion-overview {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
}

.emotion-card {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 80px;
}

.emotion-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.emotion-score {
  font-size: 24px;
  font-weight: bold;
}

.emotion-arrow {
  opacity: 0.6;
}

.emotion-change {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 100px;
}

.change-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.change-score {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.change-level {
  font-size: 12px;
  opacity: 0.8;
}

/* 分数样式 */
.score-positive { color: #67C23A; }
.score-neutral { color: #E6A23C; }
.score-negative { color: #F56C6C; }
.score-unknown { color: #909399; }

.change-positive { color: #67C23A; }
.change-negative { color: #F56C6C; }
.change-neutral { color: #909399; }
.change-unknown { color: #909399; }

/* 统计卡片 */
.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.stat-icon {
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
}

.stat-icon.messages { color: #409EFF; background: #ecf5ff; }
.stat-icon.duration { color: #67C23A; background: #f0f9ff; }
.stat-icon.keywords { color: #E6A23C; background: #fdf6ec; }
.stat-icon.status { color: #F56C6C; background: #fef0f0; }

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-detail {
  font-size: 12px;
  color: #606266;
  display: flex;
  gap: 8px;
}

/* 阶段卡片 */
.phase-card, .summary-card, .assessment-card, .keywords-card, .structured-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  color: #409EFF;
}

/* 水平阶段时间轴 */
.phase-timeline-horizontal {
  display: flex;
  align-items: stretch;
  gap: 24px;
  padding: 20px 0;
  overflow-x: auto;
}

.phase-item-horizontal {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  min-width: 0;
}

.phase-card-item {
  flex: 1;
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.phase-card-item:hover {
  background: #f5f7fa;
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
}

.phase-item-horizontal.active .phase-card-item {
  background: #ecf5ff;
  border-color: #409EFF;
}

.phase-header-horizontal {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.phase-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.phase-icon.phase-initial { background: linear-gradient(135deg, #E6A23C, #F7BA2A); }
.phase-icon.phase-middle { background: linear-gradient(135deg, #409EFF, #66B1FF); }
.phase-icon.phase-final { background: linear-gradient(135deg, #67C23A, #85CE61); }

.phase-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.phase-score-large {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  border-radius: 12px;
  background: white;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.phase-content-horizontal {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.phase-description {
  color: #606266;
  line-height: 1.5;
  font-size: 13px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  flex: 1;
}

.phase-confidence {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 8px;
}

.confidence-label {
  font-size: 12px;
  color: #909399;
  min-width: 50px;
}

.confidence-value {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
  min-width: 30px;
}

.phase-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.phase-arrow:hover {
  opacity: 1;
  transform: scale(1.1);
}

.key-messages, .triggers {
  background: white;
  padding: 8px 12px;
  border-radius: 8px;
}

.messages-title, .triggers-title {
  font-size: 11px;
  color: #909399;
  margin-bottom: 6px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.messages-list, .triggers-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.message-tag, .trigger-tag {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
}

.more-count {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 摘要和评估 */
.summary-content, .assessment-content {
  padding: 16px 0;
}

.summary-text, .assessment-text {
  line-height: 1.6;
  color: #606266;
  margin: 0;
  font-size: 14px;
}

/* 关键词云 */
.keywords-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px 0;
}

.keyword-tag {
  margin: 0;
  cursor: default;
  transition: all 0.3s ease;
}

.keyword-tag:hover {
  transform: scale(1.05);
}

/* 结构化数据 */
.structured-content {
  padding: 16px 0;
}

.structured-section {
  padding: 12px 0;
}

.list-content {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.list-tag {
  margin: 0;
}

.text-content p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.object-content pre {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .emotion-analysis-detail {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .emotion-overview {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .phase-timeline-horizontal {
    flex-direction: column;
    gap: 16px;
  }
  
  .phase-item-horizontal {
    flex-direction: column;
    gap: 12px;
  }
  
  .phase-card-item {
    min-height: auto;
  }
  
  .phase-arrow {
    transform: rotate(90deg);
  }
  
  .phase-score-large {
    font-size: 24px;
  }
}
</style>