{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/javascript/javascript.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/javascript/javascript.ts\nimport { conf as tsConf, language as tsLanguage } from \"../typescript/typescript.js\";\nvar conf = tsConf;\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".js\",\n  keywords: [\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"const\",\n    \"constructor\",\n    \"debugger\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"function\",\n    \"get\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"instanceof\",\n    \"let\",\n    \"new\",\n    \"null\",\n    \"return\",\n    \"set\",\n    \"static\",\n    \"super\",\n    \"switch\",\n    \"symbol\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"undefined\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"async\",\n    \"await\",\n    \"of\"\n  ],\n  typeKeywords: [],\n  operators: tsLanguage.operators,\n  symbols: tsLanguage.symbols,\n  escapes: tsLanguage.escapes,\n  digits: tsLanguage.digits,\n  octaldigits: tsLanguage.octaldigits,\n  binarydigits: tsLanguage.binarydigits,\n  hexdigits: tsLanguage.hexdigits,\n  regexpctl: tsLanguage.regexpctl,\n  regexpesc: tsLanguage.regexpesc,\n  tokenizer: tsLanguage.tokenizer\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;AAUA,IAAIA,QAAO;AACX,IAAIC,YAAW;AAAA;AAAA,EAEb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc,CAAC;AAAA,EACf,WAAW,SAAW;AAAA,EACtB,SAAS,SAAW;AAAA,EACpB,SAAS,SAAW;AAAA,EACpB,QAAQ,SAAW;AAAA,EACnB,aAAa,SAAW;AAAA,EACxB,cAAc,SAAW;AAAA,EACzB,WAAW,SAAW;AAAA,EACtB,WAAW,SAAW;AAAA,EACtB,WAAW,SAAW;AAAA,EACtB,WAAW,SAAW;AACxB;", "names": ["conf", "language"]}