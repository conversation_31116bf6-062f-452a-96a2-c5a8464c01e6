from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix

table_prefix = table_prefix + "cs_manage_"

class DailyMetricGroup(CoreModel):
    """每日客服指标按组别统计表"""
    
    date = models.DateField(verbose_name="统计日期", help_text="统计的日期", db_index=True)
    group_id = models.BigIntegerField(default=0, verbose_name="组别ID", help_text="客服组ID，0表示全部")
    group_name = models.CharField(max_length=128, blank=True, verbose_name="组别名称", help_text="客服组名称")
    
    # 基础会话指标
    total_sessions = models.IntegerField(default=0, verbose_name="总会话量", help_text="当日总会话数")
    ai_sessions = models.IntegerField(default=0, verbose_name="AI会话量", help_text="AI客服处理的会话数")
    manual_sessions = models.IntegerField(default=0, verbose_name="人工会话量", help_text="人工客服处理的会话数")
    ai_transfer = models.IntegerField(default=0, verbose_name="AI转人工量", help_text="从AI转到人工的会话数")
    
    # 工单相关
    worksheet_count = models.IntegerField(default=0, verbose_name="工单量", help_text="当日工单数量")

    # 情绪分析已移除，改为实时计算

    # 计算字段（API中没有的数据）
    ai_transfer_rate = models.FloatField(default=0, verbose_name="AI转人工率", help_text="AI转人工的比例")
    manual_handle_ai_ratio = models.FloatField(default=0, verbose_name="人工处理AI占比", help_text="人工处理AI转接的占比")

    # 注意：以下字段已删除，改为从qiyu_raw_data中获取：
    # - online_ratio: 从raw_data.realSessionInRatio获取
    # - manual_satisfaction: 从raw_data.satisfactionRatio获取
    # - fcr_ratio: 从raw_data.oneOffRatio获取
    # - avg_first_resp: 从raw_data.avgFirstRespTime获取
    # - resp_30_ratio: 从raw_data.specialAnswerRatio获取
    # - eva_count: 从raw_data计算得出
    # - eva_ratio: 从raw_data.evaRatio获取
    # - invite_count: 从raw_data.evaFromCount获取

    # 七鱼原始数据存储
    qiyu_raw_data = models.JSONField(
        null=True, blank=True,
        verbose_name="七鱼原始数据",
        help_text="完整的七鱼API响应数据，用于扩展性和数据完整性"
    )
    data_version = models.CharField(
        max_length=20, default="v1.0",
        verbose_name="数据版本",
        help_text="数据结构版本，用于兼容性管理"
    )
    
    class Meta:
        db_table = table_prefix + "daily_metric_group"
        verbose_name = "每日客服指标统计"
        verbose_name_plural = verbose_name
        unique_together = ("date", "group_id")
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['group_id']),
            models.Index(fields=['date', 'group_id']),
        ]
        
    def __str__(self):
        group_display = self.group_name if self.group_name else f"组别{self.group_id}"
        if self.group_id == 0:
            group_display = "全部"
        return f"{self.date} - {group_display}"
    
    @property
    def ai_sessions_ratio(self):
        """AI会话占比"""
        if self.total_sessions == 0:
            return 0
        return round(self.ai_sessions / self.total_sessions * 100, 2)
    
    @property
    def manual_sessions_ratio(self):
        """人工会话占比"""
        if self.total_sessions == 0:
            return 0
        return round(self.manual_sessions / self.total_sessions * 100, 2)
    
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        # AI转人工率
        if self.ai_sessions > 0:
            self.ai_transfer_rate = round(self.ai_transfer / self.ai_sessions * 100, 2)
        else:
            self.ai_transfer_rate = 0

        # 人工处理AI占比
        if self.manual_sessions > 0:
            self.manual_handle_ai_ratio = round(self.ai_transfer / self.manual_sessions * 100, 2)
        else:
            self.manual_handle_ai_ratio = 0

    # 从raw数据获取字段的属性方法
    @property
    def online_ratio(self):
        """在线接入率（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return round(self.qiyu_raw_data.get('realSessionInRatio', 0) * 100, 2)

    @property
    def manual_satisfaction(self):
        """人工满意度（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return round(self.qiyu_raw_data.get('satisfactionRatio', 0) * 100, 2)

    @property
    def fcr_ratio(self):
        """FCR率（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return round(self.qiyu_raw_data.get('oneOffRatio', 0) * 100, 2)

    @property
    def avg_first_resp(self):
        """平均首响时间（从raw数据获取，毫秒转秒）"""
        if not self.qiyu_raw_data:
            return 0
        return int(self.qiyu_raw_data.get('avgFirstRespTime', 0) / 1000)

    @property
    def resp_30_ratio(self):
        """30秒应答率（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return round(self.qiyu_raw_data.get('specialAnswerRatio', 0) * 100, 2)

    @property
    def eva_count(self):
        """参评数（从raw数据计算）"""
        if not self.qiyu_raw_data:
            return 0
        effect_sessions = self.qiyu_raw_data.get('effectSessions', 0)
        eva_ratio = self.qiyu_raw_data.get('evaRatio', 0)
        return int(effect_sessions * eva_ratio)

    @property
    def eva_ratio(self):
        """参评率（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return round(self.qiyu_raw_data.get('evaRatio', 0) * 100, 2)

    @property
    def invite_count(self):
        """邀评数（从raw数据获取）"""
        if not self.qiyu_raw_data:
            return 0
        return self.qiyu_raw_data.get('evaFromCount', 0)
