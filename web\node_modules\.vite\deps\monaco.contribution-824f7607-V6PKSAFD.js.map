{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/language/json/monaco.contribution.js"], "sourcesContent": ["import '../../editor/editor.api.js';\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/monaco.contribution.ts\nvar LanguageServiceDefaultsImpl = class {\n  constructor(languageId, diagnosticsOptions, modeConfiguration) {\n    this._onDidChange = new monaco_editor_core_exports.Emitter();\n    this._languageId = languageId;\n    this.setDiagnosticsOptions(diagnosticsOptions);\n    this.setModeConfiguration(modeConfiguration);\n  }\n  get onDidChange() {\n    return this._onDidChange.event;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get modeConfiguration() {\n    return this._modeConfiguration;\n  }\n  get diagnosticsOptions() {\n    return this._diagnosticsOptions;\n  }\n  setDiagnosticsOptions(options) {\n    this._diagnosticsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(this);\n  }\n  setModeConfiguration(modeConfiguration) {\n    this._modeConfiguration = modeConfiguration || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(this);\n  }\n};\nvar diagnosticDefault = {\n  validate: true,\n  allowComments: true,\n  schemas: [],\n  enableSchemaRequest: false,\n  schemaRequest: \"warning\",\n  schemaValidation: \"warning\",\n  comments: \"error\",\n  trailingCommas: \"error\"\n};\nvar modeConfigurationDefault = {\n  documentFormattingEdits: true,\n  documentRangeFormattingEdits: true,\n  completionItems: true,\n  hovers: true,\n  documentSymbols: true,\n  tokens: true,\n  colors: true,\n  foldingRanges: true,\n  diagnostics: true,\n  selectionRanges: true\n};\nvar jsonDefaults = new LanguageServiceDefaultsImpl(\n  \"json\",\n  diagnosticDefault,\n  modeConfigurationDefault\n);\nvar getWorker = () => getMode().then((mode) => mode.getWorker());\nmonaco_editor_core_exports.languages.json = { jsonDefaults, getWorker };\nfunction getMode() {\n  if (false) {\n    return new Promise((resolve, reject) => {\n      __require([\"vs/language/json/jsonMode\"], resolve, reject);\n    });\n  } else {\n    return import(\"./jsonMode.js\");\n  }\n}\nmonaco_editor_core_exports.languages.register({\n  id: \"json\",\n  extensions: [\".json\", \".bowerrc\", \".jshintrc\", \".jscsrc\", \".eslintrc\", \".babelrc\", \".har\"],\n  aliases: [\"JSON\", \"json\"],\n  mimetypes: [\"application/json\"]\n});\nmonaco_editor_core_exports.languages.onLanguage(\"json\", () => {\n  getMode().then((mode) => mode.setupMode(jsonDefaults));\n});\nexport {\n  getWorker,\n  jsonDefaults\n};\n"], "mappings": ";;;;;;AAQA,IAAIA,IAAY,OAAO;AAAvB,IACIC,IAAmB,OAAO;AAD9B,IAEIC,IAAoB,OAAO;AAF/B,IAGIC,IAAe,OAAO,UAAU;AAHpC,IAIIC,IAAc,CAACC,GAAIC,GAAMC,GAAQC,MAAS;AAC5C,MAAIF,KAAQ,OAAOA,KAAS,YAAY,OAAOA,KAAS;AACtD,aAASG,KAAOP,EAAkBI,CAAI;AAChC,OAACH,EAAa,KAAKE,GAAII,CAAG,KAAKA,MAAQF,KACzCP,EAAUK,GAAII,GAAK,EAAE,KAAK,MAAMH,EAAKG,CAAG,GAAG,YAAY,EAAED,IAAOP,EAAiBK,GAAMG,CAAG,MAAMD,EAAK,WAAU,CAAE;AAEvH,SAAOH;AACT;AAXA,IAYIK,IAAa,CAACC,GAAQC,GAAKC,OAAkBT,EAAYO,GAAQC,GAAK,SAAS,GAAGC,KAAgBT,EAAYS,GAAcD,GAAK,SAAS;AAZ9I,IAeIE,IAA6B,CAAA;AACjCJ,EAAWI,GAA4BC,GAAuB;AAI9D,IAAIC,IAA8B,MAAM;EACtC,YAAYC,GAAYC,GAAoBC,GAAmB;AAC7D,SAAK,eAAe,IAAIL,EAA2B,QAAO,GAC1D,KAAK,cAAcG,GACnB,KAAK,sBAAsBC,CAAkB,GAC7C,KAAK,qBAAqBC,CAAiB;EAC5C;EACD,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa;EAC1B;EACD,IAAI,aAAa;AACf,WAAO,KAAK;EACb;EACD,IAAI,oBAAoB;AACtB,WAAO,KAAK;EACb;EACD,IAAI,qBAAqB;AACvB,WAAO,KAAK;EACb;EACD,sBAAsBC,GAAS;AAC7B,SAAK,sBAAsBA,KAA2B,uBAAO,OAAO,IAAI,GACxE,KAAK,aAAa,KAAK,IAAI;EAC5B;EACD,qBAAqBD,GAAmB;AACtC,SAAK,qBAAqBA,KAAqC,uBAAO,OAAO,IAAI,GACjF,KAAK,aAAa,KAAK,IAAI;EAC5B;AACH;AA3BA,IA4BIE,IAAoB;EACtB,UAAU;EACV,eAAe;EACf,SAAS,CAAE;EACX,qBAAqB;EACrB,eAAe;EACf,kBAAkB;EAClB,UAAU;EACV,gBAAgB;AAClB;AArCA,IAsCIC,IAA2B;EAC7B,yBAAyB;EACzB,8BAA8B;EAC9B,iBAAiB;EACjB,QAAQ;EACR,iBAAiB;EACjB,QAAQ;EACR,QAAQ;EACR,eAAe;EACf,aAAa;EACb,iBAAiB;AACnB;AAjDA,IAkDIC,IAAe,IAAIP;EACrB;EACAK;EACAC;AACF;AAtDA,IAuDIE,IAAY,MAAMC,EAAAA,EAAU,KAAK,CAACC,MAASA,EAAK,UAAA,CAAW;AAC/DZ,EAA2B,UAAU,OAAO,EAAE,cAAAS,GAAc,WAAAC,EAAS;AACrE,SAASC,IAAU;AAMf,SAAO,OAAO,iCAAe;AAEjC;AACAX,EAA2B,UAAU,SAAS;EAC5C,IAAI;EACJ,YAAY,CAAC,SAAS,YAAY,aAAa,WAAW,aAAa,YAAY,MAAM;EACzF,SAAS,CAAC,QAAQ,MAAM;EACxB,WAAW,CAAC,kBAAkB;AAChC,CAAC;AACDA,EAA2B,UAAU,WAAW,QAAQ,MAAM;AAC5DW,IAAO,EAAG,KAAK,CAACC,MAASA,EAAK,UAAUH,CAAY,CAAC;AACvD,CAAC;", "names": ["__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "__reExport", "target", "mod", "second<PERSON><PERSON><PERSON>", "monaco_editor_core_exports", "monaco_editor_core_star", "LanguageServiceDefaultsImpl", "languageId", "diagnosticsOptions", "modeConfiguration", "options", "diagnosticDefault", "modeConfigurationDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getWorker", "getMode", "mode"]}