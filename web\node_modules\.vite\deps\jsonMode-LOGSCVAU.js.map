{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/json/jsonMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `JSONWorker` instance\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker2) => {\n      return worker2.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia = false) {\n  const len = text.length;\n  let pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    let digits = 0;\n    let value2 = 0;\n    while (digits < count || !exact) {\n      let ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    let start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    let end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    let result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      const ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        const ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            const ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    let code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        const start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          const safeLength = len - 1;\n          let commentClosed = false;\n          while (pos < safeLength) {\n            const ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    let result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: () => pos,\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: () => token,\n    getTokenValue: () => value,\n    getTokenOffset: () => tokenOffset,\n    getTokenLength: () => pos - tokenOffset,\n    getTokenStartLine: () => lineStartOffset,\n    getTokenStartCharacter: () => tokenOffset - prevTokenLineStartOffset,\n    getTokenError: () => scanError\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function(CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\n\n// node_modules/jsonc-parser/lib/esm/impl/string-intern.js\nvar cachedSpaces = new Array(20).fill(0).map((_, index) => {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\nvar ScanError;\n(function(ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function(SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar ParseErrorCode;\n(function(ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class _ParentsStack {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new _ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class _JSONState {\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new _JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof _JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\n        \"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3)\n      );\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(\n      state.getStateData(),\n      scanner.getTokenError(),\n      lastWasColon,\n      parents\n    );\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar worker;\nfunction getWorker() {\n  return new Promise((resolve, reject) => {\n    if (!worker) {\n      return reject(\"JSON not registered!\");\n    }\n    resolve(worker);\n  });\n}\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker2, defaults) {\n    super(languageId, worker2, defaults.onDidChange);\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n        this._resetSchema(model.uri);\n      })\n    );\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        this._resetSchema(event.model.uri);\n      })\n    );\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker2) => {\n      worker2.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\" \", \":\", '\"'])\n        )\n      );\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\nexport {\n  CompletionAdapter,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentColorAdapter,\n  DocumentFormattingEditProvider,\n  DocumentHighlightAdapter,\n  DocumentLinkAdapter,\n  DocumentRangeFormattingEditProvider,\n  DocumentSymbolAdapter,\n  FoldingRangeAdapter,\n  HoverAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SelectionRangeAdapter,\n  WorkerManager,\n  fromPosition,\n  fromRange,\n  getWorker,\n  setupMode,\n  toRange,\n  toTextEdit\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,qBAAqB,IAAI,KAAK;AAClC,IAAI,gBAAgB,MAAM;AAAA,EACxB,YAAY,UAAU;AACpB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,qBAAqB,OAAO,YAAY,MAAM,KAAK,aAAa,GAAG,KAAK,GAAG;AAChF,SAAK,gBAAgB;AACrB,SAAK,wBAAwB,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,CAAC;AAAA,EAClF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU;AACR,kBAAc,KAAK,kBAAkB;AACrC,SAAK,sBAAsB,QAAQ;AACnC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AACA,QAAI,0BAA0B,KAAK,IAAI,IAAI,KAAK;AAChD,QAAI,0BAA0B,oBAAoB;AAChD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,gBAAgB,KAAK,IAAI;AAC9B,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,2BAA2B,OAAO,gBAAgB;AAAA;AAAA,QAE/D,UAAU;AAAA,QACV,OAAO,KAAK,UAAU;AAAA;AAAA,QAEtB,YAAY;AAAA,UACV,kBAAkB,KAAK,UAAU;AAAA,UACjC,YAAY,KAAK,UAAU;AAAA,UAC3B,qBAAqB,KAAK,UAAU,mBAAmB;AAAA,QACzD;AAAA,MACF,CAAC;AACD,WAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,IACvC;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,4BAA4B,WAAW;AACrC,QAAI;AACJ,WAAO,KAAK,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,gBAAU;AAAA,IACZ,CAAC,EAAE,KAAK,CAAC,MAAM;AACb,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,oBAAoB,SAAS;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,MAAM,OAAO;AAAA,EACxB;AACF;AAGA,IAAI;AAAA,CACH,SAAS,cAAc;AACtB,WAAS,GAAG,OAAO;AACjB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,eAAa,KAAK;AACpB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAS,MAAM;AACd,WAAS,GAAG,OAAO;AACjB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,OAAK,KAAK;AACZ,GAAG,QAAQ,MAAM,CAAC,EAAE;AACpB,IAAI;AAAA,CACH,SAAS,UAAU;AAClB,WAAS,YAAY;AACrB,WAAS,YAAY;AACrB,WAAS,GAAG,OAAO;AACjB,WAAO,OAAO,UAAU,YAAY,SAAS,aAAa,SAAS,SAAS,SAAS;AAAA,EACvF;AACA,WAAS,KAAK;AAChB,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,YAAU,YAAY;AACtB,YAAU,YAAY;AACtB,WAAS,GAAG,OAAO;AACjB,WAAO,OAAO,UAAU,YAAY,UAAU,aAAa,SAAS,SAAS,UAAU;AAAA,EACzF;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,MAAM,WAAW;AAC/B,QAAI,SAAS,OAAO,WAAW;AAC7B,aAAO,SAAS;AAAA,IAClB;AACA,QAAI,cAAc,OAAO,WAAW;AAClC,kBAAY,SAAS;AAAA,IACvB;AACA,WAAO,EAAE,MAAM,UAAU;AAAA,EAC3B;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,EACtG;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACrC,QAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACnF,aAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,IAC/E,WAAW,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC/C,aAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IAChC,OAAO;AACL,YAAM,IAAI,MAAM,8CAA8C,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI,GAAG;AAAA,IACjG;AAAA,EACF;AACA,SAAO,SAAS;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,EACjG;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,KAAK,OAAO;AAC1B,WAAO,EAAE,KAAK,MAAM;AAAA,EACtB;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,EAC5H;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAClF,WAAO,EAAE,WAAW,aAAa,sBAAsB,qBAAqB;AAAA,EAC9E;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,KAAK,MAAM,GAAG,UAAU,oBAAoB,MAAM,MAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,EACjP;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS;AAChB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,EACpM;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,OAAO,OAAO;AAC5B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAC7F;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,OAAO,UAAU,qBAAqB;AACpD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OAAO,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,EAC9O;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,oBAAkB,UAAU;AAC5B,oBAAkB,UAAU;AAC5B,oBAAkB,SAAS;AAC7B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM,eAAe;AACrF,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,QAAI,GAAG,QAAQ,cAAc,GAAG;AAC9B,aAAO,iBAAiB;AAAA,IAC1B;AACA,QAAI,GAAG,QAAQ,YAAY,GAAG;AAC5B,aAAO,eAAe;AAAA,IACxB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC7B,aAAO,gBAAgB;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MAAM,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAAO,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAAO,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC/U;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,+BAA+B;AACvC,WAAS,OAAO,UAAU,SAAS;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,gCAA8B,SAAS;AACvC,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAChG;AACA,gCAA8B,KAAK;AACrC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AACtE,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,sBAAoB,QAAQ;AAC5B,sBAAoB,UAAU;AAC9B,sBAAoB,cAAc;AAClC,sBAAoB,OAAO;AAC7B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,iBAAe,cAAc;AAC7B,iBAAe,aAAa;AAC9B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,kBAAkB;AAC1B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAChE;AACA,mBAAiB,KAAK;AACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AAC1E,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,QAAQ,GAAG;AACxB,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,GAAG,QAAQ,MAAM,GAAG;AACtB,aAAO,SAAS;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,kBAAkB,GAAG;AAClC,aAAO,qBAAqB;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI;AACJ,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO,MAAM,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAAO,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OAAO,GAAG,UAAU,UAAU,eAAe,KAAK,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAO,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAAO,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,EACzkB;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,UAAU;AAClB,WAAS,OAAO,OAAO,YAAY,MAAM;AACvC,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACvC,aAAO,YAAY;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,WAAS,SAAS;AAClB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAC3F;AACA,WAAS,KAAK;AAChB,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,QAAQ,OAAO,SAAS;AAC/B,WAAO,EAAE,OAAO,QAAQ;AAAA,EAC1B;AACA,YAAU,UAAU;AACpB,WAAS,OAAO,UAAU,SAAS;AACjC,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAQ;AAAA,EAC9D;AACA,YAAU,SAAS;AACnB,WAAS,IAAI,OAAO;AAClB,WAAO,EAAE,OAAO,SAAS,GAAG;AAAA,EAC9B;AACA,YAAU,MAAM;AAChB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAChG;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,OAAO,mBAAmB,aAAa;AACrD,UAAM,SAAS,EAAE,MAAM;AACvB,QAAI,sBAAsB,QAAQ;AAChC,aAAO,oBAAoB;AAAA,IAC7B;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO,cAAc;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAAY,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EAC5N;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,6BAA6B;AACrC,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,OAAO,SAAS;AAAA,EAC5B;AACA,8BAA4B,KAAK;AACnC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAClE,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,QAAQ,OAAO,SAAS,YAAY;AAC3C,WAAO,EAAE,OAAO,SAAS,cAAc,WAAW;AAAA,EACpD;AACA,qBAAmB,UAAU;AAC7B,WAAS,OAAO,UAAU,SAAS,YAAY;AAC7C,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAS,cAAc,WAAW;AAAA,EACxF;AACA,qBAAmB,SAAS;AAC5B,WAAS,IAAI,OAAO,YAAY;AAC9B,WAAO,EAAE,OAAO,SAAS,IAAI,cAAc,WAAW;AAAA,EACxD;AACA,qBAAmB,MAAM;AACzB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACvI;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,cAAc,OAAO;AACnC,WAAO,EAAE,cAAc,MAAM;AAAA,EAC/B;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,wCAAwC,GAAG,UAAU,YAAY,KAAK,MAAM,QAAQ,UAAU,KAAK;AAAA,EACrI;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,KAAK,SAAS,YAAY;AACxC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACrY;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACnD,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACva;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,KAAK,SAAS,YAAY;AACxC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,sBAAsB,SAAS;AAChG,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ;AACzB,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAU,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,EAC3Y;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,cAAc,UAAU,YAAY,UAAU,UAAU,oBAAoB,YAAY,UAAU,oBAAoB,UAAU,UAAU,gBAAgB,MAAM,CAAC,WAAW;AACjL,UAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AAC1B,eAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,MAC/E,OAAO;AACL,eAAO,iBAAiB,GAAG,MAAM;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AACA,iBAAe,KAAK;AACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,yBAAyB;AACjC,WAAS,OAAO,KAAK;AACnB,WAAO,EAAE,IAAI;AAAA,EACf;AACA,0BAAwB,SAAS;AACjC,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,EACzD;AACA,0BAAwB,KAAK;AAC/B,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAC1D,IAAI;AAAA,CACH,SAAS,kCAAkC;AAC1C,WAAS,OAAO,KAAK,SAAS;AAC5B,WAAO,EAAE,KAAK,QAAQ;AAAA,EACxB;AACA,mCAAiC,SAAS;AAC1C,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC1F;AACA,mCAAiC,KAAK;AACxC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAC5E,IAAI;AAAA,CACH,SAAS,0CAA0C;AAClD,WAAS,OAAO,KAAK,SAAS;AAC5B,WAAO,EAAE,KAAK,QAAQ;AAAA,EACxB;AACA,2CAAyC,SAAS;AAClD,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,EACzH;AACA,2CAAyC,KAAK;AAChD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAC5F,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,WAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC9C,WAAO,EAAE,KAAK,YAAY,SAAS,KAAK;AAAA,EAC1C;AACA,oBAAkB,SAAS;AAC3B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC1J;AACA,oBAAkB,KAAK;AACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,cAAY,YAAY;AACxB,cAAY,WAAW;AACvB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,cAAc,YAAY,aAAa,cAAc,YAAY;AAAA,EAC1E;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC9F;AACA,iBAAe,KAAK;AACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,sBAAoB,OAAO;AAC3B,sBAAoB,SAAS;AAC7B,sBAAoB,WAAW;AAC/B,sBAAoB,cAAc;AAClC,sBAAoB,QAAQ;AAC5B,sBAAoB,WAAW;AAC/B,sBAAoB,QAAQ;AAC5B,sBAAoB,YAAY;AAChC,sBAAoB,SAAS;AAC7B,sBAAoB,WAAW;AAC/B,sBAAoB,OAAO;AAC3B,sBAAoB,QAAQ;AAC5B,sBAAoB,OAAO;AAC3B,sBAAoB,UAAU;AAC9B,sBAAoB,UAAU;AAC9B,sBAAoB,QAAQ;AAC5B,sBAAoB,OAAO;AAC3B,sBAAoB,YAAY;AAChC,sBAAoB,SAAS;AAC7B,sBAAoB,aAAa;AACjC,sBAAoB,WAAW;AAC/B,sBAAoB,SAAS;AAC7B,sBAAoB,QAAQ;AAC5B,sBAAoB,WAAW;AAC/B,sBAAoB,gBAAgB;AACtC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,mBAAmB;AAC3B,oBAAkB,YAAY;AAC9B,oBAAkB,UAAU;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,qBAAmB,aAAa;AAClC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,SAAS,QAAQ,SAAS;AACxC,WAAO,EAAE,SAAS,QAAQ,QAAQ;AAAA,EACpC;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,MAAM,KAAK,MAAM,GAAG,UAAU,OAAO;AAAA,EAC9G;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,OAAO;AACvB,kBAAgB,oBAAoB;AACtC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,6BAA6B;AACrC,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,cAAc,GAAG,OAAO,UAAU,MAAM,KAAK,UAAU,WAAW,YAAY,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACrJ;AACA,8BAA4B,KAAK;AACnC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAClE,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO;AACrB,WAAO,EAAE,MAAM;AAAA,EACjB;AACA,kBAAgB,SAAS;AAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO,cAAc;AACnC,WAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,EACnE;AACA,kBAAgB,SAAS;AAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,cAAc,WAAW;AAChC,WAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,EAC1D;AACA,gBAAc,gBAAgB;AAC9B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,OAAO,SAAS,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC1H;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,QAAQ;AAChB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KAAK,aAAa,GAAG,UAAU,QAAQ,KAAK,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAU,MAAM,GAAG,MAAM,KAAK;AAAA,EAC7O;AACA,SAAO,KAAK;AACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,OAAO,eAAe;AACpC,WAAO,gBAAgB,EAAE,OAAO,cAAc,IAAI,EAAE,MAAM;AAAA,EAC5D;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,OAAO,kBAAkB,YAAY;AACnD,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC7B,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,GAAG,QAAQ,UAAU,GAAG;AAC1B,aAAO,aAAa;AAAA,IACtB,OAAO;AACL,aAAO,aAAa,CAAC;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,wBAAwB;AAChC,yBAAuB,OAAO;AAC9B,yBAAuB,OAAO;AAC9B,yBAAuB,QAAQ;AACjC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AACxD,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,OAAO,MAAM;AAC3B,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,OAAO,IAAI,GAAG;AACnB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,cAAY,OAAO;AACnB,cAAY,SAAS;AACrB,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,cAAY,WAAW;AACvB,cAAY,QAAQ;AACpB,cAAY,cAAc;AAC1B,cAAY,OAAO;AACnB,cAAY,YAAY;AACxB,cAAY,WAAW;AACvB,cAAY,WAAW;AACvB,cAAY,WAAW;AACvB,cAAY,SAAS;AACrB,cAAY,SAAS;AACrB,cAAY,UAAU;AACtB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,cAAY,MAAM;AAClB,cAAY,OAAO;AACnB,cAAY,aAAa;AACzB,cAAY,SAAS;AACrB,cAAY,QAAQ;AACpB,cAAY,WAAW;AACvB,cAAY,gBAAgB;AAC9B,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,YAAY;AACpB,aAAW,aAAa;AAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK,eAAe;AACrD,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA,UAAU,EAAE,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AACjB,aAAO,gBAAgB;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,kBAAkB;AAC1B,WAAS,OAAO,MAAM,MAAM,KAAK,OAAO;AACtC,WAAO,UAAU,SAAS,EAAE,MAAM,MAAM,UAAU,EAAE,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,MAAM,UAAU,EAAE,IAAI,EAAE;AAAA,EACvG;AACA,mBAAiB,SAAS;AAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACnE,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,aAAa,QAAQ;AACvB,aAAO,WAAW;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AACA,kBAAgB,SAAS;AACzB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,cAAc,MAAM,UAAU,WAAW,UAAU,GAAG,OAAO,UAAU,MAAM,OAAO,UAAU,eAAe,UAAU,GAAG,QAAQ,UAAU,UAAU,OAAO,UAAU,aAAa,UAAU,MAAM,QAAQ,UAAU,QAAQ,OAAO,UAAU,SAAS,UAAU,MAAM,QAAQ,UAAU,IAAI;AAAA,EAC9Z;AACA,kBAAgB,KAAK;AACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,QAAQ;AACxB,kBAAgB,WAAW;AAC3B,kBAAgB,WAAW;AAC3B,kBAAgB,kBAAkB;AAClC,kBAAgB,iBAAiB;AACjC,kBAAgB,kBAAkB;AAClC,kBAAgB,SAAS;AACzB,kBAAgB,wBAAwB;AACxC,kBAAgB,eAAe;AACjC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,wBAAwB;AAChC,yBAAuB,UAAU;AACjC,yBAAuB,YAAY;AACrC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AACxD,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,aAAa,MAAM,aAAa;AAC9C,QAAI,SAAS,EAAE,YAAY;AAC3B,QAAI,SAAS,UAAU,SAAS,MAAM;AACpC,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,gBAAgB,UAAU,gBAAgB,MAAM;AAClD,aAAO,cAAc;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAAM,UAAU,SAAS,UAAU,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM,OAAO,UAAU,gBAAgB,UAAU,UAAU,gBAAgB,sBAAsB,WAAW,UAAU,gBAAgB,sBAAsB;AAAA,EACpT;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,WAAS,OAAO,OAAO,qBAAqB,MAAM;AAChD,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB,UAAU;AAC3C,kBAAY;AACZ,aAAO,OAAO;AAAA,IAChB,WAAW,QAAQ,GAAG,mBAAmB,GAAG;AAC1C,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,aAAa,SAAS,QAAQ;AAChC,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,cAAY,SAAS;AACrB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MAAM,UAAU,gBAAgB,UAAU,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OAAO,UAAU,SAAS,UAAU,GAAG,OAAO,UAAU,IAAI,OAAO,UAAU,SAAS,UAAU,UAAU,YAAY,YAAY,UAAU,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,GAAG,QAAQ,UAAU,WAAW,OAAO,UAAU,SAAS,UAAU,cAAc,GAAG,UAAU,IAAI;AAAA,EACvd;AACA,cAAY,KAAK;AACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,WAAW;AACnB,WAAS,OAAO,OAAO,MAAM;AAC3B,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,YAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,EAC/H;AACA,YAAU,KAAK;AACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAS,oBAAoB;AAC5B,WAAS,OAAO,SAAS,cAAc;AACrC,WAAO,EAAE,SAAS,aAAa;AAAA,EACjC;AACA,qBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,EACrG;AACA,qBAAmB,KAAK;AAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,OAAO,QAAQ,MAAM;AACnC,WAAO,EAAE,OAAO,QAAQ,KAAK;AAAA,EAC/B;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,EAC5H;AACA,gBAAc,KAAK;AACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,OAAO,OAAO,QAAQ;AAC7B,WAAO,EAAE,OAAO,OAAO;AAAA,EACzB;AACA,kBAAgB,SAAS;AACzB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAU,gBAAgB,GAAG,UAAU,MAAM;AAAA,EACxI;AACA,kBAAgB,KAAK;AACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,sBAAoB,WAAW,IAAI;AACnC,sBAAoB,MAAM,IAAI;AAC9B,sBAAoB,OAAO,IAAI;AAC/B,sBAAoB,MAAM,IAAI;AAC9B,sBAAoB,WAAW,IAAI;AACnC,sBAAoB,QAAQ,IAAI;AAChC,sBAAoB,eAAe,IAAI;AACvC,sBAAoB,WAAW,IAAI;AACnC,sBAAoB,UAAU,IAAI;AAClC,sBAAoB,UAAU,IAAI;AAClC,sBAAoB,YAAY,IAAI;AACpC,sBAAoB,OAAO,IAAI;AAC/B,sBAAoB,UAAU,IAAI;AAClC,sBAAoB,QAAQ,IAAI;AAChC,sBAAoB,OAAO,IAAI;AAC/B,sBAAoB,SAAS,IAAI;AACjC,sBAAoB,UAAU,IAAI;AAClC,sBAAoB,SAAS,IAAI;AACjC,sBAAoB,QAAQ,IAAI;AAChC,sBAAoB,QAAQ,IAAI;AAChC,sBAAoB,QAAQ,IAAI;AAChC,sBAAoB,UAAU,IAAI;AAClC,sBAAoB,WAAW,IAAI;AACrC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,yBAAyB;AACjC,0BAAwB,aAAa,IAAI;AACzC,0BAAwB,YAAY,IAAI;AACxC,0BAAwB,UAAU,IAAI;AACtC,0BAAwB,QAAQ,IAAI;AACpC,0BAAwB,YAAY,IAAI;AACxC,0BAAwB,UAAU,IAAI;AACtC,0BAAwB,OAAO,IAAI;AACnC,0BAAwB,cAAc,IAAI;AAC1C,0BAAwB,eAAe,IAAI;AAC3C,0BAAwB,gBAAgB,IAAI;AAC9C,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAC1D,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,MAAM,UAAU,aAAa,UAAU,OAAO,UAAU,aAAa,aAAa,MAAM,QAAQ,UAAU,IAAI,MAAM,UAAU,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,CAAC,MAAM;AAAA,EACnN;AACA,kBAAgB,KAAK;AACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAS,kBAAkB;AAC1B,WAAS,OAAO,OAAO,MAAM;AAC3B,WAAO,EAAE,OAAO,KAAK;AAAA,EACvB;AACA,mBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC5G;AACA,mBAAiB,KAAK;AACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAS,4BAA4B;AACpC,WAAS,OAAO,OAAO,cAAc,qBAAqB;AACxD,WAAO,EAAE,OAAO,cAAc,oBAAoB;AAAA,EACpD;AACA,6BAA2B,SAAS;AACpC,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,QAAQ,UAAU,mBAAmB,MAAM,GAAG,OAAO,UAAU,YAAY,KAAK,UAAU,iBAAiB;AAAA,EAClM;AACA,6BAA2B,KAAK;AAClC,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAChE,IAAI;AAAA,CACH,SAAS,mCAAmC;AAC3C,WAAS,OAAO,OAAO,YAAY;AACjC,WAAO,EAAE,OAAO,WAAW;AAAA,EAC7B;AACA,oCAAkC,SAAS;AAC3C,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,UAAU,KAAK,UAAU,eAAe;AAAA,EACjJ;AACA,oCAAkC,KAAK;AACzC,GAAG,qCAAqC,mCAAmC,CAAC,EAAE;AAC9E,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,WAAS,OAAO,SAAS,iBAAiB;AACxC,WAAO,EAAE,SAAS,gBAAgB;AAAA,EACpC;AACA,sBAAoB,SAAS;AAC7B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,MAAM,eAAe;AAAA,EAChE;AACA,sBAAoB,KAAK;AAC3B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,gBAAgB;AACxB,iBAAe,OAAO;AACtB,iBAAe,YAAY;AAC3B,WAAS,GAAG,OAAO;AACjB,WAAO,UAAU,KAAK,UAAU;AAAA,EAClC;AACA,iBAAe,KAAK;AACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAS,qBAAqB;AAC7B,WAAS,OAAO,OAAO;AACrB,WAAO,EAAE,MAAM;AAAA,EACjB;AACA,sBAAoB,SAAS;AAC7B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,MAAM,UAAU,YAAY,UAAU,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OAAO,UAAU,aAAa,UAAU,SAAS,GAAG,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO;AAAA,EACpR;AACA,sBAAoB,KAAK;AAC3B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAS,YAAY;AACpB,WAAS,OAAO,UAAU,OAAO,MAAM;AACrC,UAAM,SAAS,EAAE,UAAU,MAAM;AACjC,QAAI,SAAS,QAAQ;AACnB,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,aAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,MAAM,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,WAAW,UAAU,OAAO,mBAAmB,EAAE,OAAO,UAAU,SAAS,UAAU,cAAc,GAAG,UAAU,IAAI,MAAM,UAAU,cAAc,UAAU,GAAG,WAAW,UAAU,WAAW,SAAS,EAAE,MAAM,UAAU,YAAY,UAAU,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,GAAG,QAAQ,UAAU,WAAW,OAAO,UAAU,iBAAiB,UAAU,GAAG,QAAQ,UAAU,YAAY;AAAA,EACvjB;AACA,aAAW,KAAK;AAClB,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAS,cAAc;AACtB,WAAS,cAAc,OAAO;AAC5B,WAAO,EAAE,MAAM,WAAW,MAAM;AAAA,EAClC;AACA,eAAa,gBAAgB;AAC/B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,YAAY,YAAY,OAAO,SAAS;AACtD,WAAO,EAAE,YAAY,YAAY,OAAO,QAAQ;AAAA,EAClD;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,uBAAuB;AAC/B,WAAS,OAAO,OAAO;AACrB,WAAO,EAAE,MAAM;AAAA,EACjB;AACA,wBAAsB,SAAS;AACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AACtD,IAAI;AAAA,CACH,SAAS,8BAA8B;AACtC,+BAA6B,UAAU;AACvC,+BAA6B,YAAY;AAC3C,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AACpE,IAAI;AAAA,CACH,SAAS,yBAAyB;AACjC,WAAS,OAAO,OAAO,MAAM;AAC3B,WAAO,EAAE,OAAO,KAAK;AAAA,EACvB;AACA,0BAAwB,SAAS;AACnC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAC1D,IAAI;AAAA,CACH,SAAS,0BAA0B;AAClC,WAAS,OAAO,aAAa,wBAAwB;AACnD,WAAO,EAAE,aAAa,uBAAuB;AAAA,EAC/C;AACA,2BAAyB,SAAS;AACpC,GAAG,4BAA4B,0BAA0B,CAAC,EAAE;AAC5D,IAAI;AAAA,CACH,SAAS,kBAAkB;AAC1B,WAAS,GAAG,OAAO;AACjB,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,IAAI,GAAG,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EACzF;AACA,mBAAiB,KAAK;AACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,WAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AACjD,WAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,EAC/D;AACA,gBAAc,SAAS;AACvB,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,EACjR;AACA,gBAAc,KAAK;AACnB,WAAS,WAAW,UAAU,OAAO;AACnC,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,cAAc,UAAU,OAAO,CAAC,GAAG,MAAM;AAC3C,UAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,UAAI,SAAS,GAAG;AACd,eAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,MACjD;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,qBAAqB,KAAK;AAC9B,aAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,UAAI,IAAI,YAAY,CAAC;AACrB,UAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,UAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,UAAI,aAAa,oBAAoB;AACnC,eAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,MAC3F,OAAO;AACL,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACpC;AACA,2BAAqB;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,gBAAc,aAAa;AAC3B,WAAS,UAAU,MAAM,SAAS;AAChC,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,UAAM,OAAO,KAAK,MAAM,GAAG,CAAC;AAC5B,UAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,cAAU,MAAM,OAAO;AACvB,cAAU,OAAO,OAAO;AACxB,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,IAAI;AACR,WAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACvD,UAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,UAAI,OAAO,GAAG;AACZ,aAAK,GAAG,IAAI,KAAK,SAAS;AAAA,MAC5B,OAAO;AACL,aAAK,GAAG,IAAI,MAAM,UAAU;AAAA,MAC9B;AAAA,IACF;AACA,WAAO,UAAU,KAAK,QAAQ;AAC5B,WAAK,GAAG,IAAI,KAAK,SAAS;AAAA,IAC5B;AACA,WAAO,WAAW,MAAM,QAAQ;AAC9B,WAAK,GAAG,IAAI,MAAM,UAAU;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI,mBAAmB,MAAM;AAAA,EAC3B,YAAY,KAAK,YAAY,SAAS,SAAS;AAC7C,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,OAAO;AACT,UAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,UAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,aAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,IAC3C;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAO,SAAS;AACrB,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,iBAAiB,QAAQ;AAChC,UAAI,cAAc,CAAC;AACnB,UAAI,OAAO,KAAK;AAChB,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,aAAa;AACf,sBAAY,KAAK,CAAC;AAClB,wBAAc;AAAA,QAChB;AACA,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,sBAAc,OAAO,QAAQ,OAAO;AACpC,YAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACrE;AAAA,QACF;AAAA,MACF;AACA,UAAI,eAAe,KAAK,SAAS,GAAG;AAClC,oBAAY,KAAK,KAAK,MAAM;AAAA,MAC9B;AACA,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,QAAQ;AACjB,aAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,MAAM,GAAG,OAAO,YAAY;AAChC,QAAI,SAAS,GAAG;AACd,aAAO,SAAS,OAAO,GAAG,MAAM;AAAA,IAClC;AACA,WAAO,MAAM,MAAM;AACjB,UAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,UAAI,YAAY,GAAG,IAAI,QAAQ;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,cAAM,MAAM;AAAA,MACd;AAAA,IACF;AACA,QAAI,OAAO,MAAM;AACjB,WAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,EACzD;AAAA,EACA,SAAS,UAAU;AACjB,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,SAAS,QAAQ,YAAY,QAAQ;AACvC,aAAO,KAAK,SAAS;AAAA,IACvB,WAAW,SAAS,OAAO,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,QAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,QAAI,iBAAiB,SAAS,OAAO,IAAI,YAAY,SAAS,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC7G,WAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,EACvF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,eAAe,EAAE;AAAA,EAC/B;AACF;AACA,IAAI;AAAA,CACH,SAAS,KAAK;AACb,QAAM,WAAW,OAAO,UAAU;AAClC,WAAS,QAAQ,OAAO;AACtB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,MAAI,UAAU;AACd,WAAS,WAAW,OAAO;AACzB,WAAO,OAAO,UAAU;AAAA,EAC1B;AACA,MAAI,YAAY;AAChB,WAAS,QAAQ,OAAO;AACtB,WAAO,UAAU,QAAQ,UAAU;AAAA,EACrC;AACA,MAAI,UAAU;AACd,WAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,SAAS;AACb,WAAS,YAAY,OAAO,KAAK,KAAK;AACpC,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,EAChF;AACA,MAAI,cAAc;AAClB,WAAS,SAAS,OAAO;AACvB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,EACxF;AACA,MAAI,UAAU;AACd,WAAS,UAAU,OAAO;AACxB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,EAC9E;AACA,MAAI,WAAW;AACf,WAAS,KAAK,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EAClC;AACA,MAAI,OAAO;AACX,WAAS,cAAc,OAAO;AAC5B,WAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,EAC5C;AACA,MAAI,gBAAgB;AACpB,WAAS,WAAW,OAAO,OAAO;AAChC,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EAClD;AACA,MAAI,aAAa;AACnB,GAAG,OAAO,KAAK,CAAC,EAAE;AAGlB,IAAI,qBAAqB,MAAM;AAAA,EAC7B,YAAY,aAAa,SAAS,mBAAmB;AACnD,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,eAAe,CAAC;AACrB,SAAK,YAA4B,uBAAO,OAAO,IAAI;AACnD,UAAM,aAAa,CAAC,UAAU;AAC5B,UAAI,SAAS,MAAM,cAAc;AACjC,UAAI,WAAW,KAAK,aAAa;AAC/B;AAAA,MACF;AACA,UAAI;AACJ,WAAK,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,mBAAmB,MAAM;AACpE,eAAO,aAAa,MAAM;AAC1B,iBAAS,OAAO,WAAW,MAAM,KAAK,YAAY,MAAM,KAAK,MAAM,GAAG,GAAG;AAAA,MAC3E,CAAC;AACD,WAAK,YAAY,MAAM,KAAK,MAAM;AAAA,IACpC;AACA,UAAM,iBAAiB,CAAC,UAAU;AAChC,iCAA2B,OAAO,gBAAgB,OAAO,KAAK,aAAa,CAAC,CAAC;AAC7E,UAAI,SAAS,MAAM,IAAI,SAAS;AAChC,UAAI,WAAW,KAAK,UAAU,MAAM;AACpC,UAAI,UAAU;AACZ,iBAAS,QAAQ;AACjB,eAAO,KAAK,UAAU,MAAM;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,aAAa,KAAK,2BAA2B,OAAO,iBAAiB,UAAU,CAAC;AACrF,SAAK,aAAa,KAAK,2BAA2B,OAAO,mBAAmB,cAAc,CAAC;AAC3F,SAAK,aAAa;AAAA,MAChB,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AACpE,uBAAe,MAAM,KAAK;AAC1B,mBAAW,MAAM,KAAK;AAAA,MACxB,CAAC;AAAA,IACH;AACA,SAAK,aAAa;AAAA,MAChB,kBAAkB,CAAC,MAAM;AACvB,mCAA2B,OAAO,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC/D,cAAI,MAAM,cAAc,MAAM,KAAK,aAAa;AAC9C,2BAAe,KAAK;AACpB,uBAAW,KAAK;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,SAAK,aAAa,KAAK;AAAA,MACrB,SAAS,MAAM;AACb,mCAA2B,OAAO,UAAU,EAAE,QAAQ,cAAc;AACpE,iBAAS,OAAO,KAAK,WAAW;AAC9B,eAAK,UAAU,GAAG,EAAE,QAAQ;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,CAAC;AACD,+BAA2B,OAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,EAClE;AAAA,EACA,UAAU;AACR,SAAK,aAAa,QAAQ,CAAC,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjD,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,SAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AACvC,aAAO,QAAQ,aAAa,SAAS,SAAS,CAAC;AAAA,IACjD,CAAC,EAAE,KAAK,CAAC,gBAAgB;AACvB,YAAM,UAAU,YAAY,IAAI,CAAC,MAAM,cAAc,UAAU,CAAC,CAAC;AACjE,UAAI,QAAQ,2BAA2B,OAAO,SAAS,QAAQ;AAC/D,UAAI,SAAS,MAAM,cAAc,MAAM,YAAY;AACjD,mCAA2B,OAAO,gBAAgB,OAAO,YAAY,OAAO;AAAA,MAC9E;AAAA,IACF,CAAC,EAAE,KAAK,QAAQ,CAAC,QAAQ;AACvB,cAAQ,MAAM,GAAG;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,YAAY;AAC9B,UAAQ,YAAY;AAAA,IAClB,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD;AACE,aAAO,2BAA2B,eAAe;AAAA,EACrD;AACF;AACA,SAAS,cAAc,UAAU,MAAM;AACrC,MAAI,OAAO,OAAO,KAAK,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO;AAAA,IACL,UAAU,WAAW,KAAK,QAAQ;AAAA,IAClC,iBAAiB,KAAK,MAAM,MAAM,OAAO;AAAA,IACzC,aAAa,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1C,eAAe,KAAK,MAAM,IAAI,OAAO;AAAA,IACrC,WAAW,KAAK,MAAM,IAAI,YAAY;AAAA,IACtC,SAAS,KAAK;AAAA,IACd;AAAA,IACA,QAAQ,KAAK;AAAA,EACf;AACF;AACA,IAAI,oBAAoB,MAAM;AAAA,EAC5B,YAAY,SAAS,oBAAoB;AACvC,SAAK,UAAU;AACf,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,uBAAuB,OAAO,UAAU,SAAS,OAAO;AACtD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,WAAW,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IACvE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,YAAM,WAAW,MAAM,qBAAqB,QAAQ;AACpD,YAAM,YAAY,IAAI,2BAA2B;AAAA,QAC/C,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AACA,YAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,UAAU;AACtC,cAAM,OAAO;AAAA,UACX,OAAO,MAAM;AAAA,UACb,YAAY,MAAM,cAAc,MAAM;AAAA,UACtC,UAAU,MAAM;AAAA,UAChB,YAAY,MAAM;AAAA,UAClB,eAAe,MAAM;AAAA,UACrB,QAAQ,MAAM;AAAA,UACd,SAAS,UAAU,MAAM,OAAO;AAAA,UAChC,OAAO;AAAA,UACP,MAAM,qBAAqB,MAAM,IAAI;AAAA,QACvC;AACA,YAAI,MAAM,UAAU;AAClB,cAAI,oBAAoB,MAAM,QAAQ,GAAG;AACvC,iBAAK,QAAQ;AAAA,cACX,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAAA,cACrC,SAAS,QAAQ,MAAM,SAAS,OAAO;AAAA,YACzC;AAAA,UACF,OAAO;AACL,iBAAK,QAAQ,QAAQ,MAAM,SAAS,KAAK;AAAA,UAC3C;AACA,eAAK,aAAa,MAAM,SAAS;AAAA,QACnC;AACA,YAAI,MAAM,qBAAqB;AAC7B,eAAK,sBAAsB,MAAM,oBAAoB,IAAI,UAAU;AAAA,QACrE;AACA,YAAI,MAAM,qBAAqB,iBAAiB,SAAS;AACvD,eAAK,kBAAkB,2BAA2B,UAAU,6BAA6B;AAAA,QAC3F;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,QACL,cAAc,KAAK;AAAA,QACnB,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAE;AACzE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM,MAAM,kBAAkB;AAAA,MAC9B,WAAW,MAAM,cAAc;AAAA,IACjC;AAAA,IACA,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAE;AAAA,EACvE;AACF;AACA,SAAS,QAAQ,OAAO;AACtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,IAAI,2BAA2B;AAAA,IACpC,MAAM,MAAM,OAAO;AAAA,IACnB,MAAM,MAAM,YAAY;AAAA,IACxB,MAAM,IAAI,OAAO;AAAA,IACjB,MAAM,IAAI,YAAY;AAAA,EACxB;AACF;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,OAAO,KAAK,WAAW,eAAe,OAAO,KAAK,YAAY;AACvE;AACA,SAAS,qBAAqB,MAAM;AAClC,QAAM,YAAY,2BAA2B,UAAU;AACvD,UAAQ,MAAM;AAAA,IACZ,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,EACrB;AACA,SAAO,UAAU;AACnB;AACA,SAAS,WAAW,UAAU;AAC5B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B,MAAM,SAAS;AAAA,EACjB;AACF;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK,EAAE,YAAY,iCAAiC,EAAE,IAAI,EAAE,SAAS,OAAO,EAAE,OAAO,WAAW,EAAE,UAAU,IAAI;AACzH;AACA,IAAI,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,OAAO,UAAU,OAAO;AACnC,QAAI,WAAW,MAAM;AACrB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,QAAQ,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IACpE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,QAAQ,KAAK,KAAK;AAAA,QACzB,UAAU,oBAAoB,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AACrE;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,gBAAgB,KAAK,GAAG;AAC1B,QAAI,MAAM,SAAS,aAAa;AAC9B,aAAO;AAAA,QACL,OAAO,MAAM,MAAM,QAAQ,yBAAyB,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO,EAAE,OAAO,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AAC1E;AACA,SAAS,oBAAoB,UAAU;AACrC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,SAAS,IAAI,gBAAgB;AAAA,EACtC;AACA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACpC;AACA,IAAI,2BAA2B,MAAM;AAAA,EACnC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,0BAA0B,OAAO,UAAU,OAAO;AAChD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,uBAAuB,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY;AAC7I,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,eAAO;AAAA,UACL,OAAO,QAAQ,MAAM,KAAK;AAAA,UAC1B,MAAM,wBAAwB,MAAM,IAAI;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,wBAAwB,MAAM;AACrC,UAAQ,MAAM;AAAA,IACZ,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,EACtE;AACA,SAAO,2BAA2B,UAAU,sBAAsB;AACpE;AACA,IAAI,oBAAoB,MAAM;AAAA,EAC5B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,OAAO,UAAU,OAAO;AACxC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IAC3E,CAAC,EAAE,KAAK,CAAC,eAAe;AACtB,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,aAAO,CAAC,WAAW,UAAU,CAAC;AAAA,IAChC,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,UAAU;AAC5B,SAAO;AAAA,IACL,KAAK,2BAA2B,IAAI,MAAM,SAAS,GAAG;AAAA,IACtD,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC/B;AACF;AACA,IAAI,mBAAmB,MAAM;AAAA,EAC3B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,OAAO,UAAU,SAAS,OAAO;AACjD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,IAC3E,CAAC,EAAE,KAAK,CAAC,YAAY;AACnB,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,UAAU;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,IAAI,gBAAgB,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,mBAAmB,OAAO,UAAU,SAAS,OAAO;AAClD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,SAAS,SAAS,SAAS,GAAG,aAAa,QAAQ,GAAG,OAAO;AAAA,IAC9E,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,aAAO,gBAAgB,IAAI;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,CAAC;AACrB,WAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,OAAO,2BAA2B,IAAI,MAAM,GAAG;AACrD,aAAS,KAAK,KAAK,QAAQ,GAAG,GAAG;AAC/B,oBAAc,KAAK;AAAA,QACjB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR,OAAO,QAAQ,EAAE,KAAK;AAAA,UACtB,MAAM,EAAE;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,wBAAwB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,uBAAuB,OAAO,OAAO;AACnC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,oBAAoB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAChH,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO,MAAM,IAAI,CAAC,SAAS;AACzB,YAAI,iBAAiB,IAAI,GAAG;AAC1B,iBAAO,iBAAiB,IAAI;AAAA,QAC9B;AACA,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,eAAe,KAAK;AAAA,UACpB,MAAM,aAAa,KAAK,IAAI;AAAA,UAC5B,OAAO,QAAQ,KAAK,SAAS,KAAK;AAAA,UAClC,gBAAgB,QAAQ,KAAK,SAAS,KAAK;AAAA,UAC3C,MAAM,CAAC;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,cAAc;AACvB;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO;AAAA,IACL,MAAM,OAAO;AAAA,IACb,QAAQ,OAAO,UAAU;AAAA,IACzB,MAAM,aAAa,OAAO,IAAI;AAAA,IAC9B,OAAO,QAAQ,OAAO,KAAK;AAAA,IAC3B,gBAAgB,QAAQ,OAAO,cAAc;AAAA,IAC7C,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtB,WAAW,OAAO,YAAY,CAAC,GAAG,IAAI,CAAC,SAAS,iBAAiB,IAAI,CAAC;AAAA,EACxE;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,QAAQ,2BAA2B,UAAU;AACjD,UAAQ,MAAM;AAAA,IACZ,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,EACjB;AACA,SAAO,MAAM;AACf;AACA,IAAI,sBAAsB,MAAM;AAAA,EAC9B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa,OAAO,OAAO;AACzB,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,kBAAkB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9G,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,MAAM,IAAI,CAAC,UAAU;AAAA,UAC1B,OAAO,QAAQ,KAAK,KAAK;AAAA,UACzB,KAAK,KAAK;AAAA,QACZ,EAAE;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAI,iCAAiC,MAAM;AAAA,EACzC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,+BAA+B,OAAO,SAAS,OAAO;AACpD,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/F,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,sCAAsC,MAAM;AAAA,EAC9C,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,oCAAoC,OAAO,OAAO,SAAS,OAAO;AAChE,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,aAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,GAAG,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3G,YAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,QACF;AACA,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,cAAc,QAAQ;AAAA,EACxB;AACF;AACA,IAAI,uBAAuB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,sBAAsB,OAAO,OAAO;AAClC,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,mBAAmB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/G,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,aAAO,MAAM,IAAI,CAAC,UAAU;AAAA,QAC1B,OAAO,KAAK;AAAA,QACZ,OAAO,QAAQ,KAAK,KAAK;AAAA,MAC3B,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B,OAAO,MAAM,OAAO;AAC5C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,MAC5B,CAAC,YAAY,QAAQ,sBAAsB,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,IACnG,EAAE,KAAK,CAAC,kBAAkB;AACxB,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AACA,aAAO,cAAc,IAAI,CAAC,iBAAiB;AACzC,YAAI,OAAO;AAAA,UACT,OAAO,aAAa;AAAA,QACtB;AACA,YAAI,aAAa,UAAU;AACzB,eAAK,WAAW,WAAW,aAAa,QAAQ;AAAA,QAClD;AACA,YAAI,aAAa,qBAAqB;AACpC,eAAK,sBAAsB,aAAa,oBAAoB,IAAI,UAAU;AAAA,QAC5E;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAI,sBAAsB,MAAM;AAAA,EAC9B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,qBAAqB,OAAO,SAAS,OAAO;AAC1C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,iBAAiB,SAAS,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,WAAW;AACvH,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,aAAO,OAAO,IAAI,CAAC,UAAU;AAC3B,cAAM,SAAS;AAAA,UACb,OAAO,MAAM,YAAY;AAAA,UACzB,KAAK,MAAM,UAAU;AAAA,QACvB;AACA,YAAI,OAAO,MAAM,SAAS,aAAa;AACrC,iBAAO,OAAO,mBAAmB,MAAM,IAAI;AAAA,QAC7C;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACZ,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,EACjE;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,uBAAuB,OAAO,WAAW,OAAO;AAC9C,UAAM,WAAW,MAAM;AACvB,WAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,MAC5B,CAAC,YAAY,QAAQ;AAAA,QACnB,SAAS,SAAS;AAAA,QAClB,UAAU,IAAI,YAAY;AAAA,MAC5B;AAAA,IACF,EAAE,KAAK,CAAC,oBAAoB;AAC1B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,aAAO,gBAAgB,IAAI,CAAC,mBAAmB;AAC7C,cAAM,SAAS,CAAC;AAChB,eAAO,gBAAgB;AACrB,iBAAO,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,EAAE,CAAC;AACpD,2BAAiB,eAAe;AAAA,QAClC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,SAAS,cAAc,MAAM,eAAe,OAAO;AACjD,QAAM,MAAM,KAAK;AACjB,MAAI,MAAM,GAAG,QAAQ,IAAI,cAAc,GAAG,QAAQ,IAAI,aAAa,GAAG,kBAAkB,GAAG,uBAAuB,GAAG,2BAA2B,GAAG,YAAY;AAC/J,WAAS,cAAc,OAAO,OAAO;AACnC,QAAI,SAAS;AACb,QAAI,SAAS;AACb,WAAO,SAAS,SAAS,CAAC,OAAO;AAC/B,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM,MAAM,MAAM,IAAI;AACxB,iBAAS,SAAS,KAAK,KAAK;AAAA,MAC9B,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,WAAW,MAAM,MAAM,MAAM,KAAK;AAChC,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA;AACA;AAAA,IACF;AACA,QAAI,SAAS,OAAO;AAClB,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAY,aAAa;AAChC,UAAM;AACN,YAAQ;AACR,kBAAc;AACd,YAAQ;AACR,gBAAY;AAAA,EACd;AACA,WAAS,aAAa;AACpB,QAAI,QAAQ;AACZ,QAAI,KAAK,WAAW,GAAG,MAAM,IAAI;AAC/B;AAAA,IACF,OAAO;AACL;AACA,aAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,IAAI;AACpD;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AAAA,MACF,OAAO;AACL,oBAAY;AACZ,eAAO,KAAK,UAAU,OAAO,GAAG;AAAA,MAClC;AAAA,IACF;AACA,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM;AACtF;AACA,UAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AACnF;AAAA,MACF;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AACA,cAAM;AAAA,MACR,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,KAAK,UAAU,OAAO,GAAG;AAAA,EAClC;AACA,WAAS,aAAa;AACpB,QAAI,SAAS,IAAI,QAAQ;AACzB,WAAO,MAAM;AACX,UAAI,OAAO,KAAK;AACd,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,oBAAY;AACZ;AAAA,MACF;AACA,YAAM,KAAK,KAAK,WAAW,GAAG;AAC9B,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA,YAAI,OAAO,KAAK;AACd,sBAAY;AACZ;AAAA,QACF;AACA,cAAM,MAAM,KAAK,WAAW,KAAK;AACjC,gBAAQ,KAAK;AAAA,UACX,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,kBAAM,MAAM,cAAc,GAAG,IAAI;AACjC,gBAAI,OAAO,GAAG;AACZ,wBAAU,OAAO,aAAa,GAAG;AAAA,YACnC,OAAO;AACL,0BAAY;AAAA,YACd;AACA;AAAA,UACF;AACE,wBAAY;AAAA,QAChB;AACA,gBAAQ;AACR;AAAA,MACF;AACA,UAAI,MAAM,KAAK,MAAM,IAAI;AACvB,YAAI,YAAY,EAAE,GAAG;AACnB,oBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,sBAAY;AACZ;AAAA,QACF,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,WAAW;AAClB,YAAQ;AACR,gBAAY;AACZ,kBAAc;AACd,sBAAkB;AAClB,+BAA2B;AAC3B,QAAI,OAAO,KAAK;AACd,oBAAc;AACd,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,OAAO,KAAK,WAAW,GAAG;AAC9B,QAAI,aAAa,IAAI,GAAG;AACtB,SAAG;AACD;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC5B,SAAS,aAAa,IAAI;AAC1B,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,YAAY,IAAI,GAAG;AACrB;AACA,eAAS,OAAO,aAAa,IAAI;AACjC,UAAI,SAAS,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC9C;AACA,iBAAS;AAAA,MACX;AACA;AACA,6BAAuB;AACvB,aAAO,QAAQ;AAAA,IACjB;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,gBAAQ,WAAW;AACnB,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,cAAM,QAAQ,MAAM;AACpB,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,iBAAO,MAAM,KAAK;AAChB,gBAAI,YAAY,KAAK,WAAW,GAAG,CAAC,GAAG;AACrC;AAAA,YACF;AACA;AAAA,UACF;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,gBAAM,aAAa,MAAM;AACzB,cAAI,gBAAgB;AACpB,iBAAO,MAAM,YAAY;AACvB,kBAAM,KAAK,KAAK,WAAW,GAAG;AAC9B,gBAAI,OAAO,MAAM,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AAChD,qBAAO;AACP,8BAAgB;AAChB;AAAA,YACF;AACA;AACA,gBAAI,YAAY,EAAE,GAAG;AACnB,kBAAI,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC5C;AAAA,cACF;AACA;AACA,qCAAuB;AAAA,YACzB;AAAA,UACF;AACA,cAAI,CAAC,eAAe;AAClB;AACA,wBAAY;AAAA,UACd;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,YAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACjD,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,WAAW;AACpB,eAAO,QAAQ;AAAA,MACjB;AACE,eAAO,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACnD;AACA,iBAAO,KAAK,WAAW,GAAG;AAAA,QAC5B;AACA,YAAI,gBAAgB,KAAK;AACvB,kBAAQ,KAAK,UAAU,aAAa,GAAG;AACvC,kBAAQ,OAAO;AAAA,YACb,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,UACnB;AACA,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,WAAS,0BAA0B,MAAM;AACvC,QAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,oBAAoB;AAC3B,QAAI;AACJ,OAAG;AACD,eAAS,SAAS;AAAA,IACpB,SAAS,UAAU,MAAM,UAAU;AACnC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,MAAM,eAAe,oBAAoB;AAAA,IACzC,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,IACrB,gBAAgB,MAAM;AAAA,IACtB,gBAAgB,MAAM,MAAM;AAAA,IAC5B,mBAAmB,MAAM;AAAA,IACzB,wBAAwB,MAAM,cAAc;AAAA,IAC5C,eAAe,MAAM;AAAA,EACvB;AACF;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,MAAM,MAAM;AAC3B;AACA,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,WAAW,IAAI,EAAE,IAAI;AACrD,kBAAgB,gBAAgB,YAAY,IAAI,GAAG,IAAI;AACvD,kBAAgB,gBAAgB,cAAc,IAAI,EAAE,IAAI;AACxD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,KAAK,IAAI,EAAE,IAAI;AAC/C,kBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,WAAW,IAAI,GAAG,IAAI;AACtD,kBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,kBAAgB,gBAAgB,MAAM,IAAI,EAAE,IAAI;AAChD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,KAAK,IAAI,CAAC,IAAI;AAChD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAG1C,IAAI,eAAe,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,SAAO,IAAI,OAAO,KAAK;AACzB,CAAC;AACD,IAAI,kBAAkB;AACtB,IAAI,6BAA6B;AAAA,EAC/B,KAAK;AAAA,IACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC3D,aAAO,SAAS,IAAI,OAAO,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,KAAK;AAAA,IACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC3D,aAAO,SAAS,IAAI,OAAO,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAGA,IAAI;AAAA,CACH,SAAS,eAAe;AACvB,gBAAc,UAAU;AAAA,IACtB,oBAAoB;AAAA,EACtB;AACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAGtC,IAAI,iBAAiB;AACrB,IAAI;AAAA,CACH,SAAS,YAAY;AACpB,aAAW,WAAW,MAAM,IAAI,CAAC,IAAI;AACrC,aAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,aAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,aAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,aAAW,WAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,aAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,aAAW,WAAW,kBAAkB,IAAI,CAAC,IAAI;AACnD,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAS,aAAa;AACrB,cAAY,YAAY,gBAAgB,IAAI,CAAC,IAAI;AACjD,cAAY,YAAY,iBAAiB,IAAI,CAAC,IAAI;AAClD,cAAY,YAAY,kBAAkB,IAAI,CAAC,IAAI;AACnD,cAAY,YAAY,mBAAmB,IAAI,CAAC,IAAI;AACpD,cAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,cAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,cAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,cAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,cAAY,YAAY,cAAc,IAAI,CAAC,IAAI;AAC/C,cAAY,YAAY,eAAe,IAAI,EAAE,IAAI;AACjD,cAAY,YAAY,gBAAgB,IAAI,EAAE,IAAI;AAClD,cAAY,YAAY,mBAAmB,IAAI,EAAE,IAAI;AACrD,cAAY,YAAY,oBAAoB,IAAI,EAAE,IAAI;AACtD,cAAY,YAAY,iBAAiB,IAAI,EAAE,IAAI;AACnD,cAAY,YAAY,QAAQ,IAAI,EAAE,IAAI;AAC1C,cAAY,YAAY,SAAS,IAAI,EAAE,IAAI;AAC3C,cAAY,YAAY,KAAK,IAAI,EAAE,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAI;AAAA,CACH,SAAS,iBAAiB;AACzB,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,qBAAqB,IAAI,CAAC,IAAI;AAC9D,kBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,oBAAoB,IAAI,CAAC,IAAI;AAC7D,kBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,kBAAgB,gBAAgB,mBAAmB,IAAI,CAAC,IAAI;AAC5D,kBAAgB,gBAAgB,qBAAqB,IAAI,EAAE,IAAI;AAC/D,kBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,kBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,kBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,kBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,kBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,kBAAgB,gBAAgB,kBAAkB,IAAI,EAAE,IAAI;AAC9D,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAG1C,SAAS,0BAA0B,iBAAiB;AAClD,SAAO;AAAA,IACL,iBAAiB,MAAM,IAAI,UAAU,MAAM,MAAM,OAAO,IAAI;AAAA,IAC5D,UAAU,CAAC,MAAM,UAAU,SAAS,iBAAiB,MAAM,KAAK;AAAA,EAClE;AACF;AACA,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AACzB,IAAI,eAAe,MAAM,cAAc;AAAA,EACrC,YAAY,QAAQ,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,IAAI,SAAS;AAClB,QAAI,SAAS;AACX,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,SAAS,MAAM;AACzB,WAAO,IAAI,cAAc,SAAS,IAAI;AAAA,EACxC;AAAA,EACA,OAAO,OAAO,GAAG,GAAG;AAClB,QAAI,CAAC,KAAK,CAAC,GAAG;AACZ,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,CAAC,GAAG;AACZ,aAAO;AAAA,IACT;AACA,WAAO,KAAK,GAAG;AACb,UAAI,MAAM,GAAG;AACX,eAAO;AAAA,MACT;AACA,UAAI,EAAE,SAAS,EAAE,MAAM;AACrB,eAAO;AAAA,MACT;AACA,UAAI,EAAE;AACN,UAAI,EAAE;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,YAAY,MAAM,WAAW;AAAA,EAC/B,YAAY,OAAO,WAAW,cAAc,SAAS;AACnD,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO;AAAA,EACpF;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,EAAE,iBAAiB,aAAa;AAC5C,aAAO;AAAA,IACT;AACA,WAAO,KAAK,cAAc,MAAM,aAAa,KAAK,iBAAiB,MAAM,gBAAgB,aAAa,OAAO,KAAK,SAAS,MAAM,OAAO;AAAA,EAC1I;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,SAAS,UAAU,MAAM,OAAO,cAAc,GAAG;AACxD,MAAI,6BAA6B;AACjC,MAAI,eAAe;AACnB,UAAQ,MAAM,WAAW;AAAA,IACvB,KAAK;AACH,aAAO,MAAM;AACb,mCAA6B;AAC7B;AAAA,IACF,KAAK;AACH,aAAO,OAAO;AACd,mCAA6B;AAC7B;AAAA,EACJ;AACA,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,eAAe,MAAM;AACzB,MAAI,UAAU,MAAM;AACpB,QAAM,MAAM;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,UAAU,MAAM,MAAM;AAAA,EACxB;AACA,SAAO,MAAM;AACX,QAAI,SAAS,cAAc,QAAQ,YAAY;AAC/C,QAAI,OAAO;AACX,UAAM,OAAO,QAAQ,KAAK;AAC1B,QAAI,SAAS,IAAc;AACzB;AAAA,IACF;AACA,QAAI,WAAW,cAAc,QAAQ,YAAY,GAAG;AAClD,YAAM,IAAI;AAAA,QACR,qDAAqD,KAAK,OAAO,QAAQ,YAAY,GAAG,CAAC;AAAA,MAC3F;AAAA,IACF;AACA,QAAI,cAAc;AAChB,gBAAU;AAAA,IACZ;AACA,mBAAe,6BAA6B;AAC5C,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAc;AACnD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAa;AAClD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,cAAM,gBAAgB,UAAU,QAAQ,OAAO;AAC/C,cAAM,UAAU,kBAAkB;AAClC,eAAO,gBAAgB,UAAU,qBAAqB;AACtD,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,IACJ;AACA,QAAI,UAAU;AACZ,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,MACJ;AAAA,IACF;AACA,QAAI,WAAW,IAAI;AAAA,MACjB,MAAM,aAAa;AAAA,MACnB,QAAQ,cAAc;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAGA,IAAI;AACJ,SAAS,YAAY;AACnB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,QAAQ;AACX,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,YAAQ,MAAM;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB,cAAc,mBAAmB;AAAA,EAC5D,YAAY,YAAY,SAAS,UAAU;AACzC,UAAM,YAAY,SAAS,SAAS,WAAW;AAC/C,SAAK,aAAa;AAAA,MAChB,2BAA2B,OAAO,mBAAmB,CAAC,UAAU;AAC9D,aAAK,aAAa,MAAM,GAAG;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,SAAK,aAAa;AAAA,MAChB,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AACpE,aAAK,aAAa,MAAM,MAAM,GAAG;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC/B,cAAQ,YAAY,SAAS,SAAS,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AACF;AACA,SAAS,UAAU,UAAU;AAC3B,QAAM,cAAc,CAAC;AACrB,QAAM,YAAY,CAAC;AACnB,QAAM,SAAS,IAAI,cAAc,QAAQ;AACzC,cAAY,KAAK,MAAM;AACvB,WAAS,IAAI,SAAS;AACpB,WAAO,OAAO,yBAAyB,GAAG,IAAI;AAAA,EAChD;AACA,WAAS,oBAAoB;AAC3B,UAAM,EAAE,YAAY,mBAAmB,mBAAmB,IAAI;AAC9D,eAAW,SAAS;AACpB,QAAI,mBAAmB,yBAAyB;AAC9C,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,+BAA+B,MAAM;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,8BAA8B;AACnD,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,oCAAoC,MAAM;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,kBAAkB,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU;AAAA,QACR,2BAA2B,UAAU,sBAAsB,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,MACjG;AAAA,IACF;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,sBAAsB,MAAM;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU,KAAK,2BAA2B,UAAU,kBAAkB,YAAY,0BAA0B,IAAI,CAAC,CAAC;AAAA,IACpH;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,qBAAqB,MAAM;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,eAAe;AACpC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,oBAAoB,MAAM;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,aAAa;AAClC,gBAAU,KAAK,IAAI,uBAAuB,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACzE;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,sBAAsB,MAAM;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,oBAAkB;AAClB,cAAY,KAAK,2BAA2B,UAAU,yBAAyB,SAAS,YAAY,qBAAqB,CAAC;AAC1H,MAAI,oBAAoB,SAAS;AACjC,WAAS,YAAY,CAAC,gBAAgB;AACpC,QAAI,YAAY,sBAAsB,mBAAmB;AACvD,0BAAoB,YAAY;AAChC,wBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO,aAAa,WAAW;AACjC;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,EAAE,SAAS,MAAM,WAAW,WAAW,EAAE;AAClD;AACA,SAAS,WAAW,aAAa;AAC/B,SAAO,YAAY,QAAQ;AACzB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC5B;AACF;AACA,IAAI,wBAAwB;AAAA,EAC1B,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AACF;", "names": []}