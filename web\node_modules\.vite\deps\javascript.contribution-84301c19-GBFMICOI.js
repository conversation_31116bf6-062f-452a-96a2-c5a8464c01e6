import {
  h
} from "./chunk-GBNBLBMS.js";
import "./chunk-R5IZP54R.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/javascript.contribution-84301c19.mjs
h({
  id: "javascript",
  extensions: [".js", ".es6", ".jsx", ".mjs", ".cjs"],
  firstLine: "^#!.*\\bnode",
  filenames: ["jakefile"],
  aliases: ["JavaScript", "javascript", "js"],
  mimetypes: ["text/javascript"],
  loader: () => import("./javascript-92a77055-2UQ2CA3S.js")
});
/*! Bundled license information:

@fast-crud/fast-extends/dist/javascript.contribution-84301c19.mjs:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=javascript.contribution-84301c19-GBFMICOI.js.map
