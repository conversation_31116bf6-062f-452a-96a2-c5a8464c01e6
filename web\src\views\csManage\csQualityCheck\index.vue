<template>
  <div class="cs-quality-check-new">
    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-row">
          <div class="filter-item">
            <label>统计周期：</label>
            <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :shortcuts="dateShortcuts"
              @change="onDateRangeChange" style="width: 300px;" />
          </div>
          <div class="filter-item">
            <label>游戏筛选：</label>
            <el-select v-model="selectedGame" placeholder="请选择游戏" clearable filterable @change="onGameChange"
              style="width: 200px;">
              <el-option v-for="game in gameList" :key="game.id" :label="game.name" :value="game.id" />
            </el-select>
          </div>
          <div class="filter-item">
            <el-button type="primary" @click="refreshDashboard">刷新看板</el-button>
            <el-button @click="resetFilters">重置筛选</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 看板统计区域 -->
    <div class="dashboard-section">
      <el-row :gutter="20">
        <!-- 总体统计 -->
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ dashboardData.total_stats?.total_sessions || 0 }}</div>
              <div class="stat-label">总会话数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ (dashboardData.total_stats?.avg_score || 0).toFixed(1) }}</div>
              <div class="stat-label">平均分数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ getDeductionSessionsCount() }}</div>
              <div class="stat-label">有扣分会话</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ dashboardData.total_stats?.total_deductions || 0 }}</div>
              <div class="stat-label">总扣分</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 扣分项统计图表 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>扣分项统计</span>
                <el-button type="text" @click="refreshDeductionStats">刷新</el-button>
              </div>
            </template>
            <div ref="deductionChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <!-- 客服表现排行 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>客服表现排行</span>
                <el-button type="text" @click="refreshServiceRanking">刷新</el-button>
              </div>
            </template>
            <div ref="serviceChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>


    </div>

    <!-- 表格区域 -->
    <div class="table-section h-[100vh]">
      <el-card class="h-full w-full">
        <template #header>
          <div class="card-header">
            <span>质检记录列表</span>
            <div class="header-actions">
              <el-button v-if="currentServiceFilter" type="warning" size="small" @click="clearServiceFilter">
                清除客服筛选: {{ currentServiceFilter.name }}
              </el-button>
              <el-button v-if="currentDeductionFilter" type="danger" size="small" @click="clearDeductionFilter">
                清除扣分项筛选: {{ currentDeductionFilter }}
              </el-button>
            </div>
          </div>
        </template>

        <div class="relative">
          <fs-page>
            <fs-crud ref="crudRef" v-bind="crudBinding"> </fs-crud>
          </fs-page>
        </div>
      </el-card>
    </div>

    <!-- 质检详情弹窗 -->
    <RecordDetailDialog v-model="detailDialogVisible" :record="currentRecord" @deduction-filter="onDeductionFilter" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import RecordDetailDialog from './components/RecordDetailDialog.vue';
import * as api from './api';

// 响应式数据
const dateRange = ref<[string, string]>([]);
const selectedGame = ref<string>('');
const gameList = ref<any[]>([]);
const dashboardData = ref<any>({});
const detailDialogVisible = ref(false);
const currentRecord = ref<any>(null);
const currentServiceFilter = ref<any>(null);
const currentDeductionFilter = ref<string>('');

// 图表实例
const deductionChart = ref<HTMLElement>();
const serviceChart = ref<HTMLElement>();
let deductionChartInstance: echarts.ECharts | null = null;
let serviceChartInstance: echarts.ECharts | null = null;

// CRUD相关
const { crudBinding, crudRef, crudExpose, context } = useFs({ createCrudOptions });

// 计算有扣分的会话数
const getDeductionSessionsCount = () => {
  const totalStats = dashboardData.value.total_stats;
  if (!totalStats) return 0;
  
  // 直接使用后端计算的有扣分会话数
  return totalStats.sessions_with_deductions || 0;
};

// 日期快捷选项
const dateShortcuts = [
  {
    text: '昨日',
    value: () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const dateStr = yesterday.toISOString().split('T')[0];
      return [dateStr, dateStr];
    },
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
    },
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 30);
      return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
    },
  },
  {
    text: '最近3个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
    },
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 6);
      return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
    },
  },
];

// 初始化
onMounted(async () => {
  // 设置默认时间为昨日
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const dateStr = yesterday.toISOString().split('T')[0];
  dateRange.value = [dateStr, dateStr];

  // 加载游戏列表
  await loadGameList();
  
  // 加载看板数据
  await loadDashboardData();
  
  // 初始化图表
  await nextTick();
  initCharts();
  
  // 初始化质检记录列表数据
  crudExpose.doRefresh();
});

// 加载游戏列表
const loadGameList = async () => {
  try {
    const response = await api.getGameList();
    gameList.value = response.data || [];
  } catch (error) {
    ElMessage.error('加载游戏列表失败');
  }
};

// 查看详情处理函数
const onViewDetail = (record: any) => {
  currentRecord.value = record;
  detailDialogVisible.value = true;
};

// 客服筛选处理函数
const onServiceFilter = (serviceId: string, serviceName: string) => {
  currentServiceFilter.value = { id: serviceId, name: serviceName };
  // 更新表格筛选条件
  const searchForm = crudExpose.getSearchFormData() || {};
  searchForm.service_id = serviceId;
  crudExpose.setSearchFormData({ form: searchForm, mergeForm: true });
  crudExpose.doRefresh();
};

// 扣分项筛选处理函数
const onDeductionFilter = (deductionItem: string) => {
  currentDeductionFilter.value = deductionItem;
  // 更新表格筛选条件
  const searchForm = crudExpose.getSearchFormData() || {};
  searchForm.deduction_item = deductionItem;
  crudExpose.setSearchFormData({ form: searchForm });
  crudExpose.doRefresh();
};

// 将函数暴露给全局上下文，使crud.tsx中可以访问
context.onViewDetail = onViewDetail;
context.onServiceFilter = onServiceFilter;
context.onDeductionFilter = onDeductionFilter;

// 加载看板数据
const loadDashboardData = async () => {
  try {
    const params: any = {};
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0];
      params.end_date = dateRange.value[1];
    }
    if (selectedGame.value) {
      params.game = selectedGame.value;
    }
    
    const response = await api.getDashboardData(params);
    dashboardData.value = response.data || {};
  } catch (error) {
    ElMessage.error('加载看板数据失败');
  }
};

// 初始化图表
const initCharts = async () => {
  if (deductionChart.value) {
    deductionChartInstance = echarts.init(deductionChart.value);
    await loadDeductionStats();
  }
  
  if (serviceChart.value) {
    serviceChartInstance = echarts.init(serviceChart.value);
    await loadServiceRanking();
  }
};

// 加载扣分项统计
const loadDeductionStats = async () => {
  try {
    // 直接使用dashboard数据中的扣分项统计
    const deductionStats = dashboardData.value.deduction_stats || [];
    
    const option = {
      title: {
        text: '扣分项分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        itemGap: 8,
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          fontSize: 12
        },
        formatter: function(name) {
          // 查找对应的数据项
          const item = deductionStats.find((item: any) => item.item_name === name);
          if (item) {
            return `${name} (${item.count})`;
          }
          return name;
        }
      },
      series: [
        {
          name: '扣分项',
          type: 'pie',
          radius: '60%',
          center: ['60%', '50%'], // 将饼图向右移动，为左侧图例留出空间
          data: deductionStats.map((item: any) => ({
            value: item.count,
            name: item.item_name
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)',
            fontSize: 12
          }
        }
      ]
    };
    
    deductionChartInstance?.setOption(option);
    
    // 添加点击事件，支持扣分项筛选
    deductionChartInstance?.off('click');
    deductionChartInstance?.on('click', (params: any) => {
      if (params.data && params.data.name) {
        onDeductionFilter(params.data.name);
      }
    });
  } catch (error) {
    ElMessage.error('加载扣分项统计失败');
  }
};

// 加载客服排行
const loadServiceRanking = async () => {
  try {
    // 直接使用dashboard数据中的客服排行
    const serviceRanking = dashboardData.value.service_ranking || [];
    
    const option = {
      title: {
        text: '客服平均分排行',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: serviceRanking.map((item: any) => item.service_name),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '平均分'
      },
      series: [
        {
          name: '平均分',
          type: 'bar',
          data: serviceRanking.map((item: any) => item.avg_score),
          itemStyle: {
            color: function(params: any) {
              const score = params.value;
              if (score >= 90) return '#67C23A';
              if (score >= 80) return '#409EFF';
              if (score >= 70) return '#E6A23C';
              return '#F56C6C';
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            fontSize: 12,
            color: '#333'
          }
        }
      ]
    };
    
    serviceChartInstance?.setOption(option);
    
    // 添加点击事件，支持客服筛选
    serviceChartInstance?.off('click');
    serviceChartInstance?.on('click', (params: any) => {
      const serviceData = serviceRanking[params.dataIndex];
      if (serviceData) {
        onServiceFilter(serviceData.service_id, serviceData.service_name);
      }
    });
  } catch (error) {
    ElMessage.error('加载客服排行失败');
  }
};



// 事件处理函数
const onDateRangeChange = () => {
  refreshDashboard();
};

const onGameChange = () => {
  refreshDashboard();
};

const refreshDashboard = async () => {
  await loadDashboardData();
  await loadDeductionStats();
  await loadServiceRanking();
  
  // 同步更新表格筛选条件
  const searchForm = crudExpose.getSearchFormData() || {};
  if (dateRange.value && dateRange.value.length === 2) {
    searchForm.create_datetime = dateRange.value;
  }
  if (selectedGame.value) {
    searchForm.game = selectedGame.value;
  }
  crudExpose.setSearchFormData({ form: searchForm });
  crudExpose.doRefresh();
};

const resetFilters = () => {
  // 重置为昨日
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const dateStr = yesterday.toISOString().split('T')[0];
  dateRange.value = [dateStr, dateStr];
  
  selectedGame.value = '';
  currentServiceFilter.value = null;
  currentDeductionFilter.value = '';
  
  // 清空表格筛选条件
  crudExpose.setSearchFormData({});
  
  refreshDashboard();
};

const refreshDeductionStats = () => {
  loadDeductionStats();
};

const refreshServiceRanking = () => {
  loadServiceRanking();
};





const clearServiceFilter = () => {
  currentServiceFilter.value = null;
  const searchForm = crudExpose.getSearchFormData() || {};
  delete searchForm.service_id;
  crudExpose.setSearchFormData({ form: searchForm });
  crudExpose.doRefresh();
};

const clearDeductionFilter = () => {
  currentDeductionFilter.value = '';
  const searchForm = crudExpose.getSearchFormData() || {};
  delete searchForm.deduction_item;
  crudExpose.setSearchFormData({ form: searchForm });
  crudExpose.doRefresh();
};
</script>

<style scoped>
.cs-quality-check-new {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border-radius: 8px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.dashboard-section {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-section {
  margin-top: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}
</style>