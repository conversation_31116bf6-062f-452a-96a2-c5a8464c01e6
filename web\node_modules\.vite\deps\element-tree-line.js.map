{"version": 3, "sources": ["../../element-tree-line/dist/index.es.js"], "sourcesContent": ["function getComConfig(h) {\n    return {\n        name: 'element-tree-line',\n        props: {\n            node: {\n                type: Object,\n                required: true,\n            },\n            data: {\n                type: Object,\n            },\n            treeData: {\n                type: Array,\n            },\n            indent: {\n                type: Number,\n                default() {\n                    return 16;\n                },\n            },\n            showLabelLine: {\n                type: Boolean,\n                default: true,\n            },\n        },\n        render(createElement) {\n            const $createElement = h || createElement;\n            // 自定义整行节点label区域\n            const scopeSlotDefault = this.getScopedSlot('default');\n            // 显示横线时自定义节点label区域\n            const labelSlot = this.getScopedSlot('node-label');\n            // 显示横线时追加在横线右边的内容\n            const afterLabelSlot = this.getScopedSlot('after-node-label');\n            const labelNodes = scopeSlotDefault\n                ? this.getScopedSlotValue(scopeSlotDefault, {\n                      node: this.node,\n                      data: this.data,\n                  })\n                : [\n                      labelSlot\n                          ? this.getScopedSlotValue(labelSlot, {\n                                node: this.node,\n                                data: this.data,\n                            })\n                          : $createElement(\n                                'span',\n                                { class: 'element-tree-node-label' },\n                                this.node.label\n                            ),\n                      this.showLabelLine\n                          ? $createElement('span', {\n                                class: 'element-tree-node-label-line',\n                            })\n                          : null,\n                      this.getScopedSlotValue(afterLabelSlot, {\n                          node: this.node,\n                          data: this.data,\n                      }),\n                  ];\n            // 取得每一层的当前节点是不是在当前层级列表的最后一个\n            const lastnodeArr = [];\n            let currentNode = this.node;\n            while (currentNode) {\n                let parentNode = currentNode.parent;\n                // 兼容element-plus的 el-tree-v2 (Virtualized Tree 虚拟树)\n                if (currentNode.level === 1 && !currentNode.parent) {\n                    // el-tree-v2的第一层node是没有parent的，必需 treeData 创建一个parent\n                    if (!this.treeData || !Array.isArray(this.treeData)) {\n                        throw Error(\n                            'if you using el-tree-v2 (Virtualized Tree) of element-plus,element-tree-line required data.'\n                        );\n                    }\n                    parentNode = {\n                        children: Array.isArray(this.treeData)\n                            ? this.treeData.map((item) => {\n                                  return { ...item, key: item.id };\n                              })\n                            : [],\n                        level: 0,\n                        key: 'node-0',\n                        parent: null,\n                    };\n                }\n                if (parentNode) {\n                    // element-plus的 el-tree-v2 使用的是children和key， 其他使用的是 childNodes和id\n                    const index = (\n                        parentNode.children || parentNode.childNodes\n                    ).findIndex(\n                        (item) =>\n                            (item.key || item.id) ===\n                            (currentNode.key || currentNode.id)\n                    );\n                    lastnodeArr.unshift(\n                        index ===\n                            (parentNode.children || parentNode.childNodes)\n                                .length -\n                                1\n                    );\n                }\n                currentNode = parentNode;\n            }\n            const lineNodes = [];\n            for (let i = 0; i < this.node.level; i++) {\n                if (lastnodeArr[i] && this.node.level - 1 !== i) {\n                    continue;\n                }\n                lineNodes.push(\n                    $createElement('span', {\n                        class: {\n                            'element-tree-node-line-ver': true,\n                            'last-node-isLeaf-line':\n                                lastnodeArr[i] && this.node.level - 1 === i,\n                        },\n                        style: { left: this.indent * i + 'px' },\n                    })\n                );\n            }\n            return $createElement(\n                'span',\n                {\n                    class: 'element-tree-node-label-wrapper',\n                },\n                [labelNodes].concat(lineNodes).concat([\n                    $createElement('span', {\n                        class: 'element-tree-node-line-hor',\n                        style: {\n                            width: (this.node.isLeaf ? 24 : 8) + 'px',\n                            left: (this.node.level - 1) * this.indent + 'px',\n                        },\n                    }),\n                ])\n            );\n        },\n        methods: {\n            getScopedSlot(slotName) {\n                if (!slotName) {\n                    return null;\n                }\n                const slotNameSplits = slotName.split('||');\n                let scopeSlot = null;\n                for (let index = 0; index < slotNameSplits.length; index++) {\n                    const name = slotNameSplits[index];\n                    const slot = (this.$slots || {})[name];\n                    if (slot) {\n                        scopeSlot = slot;\n                        break;\n                    }\n                    scopeSlot = (this.$scopedSlots || {})[name];\n                    if (scopeSlot) {\n                        break;\n                    }\n                }\n                return scopeSlot;\n            },\n            getScopedSlotValue(scopeSlot, scopedData, defaultNode = null) {\n                if (typeof scopeSlot === 'function') {\n                    return scopeSlot(scopedData) || defaultNode;\n                }\n                return scopeSlot || defaultNode;\n            },\n        },\n    };\n}\n\nfunction getElementLabelLine(h) {\n    const conf = getComConfig(h);\n    if (h) {\n        conf.methods.getScopedSlot = function getScopedSlot(slotName) {\n            if (!slotName) {\n                return null;\n            }\n            const slotNameSplits = slotName.split('||');\n            let scopeSlot = null;\n            for (let index = 0; index < slotNameSplits.length; index++) {\n                const name = slotNameSplits[index];\n                const slot = (this.$slots || {})[name];\n                if (slot) {\n                    scopeSlot = slot;\n                    break;\n                }\n            }\n            return scopeSlot;\n        };\n    }\n    return conf;\n}\nconst ElementLabelLine = getElementLabelLine();\n\nexport { ElementLabelLine as default, getElementLabelLine };\n"], "mappings": ";;;AAAA,SAAS,aAAa,GAAG;AACrB,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,MACH,MAAM;AAAA,QACF,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AAAA,MACA,MAAM;AAAA,QACF,MAAM;AAAA,MACV;AAAA,MACA,UAAU;AAAA,QACN,MAAM;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,eAAe;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,OAAO,eAAe;AAClB,YAAM,iBAAiB,KAAK;AAE5B,YAAM,mBAAmB,KAAK,cAAc,SAAS;AAErD,YAAM,YAAY,KAAK,cAAc,YAAY;AAEjD,YAAM,iBAAiB,KAAK,cAAc,kBAAkB;AAC5D,YAAM,aAAa,mBACb,KAAK,mBAAmB,kBAAkB;AAAA,QACtC,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,MACf,CAAC,IACD;AAAA,QACI,YACM,KAAK,mBAAmB,WAAW;AAAA,UAC/B,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,QACf,CAAC,IACD;AAAA,UACI;AAAA,UACA,EAAE,OAAO,0BAA0B;AAAA,UACnC,KAAK,KAAK;AAAA,QACd;AAAA,QACN,KAAK,gBACC,eAAe,QAAQ;AAAA,UACnB,OAAO;AAAA,QACX,CAAC,IACD;AAAA,QACN,KAAK,mBAAmB,gBAAgB;AAAA,UACpC,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,QACf,CAAC;AAAA,MACL;AAEN,YAAM,cAAc,CAAC;AACrB,UAAI,cAAc,KAAK;AACvB,aAAO,aAAa;AAChB,YAAI,aAAa,YAAY;AAE7B,YAAI,YAAY,UAAU,KAAK,CAAC,YAAY,QAAQ;AAEhD,cAAI,CAAC,KAAK,YAAY,CAAC,MAAM,QAAQ,KAAK,QAAQ,GAAG;AACjD,kBAAM;AAAA,cACF;AAAA,YACJ;AAAA,UACJ;AACA,uBAAa;AAAA,YACT,UAAU,MAAM,QAAQ,KAAK,QAAQ,IAC/B,KAAK,SAAS,IAAI,CAAC,SAAS;AACxB,qBAAO,EAAE,GAAG,MAAM,KAAK,KAAK,GAAG;AAAA,YACnC,CAAC,IACD,CAAC;AAAA,YACP,OAAO;AAAA,YACP,KAAK;AAAA,YACL,QAAQ;AAAA,UACZ;AAAA,QACJ;AACA,YAAI,YAAY;AAEZ,gBAAM,SACF,WAAW,YAAY,WAAW,YACpC;AAAA,YACE,CAAC,UACI,KAAK,OAAO,KAAK,SACjB,YAAY,OAAO,YAAY;AAAA,UACxC;AACA,sBAAY;AAAA,YACR,WACK,WAAW,YAAY,WAAW,YAC9B,SACD;AAAA,UACZ;AAAA,QACJ;AACA,sBAAc;AAAA,MAClB;AACA,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,OAAO,KAAK;AACtC,YAAI,YAAY,CAAC,KAAK,KAAK,KAAK,QAAQ,MAAM,GAAG;AAC7C;AAAA,QACJ;AACA,kBAAU;AAAA,UACN,eAAe,QAAQ;AAAA,YACnB,OAAO;AAAA,cACH,8BAA8B;AAAA,cAC9B,yBACI,YAAY,CAAC,KAAK,KAAK,KAAK,QAAQ,MAAM;AAAA,YAClD;AAAA,YACA,OAAO,EAAE,MAAM,KAAK,SAAS,IAAI,KAAK;AAAA,UAC1C,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,OAAO;AAAA,QACX;AAAA,QACA,CAAC,UAAU,EAAE,OAAO,SAAS,EAAE,OAAO;AAAA,UAClC,eAAe,QAAQ;AAAA,YACnB,OAAO;AAAA,YACP,OAAO;AAAA,cACH,QAAQ,KAAK,KAAK,SAAS,KAAK,KAAK;AAAA,cACrC,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS;AAAA,YAChD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,cAAc,UAAU;AACpB,YAAI,CAAC,UAAU;AACX,iBAAO;AAAA,QACX;AACA,cAAM,iBAAiB,SAAS,MAAM,IAAI;AAC1C,YAAI,YAAY;AAChB,iBAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AACxD,gBAAM,OAAO,eAAe,KAAK;AACjC,gBAAM,QAAQ,KAAK,UAAU,CAAC,GAAG,IAAI;AACrC,cAAI,MAAM;AACN,wBAAY;AACZ;AAAA,UACJ;AACA,uBAAa,KAAK,gBAAgB,CAAC,GAAG,IAAI;AAC1C,cAAI,WAAW;AACX;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,MACA,mBAAmB,WAAW,YAAY,cAAc,MAAM;AAC1D,YAAI,OAAO,cAAc,YAAY;AACjC,iBAAO,UAAU,UAAU,KAAK;AAAA,QACpC;AACA,eAAO,aAAa;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,oBAAoB,GAAG;AAC5B,QAAM,OAAO,aAAa,CAAC;AAC3B,MAAI,GAAG;AACH,SAAK,QAAQ,gBAAgB,SAAS,cAAc,UAAU;AAC1D,UAAI,CAAC,UAAU;AACX,eAAO;AAAA,MACX;AACA,YAAM,iBAAiB,SAAS,MAAM,IAAI;AAC1C,UAAI,YAAY;AAChB,eAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AACxD,cAAM,OAAO,eAAe,KAAK;AACjC,cAAM,QAAQ,KAAK,UAAU,CAAC,GAAG,IAAI;AACrC,YAAI,MAAM;AACN,sBAAY;AACZ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,mBAAmB,oBAAoB;", "names": []}