# Generated by Django 4.2.7 on 2025-07-21 17:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cs_manage', '0004_csqualitycheck_csqualitydeduction_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='csqualitycheck',
            name='service_user',
            field=models.ForeignKey(blank=True, help_text='通过姓名关联的系统用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='quality_checks', to=settings.AUTH_USER_MODEL, verbose_name='关联系统用户'),
        ),
        migrations.AddField(
            model_name='csqualitycheck',
            name='session_summary',
            field=models.TextField(blank=True, help_text='会话总结', verbose_name='会话总结'),
        ),
    ]
