<template>
  <div class="quality-metrics-chart">
    <div v-if="loading" class="flex items-center justify-center h-80">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
    </div>
    
    <div v-else-if="!hasData" class="flex items-center justify-center h-80 text-gray-500">
      <div class="text-center">
        <i class="fas fa-chart-line text-4xl mb-2"></i>
        <p>暂无数据</p>
      </div>
    </div>
    
    <div v-else ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import type { ChartData } from '../api'

interface Props {
  data?: ChartData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 计算是否有数据
const hasData = computed(() => {
  return props.data && props.data.dates && props.data.dates.length > 0
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !hasData.value) return
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      }
    },
    legend: {
      data: props.data?.series.map(s => s.name) || [],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data?.dates || [],
      axisLabel: {
        rotate: 0,
        interval: 0,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      },
      min: 0,
      max: 100
    },
    series: props.data?.series.map((series, index) => ({
      name: series.name,
      type: 'line',
      data: series.data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3
      },
      itemStyle: {
        color: getSeriesColor(index)
      },
      areaStyle: {
        opacity: 0.1,
        color: getSeriesColor(index)
      },
      emphasis: {
        focus: 'series'
      }
    })) || []
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeHandler = () => {
    chartInstance?.resize()
  }
  window.addEventListener('resize', resizeHandler)
  
  // 清理函数
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}

// 获取系列颜色
const getSeriesColor = (index: number) => {
  const colors = [
    '#10B981', // green-500 - 满意度
    '#3B82F6', // blue-500 - FCR
    '#F59E0B', // orange-500 - 30秒应答率
    '#8B5CF6', // purple-500
    '#EF4444', // red-500
    '#06B6D4'  // cyan-500
  ]
  return colors[index % colors.length]
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    nextTick(() => {
      initChart()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (newLoading) => {
    if (!newLoading) {
      nextTick(() => {
        initChart()
      })
    }
  }
)

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载时清理
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.quality-metrics-chart {
  width: 100%;
  height: 320px;
}
</style>
