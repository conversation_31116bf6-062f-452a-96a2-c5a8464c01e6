{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/yaml/yaml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/yaml/yaml.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: true\n  },\n  onEnterRules: [\n    {\n      beforeText: /:\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.Indent\n      }\n    }\n  ]\n};\nvar language = {\n  tokenPostfix: \".yaml\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\"true\", \"True\", \"TRUE\", \"false\", \"False\", \"FALSE\", \"null\", \"Null\", \"Null\", \"~\"],\n  numberInteger: /(?:0|[+-]?[0-9]+)/,\n  numberFloat: /(?:0|[+-]?[0-9]+)(?:\\.[0-9]+)?(?:e[-+][1-9][0-9]*)?/,\n  numberOctal: /0o[0-7]+/,\n  numberHex: /0x[0-9a-fA-F]+/,\n  numberInfinity: /[+-]?\\.(?:inf|Inf|INF)/,\n  numberNaN: /\\.(?:nan|Nan|NAN)/,\n  numberDate: /\\d{4}-\\d\\d-\\d\\d([Tt ]\\d\\d:\\d\\d:\\d\\d(\\.\\d+)?(( ?[+-]\\d\\d?(:\\d\\d)?)|Z)?)?/,\n  escapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Directive\n      [/%[^ ]+.*$/, \"meta.directive\"],\n      // Document Markers\n      [/---/, \"operators.directivesEnd\"],\n      [/\\.{3}/, \"operators.documentEnd\"],\n      // Block Structure Indicators\n      [/[-?:](?= )/, \"operators\"],\n      { include: \"@anchor\" },\n      { include: \"@tagHandle\" },\n      { include: \"@flowCollections\" },\n      { include: \"@blockStyle\" },\n      // Numbers\n      [/@numberInteger(?![ \\t]*\\S+)/, \"number\"],\n      [/@numberFloat(?![ \\t]*\\S+)/, \"number.float\"],\n      [/@numberOctal(?![ \\t]*\\S+)/, \"number.octal\"],\n      [/@numberHex(?![ \\t]*\\S+)/, \"number.hex\"],\n      [/@numberInfinity(?![ \\t]*\\S+)/, \"number.infinity\"],\n      [/@numberNaN(?![ \\t]*\\S+)/, \"number.nan\"],\n      [/@numberDate(?![ \\t]*\\S+)/, \"number.date\"],\n      // Key:Value pair\n      [/(\".*?\"|'.*?'|[^#'\"]*?)([ \\t]*)(:)( |$)/, [\"type\", \"white\", \"operators\", \"white\"]],\n      { include: \"@flowScalars\" },\n      // String nodes\n      [\n        /.+?(?=(\\s+#|$))/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Mapping\n    object: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Mapping termination\n      [/\\}/, \"@brackets\", \"@pop\"],\n      // Flow Mapping delimiter\n      [/,/, \"delimiter.comma\"],\n      // Flow Mapping Key:Value delimiter\n      [/:(?= )/, \"operators\"],\n      // Flow Mapping Key:Value key\n      [/(?:\".*?\"|'.*?'|[^,\\{\\[]+?)(?=: )/, \"type\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\},]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Sequence\n    array: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Sequence termination\n      [/\\]/, \"@brackets\", \"@pop\"],\n      // Flow Sequence delimiter\n      [/,/, \"delimiter.comma\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\],]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // First line of a Block Style\n    multiString: [[/^( +).+$/, \"string\", \"@multiStringContinued.$1\"]],\n    // Further lines of a Block Style\n    //   Workaround for indentation detection\n    multiStringContinued: [\n      [\n        /^( *).+$/,\n        {\n          cases: {\n            \"$1==$S2\": \"string\",\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]],\n    // Only line comments\n    comment: [[/#.*$/, \"comment\"]],\n    // Start Flow Collections\n    flowCollections: [\n      [/\\[/, \"@brackets\", \"@array\"],\n      [/\\{/, \"@brackets\", \"@object\"]\n    ],\n    // Start Flow Scalars (quoted strings)\n    flowScalars: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'[^']*'/, \"string\"],\n      [/\"/, \"string\", \"@doubleQuotedString\"]\n    ],\n    doubleQuotedString: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    // Start Block Scalar\n    blockStyle: [[/[>|][0-9]*[+-]?$/, \"operators\", \"@multiString\"]],\n    // Numbers in Flow Collections (terminate with ,]})\n    flowNumber: [\n      [/@numberInteger(?=[ \\t]*[,\\]\\}])/, \"number\"],\n      [/@numberFloat(?=[ \\t]*[,\\]\\}])/, \"number.float\"],\n      [/@numberOctal(?=[ \\t]*[,\\]\\}])/, \"number.octal\"],\n      [/@numberHex(?=[ \\t]*[,\\]\\}])/, \"number.hex\"],\n      [/@numberInfinity(?=[ \\t]*[,\\]\\}])/, \"number.infinity\"],\n      [/@numberNaN(?=[ \\t]*[,\\]\\}])/, \"number.nan\"],\n      [/@numberDate(?=[ \\t]*[,\\]\\}])/, \"number.date\"]\n    ],\n    tagHandle: [[/\\![^ ]*/, \"tag\"]],\n    anchor: [[/[&*][^ ]+/, \"namespace\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAIA,IAAY,OAAO;AAAvB,IACIC,IAAmB,OAAO;AAD9B,IAEIC,IAAoB,OAAO;AAF/B,IAGIC,IAAe,OAAO,UAAU;AAHpC,IAIIC,IAAc,CAACC,GAAIC,GAAMC,GAAQC,MAAS;AAC5C,MAAIF,KAAQ,OAAOA,KAAS,YAAY,OAAOA,KAAS;AACtD,aAASG,KAAOP,EAAkBI,CAAI;AAChC,OAACH,EAAa,KAAKE,GAAII,CAAG,KAAKA,MAAQF,KACzCP,EAAUK,GAAII,GAAK,EAAE,KAAK,MAAMH,EAAKG,CAAG,GAAG,YAAY,EAAED,IAAOP,EAAiBK,GAAMG,CAAG,MAAMD,EAAK,WAAU,CAAE;AAEvH,SAAOH;AACT;AAXA,IAYIK,IAAa,CAACC,GAAQC,GAAKC,OAAkBT,EAAYO,GAAQC,GAAK,SAAS,GAAGC,KAAgBT,EAAYS,GAAcD,GAAK,SAAS;AAZ9I,IAeIE,IAA6B,CAAA;AACjCJ,EAAWI,GAA4BC,GAAuB;AAI3D,IAACC,IAAO;EACT,UAAU;IACR,aAAa;EACd;EACD,UAAU;IACR,CAAC,KAAK,GAAG;IACT,CAAC,KAAK,GAAG;IACT,CAAC,KAAK,GAAG;EACV;EACD,kBAAkB;IAChB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;EAC1B;EACD,kBAAkB;IAChB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;IACzB,EAAE,MAAM,KAAK,OAAO,IAAK;EAC1B;EACD,SAAS;IACP,SAAS;EACV;EACD,cAAc;IACZ;MACE,YAAY;MACZ,QAAQ;QACN,cAAcF,EAA2B,UAAU,aAAa;MACjE;IACF;EACF;AACH;AAlCG,IAmCCG,IAAW;EACb,cAAc;EACd,UAAU;IACR,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAK;IACrD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAK;EACrD;EACD,UAAU,CAAC,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,QAAQ,QAAQ,GAAG;EACzF,eAAe;EACf,aAAa;EACb,aAAa;EACb,WAAW;EACX,gBAAgB;EAChB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,WAAW;IACT,MAAM;MACJ,EAAE,SAAS,cAAe;MAC1B,EAAE,SAAS,WAAY;;MAEvB,CAAC,aAAa,gBAAgB;;MAE9B,CAAC,OAAO,yBAAyB;MACjC,CAAC,SAAS,uBAAuB;;MAEjC,CAAC,cAAc,WAAW;MAC1B,EAAE,SAAS,UAAW;MACtB,EAAE,SAAS,aAAc;MACzB,EAAE,SAAS,mBAAoB;MAC/B,EAAE,SAAS,cAAe;;MAE1B,CAAC,+BAA+B,QAAQ;MACxC,CAAC,6BAA6B,cAAc;MAC5C,CAAC,6BAA6B,cAAc;MAC5C,CAAC,2BAA2B,YAAY;MACxC,CAAC,gCAAgC,iBAAiB;MAClD,CAAC,2BAA2B,YAAY;MACxC,CAAC,4BAA4B,aAAa;;MAE1C,CAAC,0CAA0C,CAAC,QAAQ,SAAS,aAAa,OAAO,CAAC;MAClF,EAAE,SAAS,eAAgB;;MAE3B;QACE;QACA;UACE,OAAO;YACL,aAAa;YACb,YAAY;UACb;QACF;MACF;IACF;;IAED,QAAQ;MACN,EAAE,SAAS,cAAe;MAC1B,EAAE,SAAS,WAAY;;MAEvB,CAAC,MAAM,aAAa,MAAM;;MAE1B,CAAC,KAAK,iBAAiB;;MAEvB,CAAC,UAAU,WAAW;;MAEtB,CAAC,oCAAoC,MAAM;;MAE3C,EAAE,SAAS,mBAAoB;MAC/B,EAAE,SAAS,eAAgB;;MAE3B,EAAE,SAAS,aAAc;MACzB,EAAE,SAAS,UAAW;MACtB,EAAE,SAAS,cAAe;;MAE1B;QACE;QACA;UACE,OAAO;YACL,aAAa;YACb,YAAY;UACb;QACF;MACF;IACF;;IAED,OAAO;MACL,EAAE,SAAS,cAAe;MAC1B,EAAE,SAAS,WAAY;;MAEvB,CAAC,MAAM,aAAa,MAAM;;MAE1B,CAAC,KAAK,iBAAiB;;MAEvB,EAAE,SAAS,mBAAoB;MAC/B,EAAE,SAAS,eAAgB;;MAE3B,EAAE,SAAS,aAAc;MACzB,EAAE,SAAS,UAAW;MACtB,EAAE,SAAS,cAAe;;MAE1B;QACE;QACA;UACE,OAAO;YACL,aAAa;YACb,YAAY;UACb;QACF;MACF;IACF;;IAED,aAAa,CAAC,CAAC,YAAY,UAAU,0BAA0B,CAAC;;;IAGhE,sBAAsB;MACpB;QACE;QACA;UACE,OAAO;YACL,WAAW;YACX,YAAY,EAAE,OAAO,YAAY,MAAM,UAAW;UACnD;QACF;MACF;IACF;IACD,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;;IAEpC,SAAS,CAAC,CAAC,QAAQ,SAAS,CAAC;;IAE7B,iBAAiB;MACf,CAAC,MAAM,aAAa,QAAQ;MAC5B,CAAC,MAAM,aAAa,SAAS;IAC9B;;IAED,aAAa;MACX,CAAC,mBAAmB,gBAAgB;MACpC,CAAC,mBAAmB,gBAAgB;MACpC,CAAC,WAAW,QAAQ;MACpB,CAAC,KAAK,UAAU,qBAAqB;IACtC;IACD,oBAAoB;MAClB,CAAC,WAAW,QAAQ;MACpB,CAAC,YAAY,eAAe;MAC5B,CAAC,OAAO,uBAAuB;MAC/B,CAAC,KAAK,UAAU,MAAM;IACvB;;IAED,YAAY,CAAC,CAAC,oBAAoB,aAAa,cAAc,CAAC;;IAE9D,YAAY;MACV,CAAC,mCAAmC,QAAQ;MAC5C,CAAC,iCAAiC,cAAc;MAChD,CAAC,iCAAiC,cAAc;MAChD,CAAC,+BAA+B,YAAY;MAC5C,CAAC,oCAAoC,iBAAiB;MACtD,CAAC,+BAA+B,YAAY;MAC5C,CAAC,gCAAgC,aAAa;IAC/C;IACD,WAAW,CAAC,CAAC,WAAW,KAAK,CAAC;IAC9B,QAAQ,CAAC,CAAC,aAAa,WAAW,CAAC;EACpC;AACH;", "names": ["__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__hasOwnProp", "__copyProps", "to", "from", "except", "desc", "key", "__reExport", "target", "mod", "second<PERSON><PERSON><PERSON>", "monaco_editor_core_exports", "monaco_editor_core_star", "conf", "language"]}