# Generated by Django 4.2.7 on 2025-07-22 15:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('kcs', '0012_alter_csemotionanalysisdetail_unique_together_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cs_manage', '0005_csqualitycheck_service_user_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CsEmotionAnalysis',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('session_id', models.CharField(db_index=True, help_text='七鱼会话ID', max_length=512, unique=True, verbose_name='会话ID')),
                ('service_id', models.CharField(db_index=True, help_text='客服ID', max_length=255, verbose_name='客服ID')),
                ('service_name', models.CharField(help_text='客服姓名', max_length=255, verbose_name='客服姓名')),
                ('service_account', models.CharField(blank=True, help_text='客服账号', max_length=255, null=True, verbose_name='客服账号')),
                ('initial_emotion_score', models.FloatField(blank=True, help_text='用户初始情绪分数(-10到10)', null=True, verbose_name='初始情绪分数')),
                ('initial_emotion_justification', models.TextField(blank=True, help_text='LLM分析初始情绪的理由', verbose_name='初始情绪分析理由')),
                ('final_emotion_score', models.FloatField(blank=True, help_text='用户最终情绪分数(-10到10)', null=True, verbose_name='最终情绪分数')),
                ('final_emotion_justification', models.TextField(blank=True, help_text='LLM分析最终情绪的理由', verbose_name='最终情绪分析理由')),
                ('emotion_change_score', models.FloatField(blank=True, help_text='最终情绪分数 - 初始情绪分数', null=True, verbose_name='情绪变化分数')),
                ('overall_assessment', models.TextField(blank=True, help_text='LLM对客服表现的整体评估', verbose_name='整体评估')),
                ('session_start_time', models.DateTimeField(blank=True, null=True, verbose_name='会话开始时间')),
                ('session_end_time', models.DateTimeField(blank=True, null=True, verbose_name='会话结束时间')),
                ('session_duration', models.IntegerField(default=0, help_text='会话时长(秒)', verbose_name='会话时长')),
                ('message_count', models.IntegerField(default=0, help_text='会话中的消息总数', verbose_name='消息总数')),
                ('user_message_count', models.IntegerField(default=0, help_text='用户发送的消息数', verbose_name='用户消息数')),
                ('service_message_count', models.IntegerField(default=0, help_text='客服发送的消息数', verbose_name='客服消息数')),
                ('analysis_details', models.JSONField(blank=True, help_text='LLM返回的完整分析结果', null=True, verbose_name='完整分析结果')),
                ('conversation_summary', models.TextField(blank=True, help_text='会话内容摘要', verbose_name='会话摘要')),
                ('emotion_keywords', models.JSONField(blank=True, default=list, help_text='提取的情绪相关关键词', verbose_name='情绪关键词')),
                ('structured_conversation', models.JSONField(blank=True, help_text='用户问题、客服行为、解决方案等结构化数据', null=True, verbose_name='结构化会话数据')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '处理失败')], default='pending', max_length=20, verbose_name='处理状态')),
                ('error_message', models.TextField(blank=True, help_text='处理失败时的错误信息', verbose_name='错误信息')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('game', models.ForeignKey(blank=True, help_text='关联游戏', null=True, on_delete=django.db.models.deletion.SET_NULL, to='kcs.game', verbose_name='游戏')),
                ('link', models.ForeignKey(blank=True, help_text='关联的文章链接', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='emotion_analysis', to='kcs.link', verbose_name='关联Link')),
                ('service_user', models.ForeignKey(blank=True, help_text='通过姓名关联的系统用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='emotion_analyses', to=settings.AUTH_USER_MODEL, verbose_name='关联系统用户')),
            ],
            options={
                'verbose_name': '客服会话情绪分析',
                'verbose_name_plural': '客服会话情绪分析',
                'db_table': 'admin_cs_manage_cs_emotion_analysis',
            },
        ),
        migrations.CreateModel(
            name='CsEmotionAnalysisDetail',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('phase', models.CharField(choices=[('initial', '初始阶段'), ('middle', '中间阶段'), ('final', '最终阶段')], max_length=20, verbose_name='分析阶段')),
                ('start_message_index', models.IntegerField(help_text='该阶段分析的起始消息索引', verbose_name='起始消息索引')),
                ('end_message_index', models.IntegerField(help_text='该阶段分析的结束消息索引', verbose_name='结束消息索引')),
                ('emotion_score', models.FloatField(help_text='该阶段的情绪分数(-10到10)', verbose_name='情绪分数')),
                ('emotion_description', models.CharField(help_text='情绪的文字描述', max_length=100, verbose_name='情绪描述')),
                ('confidence_level', models.FloatField(default=0.5, help_text='分析结果的置信度(0-1)', verbose_name='置信度')),
                ('key_messages', models.JSONField(default=list, help_text='影响情绪判断的关键消息', verbose_name='关键消息')),
                ('emotion_triggers', models.JSONField(default=list, help_text='导致情绪变化的触发因素', verbose_name='情绪触发因素')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('emotion_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='cs_manage.csemotionanalysis', verbose_name='情绪分析记录')),
            ],
            options={
                'verbose_name': '客服会话情绪分析详情',
                'verbose_name_plural': '客服会话情绪分析详情',
                'db_table': 'admin_cs_manage_cs_emotion_analysis_detail',
                'indexes': [models.Index(fields=['emotion_analysis', 'phase'], name='admin_cs_ma_emotion_745e7e_idx'), models.Index(fields=['emotion_score'], name='admin_cs_ma_emotion_8cebd9_idx')],
                'unique_together': {('emotion_analysis', 'phase')},
            },
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['session_id'], name='admin_cs_ma_session_68244c_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['service_id'], name='admin_cs_ma_service_c8e264_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['emotion_change_score'], name='admin_cs_ma_emotion_edfea6_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['create_datetime'], name='admin_cs_ma_create__26325a_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['game'], name='admin_cs_ma_game_id_639bbc_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['status'], name='admin_cs_ma_status_0d17ec_idx'),
        ),
        migrations.AddIndex(
            model_name='csemotionanalysis',
            index=models.Index(fields=['session_start_time'], name='admin_cs_ma_session_48141a_idx'),
        ),
    ]
