import {
  he
} from "./chunk-WQP6ZAOK.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import {
  B
} from "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import {
  merge_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  Fragment,
  computed,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  defineComponent,
  mergeProps,
  normalizeClass,
  openBlock,
  ref,
  renderSlot,
  resolveDirective,
  resolveDynamicComponent,
  toDisplayString,
  withCtx,
  withDirectives
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/fs-copyable-52723316.mjs
var N = defineComponent({
  name: "FsCopyable",
  props: {
    modelValue: {
      type: [String, Number, Boolean],
      default: void 0
    },
    /**
     * 复制按钮
     * show: 是否显示，默认true
     * text: 按钮文字
     * ...其他tag组件参数
     */
    button: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 成功信息
     */
    successMessage: {
      type: [Boolean, String],
      default: true
    },
    /**
     * 错误时的信息
     */
    errorMessage: {
      type: [Boolean, String],
      default: true
    },
    inline: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    "update:modelValue",
    /**
     * 成功事件
     */
    "success",
    /**
     * 失败事件
     */
    "error"
  ],
  setup(e, { emit: n, slots: a }) {
    const u = computed(() => a.default != null), { ui: s } = B(), l = ref(s.tag.name), o = computed(() => {
      const y = {
        text: "复制",
        size: "small",
        [s.tag.type]: "success",
        show: true,
        showOnHover: false
      };
      return merge_default({}, y, e.button);
    });
    function f() {
      n("success"), e.successMessage && s.message.success(e.successMessage === true ? "复制成功" : e.successMessage);
    }
    function p() {
      n("error"), e.errorMessage && s.message.error(e.errorMessage === true ? "复制失败" : e.errorMessage);
    }
    return {
      textInSlot: u,
      tagName: l,
      copyButton: o,
      onSuccess: f,
      onError: p
    };
  }
});
var E = { class: "pointer text" };
var O = {
  key: 0,
  class: "copy-button"
};
function z(e, n, a, u, s, l) {
  const o = resolveDirective("clipboard");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["fs-copyable", { "show-on-hover": e.copyButton.showOnHover, inline: e.inline }])
  }, [
    withDirectives((openBlock(), createElementBlock("span", E, [
      e.$slots.default ? renderSlot(e.$slots, "default", { key: 0 }) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        createTextVNode(toDisplayString(e.modelValue), 1)
      ], 64))
    ])), [
      [o, e.modelValue],
      [o, e.onSuccess, "success"],
      [o, e.onError, "error"]
    ]),
    e.modelValue != null && e.copyButton.show !== false ? (openBlock(), createElementBlock("div", O, [
      withDirectives((openBlock(), createBlock(resolveDynamicComponent(e.tagName), mergeProps({ class: "pointer text" }, e.copyButton), {
        default: withCtx(() => [
          createTextVNode(toDisplayString(e.copyButton.text ?? "复制"), 1)
        ]),
        _: 1
      }, 16)), [
        [o, e.modelValue],
        [o, e.onSuccess, "success"],
        [o, e.onError, "error"]
      ])
    ])) : createCommentVNode("", true)
  ], 2);
}
var P = he(N, [["render", z]]);
export {
  P as default
};
//# sourceMappingURL=fs-copyable-52723316-JRM6CMLS.js.map
