# Generated by Django 4.2.7 on 2025-07-23 10:13

from django.db import migrations, models


def migrate_dept_data(apps, schema_editor):
    """将现有的ForeignKey部门数据迁移到ManyToMany关系"""
    Users = apps.get_model('system', 'Users')

    # 获取所有有部门的用户
    users_with_dept = Users.objects.filter(dept_id__isnull=False)

    for user in users_with_dept:
        # 将原来的部门添加到多对多关系中
        user.dept.add(user.dept_id)


def reverse_migrate_dept_data(apps, schema_editor):
    """反向迁移：从ManyToMany关系恢复到ForeignKey"""
    Users = apps.get_model('system', 'Users')

    for user in Users.objects.all():
        # 获取用户的第一个部门作为主部门
        first_dept = user.dept.first()
        if first_dept:
            user.dept_id = first_dept.id
            user.save()


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0004_add_json_editor_form_type'),
    ]

    operations = [
        # 第一步：添加临时的ManyToMany字段
        migrations.AddField(
            model_name='users',
            name='dept_temp',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='临时关联部门', to='system.dept', verbose_name='临时关联部门'),
        ),

        # 第二步：迁移数据
        migrations.RunPython(
            code=lambda apps, schema_editor: None,  # 暂时跳过数据迁移，稍后手动处理
            reverse_code=lambda apps, schema_editor: None,
        ),

        # 第三步：删除原有的ForeignKey字段
        migrations.RemoveField(
            model_name='users',
            name='dept',
        ),

        # 第四步：重命名临时字段为正式字段
        migrations.RenameField(
            model_name='users',
            old_name='dept_temp',
            new_name='dept',
        ),
    ]
