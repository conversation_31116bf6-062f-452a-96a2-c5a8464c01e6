import {
  bt,
  vt
} from "./chunk-WQP6ZAOK.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/uploader-form-95c3d72e.mjs
function p(e, n, s) {
  let t;
  s.response ? t = `${s.response.error || s.response}` : s.responseText ? t = `${s.responseText}` : t = `fail to post ${e} ${s.status}`;
  const o = new Error(t);
  return o.status = s.status, o.method = "post", o.url = e, o;
}
function m(e) {
  const n = e.responseText || e.response;
  if (!n)
    return n;
  try {
    return JSON.parse(n);
  } catch {
    return n;
  }
}
function y(e, n, s) {
  if (typeof XMLHttpRequest > "u")
    return;
  const t = new XMLHttpRequest(), o = e.action;
  t.timeout = e.timeout, t.upload && (t.upload.onprogress = function(r) {
    r.total > 0 && (r.percent = r.loaded / r.total * 100), e.onProgress(r);
  });
  const i = new FormData();
  e.data && Object.keys(e.data).forEach((a) => {
    i.append(a, e.data[a]);
  }), i.append(e.name, e.file, e.file.name), t.onerror = function(r) {
    s(r);
  }, t.onload = function() {
    if (t.status < 200 || t.status >= 300)
      return e.onError(p(o, e, t));
    n(m(t));
  }, t.open("post", o, true), e.withCredentials && "withCredentials" in t && (t.withCredentials = true);
  const u = e.headers || {};
  for (const a in u)
    u.hasOwnProperty(a) && u[a] !== null && t.setRequestHeader(a, u[a]);
  return t.send(i), t;
}
function w(e) {
  return new Promise((n, s) => {
    y(e, async (t) => {
      n(t);
    }, (t) => {
      s(t);
    });
  });
}
async function g(e) {
  const { file: n, fileName: s, onProgress: t } = e, o = e.options, i = await vt(n, s, o);
  o.data == null && (o.data = {}), o.data.key = i;
  const u = {
    file: n,
    onProgress: t,
    timeout: 6e4,
    ...o
  };
  delete u.uploadRequest;
  let r = await (o.uploadRequest ?? w)(u);
  return o.successHandle && (r = await o.successHandle(r, u)), r && typeof r == "object" && r.key == null && (r.key = i), r;
}
async function $(e) {
  const { getConfig: n } = bt(), s = n("form");
  return e.options = merge_default({}, cloneDeep_default(s), e.options), await g(e);
}
export {
  $ as upload
};
//# sourceMappingURL=uploader-form-95c3d72e-BQ6YZJNW.js.map
