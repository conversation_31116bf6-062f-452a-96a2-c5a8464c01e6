{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/yaml/yaml.contribution.ts\nimport { registerLanguage } from \"../_.contribution.js\";\nregisterLanguage({\n  id: \"yaml\",\n  extensions: [\".yaml\", \".yml\"],\n  aliases: [\"YAML\", \"yaml\", \"YML\", \"yml\"],\n  mimetypes: [\"application/x-yaml\", \"text/x-yaml\"],\n  loader: () => {\n    if (false) {\n      return new Promise((resolve, reject) => {\n        __require([\"vs/basic-languages/yaml/yaml\"], resolve, reject);\n      });\n    } else {\n      return import(\"./yaml.js\");\n    }\n  }\n});\n"], "mappings": ";;;;;;;AAUAA,EAAiB;EACf,IAAI;EACJ,YAAY,CAAC,SAAS,MAAM;EAC5B,SAAS,CAAC,QAAQ,QAAQ,OAAO,KAAK;EACtC,WAAW,CAAC,sBAAsB,aAAa;EAC/C,QAAQ,MAMG,OAAO,6BAAW;AAG/B,CAAC;", "names": ["registerLanguage"]}