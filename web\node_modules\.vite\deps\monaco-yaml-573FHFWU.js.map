{"version": 3, "sources": ["../../vscode-uri/lib/esm/webpack:/LIB/node_modules/path-browserify/index.js", "../../vscode-uri/lib/esm/webpack:/LIB/webpack/bootstrap", "../../vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/define property getters", "../../vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/hasOwnProperty shorthand", "../../vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/make namespace object", "../../vscode-uri/lib/esm/webpack:/LIB/src/platform.ts", "../../vscode-uri/lib/esm/webpack:/LIB/src/uri.ts", "../../vscode-uri/lib/esm/webpack:/LIB/src/utils.ts", "../../monaco-languageserver-types/src/markerSeverity.ts", "../../monaco-languageserver-types/src/markerTag.ts", "../../monaco-languageserver-types/src/range.ts", "../../monaco-languageserver-types/src/relatedInformation.ts", "../../monaco-languageserver-types/src/markerData.ts", "../../monaco-languageserver-types/src/textEdit.ts", "../../monaco-languageserver-types/src/workspaceFileEditOptions.ts", "../../monaco-languageserver-types/src/workspaceFileEdit.ts", "../../monaco-languageserver-types/src/workspaceEdit.ts", "../../monaco-languageserver-types/src/codeAction.ts", "../../monaco-languageserver-types/src/codeActionTriggerType.ts", "../../monaco-languageserver-types/src/command.ts", "../../monaco-languageserver-types/src/completionItemKind.ts", "../../monaco-languageserver-types/src/completionItemTag.ts", "../../monaco-languageserver-types/src/markdownString.ts", "../../monaco-languageserver-types/src/singleEditOperation.ts", "../../monaco-languageserver-types/src/completionItem.ts", "../../monaco-languageserver-types/src/completionList.ts", "../../monaco-languageserver-types/src/symbolKind.ts", "../../monaco-languageserver-types/src/symbolTag.ts", "../../monaco-languageserver-types/src/documentSymbol.ts", "../../monaco-languageserver-types/src/foldingRange.ts", "../../monaco-languageserver-types/src/formattingOptions.ts", "../../monaco-languageserver-types/src/hover.ts", "../../monaco-languageserver-types/src/position.ts", "../../monaco-languageserver-types/src/link.ts", "../../monaco-languageserver-types/src/locationLink.ts", "../../monaco-languageserver-types/src/selectionRanges.ts", "../../monaco-marker-data-provider/src/monaco-marker-data-provider.ts", "../../monaco-worker-manager/index.js", "../../monaco-yaml/src/index.ts"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\n// !!!!!\n// SEE https://github.com/microsoft/vscode/blob/master/src/vs/base/common/platform.ts\n// !!!!!\n\ndeclare const process: { platform: 'win32' };\ndeclare const navigator: { userAgent: string };\n\nexport let isWindows: boolean;\n\nif (typeof process === 'object') {\n\tisWindows = process.platform === 'win32';\n} else if (typeof navigator === 'object') {\n\tlet userAgent = navigator.userAgent;\n\tisWindows = userAgent.indexOf('Windows') >= 0;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\nimport { CharCode } from './charCode'\nimport { isWindows } from './platform';\n\nconst _schemePattern = /^\\w[\\w\\d+.-]*$/;\nconst _singleSlashStart = /^\\//;\nconst _doubleSlashStart = /^\\/\\//;\n\nfunction _validateUri(ret: URI, _strict?: boolean): void {\n\n\t// scheme, must be set\n\tif (!ret.scheme && _strict) {\n\t\tthrow new Error(`[UriError]: Scheme is missing: {scheme: \"\", authority: \"${ret.authority}\", path: \"${ret.path}\", query: \"${ret.query}\", fragment: \"${ret.fragment}\"}`);\n\t}\n\n\t// scheme, https://tools.ietf.org/html/rfc3986#section-3.1\n\t// ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\n\tif (ret.scheme && !_schemePattern.test(ret.scheme)) {\n\t\tthrow new Error('[UriError]: Scheme contains illegal characters.');\n\t}\n\n\t// path, http://tools.ietf.org/html/rfc3986#section-3.3\n\t// If a URI contains an authority component, then the path component\n\t// must either be empty or begin with a slash (\"/\") character.  If a URI\n\t// does not contain an authority component, then the path cannot begin\n\t// with two slash characters (\"//\").\n\tif (ret.path) {\n\t\tif (ret.authority) {\n\t\t\tif (!_singleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash (\"/\") character');\n\t\t\t}\n\t\t} else {\n\t\t\tif (_doubleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters (\"//\")');\n\t\t\t}\n\t\t}\n\t}\n}\n\n// for a while we allowed uris *without* schemes and this is the migration\n// for them, e.g. an uri without scheme and without strict-mode warns and falls\n// back to the file-scheme. that should cause the least carnage and still be a\n// clear warning\nfunction _schemeFix(scheme: string, _strict: boolean): string {\n\tif (!scheme && !_strict) {\n\t\treturn 'file';\n\t}\n\treturn scheme;\n}\n\n// implements a bit of https://tools.ietf.org/html/rfc3986#section-5\nfunction _referenceResolution(scheme: string, path: string): string {\n\n\t// the slash-character is our 'default base' as we don't\n\t// support constructing URIs relative to other URIs. This\n\t// also means that we alter and potentially break paths.\n\t// see https://tools.ietf.org/html/rfc3986#section-5.1.4\n\tswitch (scheme) {\n\t\tcase 'https':\n\t\tcase 'http':\n\t\tcase 'file':\n\t\t\tif (!path) {\n\t\t\t\tpath = _slash;\n\t\t\t} else if (path[0] !== _slash) {\n\t\t\t\tpath = _slash + path;\n\t\t\t}\n\t\t\tbreak;\n\t}\n\treturn path;\n}\n\nconst _empty = '';\nconst _slash = '/';\nconst _regexp = /^(([^:/?#]+?):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\n/**\n * Uniform Resource Identifier (URI) http://tools.ietf.org/html/rfc3986.\n * This class is a simple parser which creates the basic component parts\n * (http://tools.ietf.org/html/rfc3986#section-3) with minimal validation\n * and encoding.\n *\n * ```txt\n *       foo://example.com:8042/over/there?name=ferret#nose\n *       \\_/   \\______________/\\_________/ \\_________/ \\__/\n *        |           |            |            |        |\n *     scheme     authority       path        query   fragment\n *        |   _____________________|__\n *       / \\ /                        \\\n *       urn:example:animal:ferret:nose\n * ```\n */\nexport class URI implements UriComponents {\n\n\tstatic isUri(thing: any): thing is URI {\n\t\tif (thing instanceof URI) {\n\t\t\treturn true;\n\t\t}\n\t\tif (!thing) {\n\t\t\treturn false;\n\t\t}\n\t\treturn typeof (<URI>thing).authority === 'string'\n\t\t\t&& typeof (<URI>thing).fragment === 'string'\n\t\t\t&& typeof (<URI>thing).path === 'string'\n\t\t\t&& typeof (<URI>thing).query === 'string'\n\t\t\t&& typeof (<URI>thing).scheme === 'string'\n\t\t\t&& typeof (<URI>thing).fsPath === 'string'\n\t\t\t&& typeof (<URI>thing).with === 'function'\n\t\t\t&& typeof (<URI>thing).toString === 'function';\n\t}\n\n\t/**\n\t * scheme is the 'http' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part before the first colon.\n\t */\n\treadonly scheme: string;\n\n\t/**\n\t * authority is the 'www.example.com' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part between the first double slashes and the next slash.\n\t */\n\treadonly authority: string;\n\n\t/**\n\t * path is the '/some/path' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly path: string;\n\n\t/**\n\t * query is the 'query' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly query: string;\n\n\t/**\n\t * fragment is the 'fragment' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly fragment: string;\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(scheme: string, authority?: string, path?: string, query?: string, fragment?: string, _strict?: boolean);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(components: UriComponents);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(schemeOrData: string | UriComponents, authority?: string, path?: string, query?: string, fragment?: string, _strict: boolean = false) {\n\n\t\tif (typeof schemeOrData === 'object') {\n\t\t\tthis.scheme = schemeOrData.scheme || _empty;\n\t\t\tthis.authority = schemeOrData.authority || _empty;\n\t\t\tthis.path = schemeOrData.path || _empty;\n\t\t\tthis.query = schemeOrData.query || _empty;\n\t\t\tthis.fragment = schemeOrData.fragment || _empty;\n\t\t\t// no validation because it's this URI\n\t\t\t// that creates uri components.\n\t\t\t// _validateUri(this);\n\t\t} else {\n\t\t\tthis.scheme = _schemeFix(schemeOrData, _strict);\n\t\t\tthis.authority = authority || _empty;\n\t\t\tthis.path = _referenceResolution(this.scheme, path || _empty);\n\t\t\tthis.query = query || _empty;\n\t\t\tthis.fragment = fragment || _empty;\n\n\t\t\t_validateUri(this, _strict);\n\t\t}\n\t}\n\n\t// ---- filesystem path -----------------------\n\n\t/**\n\t * Returns a string representing the corresponding file system path of this URI.\n\t * Will handle UNC paths, normalizes windows drive letters to lower-case, and uses the\n\t * platform specific path separator.\n\t *\n\t * * Will *not* validate the path for invalid characters and semantics.\n\t * * Will *not* look at the scheme of this URI.\n\t * * The result shall *not* be used for display purposes but for accessing a file on disk.\n\t *\n\t *\n\t * The *difference* to `URI#path` is the use of the platform specific separator and the handling\n\t * of UNC paths. See the below sample of a file-uri with an authority (UNC path).\n\t *\n\t * ```ts\n\t\tconst u = URI.parse('file://server/c$/folder/file.txt')\n\t\tu.authority === 'server'\n\t\tu.path === '/shares/c$/file.txt'\n\t\tu.fsPath === '\\\\server\\c$\\folder\\file.txt'\n\t```\n\t *\n\t * Using `URI#path` to read a file (using fs-apis) would not be enough because parts of the path,\n\t * namely the server name, would be missing. Therefore `URI#fsPath` exists - it's sugar to ease working\n\t * with URIs that represent files on disk (`file` scheme).\n\t */\n\tget fsPath(): string {\n\t\t// if (this.scheme !== 'file') {\n\t\t// \tconsole.warn(`[UriError] calling fsPath with scheme ${this.scheme}`);\n\t\t// }\n\t\treturn uriToFsPath(this, false);\n\t}\n\n\t// ---- modify to new -------------------------\n\n\twith(change: { scheme?: string; authority?: string | null; path?: string | null; query?: string | null; fragment?: string | null }): URI {\n\n\t\tif (!change) {\n\t\t\treturn this;\n\t\t}\n\n\t\tlet { scheme, authority, path, query, fragment } = change;\n\t\tif (scheme === undefined) {\n\t\t\tscheme = this.scheme;\n\t\t} else if (scheme === null) {\n\t\t\tscheme = _empty;\n\t\t}\n\t\tif (authority === undefined) {\n\t\t\tauthority = this.authority;\n\t\t} else if (authority === null) {\n\t\t\tauthority = _empty;\n\t\t}\n\t\tif (path === undefined) {\n\t\t\tpath = this.path;\n\t\t} else if (path === null) {\n\t\t\tpath = _empty;\n\t\t}\n\t\tif (query === undefined) {\n\t\t\tquery = this.query;\n\t\t} else if (query === null) {\n\t\t\tquery = _empty;\n\t\t}\n\t\tif (fragment === undefined) {\n\t\t\tfragment = this.fragment;\n\t\t} else if (fragment === null) {\n\t\t\tfragment = _empty;\n\t\t}\n\n\t\tif (scheme === this.scheme\n\t\t\t&& authority === this.authority\n\t\t\t&& path === this.path\n\t\t\t&& query === this.query\n\t\t\t&& fragment === this.fragment) {\n\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new Uri(scheme, authority, path, query, fragment);\n\t}\n\n\t// ---- parse & validate ------------------------\n\n\t/**\n\t * Creates a new URI from a string, e.g. `http://www.example.com/some/path`,\n\t * `file:///usr/home`, or `scheme:with/path`.\n\t *\n\t * @param value A string which represents an URI (see `URI#toString`).\n\t */\n\tstatic parse(value: string, _strict: boolean = false): URI {\n\t\tconst match = _regexp.exec(value);\n\t\tif (!match) {\n\t\t\treturn new Uri(_empty, _empty, _empty, _empty, _empty);\n\t\t}\n\t\treturn new Uri(\n\t\t\tmatch[2] || _empty,\n\t\t\tpercentDecode(match[4] || _empty),\n\t\t\tpercentDecode(match[5] || _empty),\n\t\t\tpercentDecode(match[7] || _empty),\n\t\t\tpercentDecode(match[9] || _empty),\n\t\t\t_strict\n\t\t);\n\t}\n\n\t/**\n\t * Creates a new URI from a file system path, e.g. `c:\\my\\files`,\n\t * `/usr/home`, or `\\\\server\\share\\some\\path`.\n\t *\n\t * The *difference* between `URI#parse` and `URI#file` is that the latter treats the argument\n\t * as path, not as stringified-uri. E.g. `URI.file(path)` is **not the same as**\n\t * `URI.parse('file://' + path)` because the path might contain characters that are\n\t * interpreted (# and ?). See the following sample:\n\t * ```ts\n\tconst good = URI.file('/coding/c#/project1');\n\tgood.scheme === 'file';\n\tgood.path === '/coding/c#/project1';\n\tgood.fragment === '';\n\tconst bad = URI.parse('file://' + '/coding/c#/project1');\n\tbad.scheme === 'file';\n\tbad.path === '/coding/c'; // path is now broken\n\tbad.fragment === '/project1';\n\t```\n\t *\n\t * @param path A file system path (see `URI#fsPath`)\n\t */\n\tstatic file(path: string): URI {\n\n\t\tlet authority = _empty;\n\n\t\t// normalize to fwd-slashes on windows,\n\t\t// on other systems bwd-slashes are valid\n\t\t// filename character, eg /f\\oo/ba\\r.txt\n\t\tif (isWindows) {\n\t\t\tpath = path.replace(/\\\\/g, _slash);\n\t\t}\n\n\t\t// check for authority as used in UNC shares\n\t\t// or use the path as given\n\t\tif (path[0] === _slash && path[1] === _slash) {\n\t\t\tconst idx = path.indexOf(_slash, 2);\n\t\t\tif (idx === -1) {\n\t\t\t\tauthority = path.substring(2);\n\t\t\t\tpath = _slash;\n\t\t\t} else {\n\t\t\t\tauthority = path.substring(2, idx);\n\t\t\t\tpath = path.substring(idx) || _slash;\n\t\t\t}\n\t\t}\n\n\t\treturn new Uri('file', authority, path, _empty, _empty);\n\t}\n\n\tstatic from(components: { scheme: string; authority?: string; path?: string; query?: string; fragment?: string }): URI {\n\t\tconst result = new Uri(\n\t\t\tcomponents.scheme,\n\t\t\tcomponents.authority,\n\t\t\tcomponents.path,\n\t\t\tcomponents.query,\n\t\t\tcomponents.fragment,\n\t\t);\n\t\t_validateUri(result, true);\n\t\treturn result;\n\t}\n\n\t// ---- printing/externalize ---------------------------\n\n\t/**\n\t * Creates a string representation for this URI. It's guaranteed that calling\n\t * `URI.parse` with the result of this function creates an URI which is equal\n\t * to this URI.\n\t *\n\t * * The result shall *not* be used for display purposes but for externalization or transport.\n\t * * The result will be encoded using the percentage encoding and encoding happens mostly\n\t * ignore the scheme-specific encoding rules.\n\t *\n\t * @param skipEncoding Do not encode the result, default is `false`\n\t */\n\ttoString(skipEncoding: boolean = false): string {\n\t\treturn _asFormatted(this, skipEncoding);\n\t}\n\n\ttoJSON(): UriComponents {\n\t\treturn this;\n\t}\n\n\tstatic revive(data: UriComponents | URI): URI;\n\tstatic revive(data: UriComponents | URI | undefined): URI | undefined;\n\tstatic revive(data: UriComponents | URI | null): URI | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null {\n\t\tif (!data) {\n\t\t\treturn <any>data;\n\t\t} else if (data instanceof URI) {\n\t\t\treturn data;\n\t\t} else {\n\t\t\tconst result = new Uri(data);\n\t\t\tresult._formatted = (<UriState>data).external;\n\t\t\tresult._fsPath = (<UriState>data)._sep === _pathSepMarker ? (<UriState>data).fsPath : null;\n\t\t\treturn result;\n\t\t}\n\t}\n}\n\nexport interface UriComponents {\n\tscheme: string;\n\tauthority: string;\n\tpath: string;\n\tquery: string;\n\tfragment: string;\n}\n\ninterface UriState extends UriComponents {\n\t$mid: number;\n\texternal: string;\n\tfsPath: string;\n\t_sep: 1 | undefined;\n}\n\nconst _pathSepMarker = isWindows ? 1 : undefined;\n\n// This class exists so that URI is compatible with vscode.Uri (API).\nclass Uri extends URI {\n\n\t_formatted: string | null = null;\n\t_fsPath: string | null = null;\n\n\toverride get fsPath(): string {\n\t\tif (!this._fsPath) {\n\t\t\tthis._fsPath = uriToFsPath(this, false);\n\t\t}\n\t\treturn this._fsPath;\n\t}\n\n\toverride toString(skipEncoding: boolean = false): string {\n\t\tif (!skipEncoding) {\n\t\t\tif (!this._formatted) {\n\t\t\t\tthis._formatted = _asFormatted(this, false);\n\t\t\t}\n\t\t\treturn this._formatted;\n\t\t} else {\n\t\t\t// we don't cache that\n\t\t\treturn _asFormatted(this, true);\n\t\t}\n\t}\n\n\toverride toJSON(): UriComponents {\n\t\tconst res = <UriState>{\n\t\t\t$mid: 1\n\t\t};\n\t\t// cached state\n\t\tif (this._fsPath) {\n\t\t\tres.fsPath = this._fsPath;\n\t\t\tres._sep = _pathSepMarker;\n\t\t}\n\t\tif (this._formatted) {\n\t\t\tres.external = this._formatted;\n\t\t}\n\t\t// uri components\n\t\tif (this.path) {\n\t\t\tres.path = this.path;\n\t\t}\n\t\tif (this.scheme) {\n\t\t\tres.scheme = this.scheme;\n\t\t}\n\t\tif (this.authority) {\n\t\t\tres.authority = this.authority;\n\t\t}\n\t\tif (this.query) {\n\t\t\tres.query = this.query;\n\t\t}\n\t\tif (this.fragment) {\n\t\t\tres.fragment = this.fragment;\n\t\t}\n\t\treturn res;\n\t}\n}\n\n// reserved characters: https://tools.ietf.org/html/rfc3986#section-2.2\nconst encodeTable: { [ch: number]: string } = {\n\t[CharCode.Colon]: '%3A', // gen-delims\n\t[CharCode.Slash]: '%2F',\n\t[CharCode.QuestionMark]: '%3F',\n\t[CharCode.Hash]: '%23',\n\t[CharCode.OpenSquareBracket]: '%5B',\n\t[CharCode.CloseSquareBracket]: '%5D',\n\t[CharCode.AtSign]: '%40',\n\n\t[CharCode.ExclamationMark]: '%21', // sub-delims\n\t[CharCode.DollarSign]: '%24',\n\t[CharCode.Ampersand]: '%26',\n\t[CharCode.SingleQuote]: '%27',\n\t[CharCode.OpenParen]: '%28',\n\t[CharCode.CloseParen]: '%29',\n\t[CharCode.Asterisk]: '%2A',\n\t[CharCode.Plus]: '%2B',\n\t[CharCode.Comma]: '%2C',\n\t[CharCode.Semicolon]: '%3B',\n\t[CharCode.Equals]: '%3D',\n\n\t[CharCode.Space]: '%20',\n};\n\nfunction encodeURIComponentFast(uriComponent: string, isPath: boolean, isAuthority: boolean): string {\n\tlet res: string | undefined = undefined;\n\tlet nativeEncodePos = -1;\n\n\tfor (let pos = 0; pos < uriComponent.length; pos++) {\n\t\tconst code = uriComponent.charCodeAt(pos);\n\n\t\t// unreserved characters: https://tools.ietf.org/html/rfc3986#section-2.3\n\t\tif (\n\t\t\t(code >= CharCode.a && code <= CharCode.z)\n\t\t\t|| (code >= CharCode.A && code <= CharCode.Z)\n\t\t\t|| (code >= CharCode.Digit0 && code <= CharCode.Digit9)\n\t\t\t|| code === CharCode.Dash\n\t\t\t|| code === CharCode.Period\n\t\t\t|| code === CharCode.Underline\n\t\t\t|| code === CharCode.Tilde\n\t\t\t|| (isPath && code === CharCode.Slash)\n\t\t\t|| (isAuthority && code === CharCode.OpenSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.CloseSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.Colon)\n\t\t) {\n\t\t\t// check if we are delaying native encode\n\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\tnativeEncodePos = -1;\n\t\t\t}\n\t\t\t// check if we write into a new string (by default we try to return the param)\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += uriComponent.charAt(pos);\n\t\t\t}\n\n\t\t} else {\n\t\t\t// encoding needed, we need to allocate a new string\n\t\t\tif (res === undefined) {\n\t\t\t\tres = uriComponent.substr(0, pos);\n\t\t\t}\n\n\t\t\t// check with default table first\n\t\t\tconst escaped = encodeTable[code];\n\t\t\tif (escaped !== undefined) {\n\n\t\t\t\t// check if we are delaying native encode\n\t\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\t\tnativeEncodePos = -1;\n\t\t\t\t}\n\n\t\t\t\t// append escaped variant to result\n\t\t\t\tres += escaped;\n\n\t\t\t} else if (nativeEncodePos === -1) {\n\t\t\t\t// use native encode only when needed\n\t\t\t\tnativeEncodePos = pos;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (nativeEncodePos !== -1) {\n\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos));\n\t}\n\n\treturn res !== undefined ? res : uriComponent;\n}\n\nfunction encodeURIComponentMinimal(path: string): string {\n\tlet res: string | undefined = undefined;\n\tfor (let pos = 0; pos < path.length; pos++) {\n\t\tconst code = path.charCodeAt(pos);\n\t\tif (code === CharCode.Hash || code === CharCode.QuestionMark) {\n\t\t\tif (res === undefined) {\n\t\t\t\tres = path.substr(0, pos);\n\t\t\t}\n\t\t\tres += encodeTable[code];\n\t\t} else {\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += path[pos];\n\t\t\t}\n\t\t}\n\t}\n\treturn res !== undefined ? res : path;\n}\n\n/**\n * Compute `fsPath` for the given uri\n */\nexport function uriToFsPath(uri: URI, keepDriveLetterCasing: boolean): string {\n\n\tlet value: string;\n\tif (uri.authority && uri.path.length > 1 && uri.scheme === 'file') {\n\t\t// unc path: file://shares/c$/far/boo\n\t\tvalue = `//${uri.authority}${uri.path}`;\n\t} else if (\n\t\turi.path.charCodeAt(0) === CharCode.Slash\n\t\t&& (uri.path.charCodeAt(1) >= CharCode.A && uri.path.charCodeAt(1) <= CharCode.Z || uri.path.charCodeAt(1) >= CharCode.a && uri.path.charCodeAt(1) <= CharCode.z)\n\t\t&& uri.path.charCodeAt(2) === CharCode.Colon\n\t) {\n\t\tif (!keepDriveLetterCasing) {\n\t\t\t// windows drive letter: file:///c:/far/boo\n\t\t\tvalue = uri.path[1].toLowerCase() + uri.path.substr(2);\n\t\t} else {\n\t\t\tvalue = uri.path.substr(1);\n\t\t}\n\t} else {\n\t\t// other path\n\t\tvalue = uri.path;\n\t}\n\tif (isWindows) {\n\t\tvalue = value.replace(/\\//g, '\\\\');\n\t}\n\treturn value;\n}\n\n/**\n * Create the external version of a uri\n */\nfunction _asFormatted(uri: URI, skipEncoding: boolean): string {\n\n\tconst encoder = !skipEncoding\n\t\t? encodeURIComponentFast\n\t\t: encodeURIComponentMinimal;\n\n\tlet res = '';\n\tlet { scheme, authority, path, query, fragment } = uri;\n\tif (scheme) {\n\t\tres += scheme;\n\t\tres += ':';\n\t}\n\tif (authority || scheme === 'file') {\n\t\tres += _slash;\n\t\tres += _slash;\n\t}\n\tif (authority) {\n\t\tlet idx = authority.indexOf('@');\n\t\tif (idx !== -1) {\n\t\t\t// <user>@<auth>\n\t\t\tconst userinfo = authority.substr(0, idx);\n\t\t\tauthority = authority.substr(idx + 1);\n\t\t\tidx = userinfo.lastIndexOf(':');\n\t\t\tif (idx === -1) {\n\t\t\t\tres += encoder(userinfo, false, false);\n\t\t\t} else {\n\t\t\t\t// <user>:<pass>@<auth>\n\t\t\t\tres += encoder(userinfo.substr(0, idx), false, false);\n\t\t\t\tres += ':';\n\t\t\t\tres += encoder(userinfo.substr(idx + 1), false, true);\n\t\t\t}\n\t\t\tres += '@';\n\t\t}\n\t\tauthority = authority.toLowerCase();\n\t\tidx = authority.lastIndexOf(':');\n\t\tif (idx === -1) {\n\t\t\tres += encoder(authority, false, true);\n\t\t} else {\n\t\t\t// <auth>:<port>\n\t\t\tres += encoder(authority.substr(0, idx), false, true);\n\t\t\tres += authority.substr(idx);\n\t\t}\n\t}\n\tif (path) {\n\t\t// lower-case windows drive letters in /C:/fff or C:/fff\n\t\tif (path.length >= 3 && path.charCodeAt(0) === CharCode.Slash && path.charCodeAt(2) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(1);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `/${String.fromCharCode(code + 32)}:${path.substr(3)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t} else if (path.length >= 2 && path.charCodeAt(1) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(0);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `${String.fromCharCode(code + 32)}:${path.substr(2)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t}\n\t\t// encode the rest of the path\n\t\tres += encoder(path, true, false);\n\t}\n\tif (query) {\n\t\tres += '?';\n\t\tres += encoder(query, false, false);\n\t}\n\tif (fragment) {\n\t\tres += '#';\n\t\tres += !skipEncoding ? encodeURIComponentFast(fragment, false, false) : fragment;\n\t}\n\treturn res;\n}\n\n// --- decode\n\nfunction decodeURIComponentGraceful(str: string): string {\n\ttry {\n\t\treturn decodeURIComponent(str);\n\t} catch {\n\t\tif (str.length > 3) {\n\t\t\treturn str.substr(0, 3) + decodeURIComponentGraceful(str.substr(3));\n\t\t} else {\n\t\t\treturn str;\n\t\t}\n\t}\n}\n\nconst _rEncodedAsHex = /(%[0-9A-Za-z][0-9A-Za-z])+/g;\n\nfunction percentDecode(str: string): string {\n\tif (!str.match(_rEncodedAsHex)) {\n\t\treturn str;\n\t}\n\treturn str.replace(_rEncodedAsHex, (match) => decodeURIComponentGraceful(match));\n}\n\n/**\n * Mapped-type that replaces all occurrences of URI with UriComponents\n */\nexport type UriDto<T> = { [K in keyof T]: T[K] extends URI\n\t? UriComponents\n\t: UriDto<T[K]> };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n'use strict';\n\nimport { CharCode } from './charCode';\nimport { URI } from './uri';\nimport * as nodePath from 'path';\n\nconst posixPath = nodePath.posix || nodePath;\nconst slash = '/';\n\nexport namespace Utils {\n\n    /**\n     * Joins one or more input paths to the path of URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved.\n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are preserved.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to be joined with the path of URI.\n     * @returns A URI with the joined path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function joinPath(uri: URI, ...paths: string[]): URI {\n        return uri.with({ path: posixPath.join(uri.path, ...paths) });\n    }\n\n\n    /**\n     * Resolves one or more paths against the path of a URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved. \n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are removed.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to resolve against the path of URI.\n     * @returns A URI with the resolved path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function resolvePath(uri: URI, ...paths: string[]): URI {\n        let path = uri.path; \n        let slashAdded = false;\n        if (path[0] !== slash) {\n            path = slash + path; // make the path abstract: for posixPath.resolve the first segments has to be absolute or cwd is used.\n            slashAdded = true;\n        }\n        let resolvedPath = posixPath.resolve(path, ...paths);\n        if (slashAdded && resolvedPath[0] === slash && !uri.authority) {\n            resolvedPath = resolvedPath.substring(1);\n        }\n        return uri.with({ path: resolvedPath });\n    }\n\n    /**\n     * Returns a URI where the path is the directory name of the input uri, similar to the Unix dirname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The orignal URI is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The last segment of the URIs path.\n     */\n    export function dirname(uri: URI): URI {\n        if (uri.path.length === 0 || uri.path === slash) {\n            return uri;\n        }\n        let path = posixPath.dirname(uri.path);\n        if (path.length === 1 && path.charCodeAt(0) === CharCode.Period) {\n            path = '';\n        }\n        return uri.with({ path });\n    }\n\n    /**\n     * Returns the last segment of the path of a URI, similar to the Unix basename command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The base name of the URIs path.\n     */\n    export function basename(uri: URI): string {\n        return posixPath.basename(uri.path);\n    }\n\n    /**\n     * Returns the extension name of the path of a URI, similar to the Unix extname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The extension name of the URIs path.\n     */\n    export function extname(uri: URI): string {\n        return posixPath.extname(uri.path);\n    }\n}", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor marker severity to an LSP diagnostic severity.\n *\n * @param severity\n *   The Monaco marker severity to convert.\n * @returns\n *   The marker severity as an LSP diagnostic severity.\n */\nexport function fromMarkerSeverity(severity: monaco.MarkerSeverity): lsp.DiagnosticSeverity {\n  if (severity === (1 satisfies monaco.MarkerSeverity.Hint)) {\n    return 4 satisfies typeof lsp.DiagnosticSeverity.Hint\n  }\n  if (severity === (2 satisfies monaco.MarkerSeverity.Info)) {\n    return 3 satisfies typeof lsp.DiagnosticSeverity.Information\n  }\n  if (severity === (4 satisfies monaco.MarkerSeverity.Warning)) {\n    return 2 satisfies typeof lsp.DiagnosticSeverity.Warning\n  }\n  return 1 satisfies typeof lsp.DiagnosticSeverity.Error\n}\n\n/**\n * Convert an LSP diagnostic severity to a Monaco editor marker severity.\n *\n * @param severity\n *   The LSP diagnostic severity to convert.\n * @returns\n *   The diagnostic severity as Monaco editor marker severity.\n */\nexport function toMarkerSeverity(severity: lsp.DiagnosticSeverity): monaco.MarkerSeverity {\n  if (severity === (4 satisfies typeof lsp.DiagnosticSeverity.Hint)) {\n    return 1 satisfies monaco.MarkerSeverity.Hint\n  }\n  if (severity === (3 satisfies typeof lsp.DiagnosticSeverity.Information)) {\n    return 2 satisfies monaco.MarkerSeverity.Info\n  }\n  if (severity === (2 satisfies typeof lsp.DiagnosticSeverity.Warning)) {\n    return 4 satisfies monaco.MarkerSeverity.Warning\n  }\n  return 8 satisfies monaco.MarkerSeverity.Error\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor marker tag to an LSP diagnostic tag.\n *\n * @param tag\n *   The Monaco marker tag to convert.\n * @returns\n *   The marker tag as an LSP diagnostic tag.\n */\nexport function fromMarkerTag(tag: monaco.MarkerTag): lsp.DiagnosticTag {\n  return tag\n}\n\n/**\n * Convert an LSP diagnostic tag to a Monaco editor marker tag.\n *\n * @param tag\n *   The LSP diagnostic tag to convert.\n * @returns\n *   The diagnostic tag as Monaco editor marker tag.\n */\nexport function toMarkerTag(tag: lsp.DiagnosticTag): monaco.MarkerTag {\n  return tag\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor range to an LSP range.\n *\n * @param range\n *   The Monaco range to convert.\n * @returns\n *   The range as an LSP range.\n */\nexport function fromRange(range: monaco.IRange): lsp.Range {\n  return {\n    start: { line: range.startLineNumber - 1, character: range.startColumn - 1 },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  }\n}\n\n/**\n * Convert an LSP range to a Monaco editor range.\n *\n * @param range\n *   The LSP range to convert.\n * @returns\n *   The range as Monaco editor range.\n */\nexport function toRange(range: lsp.Range): monaco.IRange {\n  return {\n    startLineNumber: range.start.line + 1,\n    startColumn: range.start.character + 1,\n    endLineNumber: range.end.line + 1,\n    endColumn: range.end.character + 1\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport { from<PERSON>ang<PERSON>, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor related information to an LSP diagnostic related information.\n *\n * @param relatedInformation\n *   The Monaco related information to convert.\n * @returns\n *   The related information as an LSP diagnostic related information.\n */\nexport function fromRelatedInformation(\n  relatedInformation: monaco.editor.IRelatedInformation\n): lsp.DiagnosticRelatedInformation {\n  return {\n    location: {\n      range: from<PERSON>ange(relatedInformation),\n      uri: String(relatedInformation.resource)\n    },\n    message: relatedInformation.message\n  }\n}\n\n/**\n * Convert an LSP diagnostic related information to a Monaco editor related information.\n *\n * @param relatedInformation\n *   The LSP diagnostic related information to convert.\n * @returns\n *   The diagnostic related information as Monaco editor related information.\n */\nexport function toRelatedInformation(\n  relatedInformation: lsp.DiagnosticRelatedInformation\n): monaco.editor.IRelatedInformation {\n  return {\n    ...toRange(relatedInformation.location.range),\n    message: relatedInformation.message,\n    resource: URI.parse(relatedInformation.location.uri)\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport { fromMarkerSeverity, toMarkerSeverity } from './markerSeverity.js'\nimport { fromMarkerTag, toMarkerTag } from './markerTag.js'\nimport { fromRange, toRange } from './range.js'\nimport { fromRelatedInformation, toRelatedInformation } from './relatedInformation.js'\n\n/**\n * Convert a Monaco editor marker data to an LSP diagnostic.\n *\n * @param markerData\n *   The Monaco marker data to convert.\n * @returns\n *   The marker data as an LSP diagnostic.\n */\nexport function fromMarkerData(markerData: monaco.editor.IMarkerData): lsp.Diagnostic {\n  const diagnostic: lsp.Diagnostic = {\n    message: markerData.message,\n    range: fromRange(markerData),\n    severity: fromMarkerSeverity(markerData.severity)\n  }\n\n  if (typeof markerData.code === 'string') {\n    diagnostic.code = markerData.code\n  } else if (markerData.code != null) {\n    diagnostic.code = markerData.code.value\n    diagnostic.codeDescription = { href: String(markerData.code.target) }\n  }\n\n  if (markerData.relatedInformation) {\n    diagnostic.relatedInformation = markerData.relatedInformation.map(fromRelatedInformation)\n  }\n\n  if (markerData.tags) {\n    diagnostic.tags = markerData.tags.map(fromMarkerTag)\n  }\n\n  if (markerData.source != null) {\n    diagnostic.source = markerData.source\n  }\n\n  return diagnostic\n}\n\n/**\n * Convert an LSP diagnostic to a Monaco editor marker data.\n *\n * @param diagnostic\n *   The LSP diagnostic to convert.\n * @returns\n *   The diagnostic as Monaco editor marker data.\n */\nexport function toMarkerData(diagnostic: lsp.Diagnostic): monaco.editor.IMarkerData {\n  const markerData: monaco.editor.IMarkerData = {\n    ...toRange(diagnostic.range),\n    message: diagnostic.message,\n    severity: diagnostic.severity\n      ? toMarkerSeverity(diagnostic.severity)\n      : (8 satisfies monaco.MarkerSeverity.Error)\n  }\n\n  if (diagnostic.code != null) {\n    markerData.code =\n      diagnostic.codeDescription == null\n        ? String(diagnostic.code)\n        : { value: String(diagnostic.code), target: URI.parse(diagnostic.codeDescription.href) }\n  }\n\n  if (diagnostic.relatedInformation) {\n    markerData.relatedInformation = diagnostic.relatedInformation.map(toRelatedInformation)\n  }\n\n  if (diagnostic.tags) {\n    markerData.tags = diagnostic.tags.map(toMarkerTag)\n  }\n\n  if (diagnostic.source != null) {\n    markerData.source = diagnostic.source\n  }\n\n  return markerData\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor text edit to an LSP text edit.\n *\n * @param textEdit\n *   The Monaco text edit to convert.\n * @returns\n *   The text edit as an LSP text edit.\n */\nexport function fromTextEdit(textEdit: monaco.languages.TextEdit): lsp.TextEdit {\n  return {\n    range: fromRange(textEdit.range),\n    newText: textEdit.text\n  }\n}\n\n/**\n * Convert an LSP text edit to a Monaco editor text edit.\n *\n * @param textEdit\n *   The LSP text edit to convert.\n * @returns\n *   The text edit as Monaco editor text edit.\n */\nexport function toTextEdit(textEdit: lsp.TextEdit): monaco.languages.TextEdit {\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\ntype LSPFileEditOptions = lsp.CreateFileOptions & lsp.DeleteFileOptions & lsp.RenameFileOptions\n\n/**\n * Convert Monaco editor workspace file edit options to LSP workspace file edit options.\n *\n * @param options\n *   The Monaco workspace file edit options to convert.\n * @returns\n *   The range as LSP workspace file edit options.\n */\nexport function fromWorkspaceFileEditOptions(\n  options: monaco.languages.WorkspaceFileEditOptions\n): LSPFileEditOptions {\n  const result: LSPFileEditOptions = {}\n\n  if (options.ignoreIfExists != null) {\n    result.ignoreIfExists = options.ignoreIfExists\n  }\n  if (options.ignoreIfNotExists != null) {\n    result.ignoreIfNotExists = options.ignoreIfNotExists\n  }\n  if (options.overwrite != null) {\n    result.overwrite = options.overwrite\n  }\n  if (options.recursive != null) {\n    result.recursive = options.recursive\n  }\n\n  return result\n}\n\n/**\n * Convert LSP workspace file edit options to Monaco editor workspace file edit options.\n *\n * @param options\n *   The LSP workspace file edit options to convert.\n * @returns\n *   The workspace file edit options Monaco editor workspace file edit options.\n */\nexport function toWorkspaceFileEditOptions(\n  options: LSPFileEditOptions\n): monaco.languages.WorkspaceFileEditOptions {\n  const result: monaco.languages.WorkspaceFileEditOptions = {}\n\n  if (options.ignoreIfExists != null) {\n    result.ignoreIfExists = options.ignoreIfExists\n  }\n  if (options.ignoreIfNotExists != null) {\n    result.ignoreIfNotExists = options.ignoreIfNotExists\n  }\n  if (options.overwrite != null) {\n    result.overwrite = options.overwrite\n  }\n  if (options.recursive != null) {\n    result.recursive = options.recursive\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport {\n  fromWorkspaceFileEditOptions,\n  toWorkspaceFileEditOptions\n} from './workspaceFileEditOptions.js'\n\ntype WorkspaceFileEdit = lsp.CreateFile | lsp.DeleteFile | lsp.RenameFile\n\n/**\n * Convert Monaco editor workspace file edit options to LSP workspace file edit options.\n *\n * @param workspaceFileEdit\n *   The Monaco workspace file edit options to convert.\n * @returns\n *   The range as LSP workspace file edit options.\n */\nexport function fromWorkspaceFileEdit(\n  workspaceFileEdit: monaco.languages.IWorkspaceFileEdit\n): WorkspaceFileEdit {\n  let result: WorkspaceFileEdit\n\n  if (workspaceFileEdit.oldResource) {\n    result = workspaceFileEdit.newResource\n      ? {\n          kind: 'rename',\n          oldUri: String(workspaceFileEdit.oldResource),\n          newUri: String(workspaceFileEdit.newResource)\n        }\n      : {\n          kind: 'delete',\n          uri: String(workspaceFileEdit.oldResource)\n        }\n  } else if (workspaceFileEdit.newResource) {\n    result = {\n      kind: 'create',\n      uri: String(workspaceFileEdit.newResource)\n    }\n  } else {\n    throw new Error('Could not convert workspace file edit to language server type', {\n      cause: workspaceFileEdit\n    })\n  }\n\n  if (workspaceFileEdit.options) {\n    result.options = fromWorkspaceFileEditOptions(workspaceFileEdit.options)\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP workspace file edit to a Monaco editor workspace file edit.\n *\n * @param workspaceFileEdit\n *   The LSP workspace file edit to convert.\n * @returns\n *   The workspace file edit options Monaco editor workspace file edit options.\n */\nexport function toWorkspaceFileEdit(\n  workspaceFileEdit: WorkspaceFileEdit\n): monaco.languages.IWorkspaceFileEdit {\n  const result: monaco.languages.IWorkspaceFileEdit =\n    workspaceFileEdit.kind === 'create'\n      ? { newResource: URI.parse(workspaceFileEdit.uri) }\n      : workspaceFileEdit.kind === 'delete'\n        ? { oldResource: URI.parse(workspaceFileEdit.uri) }\n        : {\n            oldResource: URI.parse(workspaceFileEdit.oldUri),\n            newResource: URI.parse(workspaceFileEdit.newUri)\n          }\n\n  if (workspaceFileEdit.options) {\n    result.options = toWorkspaceFileEditOptions(workspaceFileEdit.options)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport { fromTextEdit, toTextEdit } from './textEdit.js'\nimport { fromWorkspaceFileEdit, toWorkspaceFileEdit } from './workspaceFileEdit.js'\n\n/**\n * Convert a Monaco editor workspace edit to an LSP workspace edit.\n *\n * @param workspaceEdit\n *   The Monaco workspace edit to convert.\n * @returns\n *   The workspace edit as an LSP workspace edit.\n */\nexport function fromWorkspaceEdit(\n  workspaceEdit: monaco.languages.WorkspaceEdit\n): lsp.WorkspaceEdit {\n  const changes: Record<string, lsp.TextEdit[]> = {}\n  const documentChanges: (\n    | lsp.CreateFile\n    | lsp.DeleteFile\n    | lsp.RenameFile\n    | lsp.TextDocumentEdit\n  )[] = []\n  const textDocumentMap = new Map<string, Map<number, lsp.TextEdit[]>>()\n\n  for (const edit of workspaceEdit.edits) {\n    if ('resource' in edit) {\n      const uri = String(edit.resource)\n      if (edit.versionId == null) {\n        changes[uri] = []\n        changes[uri].push(fromTextEdit(edit.textEdit))\n      } else {\n        let versionMap = textDocumentMap.get(uri)\n        if (!versionMap) {\n          versionMap = new Map()\n          textDocumentMap.set(uri, versionMap)\n        }\n        let textDocumentEdits = versionMap.get(edit.versionId)\n        if (!textDocumentEdits) {\n          textDocumentEdits = []\n          versionMap.set(edit.versionId, textDocumentEdits)\n          documentChanges.push({\n            textDocument: { uri, version: edit.versionId },\n            edits: textDocumentEdits\n          })\n        }\n        textDocumentEdits.push(fromTextEdit(edit.textEdit))\n      }\n    } else {\n      documentChanges.push(fromWorkspaceFileEdit(edit))\n    }\n  }\n\n  return {\n    changes,\n    documentChanges\n  }\n}\n\n/**\n * Convert an LSP text edit and uri to a Monaco editor workspace text edit.\n *\n * @param textEdit\n *   The LSP text edit to convert.\n * @param uri\n *   The uri of the workspace text edit.\n * @param versionId\n *   The version ID of the workspace text edit.\n * @returns\n *   The text edit and uri as Monaco editor workspace text edit.\n */\nfunction toWorkspaceTextEdit(\n  textEdit: lsp.TextEdit,\n  uri: string,\n  versionId?: number\n): monaco.languages.IWorkspaceTextEdit {\n  return {\n    resource: URI.parse(uri),\n    versionId,\n    textEdit: toTextEdit(textEdit)\n  }\n}\n\n/**\n * Convert an LSP workspace edit to a Monaco editor workspace edit.\n *\n * @param workspaceEdit\n *   The LSP workspace edit to convert.\n * @returns\n *   The workspace edit as Monaco editor workspace edit.\n */\nexport function toWorkspaceEdit(workspaceEdit: lsp.WorkspaceEdit): monaco.languages.WorkspaceEdit {\n  const edits: monaco.languages.WorkspaceEdit['edits'] = []\n\n  if (workspaceEdit.changes) {\n    for (const [uri, textEdits] of Object.entries(workspaceEdit.changes)) {\n      for (const textEdit of textEdits) {\n        edits.push(toWorkspaceTextEdit(textEdit, uri))\n      }\n    }\n  }\n\n  if (workspaceEdit.documentChanges) {\n    for (const documentChange of workspaceEdit.documentChanges) {\n      if ('textDocument' in documentChange) {\n        for (const textEdit of documentChange.edits) {\n          edits.push(\n            toWorkspaceTextEdit(\n              textEdit,\n              documentChange.textDocument.uri,\n              documentChange.textDocument.version ?? undefined\n            )\n          )\n        }\n      } else {\n        edits.push(toWorkspaceFileEdit(documentChange))\n      }\n    }\n  }\n\n  return {\n    edits\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromMarkerData, toMarkerData } from './markerData.js'\nimport { fromWorkspaceEdit, toWorkspaceEdit } from './workspaceEdit.js'\n\n/**\n * Convert a Monaco editor code action to an LSP code action.\n *\n * @param codeAction\n *   The Monaco code action to convert.\n * @returns\n *   The code action as an LSP code action.\n */\nexport function fromCodeAction(codeAction: monaco.languages.CodeAction): lsp.CodeAction {\n  const result: lsp.CodeAction = {\n    title: codeAction.title\n  }\n\n  if (codeAction.diagnostics) {\n    result.diagnostics = codeAction.diagnostics.map(fromMarkerData)\n  }\n\n  if (codeAction.disabled != null) {\n    result.disabled = { reason: codeAction.disabled }\n  }\n\n  if (codeAction.edit) {\n    result.edit = fromWorkspaceEdit(codeAction.edit)\n  }\n\n  if (codeAction.isPreferred != null) {\n    result.isPreferred = codeAction.isPreferred\n  }\n\n  if (codeAction.kind) {\n    result.kind = codeAction.kind\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP code action to a Monaco editor code action.\n *\n * @param codeAction\n *   The LSP code action to convert.\n * @returns\n *   The code action as Monaco editor code action.\n */\nexport function toCodeAction(codeAction: lsp.CodeAction): monaco.languages.CodeAction {\n  const result: monaco.languages.CodeAction = {\n    title: codeAction.title,\n    isPreferred: codeAction.isPreferred\n  }\n\n  if (codeAction.diagnostics) {\n    result.diagnostics = codeAction.diagnostics.map(toMarkerData)\n  }\n\n  if (codeAction.disabled) {\n    result.disabled = codeAction.disabled.reason\n  }\n\n  if (codeAction.edit) {\n    result.edit = toWorkspaceEdit(codeAction.edit)\n  }\n\n  if (codeAction.isPreferred != null) {\n    result.isPreferred = codeAction.isPreferred\n  }\n\n  if (codeAction.kind) {\n    result.kind = codeAction.kind\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor code action trigger type to an LSP completion item kind.\n *\n * @param type\n *   The Monaco code action trigger type to convert.\n * @returns\n *   The code action trigger type as an LSP completion item kind.\n */\nexport function fromCodeActionTriggerType(\n  type: monaco.languages.CodeActionTriggerType\n): lsp.CodeActionTriggerKind {\n  return type\n}\n\n/**\n * Convert an LSP completion item kind to a Monaco editor code action trigger type.\n *\n * @param kind\n *   The LSP completion item kind to convert.\n * @returns\n *   The completion item kind as Monaco editor code action trigger type.\n */\nexport function toCodeActionTriggerType(\n  kind: lsp.CodeActionTriggerKind\n): monaco.languages.CodeActionTriggerType {\n  return kind\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor command to an LSP command.\n *\n * @param command\n *   The Monaco command to convert.\n * @returns\n *   The command as an LSP command.\n */\nexport function fromCommand(command: monaco.languages.Command): lsp.Command {\n  const result: lsp.Command = {\n    title: command.title,\n    command: command.id\n  }\n\n  if (command.arguments) {\n    result.arguments = command.arguments\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP command to a Monaco editor command.\n *\n * @param command\n *   The LSP command to convert.\n * @returns\n *   The command as Monaco editor command.\n */\nexport function toCommand(command: lsp.Command): monaco.languages.Command {\n  const result: monaco.languages.Command = {\n    title: command.title,\n    id: command.command\n  }\n\n  if (command.arguments) {\n    result.arguments = command.arguments\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor completion item kind to an LSP completion item kind.\n *\n * @param kind\n *   The Monaco completion item kind to convert.\n * @returns\n *   The completion item kind as an LSP completion item kind.\n */\nexport function fromCompletionItemKind(\n  kind: monaco.languages.CompletionItemKind\n): lsp.CompletionItemKind | undefined {\n  if (kind === (18 satisfies monaco.languages.CompletionItemKind.Text)) {\n    return 1 satisfies typeof lsp.CompletionItemKind.Text\n  }\n  if (kind === (0 satisfies monaco.languages.CompletionItemKind.Method)) {\n    return 2 satisfies typeof lsp.CompletionItemKind.Method\n  }\n  if (kind === (1 satisfies monaco.languages.CompletionItemKind.Function)) {\n    return 3 satisfies typeof lsp.CompletionItemKind.Function\n  }\n  if (kind === (2 satisfies monaco.languages.CompletionItemKind.Constructor)) {\n    return 4 satisfies typeof lsp.CompletionItemKind.Constructor\n  }\n  if (kind === (3 satisfies monaco.languages.CompletionItemKind.Field)) {\n    return 5 satisfies typeof lsp.CompletionItemKind.Field\n  }\n  if (kind === (4 satisfies monaco.languages.CompletionItemKind.Variable)) {\n    return 6 satisfies typeof lsp.CompletionItemKind.Variable\n  }\n  if (kind === (5 satisfies monaco.languages.CompletionItemKind.Class)) {\n    return 7 satisfies typeof lsp.CompletionItemKind.Class\n  }\n  if (kind === (7 satisfies monaco.languages.CompletionItemKind.Interface)) {\n    return 8 satisfies typeof lsp.CompletionItemKind.Interface\n  }\n  if (kind === (8 satisfies monaco.languages.CompletionItemKind.Module)) {\n    return 9 satisfies typeof lsp.CompletionItemKind.Module\n  }\n  if (kind === (9 satisfies monaco.languages.CompletionItemKind.Property)) {\n    return 10 satisfies typeof lsp.CompletionItemKind.Property\n  }\n  if (kind === (12 satisfies monaco.languages.CompletionItemKind.Unit)) {\n    return 11 satisfies typeof lsp.CompletionItemKind.Unit\n  }\n  if (kind === (13 satisfies monaco.languages.CompletionItemKind.Value)) {\n    return 12 satisfies typeof lsp.CompletionItemKind.Value\n  }\n  if (kind === (15 satisfies monaco.languages.CompletionItemKind.Enum)) {\n    return 13 satisfies typeof lsp.CompletionItemKind.Enum\n  }\n  if (kind === (17 satisfies monaco.languages.CompletionItemKind.Keyword)) {\n    return 14 satisfies typeof lsp.CompletionItemKind.Keyword\n  }\n  if (kind === (27 satisfies monaco.languages.CompletionItemKind.Snippet)) {\n    return 15 satisfies typeof lsp.CompletionItemKind.Snippet\n  }\n  if (kind === (19 satisfies monaco.languages.CompletionItemKind.Color)) {\n    return 16 satisfies typeof lsp.CompletionItemKind.Color\n  }\n  if (kind === (20 satisfies monaco.languages.CompletionItemKind.File)) {\n    return 17 satisfies typeof lsp.CompletionItemKind.File\n  }\n  if (kind === (21 satisfies monaco.languages.CompletionItemKind.Reference)) {\n    return 18 satisfies typeof lsp.CompletionItemKind.Reference\n  }\n  if (kind === (23 satisfies monaco.languages.CompletionItemKind.Folder)) {\n    return 19 satisfies typeof lsp.CompletionItemKind.Folder\n  }\n  if (kind === (16 satisfies monaco.languages.CompletionItemKind.EnumMember)) {\n    return 20 satisfies typeof lsp.CompletionItemKind.EnumMember\n  }\n  if (kind === (14 satisfies monaco.languages.CompletionItemKind.Constant)) {\n    return 21 satisfies typeof lsp.CompletionItemKind.Constant\n  }\n  if (kind === (6 satisfies monaco.languages.CompletionItemKind.Struct)) {\n    return 22 satisfies typeof lsp.CompletionItemKind.Struct\n  }\n  if (kind === (10 satisfies monaco.languages.CompletionItemKind.Event)) {\n    return 23 satisfies typeof lsp.CompletionItemKind.Event\n  }\n  if (kind === (11 satisfies monaco.languages.CompletionItemKind.Operator)) {\n    return 24 satisfies typeof lsp.CompletionItemKind.Operator\n  }\n  if (kind === (24 satisfies monaco.languages.CompletionItemKind.TypeParameter)) {\n    return 25 satisfies typeof lsp.CompletionItemKind.TypeParameter\n  }\n}\n\n/**\n * Convert an LSP completion item kind to a Monaco editor completion item kind.\n *\n * @param kind\n *   The LSP completion item kind to convert.\n * @returns\n *   The completion item kind as Monaco editor completion item kind.\n */\nexport function toCompletionItemKind(\n  kind: lsp.CompletionItemKind\n): monaco.languages.CompletionItemKind {\n  if (kind === (1 satisfies typeof lsp.CompletionItemKind.Text)) {\n    return 18 satisfies monaco.languages.CompletionItemKind.Text\n  }\n  if (kind === (2 satisfies typeof lsp.CompletionItemKind.Method)) {\n    return 0 satisfies monaco.languages.CompletionItemKind.Method\n  }\n  if (kind === (3 satisfies typeof lsp.CompletionItemKind.Function)) {\n    return 1 satisfies monaco.languages.CompletionItemKind.Function\n  }\n  if (kind === (4 satisfies typeof lsp.CompletionItemKind.Constructor)) {\n    return 2 satisfies monaco.languages.CompletionItemKind.Constructor\n  }\n  if (kind === (5 satisfies typeof lsp.CompletionItemKind.Field)) {\n    return 3 satisfies monaco.languages.CompletionItemKind.Field\n  }\n  if (kind === (6 satisfies typeof lsp.CompletionItemKind.Variable)) {\n    return 4 satisfies monaco.languages.CompletionItemKind.Variable\n  }\n  if (kind === (7 satisfies typeof lsp.CompletionItemKind.Class)) {\n    return 5 satisfies monaco.languages.CompletionItemKind.Class\n  }\n  if (kind === (8 satisfies typeof lsp.CompletionItemKind.Interface)) {\n    return 7 satisfies monaco.languages.CompletionItemKind.Interface\n  }\n  if (kind === (9 satisfies typeof lsp.CompletionItemKind.Module)) {\n    return 8 satisfies monaco.languages.CompletionItemKind.Module\n  }\n  if (kind === (10 satisfies typeof lsp.CompletionItemKind.Property)) {\n    return 9 satisfies monaco.languages.CompletionItemKind.Property\n  }\n  if (kind === (11 satisfies typeof lsp.CompletionItemKind.Unit)) {\n    return 12 satisfies monaco.languages.CompletionItemKind.Unit\n  }\n  if (kind === (12 satisfies typeof lsp.CompletionItemKind.Value)) {\n    return 13 satisfies monaco.languages.CompletionItemKind.Value\n  }\n  if (kind === (13 satisfies typeof lsp.CompletionItemKind.Enum)) {\n    return 15 satisfies monaco.languages.CompletionItemKind.Enum\n  }\n  if (kind === (14 satisfies typeof lsp.CompletionItemKind.Keyword)) {\n    return 17 satisfies monaco.languages.CompletionItemKind.Keyword\n  }\n  if (kind === (15 satisfies typeof lsp.CompletionItemKind.Snippet)) {\n    return 27 satisfies monaco.languages.CompletionItemKind.Snippet\n  }\n  if (kind === (16 satisfies typeof lsp.CompletionItemKind.Color)) {\n    return 19 satisfies monaco.languages.CompletionItemKind.Color\n  }\n  if (kind === (17 satisfies typeof lsp.CompletionItemKind.File)) {\n    return 20 satisfies monaco.languages.CompletionItemKind.File\n  }\n  if (kind === (18 satisfies typeof lsp.CompletionItemKind.Reference)) {\n    return 21 satisfies monaco.languages.CompletionItemKind.Reference\n  }\n  if (kind === (19 satisfies typeof lsp.CompletionItemKind.Folder)) {\n    return 23 satisfies monaco.languages.CompletionItemKind.Folder\n  }\n  if (kind === (20 satisfies typeof lsp.CompletionItemKind.EnumMember)) {\n    return 16 satisfies monaco.languages.CompletionItemKind.EnumMember\n  }\n  if (kind === (21 satisfies typeof lsp.CompletionItemKind.Constant)) {\n    return 14 satisfies monaco.languages.CompletionItemKind.Constant\n  }\n  if (kind === (22 satisfies typeof lsp.CompletionItemKind.Struct)) {\n    return 6 satisfies monaco.languages.CompletionItemKind.Struct\n  }\n  if (kind === (23 satisfies typeof lsp.CompletionItemKind.Event)) {\n    return 10 satisfies monaco.languages.CompletionItemKind.Event\n  }\n  if (kind === (24 satisfies typeof lsp.CompletionItemKind.Operator)) {\n    return 11 satisfies monaco.languages.CompletionItemKind.Operator\n  }\n\n  // Kind === 25\n  return 24 satisfies monaco.languages.CompletionItemKind.TypeParameter\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor completion item tag to an LSP completion item tag.\n *\n * @param tag\n *   The Monaco completion item tag to convert.\n * @returns\n *   The completion item tag as an LSP completion item tag.\n */\nexport function fromCompletionItemTag(\n  tag: monaco.languages.CompletionItemTag\n): lsp.CompletionItemTag {\n  return tag\n}\n\n/**\n * Convert an LSP completion item tag to a Monaco editor completion item tag.\n *\n * @param tag\n *   The LSP completion item tag to convert.\n * @returns\n *   The completion item tag as Monaco editor completion item tag.\n */\nexport function toCompletionItemTag(\n  tag: lsp.CompletionItemTag\n): monaco.languages.CompletionItemTag {\n  return tag\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor markdown string to an LSP markup content.\n *\n * @param markdownString\n *   The Monaco markdown string to convert.\n * @returns\n *   The markdown string as an LSP markup content.\n */\nexport function fromMarkdownString(markdownString: monaco.IMarkdownString): lsp.MarkupContent {\n  return {\n    kind: 'markdown',\n    value: markdownString.value\n  }\n}\n\n/**\n * Convert an LSP markup content to a Monaco editor markdown string.\n *\n * @param markupContent\n *   The LSP markup content to convert.\n * @returns\n *   The markup content as a Monaco editor markdown string.\n */\nexport function toMarkdownString(markupContent: lsp.MarkupContent): monaco.IMarkdownString {\n  return {\n    value: markupContent.value\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor single edit operation to an LSP text edit.\n *\n * @param singleEditOperation\n *   The Monaco single edit operation to convert.\n * @returns\n *   The single edit operation as an LSP text edit.\n */\nexport function fromSingleEditOperation(\n  singleEditOperation: monaco.editor.ISingleEditOperation\n): lsp.TextEdit {\n  return {\n    newText: singleEditOperation.text ?? '',\n    range: fromRange(singleEditOperation.range)\n  }\n}\n\n/**\n * Convert an LSP text edit to a Monaco editor single edit operation.\n *\n * @param textEdit\n *   The LSP text edit to convert.\n * @returns\n *   The text edit as Monaco editor single edit operation.\n */\nexport function toSingleEditOperation(textEdit: lsp.TextEdit): monaco.editor.ISingleEditOperation {\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromCommand, toCommand } from './command.js'\nimport { fromCompletionItemKind, toCompletionItemKind } from './completionItemKind.js'\nimport { fromCompletionItemTag, toCompletionItemTag } from './completionItemTag.js'\nimport { fromMarkdownString, toMarkdownString } from './markdownString.js'\nimport { fromRange, toRange } from './range.js'\nimport { fromSingleEditOperation, toSingleEditOperation } from './singleEditOperation.js'\n\n/**\n * Convert a Monaco editor completion item range to an LSP completion item text edit.\n *\n * @param edit\n *   The Monaco completion item range to convert.\n * @param newText\n *   The text of the text edit.\n * @returns\n *   The completion item range as an LSP completion item text edit.\n */\nfunction fromCompletionItemRange(\n  edit: monaco.languages.CompletionItem['range'],\n  newText: string\n): lsp.InsertReplaceEdit | lsp.TextEdit {\n  if ('insert' in edit) {\n    return {\n      newText,\n      insert: from<PERSON><PERSON><PERSON>(edit.insert),\n      replace: from<PERSON><PERSON><PERSON>(edit.replace)\n    }\n  }\n\n  return {\n    newText,\n    range: fromRange(edit)\n  }\n}\n\n/**\n * Convert a Monaco editor completion item to an LSP completion item.\n *\n * @param completionItem\n *   The Monaco completion item to convert.\n * @returns\n *   The completion item as an LSP completion item.\n */\nexport function fromCompletionItem(\n  completionItem: monaco.languages.CompletionItem\n): lsp.CompletionItem {\n  const result: lsp.CompletionItem = {\n    kind: fromCompletionItemKind(completionItem.kind),\n    label:\n      typeof completionItem.label === 'string' ? completionItem.label : completionItem.label.label,\n    textEdit: fromCompletionItemRange(completionItem.range, completionItem.insertText)\n  }\n\n  if (completionItem.additionalTextEdits) {\n    result.additionalTextEdits = completionItem.additionalTextEdits.map(fromSingleEditOperation)\n  }\n\n  if (completionItem.command) {\n    result.command = fromCommand(completionItem.command)\n  }\n\n  if (completionItem.commitCharacters) {\n    result.commitCharacters = completionItem.commitCharacters\n  }\n\n  if (completionItem.detail != null) {\n    result.detail = completionItem.detail\n  }\n\n  if (typeof completionItem.documentation === 'string') {\n    result.documentation = completionItem.documentation\n  } else if (completionItem.documentation) {\n    result.documentation = fromMarkdownString(completionItem.documentation)\n  }\n\n  if (completionItem.filterText != null) {\n    result.filterText = completionItem.filterText\n  }\n\n  if (\n    completionItem.insertTextRules ===\n    (4 satisfies monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet)\n  ) {\n    result.insertTextFormat = 2 satisfies typeof lsp.InsertTextFormat.Snippet\n  } else if (\n    completionItem.insertTextRules ===\n    (1 satisfies monaco.languages.CompletionItemInsertTextRule.KeepWhitespace)\n  ) {\n    result.insertTextMode = 2 satisfies typeof lsp.InsertTextMode.adjustIndentation\n  }\n\n  if (completionItem.preselect != null) {\n    result.preselect = completionItem.preselect\n  }\n\n  if (completionItem.sortText != null) {\n    result.sortText = completionItem.sortText\n  }\n\n  if (completionItem.tags) {\n    result.tags = completionItem.tags.map(fromCompletionItemTag)\n  }\n\n  return result\n}\n\ninterface ToCompletionItemOptions {\n  /**\n   * The item defaults of a completion list.\n   */\n  itemDefaults?: lsp.CompletionList['itemDefaults'] | undefined\n\n  /**\n   * A fallback range to use in case the completion item doesn’t provide one.\n   */\n  range: monaco.languages.CompletionItem['range']\n}\n\n/**\n * Convert an LSP completion item text edit to a Monaco editor range.\n *\n * @param edit\n *   The LSP completion item text edit to convert.\n * @returns\n *   The completion item text edit as Monaco editor range.\n */\nfunction toCompletionItemRange(\n  edit: lsp.Range | lsp.TextEdit | Omit<lsp.InsertReplaceEdit, 'newText'>\n): monaco.languages.CompletionItem['range'] {\n  if ('range' in edit) {\n    return toRange(edit.range)\n  }\n\n  if ('insert' in edit && 'replace' in edit) {\n    return {\n      insert: toRange(edit.insert),\n      replace: toRange(edit.replace)\n    }\n  }\n\n  return toRange(edit)\n}\n\n/**\n * Convert an LSP completion item to a Monaco editor completion item.\n *\n * @param completionItem\n *   The LSP completion item to convert.\n * @param options\n *   Additional options needed to construct the Monaco completion item.\n * @returns\n *   The completion item as Monaco editor completion item.\n */\nexport function toCompletionItem(\n  completionItem: lsp.CompletionItem,\n  options: ToCompletionItemOptions\n): monaco.languages.CompletionItem {\n  const itemDefaults = options.itemDefaults ?? {}\n  const textEdit = completionItem.textEdit ?? itemDefaults.editRange\n  const commitCharacters = completionItem.commitCharacters ?? itemDefaults.commitCharacters\n  const insertTextFormat = completionItem.insertTextFormat ?? itemDefaults.insertTextFormat\n  const insertTextMode = completionItem.insertTextMode ?? itemDefaults.insertTextMode\n\n  let text = completionItem.insertText\n  let range: monaco.languages.CompletionItem['range']\n\n  if (textEdit) {\n    range = toCompletionItemRange(textEdit)\n    if ('newText' in textEdit) {\n      text = textEdit.newText\n    }\n  } else {\n    range = { ...options.range }\n  }\n\n  const result: monaco.languages.CompletionItem = {\n    insertText: text ?? completionItem.label,\n    kind:\n      completionItem.kind == null\n        ? (18 satisfies monaco.languages.CompletionItemKind.Text)\n        : toCompletionItemKind(completionItem.kind),\n    label: completionItem.label,\n    range\n  }\n\n  if (completionItem.additionalTextEdits) {\n    result.additionalTextEdits = completionItem.additionalTextEdits.map(toSingleEditOperation)\n  }\n\n  if (completionItem.command) {\n    result.command = toCommand(completionItem.command)\n  }\n\n  if (commitCharacters) {\n    result.commitCharacters = commitCharacters\n  }\n\n  if (completionItem.detail != null) {\n    result.detail = completionItem.detail\n  }\n\n  if (typeof completionItem.documentation === 'string') {\n    result.documentation = completionItem.documentation\n  } else if (completionItem.documentation) {\n    result.documentation = toMarkdownString(completionItem.documentation)\n  }\n\n  if (completionItem.filterText != null) {\n    result.filterText = completionItem.filterText\n  }\n\n  if (insertTextFormat === 2) {\n    result.insertTextRules =\n      4 satisfies monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet\n  } else if (insertTextMode === 2) {\n    result.insertTextRules =\n      1 satisfies monaco.languages.CompletionItemInsertTextRule.KeepWhitespace\n  }\n\n  if (completionItem.preselect != null) {\n    result.preselect = completionItem.preselect\n  }\n\n  if (completionItem.sortText != null) {\n    result.sortText = completionItem.sortText\n  }\n\n  if (completionItem.tags) {\n    result.tags = completionItem.tags.map(toCompletionItemTag)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromCompletionItem, toCompletionItem } from './completionItem.js'\n\n/**\n * Convert a Monaco editor completion list to an LSP completion list.\n *\n * @param completionList\n *   The Monaco completion list to convert.\n * @returns\n *   The completion list as an LSP completion list.\n */\nexport function fromCompletionList(\n  completionList: monaco.languages.CompletionList\n): lsp.CompletionList {\n  return {\n    isIncomplete: <PERSON><PERSON><PERSON>(completionList.incomplete),\n    items: completionList.suggestions.map(fromCompletionItem)\n  }\n}\n\ninterface ToCompletionListOptions {\n  /**\n   * A fallback range to use in case a completion item doesn’t provide one.\n   */\n  range: monaco.IRange\n}\n\n/**\n * Convert an LSP completion list to a Monaco editor completion list.\n *\n * @param completionList\n *   The LSP completion list to convert.\n * @param options\n *   Additional options needed to construct the Monaco completion list.\n * @returns\n *   The completion list as Monaco editor completion list.\n */\nexport function toCompletionList(\n  completionList: lsp.CompletionList,\n  options: ToCompletionListOptions\n): monaco.languages.CompletionList {\n  return {\n    incomplete: <PERSON><PERSON><PERSON>(completionList.isIncomplete),\n    suggestions: completionList.items.map((item) =>\n      toCompletionItem(item, { range: options.range, itemDefaults: completionList.itemDefaults })\n    )\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor symbol kind to an LSP symbol kind.\n *\n * @param symbolKind\n *   The Monaco symbol kind to convert.\n * @returns\n *   The symbol kind as an LSP symbol kind.\n */\nexport function fromSymbolKind(symbolKind: monaco.languages.SymbolKind): lsp.SymbolKind {\n  if (symbolKind === (0 satisfies monaco.languages.SymbolKind.File)) {\n    return 1 satisfies typeof lsp.SymbolKind.File\n  }\n  if (symbolKind === (1 satisfies monaco.languages.SymbolKind.Module)) {\n    return 2 satisfies typeof lsp.SymbolKind.Module\n  }\n  if (symbolKind === (2 satisfies monaco.languages.SymbolKind.Namespace)) {\n    return 3 satisfies typeof lsp.SymbolKind.Namespace\n  }\n  if (symbolKind === (3 satisfies monaco.languages.SymbolKind.Package)) {\n    return 4 satisfies typeof lsp.SymbolKind.Package\n  }\n  if (symbolKind === (4 satisfies monaco.languages.SymbolKind.Class)) {\n    return 5 satisfies typeof lsp.SymbolKind.Class\n  }\n  if (symbolKind === (5 satisfies monaco.languages.SymbolKind.Method)) {\n    return 6 satisfies typeof lsp.SymbolKind.Method\n  }\n  if (symbolKind === (6 satisfies monaco.languages.SymbolKind.Property)) {\n    return 7 satisfies typeof lsp.SymbolKind.Property\n  }\n  if (symbolKind === (7 satisfies monaco.languages.SymbolKind.Field)) {\n    return 8 satisfies typeof lsp.SymbolKind.Field\n  }\n  if (symbolKind === (8 satisfies monaco.languages.SymbolKind.Constructor)) {\n    return 9 satisfies typeof lsp.SymbolKind.Constructor\n  }\n  if (symbolKind === (9 satisfies monaco.languages.SymbolKind.Enum)) {\n    return 10 satisfies typeof lsp.SymbolKind.Enum\n  }\n  if (symbolKind === (10 satisfies monaco.languages.SymbolKind.Interface)) {\n    return 11 satisfies typeof lsp.SymbolKind.Interface\n  }\n  if (symbolKind === (11 satisfies monaco.languages.SymbolKind.Function)) {\n    return 12 satisfies typeof lsp.SymbolKind.Function\n  }\n  if (symbolKind === (12 satisfies monaco.languages.SymbolKind.Variable)) {\n    return 13 satisfies typeof lsp.SymbolKind.Variable\n  }\n  if (symbolKind === (13 satisfies monaco.languages.SymbolKind.Constant)) {\n    return 14 satisfies typeof lsp.SymbolKind.Constant\n  }\n  if (symbolKind === (14 satisfies monaco.languages.SymbolKind.String)) {\n    return 15 satisfies typeof lsp.SymbolKind.String\n  }\n  if (symbolKind === (15 satisfies monaco.languages.SymbolKind.Number)) {\n    return 16 satisfies typeof lsp.SymbolKind.Number\n  }\n  if (symbolKind === (16 satisfies monaco.languages.SymbolKind.Boolean)) {\n    return 17 satisfies typeof lsp.SymbolKind.Boolean\n  }\n  if (symbolKind === (17 satisfies monaco.languages.SymbolKind.Array)) {\n    return 18 satisfies typeof lsp.SymbolKind.Array\n  }\n  if (symbolKind === (18 satisfies monaco.languages.SymbolKind.Object)) {\n    return 19 satisfies typeof lsp.SymbolKind.Object\n  }\n  if (symbolKind === (19 satisfies monaco.languages.SymbolKind.Key)) {\n    return 20 satisfies typeof lsp.SymbolKind.Key\n  }\n  if (symbolKind === (20 satisfies monaco.languages.SymbolKind.Null)) {\n    return 21 satisfies typeof lsp.SymbolKind.Null\n  }\n  if (symbolKind === (21 satisfies monaco.languages.SymbolKind.EnumMember)) {\n    return 22 satisfies typeof lsp.SymbolKind.EnumMember\n  }\n  if (symbolKind === (22 satisfies monaco.languages.SymbolKind.Struct)) {\n    return 23 satisfies typeof lsp.SymbolKind.Struct\n  }\n  if (symbolKind === (23 satisfies monaco.languages.SymbolKind.Event)) {\n    return 24 satisfies typeof lsp.SymbolKind.Event\n  }\n  if (symbolKind === (24 satisfies monaco.languages.SymbolKind.Operator)) {\n    return 25 satisfies typeof lsp.SymbolKind.Operator\n  }\n  // SymbolKind === SymbolKind.TypeParameter\n  return 26 satisfies typeof lsp.SymbolKind.TypeParameter\n}\n\n/**\n * Convert an LSP symbol kind to a Monaco editor symbol kind.\n *\n * @param symbolKind\n *   The LSP symbol kind to convert.\n * @returns\n *   The symbol kind as Monaco editor symbol kind.\n */\nexport function toSymbolKind(symbolKind: lsp.SymbolKind): monaco.languages.SymbolKind {\n  if (symbolKind === (1 satisfies typeof lsp.SymbolKind.File)) {\n    return 0 satisfies monaco.languages.SymbolKind.File\n  }\n  if (symbolKind === (2 satisfies typeof lsp.SymbolKind.Module)) {\n    return 1 satisfies monaco.languages.SymbolKind.Module\n  }\n  if (symbolKind === (3 satisfies typeof lsp.SymbolKind.Namespace)) {\n    return 2 satisfies monaco.languages.SymbolKind.Namespace\n  }\n  if (symbolKind === (4 satisfies typeof lsp.SymbolKind.Package)) {\n    return 3 satisfies monaco.languages.SymbolKind.Package\n  }\n  if (symbolKind === (5 satisfies typeof lsp.SymbolKind.Class)) {\n    return 4 satisfies monaco.languages.SymbolKind.Class\n  }\n  if (symbolKind === (6 satisfies typeof lsp.SymbolKind.Method)) {\n    return 5 satisfies monaco.languages.SymbolKind.Method\n  }\n  if (symbolKind === (7 satisfies typeof lsp.SymbolKind.Property)) {\n    return 6 satisfies monaco.languages.SymbolKind.Property\n  }\n  if (symbolKind === (8 satisfies typeof lsp.SymbolKind.Field)) {\n    return 7 satisfies monaco.languages.SymbolKind.Field\n  }\n  if (symbolKind === (9 satisfies typeof lsp.SymbolKind.Constructor)) {\n    return 8 satisfies monaco.languages.SymbolKind.Constructor\n  }\n  if (symbolKind === (10 satisfies typeof lsp.SymbolKind.Enum)) {\n    return 9 satisfies monaco.languages.SymbolKind.Enum\n  }\n  if (symbolKind === (11 satisfies typeof lsp.SymbolKind.Interface)) {\n    return 10 satisfies monaco.languages.SymbolKind.Interface\n  }\n  if (symbolKind === (12 satisfies typeof lsp.SymbolKind.Function)) {\n    return 11 satisfies monaco.languages.SymbolKind.Function\n  }\n  if (symbolKind === (13 satisfies typeof lsp.SymbolKind.Variable)) {\n    return 12 satisfies monaco.languages.SymbolKind.Variable\n  }\n  if (symbolKind === (14 satisfies typeof lsp.SymbolKind.Constant)) {\n    return 13 satisfies monaco.languages.SymbolKind.Constant\n  }\n  if (symbolKind === (15 satisfies typeof lsp.SymbolKind.String)) {\n    return 14 satisfies monaco.languages.SymbolKind.String\n  }\n  if (symbolKind === (16 satisfies typeof lsp.SymbolKind.Number)) {\n    return 15 satisfies monaco.languages.SymbolKind.Number\n  }\n  if (symbolKind === (17 satisfies typeof lsp.SymbolKind.Boolean)) {\n    return 16 satisfies monaco.languages.SymbolKind.Boolean\n  }\n  if (symbolKind === (18 satisfies typeof lsp.SymbolKind.Array)) {\n    return 17 satisfies monaco.languages.SymbolKind.Array\n  }\n  if (symbolKind === (19 satisfies typeof lsp.SymbolKind.Object)) {\n    return 18 satisfies monaco.languages.SymbolKind.Object\n  }\n  if (symbolKind === (20 satisfies typeof lsp.SymbolKind.Key)) {\n    return 19 satisfies monaco.languages.SymbolKind.Key\n  }\n  if (symbolKind === (21 satisfies typeof lsp.SymbolKind.Null)) {\n    return 20 satisfies monaco.languages.SymbolKind.Null\n  }\n  if (symbolKind === (22 satisfies typeof lsp.SymbolKind.EnumMember)) {\n    return 21 satisfies monaco.languages.SymbolKind.EnumMember\n  }\n  if (symbolKind === (23 satisfies typeof lsp.SymbolKind.Struct)) {\n    return 22 satisfies monaco.languages.SymbolKind.Struct\n  }\n  if (symbolKind === (24 satisfies typeof lsp.SymbolKind.Event)) {\n    return 23 satisfies monaco.languages.SymbolKind.Event\n  }\n  if (symbolKind === (25 satisfies typeof lsp.SymbolKind.Operator)) {\n    return 24 satisfies monaco.languages.SymbolKind.Operator\n  }\n  // SymbolKind === 26\n  return 25 satisfies monaco.languages.SymbolKind.TypeParameter\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor symbol tag to an LSP symbol tag.\n *\n * @param symbolTag\n *   The Monaco symbol tag to convert.\n * @returns\n *   The symbol tag as an LSP symbol tag.\n */\nexport function fromSymbolTag(symbolTag: monaco.languages.SymbolTag): lsp.SymbolTag {\n  return symbolTag\n}\n\n/**\n * Convert an LSP symbol tag to a Monaco editor symbol tag.\n *\n * @param symbolTag\n *   The LSP symbol tag to convert.\n * @returns\n *   The symbol tag as Monaco editor symbol tag.\n */\nexport function toSymbolTag(symbolTag: lsp.SymbolTag): monaco.languages.SymbolTag {\n  return symbolTag\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromRange, toRange } from './range.js'\nimport { fromSymbolKind, toSymbolKind } from './symbolKind.js'\nimport { fromSymbolTag, toSymbolTag } from './symbolTag.js'\n\n/**\n * Convert a Monaco editor document symbol to an LSP document symbol.\n *\n * @param documentSymbol\n *   The Monaco document symbol to convert.\n * @returns\n *   The document symbol as an LSP document symbol.\n */\nexport function fromDocumentSymbol(\n  documentSymbol: monaco.languages.DocumentSymbol\n): lsp.DocumentSymbol {\n  const result: lsp.DocumentSymbol = {\n    detail: documentSymbol.detail,\n    kind: fromSymbolKind(documentSymbol.kind),\n    name: documentSymbol.name,\n    range: fromRange(documentSymbol.range),\n    selectionRange: fromRange(documentSymbol.selectionRange),\n    tags: documentSymbol.tags.map(fromSymbolTag)\n  }\n\n  if (documentSymbol.children) {\n    result.children = documentSymbol.children.map(fromDocumentSymbol)\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP document symbol to a Monaco editor document symbol.\n *\n * @param documentSymbol\n *   The LSP document symbol to convert.\n * @returns\n *   The document symbol as Monaco editor document symbol.\n */\nexport function toDocumentSymbol(\n  documentSymbol: lsp.DocumentSymbol\n): monaco.languages.DocumentSymbol {\n  const result: monaco.languages.DocumentSymbol = {\n    detail: documentSymbol.detail ?? '',\n    kind: toSymbolKind(documentSymbol.kind),\n    name: documentSymbol.name,\n    range: toRange(documentSymbol.range),\n    selectionRange: toRange(documentSymbol.selectionRange),\n    tags: documentSymbol.tags?.map(toSymbolTag) ?? []\n  }\n\n  if (documentSymbol.children) {\n    result.children = documentSymbol.children.map(toDocumentSymbol)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor folding range to an LSP folding range.\n *\n * @param foldingRange\n *   The Monaco folding range to convert.\n * @returns\n *   The folding range as an LSP folding range.\n */\nexport function fromFoldingRange(foldingRange: monaco.languages.FoldingRange): lsp.FoldingRange {\n  const result: lsp.FoldingRange = {\n    startLine: foldingRange.start - 1,\n    endLine: foldingRange.end - 1\n  }\n\n  if (foldingRange.kind != null) {\n    result.kind = foldingRange.kind.value\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP folding range to a Monaco editor folding range.\n *\n * @param foldingRange\n *   The LSP folding range to convert.\n * @returns\n *   The folding range as Monaco editor folding range.\n */\nexport function toFoldingRange(foldingRange: lsp.FoldingRange): monaco.languages.FoldingRange {\n  const result: monaco.languages.FoldingRange = {\n    start: foldingRange.startLine + 1,\n    end: foldingRange.endLine + 1\n  }\n\n  if (foldingRange.kind != null) {\n    result.kind = { value: foldingRange.kind }\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor formatting options to an LSP formatting options.\n *\n * @param formattingOptions\n *   The Monaco formatting options to convert.\n * @returns\n *   The formatting options as an LSP formatting options.\n */\nexport function fromFormattingOptions(\n  formattingOptions: monaco.languages.FormattingOptions\n): lsp.FormattingOptions {\n  return {\n    insertSpaces: formattingOptions.insertSpaces,\n    tabSize: formattingOptions.tabSize\n  }\n}\n\n/**\n * Convert an LSP formatting options to a Monaco editor formatting options.\n *\n * @param formattingOptions\n *   The LSP formatting options to convert.\n * @returns\n *   The formatting options as Monaco editor formatting options.\n */\nexport function toFormattingOptions(\n  formattingOptions: lsp.FormattingOptions\n): monaco.languages.FormattingOptions {\n  return {\n    insertSpaces: formattingOptions.insertSpaces,\n    tabSize: formattingOptions.tabSize\n  }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromMarkdownString, toMarkdownString } from './markdownString.js'\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor hover to an LSP hover.\n *\n * @param hover\n *   The Monaco hover to convert.\n * @returns\n *   The hover as an LSP hover.\n */\nexport function fromHover(hover: monaco.languages.Hover): lsp.Hover {\n  const result: lsp.Hover = {\n    contents: fromMarkdownString(hover.contents[0])\n  }\n\n  if (hover.range) {\n    result.range = fromRange(hover.range)\n  }\n\n  return result\n}\n\n/**\n * Get value of a marked string.\n *\n * @param value\n *   The marked string to get the value from.\n * @returns\n *   The value of the marked string.\n */\nfunction getDeprecatedMarkupValue(value: lsp.MarkedString): monaco.IMarkdownString {\n  if (typeof value === 'string') {\n    return { value }\n  }\n\n  return { value: `\\`\\`\\`${value.language}\\n${value.value}\\n\\`\\`\\`` }\n}\n\n/**\n * Convert LSP hover item contents to a Monaco markdown string.\n *\n * @param contents\n *   The LSP hover contents to convert.\n * @returns\n *   The hover contents as a Monaco markdown string.\n */\nfunction toHoverContents(\n  contents: lsp.MarkedString | lsp.MarkedString[] | lsp.MarkupContent\n): monaco.IMarkdownString[] {\n  if (typeof contents === 'string' || 'language' in contents) {\n    return [getDeprecatedMarkupValue(contents)]\n  }\n\n  if (Array.isArray(contents)) {\n    return contents.map(getDeprecatedMarkupValue)\n  }\n\n  return [toMarkdownString(contents)]\n}\n\n/**\n * Convert an LSP hover to a Monaco editor hover.\n *\n * @param hover\n *   The LSP hover to convert.\n * @returns\n *   The hover as Monaco editor hover.\n */\nexport function toHover(hover: lsp.Hover): monaco.languages.Hover {\n  const result: monaco.languages.Hover = {\n    contents: toHoverContents(hover.contents)\n  }\n\n  if (hover.range) {\n    result.range = toRange(hover.range)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\n/**\n * Convert a Monaco editor position to an LSP range.\n *\n * @param position\n *   The Monaco position to convert.\n * @returns\n *   The position as an LSP position.\n */\nexport function fromPosition(position: monaco.IPosition): lsp.Position {\n  return { character: position.column - 1, line: position.lineNumber - 1 }\n}\n\n/**\n * Convert an LSP position to a Monaco editor position.\n *\n * @param position\n *   The LSP position to convert.\n * @returns\n *   The position as Monaco editor position.\n */\nexport function toPosition(position: lsp.Position): monaco.IPosition {\n  return { lineNumber: position.line + 1, column: position.character + 1 }\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor link to an LSP document link.\n *\n * @param link\n *   The Monaco link to convert.\n * @returns\n *   The link as an LSP document link.\n */\nexport function fromLink(link: monaco.languages.ILink): lsp.DocumentLink {\n  const result: lsp.DocumentLink = {\n    range: fromRange(link.range)\n  }\n\n  if (link.tooltip != null) {\n    result.tooltip = link.tooltip\n  }\n\n  if (link.url != null) {\n    result.target = String(link.url)\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP document link to a Monaco editor link.\n *\n * @param documentLink\n *   The LSP document link to convert.\n * @returns\n *   The document link as Monaco editor link.\n */\nexport function toLink(documentLink: lsp.DocumentLink): monaco.languages.ILink {\n  const result: monaco.languages.ILink = {\n    range: toRange(documentLink.range)\n  }\n\n  if (documentLink.tooltip != null) {\n    result.tooltip = documentLink.tooltip\n  }\n\n  if (documentLink.target != null) {\n    result.url = URI.parse(documentLink.target)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\nimport { URI } from 'vscode-uri'\n\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert a Monaco editor location link to an LSP location link.\n *\n * @param locationLink\n *   The Monaco location link to convert.\n * @returns\n *   The location link as an LSP location link.\n */\nexport function fromLocationLink(locationLink: monaco.languages.LocationLink): lsp.LocationLink {\n  const result: lsp.LocationLink = {\n    targetRange: fromRange(locationLink.range),\n    targetSelectionRange: locationLink.targetSelectionRange\n      ? fromRange(locationLink.targetSelectionRange)\n      : fromRange(locationLink.range),\n    targetUri: String(locationLink.uri)\n  }\n\n  if (locationLink.originSelectionRange) {\n    result.originSelectionRange = fromRange(locationLink.originSelectionRange)\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP location link to a Monaco editor location link.\n *\n * @param locationLink\n *   The LSP location link to convert.\n * @returns\n *   The location link as Monaco editor location link.\n */\nexport function toLocationLink(locationLink: lsp.LocationLink): monaco.languages.LocationLink {\n  const result: monaco.languages.LocationLink = {\n    range: toRange(locationLink.targetRange),\n    targetSelectionRange: toRange(locationLink.targetSelectionRange),\n    uri: URI.parse(locationLink.targetUri)\n  }\n\n  if (locationLink.originSelectionRange) {\n    result.originSelectionRange = toRange(locationLink.originSelectionRange)\n  }\n\n  return result\n}\n", "import type * as monaco from 'monaco-types'\nimport type * as lsp from 'vscode-languageserver-protocol'\n\nimport { fromRange, toRange } from './range.js'\n\n/**\n * Convert Monaco editor selection ranges to an LSP selection ranges.\n *\n * @param selectionRanges\n *   The Monaco selections range to convert.\n * @returns\n *   The selection ranges as LSP selection range.\n */\nexport function fromSelectionRanges(\n  selectionRanges: monaco.languages.SelectionRange[]\n): lsp.SelectionRange | undefined {\n  let result: lsp.SelectionRange | undefined\n\n  for (let index = selectionRanges.length - 1; index >= 0; index -= 1) {\n    const parent = result\n\n    result = {\n      range: fromRange(selectionRanges[index].range)\n    }\n\n    if (parent) {\n      result.parent = parent\n    }\n  }\n\n  return result\n}\n\n/**\n * Convert an LSP selection range to Monaco editor selection ranges.\n *\n * @param selectionRange\n *   The LSP selection range to convert.\n * @returns\n *   The selection range as Monaco editor selection ranges.\n */\nexport function toSelectionRanges(\n  selectionRange: lsp.SelectionRange | undefined\n): monaco.languages.SelectionRange[] {\n  const result: monaco.languages.SelectionRange[] = []\n  let current = selectionRange\n\n  while (current) {\n    result.push({\n      range: toRange(current.range)\n    })\n    current = current.parent\n  }\n\n  return result\n}\n", "import { type editor, type IDisposable, type languages, type MonacoEditor } from 'monaco-types'\n\nexport interface MarkerDataProvider {\n  /**\n   * The owner of the model markers.\n   *\n   * This should be a unique string that identifies the context of who owns the marker data.\n   */\n  owner: string\n\n  /**\n   * Provide marker data for the given model.\n   *\n   * @param model\n   *   The model to provide marker data for.\n   * @returns\n   *   The new marker data for the model.\n   */\n  provideMarkerData: (model: editor.ITextModel) => languages.ProviderResult<editor.IMarkerData[]>\n\n  /**\n   * Reset the state for a model.\n   *\n   * @param model\n   *   The model to reset the state for.\n   */\n  doReset?: (model: editor.ITextModel) => unknown\n}\n\nexport interface MarkerDataProviderInstance extends IDisposable {\n  /**\n   * Revalidate all models.\n   */\n  revalidate: () => Promise<undefined>\n}\n\n/**\n * Register a marker data provider that can provide marker data for a model.\n *\n * @param monaco\n *   The Monaco editor module.\n * @param languageSelector\n *   The language id to register the provider for.\n * @param provider\n *   The provider that can provide marker data.\n * @returns\n *   A disposable.\n */\nexport function registerMarkerDataProvider(\n  monaco: Pick<MonacoEditor, 'editor'>,\n  languageSelector: string[] | string,\n  provider: MarkerDataProvider\n): MarkerDataProviderInstance {\n  const listeners = new Map<editor.ITextModel, IDisposable>()\n\n  const matchesLanguage = (model: editor.ITextModel): boolean => {\n    if (languageSelector === '*') {\n      return true\n    }\n    const languageId = model.getLanguageId()\n    return Array.isArray(languageSelector)\n      ? languageSelector.includes(languageId)\n      : languageSelector === languageId\n  }\n\n  const doValidate = async (model: editor.ITextModel): Promise<undefined> => {\n    const versionId = model.getVersionId()\n    const markers = await provider.provideMarkerData(model)\n    // The model may have been updated disposed by the time marker data has been fetched.\n    if (!model.isDisposed() && versionId === model.getVersionId() && matchesLanguage(model)) {\n      monaco.editor.setModelMarkers(model, provider.owner, markers ?? [])\n    }\n  }\n\n  const onModelAdd = (model: editor.ITextModel): undefined => {\n    if (!matchesLanguage(model)) {\n      return\n    }\n\n    let handle: ReturnType<typeof setTimeout>\n    const onDidChangeContent = model.onDidChangeContent(() => {\n      clearTimeout(handle)\n      handle = setTimeout(() => {\n        doValidate(model)\n      }, 500)\n    })\n\n    listeners.set(model, {\n      dispose() {\n        clearTimeout(handle)\n        onDidChangeContent.dispose()\n      }\n    })\n\n    doValidate(model)\n  }\n\n  const onModelRemoved = (model: editor.ITextModel): undefined => {\n    monaco.editor.setModelMarkers(model, provider.owner, [])\n    const listener = listeners.get(model)\n    if (listener) {\n      listener.dispose()\n      listeners.delete(model)\n    }\n  }\n\n  const onDidCreateModel = monaco.editor.onDidCreateModel(onModelAdd)\n  const onWillDisposeModel = monaco.editor.onWillDisposeModel((model) => {\n    onModelRemoved(model)\n    provider.doReset?.(model)\n  })\n  const onDidChangeModelLanguage = monaco.editor.onDidChangeModelLanguage(({ model }) => {\n    onModelRemoved(model)\n    onModelAdd(model)\n    provider.doReset?.(model)\n  })\n\n  for (const model of monaco.editor.getModels()) {\n    onModelAdd(model)\n  }\n\n  return {\n    dispose() {\n      for (const model of listeners.keys()) {\n        onModelRemoved(model)\n      }\n      onDidCreateModel.dispose()\n      onWillDisposeModel.dispose()\n      onDidChangeModelLanguage.dispose()\n    },\n\n    async revalidate() {\n      await Promise.all(monaco.editor.getModels().map(doValidate))\n    }\n  }\n}\n", "/**\n * Create a worker manager.\n *\n * A worker manager is an object which deals with Monaco based web workers, such as cleanups and\n * idle timeouts.\n *\n * @param monaco - The Monaco editor module.\n * @param options - The options of the worker manager.\n * @returns The worker manager.\n */\nexport function createWorkerManager(monaco, options) {\n    let { createData, interval = 30_000, label, moduleId, stopWhenIdleFor = 120_000 } = options;\n    let worker;\n    let lastUsedTime = 0;\n    let disposed = false;\n    const stopWorker = () => {\n        if (worker) {\n            worker.dispose();\n            worker = undefined;\n        }\n    };\n    const intervalId = setInterval(() => {\n        if (!worker) {\n            return;\n        }\n        const timePassedSinceLastUsed = Date.now() - lastUsedTime;\n        if (timePassedSinceLastUsed > stopWhenIdleFor) {\n            stopWorker();\n        }\n    }, interval);\n    return {\n        dispose() {\n            disposed = true;\n            clearInterval(intervalId);\n            stopWorker();\n        },\n        getWorker(...resources) {\n            if (disposed) {\n                throw new Error('Worker manager has been disposed');\n            }\n            lastUsedTime = Date.now();\n            if (!worker) {\n                worker = monaco.editor.createWebWorker({\n                    createData,\n                    label,\n                    moduleId,\n                });\n            }\n            return worker.withSyncedResources(resources);\n        },\n        updateCreateData(newCreateData) {\n            createData = newCreateData;\n            stopWorker();\n        },\n    };\n}\n", "import {\n  fromCodeActionTriggerType,\n  fromFormattingOptions,\n  fromPosition,\n  fromRange,\n  toCodeAction,\n  toCompletionList,\n  toDocumentSymbol,\n  toFoldingRange,\n  toHover,\n  toLink,\n  toLocationLink,\n  toMarkerData,\n  toRange,\n  toSelectionRanges,\n  toTextEdit\n} from 'monaco-languageserver-types'\nimport { registerMarkerDataProvider } from 'monaco-marker-data-provider'\nimport { type editor, type IDisposable, type MonacoEditor } from 'monaco-types'\nimport { createWorkerManager } from 'monaco-worker-manager'\nimport { type CompletionItemKind, type Diagnostic } from 'vscode-languageserver-types'\n\nimport { type YAMLWorker } from './yaml.worker.js'\n\n/* eslint-disable jsdoc/require-jsdoc */\nexport interface JSONSchema {\n  id?: string\n  $id?: string\n  $schema?: string\n  url?: string\n  type?: string[] | string\n  title?: string\n  closestTitle?: string\n  versions?: Record<string, string>\n  default?: unknown\n  definitions?: Record<string, JSONSchema>\n  description?: string\n  properties?: Record<string, JSONSchema | boolean>\n  patternProperties?: Record<string, JSONSchema | boolean>\n  additionalProperties?: JSONSchema | boolean\n  minProperties?: number\n  maxProperties?: number\n  dependencies?: Record<string, JSONSchema | string[] | boolean>\n  items?: (JSONSchema | boolean)[] | JSONSchema | boolean\n  minItems?: number\n  maxItems?: number\n  uniqueItems?: boolean\n  additionalItems?: JSONSchema | boolean\n  pattern?: string\n  minLength?: number\n  maxLength?: number\n  minimum?: number\n  maximum?: number\n  exclusiveMinimum?: boolean | number\n  exclusiveMaximum?: boolean | number\n  multipleOf?: number\n  required?: string[]\n  $ref?: string\n  anyOf?: (JSONSchema | boolean)[]\n  allOf?: (JSONSchema | boolean)[]\n  oneOf?: (JSONSchema | boolean)[]\n  not?: JSONSchema | boolean\n  enum?: unknown[]\n  format?: string\n  const?: unknown\n  contains?: JSONSchema | boolean\n  propertyNames?: JSONSchema | boolean\n  examples?: unknown[]\n  $comment?: string\n  if?: JSONSchema | boolean\n  then?: JSONSchema | boolean\n  else?: JSONSchema | boolean\n  defaultSnippets?: {\n    label?: string\n    description?: string\n    markdownDescription?: string\n    type?: string\n    suggestionKind?: CompletionItemKind\n    sortText?: string\n    body?: unknown\n    bodyText?: string\n  }[]\n  errorMessage?: string\n  patternErrorMessage?: string\n  deprecationMessage?: string\n  enumDescriptions?: string[]\n  markdownEnumDescriptions?: string[]\n  markdownDescription?: string\n  doNotSuggest?: boolean\n  allowComments?: boolean\n  schemaSequence?: JSONSchema[]\n  filePatternAssociation?: string\n}\n/* eslint-enable jsdoc/require-jsdoc */\n\nexport interface SchemasSettings {\n  /**\n   * A `Uri` file match which will trigger the schema validation. This may be a glob or an exact\n   * path.\n   *\n   * @example '.gitlab-ci.yml'\n   * @example 'file://**\\/.github/actions/*.yaml'\n   */\n  fileMatch: string[]\n\n  /**\n   * The JSON schema which will be used for validation. If not specified, it will be downloaded from\n   * `uri`.\n   */\n  schema?: JSONSchema\n\n  /**\n   * The source URI of the JSON schema. The JSON schema will be downloaded from here if no schema\n   * was supplied. It will also be displayed as the source in hover tooltips.\n   */\n  uri: string\n}\n\nexport interface MonacoYamlOptions {\n  /**\n   * If set, enable schema based autocompletion.\n   *\n   * @default true\n   */\n  readonly completion?: boolean\n\n  /**\n   * A list of custom tags.\n   *\n   * @default []\n   */\n  readonly customTags?: string[]\n\n  /**\n   * If set, the schema service will load schema content on-demand.\n   *\n   * @default false\n   */\n  readonly enableSchemaRequest?: boolean\n\n  /**\n   * If true, formatting using Prettier is enabled. Setting this to `false` does **not** exclude\n   * Prettier from the bundle.\n   *\n   * @default true\n   */\n  readonly format?: boolean\n\n  /**\n   * If set, enable hover typs based the JSON schema.\n   *\n   * @default true\n   */\n  readonly hover?: boolean\n\n  /**\n   * If true, a different diffing algorithm is used to generate error messages.\n   *\n   * @default false\n   */\n  readonly isKubernetes?: boolean\n\n  /**\n   * A list of known schemas and/or associations of schemas to file names.\n   *\n   * @default []\n   */\n  readonly schemas?: SchemasSettings[]\n\n  /**\n   * If set, the validator will be enabled and perform syntax validation as well as schema\n   * based validation.\n   *\n   * @default true\n   */\n  readonly validate?: boolean\n\n  /**\n   * The YAML version to use for parsing.\n   *\n   * @default '1.2'\n   */\n  readonly yamlVersion?: '1.1' | '1.2'\n}\n\nexport interface MonacoYaml extends IDisposable {\n  /**\n   * Recondigure `monaco-yaml`.\n   */\n  update: (options: MonacoYamlOptions) => Promise<undefined>\n}\n\n/**\n * Configure `monaco-yaml`.\n *\n * > **Note**: There may only be one configured instance of `monaco-yaml` at a time.\n *\n * @param monaco\n *   The Monaco editor module. Typically you get this by importing `monaco-editor`. Third party\n *   integrations often expose it as the global `monaco` variable instead.\n * @param options\n *   Options to configure `monaco-yaml`\n * @returns\n *   A disposable object that can be used to update `monaco-yaml`\n */\nexport function configureMonacoYaml(monaco: MonacoEditor, options?: MonacoYamlOptions): MonacoYaml {\n  const createData: MonacoYamlOptions = {\n    completion: true,\n    customTags: [],\n    enableSchemaRequest: false,\n    format: true,\n    isKubernetes: false,\n    hover: true,\n    schemas: [],\n    validate: true,\n    yamlVersion: '1.2',\n    ...options\n  }\n\n  monaco.languages.register({\n    id: 'yaml',\n    extensions: ['.yaml', '.yml'],\n    aliases: ['YAML', 'yaml', 'YML', 'yml'],\n    mimetypes: ['application/x-yaml']\n  })\n\n  const workerManager = createWorkerManager<YAMLWorker, MonacoYamlOptions>(monaco, {\n    label: 'yaml',\n    moduleId: 'monaco-yaml/yaml.worker',\n    createData\n  })\n\n  const diagnosticMap = new WeakMap<editor.ITextModel, Diagnostic[] | undefined>()\n\n  const markerDataProvider = registerMarkerDataProvider(monaco, 'yaml', {\n    owner: 'yaml',\n\n    async provideMarkerData(model) {\n      const worker = await workerManager.getWorker(model.uri)\n      const diagnostics = await worker.doValidation(String(model.uri))\n\n      diagnosticMap.set(model, diagnostics)\n\n      return diagnostics?.map(toMarkerData)\n    },\n\n    async doReset(model) {\n      const worker = await workerManager.getWorker(model.uri)\n      await worker.resetSchema(String(model.uri))\n    }\n  })\n\n  const disposables = [\n    workerManager,\n    markerDataProvider,\n\n    monaco.languages.registerCompletionItemProvider('yaml', {\n      triggerCharacters: [' ', ':'],\n\n      async provideCompletionItems(model, position) {\n        const wordInfo = model.getWordUntilPosition(position)\n        const worker = await workerManager.getWorker(model.uri)\n        const info = await worker.doComplete(String(model.uri), fromPosition(position))\n\n        if (info) {\n          return toCompletionList(info, {\n            range: {\n              startLineNumber: position.lineNumber,\n              startColumn: wordInfo.startColumn,\n              endLineNumber: position.lineNumber,\n              endColumn: wordInfo.endColumn\n            }\n          })\n        }\n      }\n    }),\n\n    monaco.languages.registerHoverProvider('yaml', {\n      async provideHover(model, position) {\n        const worker = await workerManager.getWorker(model.uri)\n        const info = await worker.doHover(String(model.uri), fromPosition(position))\n\n        if (info) {\n          return toHover(info)\n        }\n      }\n    }),\n\n    monaco.languages.registerDefinitionProvider('yaml', {\n      async provideDefinition(model, position) {\n        const worker = await workerManager.getWorker(model.uri)\n        const locationLinks = await worker.doDefinition(String(model.uri), fromPosition(position))\n\n        return locationLinks?.map(toLocationLink)\n      }\n    }),\n\n    monaco.languages.registerDocumentSymbolProvider('yaml', {\n      displayName: 'yaml',\n\n      async provideDocumentSymbols(model) {\n        const worker = await workerManager.getWorker(model.uri)\n        const items = await worker.findDocumentSymbols(String(model.uri))\n\n        return items?.map(toDocumentSymbol)\n      }\n    }),\n\n    monaco.languages.registerDocumentFormattingEditProvider('yaml', {\n      displayName: 'yaml',\n\n      async provideDocumentFormattingEdits(model) {\n        const worker = await workerManager.getWorker(model.uri)\n        const edits = await worker.format(String(model.uri))\n\n        return edits?.map(toTextEdit)\n      }\n    }),\n\n    monaco.languages.registerLinkProvider('yaml', {\n      async provideLinks(model) {\n        const worker = await workerManager.getWorker(model.uri)\n        const links = await worker.findLinks(String(model.uri))\n\n        if (links) {\n          return {\n            links: links.map(toLink)\n          }\n        }\n      }\n    }),\n\n    monaco.languages.registerCodeActionProvider('yaml', {\n      async provideCodeActions(model, range, context) {\n        const worker = await workerManager.getWorker(model.uri)\n        const codeActions = await worker.getCodeAction(String(model.uri), fromRange(range), {\n          diagnostics:\n            diagnosticMap\n              .get(model)\n              ?.filter((diagnostic) => range.intersectRanges(toRange(diagnostic.range))) || [],\n          only: context.only ? [context.only] : undefined,\n          triggerKind: fromCodeActionTriggerType(context.trigger)\n        })\n\n        if (codeActions) {\n          return {\n            actions: codeActions.map(toCodeAction),\n            dispose() {\n              // This is required by the TypeScript interface, but it’s not implemented.\n            }\n          }\n        }\n      }\n    }),\n\n    monaco.languages.registerFoldingRangeProvider('yaml', {\n      async provideFoldingRanges(model) {\n        const worker = await workerManager.getWorker(model.uri)\n        const foldingRanges = await worker.getFoldingRanges(String(model.uri))\n\n        return foldingRanges?.map(toFoldingRange)\n      }\n    }),\n\n    monaco.languages.setLanguageConfiguration('yaml', {\n      comments: {\n        lineComment: '#'\n      },\n      brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n      ],\n      autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n      ],\n      surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n      ]\n    }),\n\n    monaco.languages.registerOnTypeFormattingEditProvider('yaml', {\n      autoFormatTriggerCharacters: ['\\n'],\n\n      async provideOnTypeFormattingEdits(model, position, ch, formattingOptions) {\n        const worker = await workerManager.getWorker(model.uri)\n        const edits = await worker.doDocumentOnTypeFormatting(\n          String(model.uri),\n          fromPosition(position),\n          ch,\n          fromFormattingOptions(formattingOptions)\n        )\n\n        return edits?.map(toTextEdit)\n      }\n    }),\n\n    monaco.languages.registerSelectionRangeProvider('yaml', {\n      async provideSelectionRanges(model, positions) {\n        const worker = await workerManager.getWorker(model.uri)\n        const selectionRanges = await worker.getSelectionRanges(\n          String(model.uri),\n          positions.map(fromPosition)\n        )\n\n        return selectionRanges?.map(toSelectionRanges)\n      }\n    })\n  ]\n\n  return {\n    dispose() {\n      for (const disposable of disposables) {\n        disposable.dispose()\n      }\n    },\n\n    async update(newOptions) {\n      workerManager.updateCreateData(Object.assign(createData, newOptions))\n      await markerDataProvider.revalidate()\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;AA0BA,aAASA,GAAWC,IAAAA;AAClB,UAAoB,YAAA,OAATA;AACT,cAAM,IAAIC,UAAU,qCAAqCC,KAAKC,UAAUH,EAAAA,CAAAA;IAE5E;AAGA,aAASI,GAAqBJ,IAAMK,IAAAA;AAMlC,eADIC,IAJAC,KAAM,IACNC,KAAoB,GACpBC,KAAAA,IACAC,KAAO,GAEFC,KAAI,GAAGA,MAAKX,GAAKY,QAAAA,EAAUD,IAAG;AACrC,YAAIA,KAAIX,GAAKY;AACXN,UAAAA,KAAON,GAAKa,WAAWF,EAAAA;aACpB;AAAA,cAAa,OAATL;AACP;AAEAA,UAAAA,KAAO;QAAQ;AACjB,YAAa,OAATA,IAAmB;AACrB,cAAIG,OAAcE,KAAI,KAAc,MAATD;AAAAA;mBAEhBD,OAAcE,KAAI,KAAc,MAATD,IAAY;AAC5C,gBAAIH,GAAIK,SAAS,KAA2B,MAAtBJ,MAA8D,OAAnCD,GAAIM,WAAWN,GAAIK,SAAS,CAAA,KAAsD,OAAnCL,GAAIM,WAAWN,GAAIK,SAAS,CAAA;AAC1H,kBAAIL,GAAIK,SAAS,GAAG;AAClB,oBAAIE,KAAiBP,GAAIQ,YAAY,GAAA;AACrC,oBAAID,OAAmBP,GAAIK,SAAS,GAAG;AAAA,yBACjCE,MACFP,KAAM,IACNC,KAAoB,KAGpBA,MADAD,KAAMA,GAAIS,MAAM,GAAGF,EAAAA,GACKF,SAAS,IAAIL,GAAIQ,YAAY,GAAA,GAEvDN,KAAYE,IACZD,KAAO;AACP;gBACF;cACF,WAA0B,MAAfH,GAAIK,UAA+B,MAAfL,GAAIK,QAAc;AAC/CL,gBAAAA,KAAM,IACNC,KAAoB,GACpBC,KAAYE,IACZD,KAAO;AACP;cACF;;AAEEL,YAAAA,OACEE,GAAIK,SAAS,IACfL,MAAO,QAEPA,KAAM,MACRC,KAAoB;UAExB;AACMD,YAAAA,GAAIK,SAAS,IACfL,MAAO,MAAMP,GAAKgB,MAAMP,KAAY,GAAGE,EAAAA,IAEvCJ,KAAMP,GAAKgB,MAAMP,KAAY,GAAGE,EAAAA,GAClCH,KAAoBG,KAAIF,KAAY;AAEtCA,UAAAA,KAAYE,IACZD,KAAO;QACT;AAAoB,iBAATJ,MAAAA,OAAqBI,KAAAA,EAC5BA,KAEFA,KAAAA;MAEJ;AACA,aAAOH;IACT;AAcA,QAAIU,KAAQ,EAEVC,SAAS,WAAA;AAKP,eAFIC,IAFAC,KAAe,IACfC,KAAAA,OAGKV,KAAIW,UAAUV,SAAS,GAAGD,MAAAA,MAAM,CAAMU,IAAkBV,MAAK;AACpE,YAAIX;AACAW,QAAAA,MAAK,IACPX,KAAOsB,UAAUX,EAAAA,KAAAA,WAEbQ,OACFA,KAAMI,QAAQJ,IAAAA,IAChBnB,KAAOmB,KAGTpB,GAAWC,EAAAA,GAGS,MAAhBA,GAAKY,WAITQ,KAAepB,KAAO,MAAMoB,IAC5BC,KAA0C,OAAvBrB,GAAKa,WAAW,CAAA;MACrC;AAQA,aAFAO,KAAehB,GAAqBgB,IAAAA,CAAeC,EAAAA,GAE/CA,KACED,GAAaR,SAAS,IACjB,MAAMQ,KAEN,MACAA,GAAaR,SAAS,IACxBQ,KAEA;IAEX,GAEAI,WAAW,SAAmBxB,IAAAA;AAG5B,UAFAD,GAAWC,EAAAA,GAES,MAAhBA,GAAKY;AAAc,eAAO;AAE9B,UAAIa,KAAoC,OAAvBzB,GAAKa,WAAW,CAAA,GAC7Ba,KAAyD,OAArC1B,GAAKa,WAAWb,GAAKY,SAAS,CAAA;AAQtD,aAHoB,OAFpBZ,KAAOI,GAAqBJ,IAAAA,CAAOyB,EAAAA,GAE1Bb,UAAiBa,OAAYzB,KAAO,MACzCA,GAAKY,SAAS,KAAKc,OAAmB1B,MAAQ,MAE9CyB,KAAmB,MAAMzB,KACtBA;IACT,GAEAyB,YAAY,SAAoBzB,IAAAA;AAE9B,aADAD,GAAWC,EAAAA,GACJA,GAAKY,SAAS,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA;IAC5C,GAEAc,MAAM,WAAA;AACJ,UAAyB,MAArBL,UAAUV;AACZ,eAAO;AAET,eADIgB,IACKjB,KAAI,GAAGA,KAAIW,UAAUV,QAAAA,EAAUD,IAAG;AACzC,YAAIkB,KAAMP,UAAUX,EAAAA;AACpBZ,QAAAA,GAAW8B,EAAAA,GACPA,GAAIjB,SAAS,MAAA,WACXgB,KACFA,KAASC,KAETD,MAAU,MAAMC;MAEtB;AACA,aAAA,WAAID,KACK,MACFX,GAAMO,UAAUI,EAAAA;IACzB,GAEAE,UAAU,SAAkBC,IAAMC,IAAAA;AAIhC,UAHAjC,GAAWgC,EAAAA,GACXhC,GAAWiC,EAAAA,GAEPD,OAASC;AAAI,eAAO;AAKxB,WAHAD,KAAOd,GAAMC,QAAQa,EAAAA,QACrBC,KAAKf,GAAMC,QAAQc,EAAAA;AAEF,eAAO;AAIxB,eADIC,KAAY,GACTA,KAAYF,GAAKnB,UACa,OAA/BmB,GAAKlB,WAAWoB,EAAAA,GAAAA,EADYA;AAAAA;AASlC,eALIC,KAAUH,GAAKnB,QACfuB,KAAUD,KAAUD,IAGpBG,KAAU,GACPA,KAAUJ,GAAGpB,UACa,OAA3BoB,GAAGnB,WAAWuB,EAAAA,GAAAA,EADUA;AAAAA;AAW9B,eANIC,KADQL,GAAGpB,SACKwB,IAGhBxB,KAASuB,KAAUE,KAAQF,KAAUE,IACrCC,KAAAA,IACA3B,KAAI,GACDA,MAAKC,IAAAA,EAAUD,IAAG;AACvB,YAAIA,OAAMC,IAAQ;AAChB,cAAIyB,KAAQzB,IAAQ;AAClB,gBAAmC,OAA/BoB,GAAGnB,WAAWuB,KAAUzB,EAAAA;AAG1B,qBAAOqB,GAAGhB,MAAMoB,KAAUzB,KAAI,CAAA;AACzB,gBAAU,MAANA;AAGT,qBAAOqB,GAAGhB,MAAMoB,KAAUzB,EAAAA;UAE9B;AAAWwB,YAAAA,KAAUvB,OACoB,OAAnCmB,GAAKlB,WAAWoB,KAAYtB,EAAAA,IAG9B2B,KAAgB3B,KACD,MAANA,OAGT2B,KAAgB;AAGpB;QACF;AACA,YAAIC,KAAWR,GAAKlB,WAAWoB,KAAYtB,EAAAA;AAE3C,YAAI4B,OADSP,GAAGnB,WAAWuB,KAAUzB,EAAAA;AAEnC;AACoB,eAAb4B,OACPD,KAAgB3B;MACpB;AAEA,UAAI6B,KAAM;AAGV,WAAK7B,KAAIsB,KAAYK,KAAgB,GAAG3B,MAAKuB,IAAAA,EAAWvB;AAClDA,QAAAA,OAAMuB,MAAkC,OAAvBH,GAAKlB,WAAWF,EAAAA,MAChB,MAAf6B,GAAI5B,SACN4B,MAAO,OAEPA,MAAO;AAMb,aAAIA,GAAI5B,SAAS,IACR4B,KAAMR,GAAGhB,MAAMoB,KAAUE,EAAAA,KAEhCF,MAAWE,IACoB,OAA3BN,GAAGnB,WAAWuB,EAAAA,KAAAA,EACdA,IACGJ,GAAGhB,MAAMoB,EAAAA;IAEpB,GAEAK,WAAW,SAAmBzC,IAAAA;AAC5B,aAAOA;IACT,GAEA0C,SAAS,SAAiB1C,IAAAA;AAExB,UADAD,GAAWC,EAAAA,GACS,MAAhBA,GAAKY;AAAc,eAAO;AAK9B,eAJIN,KAAON,GAAKa,WAAW,CAAA,GACvB8B,KAAmB,OAATrC,IACVsC,KAAAA,IACAC,KAAAA,MACKlC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA;AAEtC,YAAa,QADbL,KAAON,GAAKa,WAAWF,EAAAA,IAAAA;AAEnB,cAAA,CAAKkC,IAAc;AACjBD,YAAAA,KAAMjC;AACN;UACF;QAAA;AAGFkC,UAAAA,KAAAA;AAIJ,aAAA,OAAID,KAAmBD,KAAU,MAAM,MACnCA,MAAmB,MAARC,KAAkB,OAC1B5C,GAAKgB,MAAM,GAAG4B,EAAAA;IACvB,GAEAE,UAAU,SAAkB9C,IAAM+C,IAAAA;AAChC,UAAA,WAAIA,MAAoC,YAAA,OAARA;AAAkB,cAAM,IAAI9C,UAAU,iCAAA;AACtEF,MAAAA,GAAWC,EAAAA;AAEX,UAGIW,IAHAqC,KAAQ,GACRJ,KAAAA,IACAC,KAAAA;AAGJ,UAAA,WAAIE,MAAqBA,GAAInC,SAAS,KAAKmC,GAAInC,UAAUZ,GAAKY,QAAQ;AACpE,YAAImC,GAAInC,WAAWZ,GAAKY,UAAUmC,OAAQ/C;AAAM,iBAAO;AACvD,YAAIiD,KAASF,GAAInC,SAAS,GACtBsC,KAAAA;AACJ,aAAKvC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,IAAG;AACrC,cAAIL,KAAON,GAAKa,WAAWF,EAAAA;AAC3B,cAAa,OAATL,IAAAA;AAGA,gBAAA,CAAKuC,IAAc;AACjBG,cAAAA,KAAQrC,KAAI;AACZ;YACF;UAAA;AAAA,mBAEEuC,OAGFL,KAAAA,OACAK,KAAmBvC,KAAI,IAErBsC,MAAU,MAER3C,OAASyC,GAAIlC,WAAWoC,EAAAA,IAAAA,MACR,EAAZA,OAGJL,KAAMjC,OAKRsC,KAAAA,IACAL,KAAMM;QAId;AAGA,eADIF,OAAUJ,KAAKA,KAAMM,KAAAA,OAA0BN,OAAYA,KAAM5C,GAAKY,SACnEZ,GAAKgB,MAAMgC,IAAOJ,EAAAA;MAC3B;AACE,WAAKjC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA;AAClC,YAA2B,OAAvBX,GAAKa,WAAWF,EAAAA,GAAAA;AAGhB,cAAA,CAAKkC,IAAc;AACjBG,YAAAA,KAAQrC,KAAI;AACZ;UACF;QAAA;AAAA,iBACSiC,OAGXC,KAAAA,OACAD,KAAMjC,KAAI;AAId,aAAA,OAAIiC,KAAmB,KAChB5C,GAAKgB,MAAMgC,IAAOJ,EAAAA;IAE7B,GAEAO,SAAS,SAAiBnD,IAAAA;AACxBD,MAAAA,GAAWC,EAAAA;AAQX,eAPIoD,KAAAA,IACAC,KAAY,GACZT,KAAAA,IACAC,KAAAA,MAGAS,KAAc,GACT3C,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,IAAG;AACzC,YAAIL,KAAON,GAAKa,WAAWF,EAAAA;AAC3B,YAAa,OAATL;AAAAA,iBASAsC,OAGFC,KAAAA,OACAD,KAAMjC,KAAI,IAEC,OAATL,KAAAA,OAEI8C,KACFA,KAAWzC,KACY,MAAhB2C,OACPA,KAAc,KAAA,OACTF,OAGTE,KAAAA;iBArBE,CAAKT,IAAc;AACjBQ,UAAAA,KAAY1C,KAAI;AAChB;QACF;MAoBN;AAEA,aAAA,OAAIyC,MAAAA,OAAmBR,MAEH,MAAhBU,MAEgB,MAAhBA,MAAqBF,OAAaR,KAAM,KAAKQ,OAAaC,KAAY,IACjE,KAEFrD,GAAKgB,MAAMoC,IAAUR,EAAAA;IAC9B,GAEAW,QAAQ,SAAgBC,IAAAA;AACtB,UAAmB,SAAfA,MAA6C,YAAA,OAAfA;AAChC,cAAM,IAAIvD,UAAU,qEAAA,OAA4EuD,EAAAA;AAElG,aAvVJ,SAAiBC,IAAKD,IAAAA;AACpB,YAAIE,KAAMF,GAAWE,OAAOF,GAAWG,MACnCC,KAAOJ,GAAWI,SAASJ,GAAWK,QAAQ,OAAOL,GAAWT,OAAO;AAC3E,eAAKW,KAGDA,OAAQF,GAAWG,OACdD,KAAME,KAERF,KA8UU,MA9UEE,KALVA;MAMX,EA6UmB,GAAKJ,EAAAA;IACtB,GAEAM,OAAO,SAAe9D,IAAAA;AACpBD,MAAAA,GAAWC,EAAAA;AAEX,UAAI+D,KAAM,EAAEJ,MAAM,IAAID,KAAK,IAAIE,MAAM,IAAIb,KAAK,IAAIc,MAAM,GAAA;AACxD,UAAoB,MAAhB7D,GAAKY;AAAc,eAAOmD;AAC9B,UAEIf,IAFA1C,KAAON,GAAKa,WAAW,CAAA,GACvBY,KAAsB,OAATnB;AAEbmB,MAAAA,MACFsC,GAAIJ,OAAO,KACXX,KAAQ,KAERA,KAAQ;AAaV,eAXII,KAAAA,IACAC,KAAY,GACZT,KAAAA,IACAC,KAAAA,MACAlC,KAAIX,GAAKY,SAAS,GAIlB0C,KAAc,GAGX3C,MAAKqC,IAAAA,EAASrC;AAEnB,YAAa,QADbL,KAAON,GAAKa,WAAWF,EAAAA;AAAAA,iBAUnBiC,OAGFC,KAAAA,OACAD,KAAMjC,KAAI,IAEC,OAATL,KAAAA,OAEI8C,KAAiBA,KAAWzC,KAA2B,MAAhB2C,OAAmBA,KAAc,KAAA,OACnEF,OAGXE,KAAAA;iBAlBE,CAAKT,IAAc;AACjBQ,UAAAA,KAAY1C,KAAI;AAChB;QACF;AAwCN,aAAA,OArBIyC,MAAAA,OAAmBR,MAEP,MAAhBU,MAEgB,MAAhBA,MAAqBF,OAAaR,KAAM,KAAKQ,OAAaC,KAAY,IAAA,OAChET,OACiCmB,GAAIH,OAAOG,GAAIF,OAAhC,MAAdR,MAAmB5B,KAAkCzB,GAAKgB,MAAM,GAAG4B,EAAAA,IAAgC5C,GAAKgB,MAAMqC,IAAWT,EAAAA,MAG7G,MAAdS,MAAmB5B,MACrBsC,GAAIF,OAAO7D,GAAKgB,MAAM,GAAGoC,EAAAA,GACzBW,GAAIH,OAAO5D,GAAKgB,MAAM,GAAG4B,EAAAA,MAEzBmB,GAAIF,OAAO7D,GAAKgB,MAAMqC,IAAWD,EAAAA,GACjCW,GAAIH,OAAO5D,GAAKgB,MAAMqC,IAAWT,EAAAA,IAEnCmB,GAAIhB,MAAM/C,GAAKgB,MAAMoC,IAAUR,EAAAA,IAG7BS,KAAY,IAAGU,GAAIL,MAAM1D,GAAKgB,MAAM,GAAGqC,KAAY,CAAA,IAAY5B,OAAYsC,GAAIL,MAAM,MAElFK;IACT,GAEAN,KAAK,KACLO,WAAW,KACXC,OAAO,MACPhD,OAAO,KAAA;AAGTA,IAAAA,GAAMA,QAAQA,IAEdiD,GAAOC,UAAUlD;EAAAA,EAAAA,GC/gBbmD,IAA2B,CAAC;AAGhC,WAASC,EAAoBC,IAAAA;AAE5B,QAAIC,KAAeH,EAAyBE,EAAAA;AAC5C,QAAA,WAAIC;AACH,aAAOA,GAAaJ;AAGrB,QAAID,KAASE,EAAyBE,EAAAA,IAAY,EAGjDH,SAAS,CAAC,EAAA;AAOX,WAHAK,EAAoBF,EAAAA,EAAUJ,IAAQA,GAAOC,SAASE,CAAAA,GAG/CH,GAAOC;EACf;ACrBAE,IAAoBI,IAAI,CAACN,IAASO,OAAAA;AACjC,aAAQC,MAAOD;AACXL,QAAoBO,EAAEF,IAAYC,EAAAA,KAAAA,CAASN,EAAoBO,EAAET,IAASQ,EAAAA,KAC5EE,OAAOC,eAAeX,IAASQ,IAAK,EAAEI,YAAAA,MAAkBC,KAAKN,GAAWC,EAAAA,EAAAA,CAAAA;EAE1E,GCNDN,EAAoBO,IAAI,CAACK,IAAKC,OAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,IAAKC,EAAAA,GCClFb,EAAoBiB,IAAKnB,CAAAA,OAAAA;AACH,mBAAA,OAAXoB,UAA0BA,OAAOC,eAC1CX,OAAOC,eAAeX,IAASoB,OAAOC,aAAa,EAAEC,OAAO,SAAA,CAAA,GAE7DZ,OAAOC,eAAeX,IAAS,cAAc,EAAEsB,OAAAA,KAAO,CAAA;EAAO;AAAA,MAAA,IAAA,CAAA;ACQvD,MAAIC;AAEX,MAAA,EAAA,EAAA,CAAA,GAAA,EAAA,EAAA,GAAA,EAAA,KAAA,MAAA,GAAA,OAAA,MAAA,EAAA,CAAA,GAAuB,YAAA,OAAZnE;AACVmE,QAAiC,YAArBnE,QAAQoE;WACW,YAAA,OAAdC,WAAwB;AACzC,QAAIC,KAAYD,UAAUC;AAC1BH,QAAYG,GAAUC,QAAQ,SAAA,KAAc;EAAA;ACV7C,QAAMC,IAAiB,kBACjBC,IAAoB,OACpBC,IAAoB;AAE1B,WAASC,EAAanC,IAAUoC,IAAAA;AAG/B,QAAA,CAAKpC,GAAIqC,UAAUD;AAClB,YAAM,IAAIE,MAAM,2DAA2DtC,GAAIuC,SAAAA,aAAsBvC,GAAI/D,IAAAA,cAAkB+D,GAAIwC,KAAAA,iBAAsBxC,GAAIyC,QAAAA,IAAAA;AAK1J,QAAIzC,GAAIqC,UAAAA,CAAWL,EAAeU,KAAK1C,GAAIqC,MAAAA;AAC1C,YAAM,IAAIC,MAAM,iDAAA;AAQjB,QAAItC,GAAI/D;AACP,UAAI+D,GAAIuC,WAAAA;AACP,YAAA,CAAKN,EAAkBS,KAAK1C,GAAI/D,IAAAA;AAC/B,gBAAM,IAAIqG,MAAM,0IAAA;MAAA,WAGbJ,EAAkBQ,KAAK1C,GAAI/D,IAAAA;AAC9B,cAAM,IAAIqG,MAAM,2HAAA;;EAIpB;AAkCA,QAAMK,IAAS,IACTC,IAAS,KACTC,IAAU;EAkBT,MAAMC,EAAAA;IA2DZ,YAAsBC,IAAsCR,IAAoBtG,IAAeuG,IAAgBC,IAAmBL,KAAAA,OAAmB;AApC5IC;AAMAE;AAKAtG;AAKAuG;AAKAC;AAiBoB,kBAAA,OAAjBM,MACVC,KAAKX,SAASU,GAAaV,UAAUM,GACrCK,KAAKT,YAAYQ,GAAaR,aAAaI,GAC3CK,KAAK/G,OAAO8G,GAAa9G,QAAQ0G,GACjCK,KAAKR,QAAQO,GAAaP,SAASG,GACnCK,KAAKP,WAAWM,GAAaN,YAAYE,MAKzCK,KAAKX,SAvHR,SAAoBA,IAAgBD,IAAAA;AACnC,eAAKC,MAAWD,KAGTC,KAFC;MAGT,EAkH4BU,IAAcX,EAAAA,GACvCY,KAAKT,YAAYA,MAAaI,GAC9BK,KAAK/G,OAjHR,SAA8BoG,IAAgBpG,IAAAA;AAM7C,gBAAQoG,IAAAA;UACP,KAAK;UACL,KAAK;UACL,KAAK;AACCpG,YAAAA,KAEMA,GAAK,CAAA,MAAO2G,MACtB3G,KAAO2G,IAAS3G,MAFhBA,KAAO2G;QAAAA;AAMV,eAAO3G;MACR,EA+FoC+G,KAAKX,QAAQpG,MAAQ0G,CAAAA,GACtDK,KAAKR,QAAQA,MAASG,GACtBK,KAAKP,WAAWA,MAAYE,GAE5BR,EAAaa,MAAMZ,EAAAA;IAErB;IA7EA,OAAA,MAAaa,IAAAA;AACZ,aAAIA,cAAiBH,KAAAA,CAAAA,CAGhBG,MAGoC,YAAA,OAArBA,GAAOV,aACU,YAAA,OAApBU,GAAOR,YACS,YAAA,OAAhBQ,GAAOhH,QACU,YAAA,OAAjBgH,GAAOT,SACW,YAAA,OAAlBS,GAAOZ,UACW,YAAA,OAAlBY,GAAOC,UACS,cAAA,OAAhBD,GAAOE,QACa,cAAA,OAApBF,GAAOG;IACzB;IA0FA,IAAA,SAAIF;AAIH,aAAOG,EAAYL,MAAAA,KAAM;IAC1B;IAIA,KAAKM,IAAAA;AAEJ,UAAA,CAAKA;AACJ,eAAON;AAGR,UAAA,EAAI,QAAEX,IAAM,WAAEE,IAAS,MAAEtG,IAAI,OAAEuG,IAAK,UAAEC,GAAAA,IAAaa;AA2BnD,aAAA,WA1BIjB,KACHA,KAASW,KAAKX,SACO,SAAXA,OACVA,KAASM,IAAAA,WAENJ,KACHA,KAAYS,KAAKT,YACO,SAAdA,OACVA,KAAYI,IAAAA,WAET1G,KACHA,KAAO+G,KAAK/G,OACO,SAATA,OACVA,KAAO0G,IAAAA,WAEJH,KACHA,KAAQQ,KAAKR,QACO,SAAVA,OACVA,KAAQG,IAAAA,WAELF,KACHA,KAAWO,KAAKP,WACO,SAAbA,OACVA,KAAWE,IAGRN,OAAWW,KAAKX,UAChBE,OAAcS,KAAKT,aACnBtG,OAAS+G,KAAK/G,QACduG,OAAUQ,KAAKR,SACfC,OAAaO,KAAKP,WAEdO,OAGD,IAAIO,EAAIlB,IAAQE,IAAWtG,IAAMuG,IAAOC,EAAAA;IAChD;IAUA,OAAA,MAAaf,IAAeU,KAAAA,OAAmB;AAC9C,YAAMoB,KAAQX,EAAQY,KAAK/B,EAAAA;AAC3B,aAAK8B,KAGE,IAAID,EACVC,GAAM,CAAA,KAAMb,GACZe,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1BP,EAAAA,IARO,IAAImB,EAAIZ,GAAQA,GAAQA,GAAQA,GAAQA,CAAAA;IAUjD;IAuBA,OAAA,KAAY1G,IAAAA;AAEX,UAAIsG,KAAYI;AAWhB,UANIhB,MACH1F,KAAOA,GAAK0H,QAAQ,OAAOf,CAAAA,IAKxB3G,GAAK,CAAA,MAAO2G,KAAU3G,GAAK,CAAA,MAAO2G,GAAQ;AAC7C,cAAMgB,KAAM3H,GAAK8F,QAAQa,GAAQ,CAAA;AAAA,eAC7BgB,MACHrB,KAAYtG,GAAK4H,UAAU,CAAA,GAC3B5H,KAAO2G,MAEPL,KAAYtG,GAAK4H,UAAU,GAAGD,EAAAA,GAC9B3H,KAAOA,GAAK4H,UAAUD,EAAAA,KAAQhB;MAAAA;AAIhC,aAAO,IAAIW,EAAI,QAAQhB,IAAWtG,IAAM0G,GAAQA,CAAAA;IACjD;IAEA,OAAA,KAAYmB,IAAAA;AACX,YAAMC,KAAS,IAAIR,EAClBO,GAAWzB,QACXyB,GAAWvB,WACXuB,GAAW7H,MACX6H,GAAWtB,OACXsB,GAAWrB,QAAAA;AAGZ,aADAN,EAAa4B,IAAAA,IAAQ,GACdA;IACR;IAeA,SAASC,KAAAA,OAAwB;AAChC,aAAOC,EAAajB,MAAMgB,EAAAA;IAC3B;IAEA,SAAAE;AACC,aAAOlB;IACR;IAMA,OAAA,OAAcmB,IAAAA;AACb,UAAKA,IAEE;AAAA,YAAIA,cAAgBrB;AAC1B,iBAAOqB;AACD;AACN,gBAAMJ,KAAS,IAAIR,EAAIY,EAAAA;AAGvB,iBAFAJ,GAAOK,aAAwBD,GAAME,UACrCN,GAAOO,UAAqBH,GAAMI,SAASC,IAA4BL,GAAMjB,SAAS,MAC/Ea;QAAAA;MAAAA;AAPP,aAAYI;IASd;EAAA;AAkBD,QAAMK,IAAiB7C,IAAY,IAAA;EAGnC,MAAM4B,UAAYT,EAAAA;IAAlB;;AAECsB,wCAA4B;AAC5BE,qCAAyB;;IAEzB,IAAA,SAAapB;AAIZ,aAHKF,KAAKsB,YACTtB,KAAKsB,UAAUjB,EAAYL,MAAAA,KAAM,IAE3BA,KAAKsB;IACb;IAES,SAASN,KAAAA,OAAwB;AACzC,aAAKA,KAOGC,EAAajB,MAAAA,IAAM,KANrBA,KAAKoB,eACTpB,KAAKoB,aAAaH,EAAajB,MAAAA,KAAM,IAE/BA,KAAKoB;IAKd;IAES,SAAAF;AACR,YAAM1H,KAAgB,EACrBiI,MAAM,EAAA;AA0BP,aAvBIzB,KAAKsB,YACR9H,GAAI0G,SAASF,KAAKsB,SAClB9H,GAAI+H,OAAOC,IAERxB,KAAKoB,eACR5H,GAAI6H,WAAWrB,KAAKoB,aAGjBpB,KAAK/G,SACRO,GAAIP,OAAO+G,KAAK/G,OAEb+G,KAAKX,WACR7F,GAAI6F,SAASW,KAAKX,SAEfW,KAAKT,cACR/F,GAAI+F,YAAYS,KAAKT,YAElBS,KAAKR,UACRhG,GAAIgG,QAAQQ,KAAKR,QAEdQ,KAAKP,aACRjG,GAAIiG,WAAWO,KAAKP,WAEdjG;IACR;EAAA;AAID,QAAMkI,IAAwC,EAC7C,IAAkB,OAClB,IAAkB,OAClB,IAAyB,OACzB,IAAiB,OACjB,IAA8B,OAC9B,IAA+B,OAC/B,IAAmB,OAEnB,IAA4B,OAC5B,IAAuB,OACvB,IAAsB,OACtB,IAAwB,OACxB,IAAsB,OACtB,IAAuB,OACvB,IAAqB,OACrB,IAAiB,OACjB,IAAkB,OAClB,IAAsB,OACtB,IAAmB,OAEnB,IAAkB,MAAA;AAGnB,WAASC,EAAuBC,IAAsBC,IAAiBC,IAAAA;AACtE,QAAItI,IACAuI,KAAAA;AAEJ,aAASC,KAAM,GAAGA,KAAMJ,GAAa/H,QAAQmI,MAAO;AACnD,YAAMzI,KAAOqI,GAAa9H,WAAWkI,EAAAA;AAGrC,UACEzI,MAAQ,MAAcA,MAAQ,OAC3BA,MAAQ,MAAcA,MAAQ,MAC9BA,MAAQ,MAAmBA,MAAQ,MAC3B,OAATA,MACS,OAATA,MACS,OAATA,MACS,QAATA,MACCsI,MAAmB,OAATtI,MACVuI,MAAwB,OAATvI,MACfuI,MAAwB,OAATvI,MACfuI,MAAwB,OAATvI;AAAAA,eAGfwI,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,IAAiBC,EAAAA,CAAAA,GAClED,KAAAA,KAAmB,WAGhBvI,OACHA,MAAOoI,GAAaM,OAAOF,EAAAA;WAGtB;AAAA,mBAEFxI,OACHA,KAAMoI,GAAaO,OAAO,GAAGH,EAAAA;AAI9B,cAAMI,KAAUV,EAAYnI,EAAAA;AAAAA,mBACxB6I,MAAAA,OAGCL,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,IAAiBC,EAAAA,CAAAA,GAClED,KAAAA,KAIDvI,MAAO4I,MAAAA,OAEGL,OAEVA,KAAkBC;MAAAA;IAAAA;AASrB,WAAA,OAJID,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,EAAAA,CAAAA,IAAAA,WAG3CvI,KAAoBA,KAAMoI;EAClC;AAEA,WAASS,EAA0BpJ,IAAAA;AAClC,QAAIO;AACJ,aAASwI,KAAM,GAAGA,KAAM/I,GAAKY,QAAQmI,MAAO;AAC3C,YAAMzI,KAAON,GAAKa,WAAWkI,EAAAA;AAChB,aAATzI,MAAmC,OAATA,MAAAA,WACzBC,OACHA,KAAMP,GAAKkJ,OAAO,GAAGH,EAAAA,IAEtBxI,MAAOkI,EAAYnI,EAAAA,KAAAA,WAEfC,OACHA,MAAOP,GAAK+I,EAAAA;IAAAA;AAIf,WAAA,WAAOxI,KAAoBA,KAAMP;EAClC;AAKO,WAASoH,EAAYiC,IAAUC,IAAAA;AAErC,QAAI7D;AAsBJ,WAnBCA,KAFG4D,GAAI/C,aAAa+C,GAAIrJ,KAAKY,SAAS,KAAoB,WAAfyI,GAAIjD,SAEvC,KAAKiD,GAAI/C,SAAAA,GAAY+C,GAAIrJ,IAAAA,KAEN,OAA3BqJ,GAAIrJ,KAAKa,WAAW,CAAA,MAChBwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,QACxH,OAA3BwI,GAAIrJ,KAAKa,WAAW,CAAA,IAElByI,KAIID,GAAIrJ,KAAKkJ,OAAO,CAAA,IAFhBG,GAAIrJ,KAAK,CAAA,EAAGuJ,YAAAA,IAAgBF,GAAIrJ,KAAKkJ,OAAO,CAAA,IAM7CG,GAAIrJ,MAET0F,MACHD,KAAQA,GAAMiC,QAAQ,OAAO,IAAA,IAEvBjC;EACR;AAKA,WAASuC,EAAaqB,IAAUtB,IAAAA;AAE/B,UAAMyB,KAAWzB,KAEdqB,IADAV;AAGH,QAAInI,KAAM,IAAA,EACN,QAAE6F,IAAM,WAAEE,IAAS,MAAEtG,IAAI,OAAEuG,IAAK,UAAEC,GAAAA,IAAa6C;AASnD,QARIjD,OACH7F,MAAO6F,IACP7F,MAAO,OAEJ+F,MAAwB,WAAXF,QAChB7F,MAAOoG,GACPpG,MAAOoG,IAEJL,IAAW;AACd,UAAIqB,KAAMrB,GAAUR,QAAQ,GAAA;AAC5B,UAAA,OAAI6B,IAAY;AAEf,cAAM8B,KAAWnD,GAAU4C,OAAO,GAAGvB,EAAAA;AACrCrB,QAAAA,KAAYA,GAAU4C,OAAOvB,KAAM,CAAA,GACnCA,KAAM8B,GAAS1I,YAAY,GAAA,GAAA,OACvB4G,KACHpH,MAAOiJ,GAAQC,IAAAA,OAAU,KAAO,KAGhClJ,MAAOiJ,GAAQC,GAASP,OAAO,GAAGvB,EAAAA,GAAAA,OAAM,KAAO,GAC/CpH,MAAO,KACPA,MAAOiJ,GAAQC,GAASP,OAAOvB,KAAM,CAAA,GAAA,OAAI,IAAO,IAEjDpH,MAAO;MAAA;AAER+F,MAAAA,KAAYA,GAAUiD,YAAAA,GACtB5B,KAAMrB,GAAUvF,YAAY,GAAA,GAAA,OACxB4G,KACHpH,MAAOiJ,GAAQlD,IAAAA,OAAW,IAAO,KAGjC/F,MAAOiJ,GAAQlD,GAAU4C,OAAO,GAAGvB,EAAAA,GAAAA,OAAM,IAAO,GAChDpH,MAAO+F,GAAU4C,OAAOvB,EAAAA;IAAAA;AAG1B,QAAI3H,IAAM;AAET,UAAIA,GAAKY,UAAU,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA,KAAgD,OAAvBb,GAAKa,WAAW,CAAA,GAAuB;AACvG,cAAMP,KAAON,GAAKa,WAAW,CAAA;AACzBP,QAAAA,MAAQ,MAAcA,MAAQ,OACjCN,KAAO,IAAI0J,OAAOC,aAAarJ,KAAO,EAAA,CAAA,IAAON,GAAKkJ,OAAO,CAAA,CAAA;MAAA,WAEhDlJ,GAAKY,UAAU,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA,GAAuB;AACrE,cAAMP,KAAON,GAAKa,WAAW,CAAA;AACzBP,QAAAA,MAAQ,MAAcA,MAAQ,OACjCN,KAAO,GAAG0J,OAAOC,aAAarJ,KAAO,EAAA,CAAA,IAAON,GAAKkJ,OAAO,CAAA,CAAA;MAAA;AAI1D3I,MAAAA,MAAOiJ,GAAQxJ,IAAAA,MAAM,KAAM;IAAA;AAU5B,WARIuG,OACHhG,MAAO,KACPA,MAAOiJ,GAAQjD,IAAAA,OAAO,KAAO,IAE1BC,OACHjG,MAAO,KACPA,MAAQwH,KAAgEvB,KAAjDkC,EAAuBlC,IAAAA,OAAU,KAAO,IAEzDjG;EACR;AAIA,WAASqJ,EAA2BC,IAAAA;AACnC,QAAA;AACC,aAAOC,mBAAmBD,EAAAA;IAAAA,QACzB;AACD,aAAIA,GAAIjJ,SAAS,IACTiJ,GAAIX,OAAO,GAAG,CAAA,IAAKU,EAA2BC,GAAIX,OAAO,CAAA,CAAA,IAEzDW;IAAAA;EAGV;AAEA,QAAME,IAAiB;AAEvB,WAAStC,EAAcoC,IAAAA;AACtB,WAAKA,GAAItC,MAAMwC,CAAAA,IAGRF,GAAInC,QAAQqC,GAAiBxC,CAAAA,OAAUqC,EAA2BrC,EAAAA,CAAAA,IAFjEsC;EAGT;AAAA,MAAA,IAAA,EAAA,GAAA;ACjqBA,QAAMG,IAAY,EAAA,SAAkB,GAC9BC,IAAQ;AAEP,MAAUC;AAAAA,GAAjB,SAAiBA,IAAAA;AAeG,IAAAC,GAAAC,WAAhB,SAAyBf,OAAagB,IAAAA;AAClC,aAAOhB,GAAInC,KAAK,EAAElH,MAAMgK,EAAUrI,KAAK0H,GAAIrJ,MAAAA,GAASqK,EAAAA,EAAAA,CAAAA;IACxD,GAgBgBF,GAAAG,cAAhB,SAA4BjB,OAAagB,IAAAA;AACrC,UAAIrK,KAAOqJ,GAAIrJ,MACXuK,KAAAA;AACAvK,MAAAA,GAAK,CAAA,MAAOiK,MACZjK,KAAOiK,IAAQjK,IACfuK,KAAAA;AAEJ,UAAInJ,KAAe4I,EAAU9I,QAAQlB,IAAAA,GAASqK,EAAAA;AAI9C,aAHIE,MAAcnJ,GAAa,CAAA,MAAO6I,KAAAA,CAAUZ,GAAI/C,cAChDlF,KAAeA,GAAawG,UAAU,CAAA,IAEnCyB,GAAInC,KAAK,EAAElH,MAAMoB,GAAAA,CAAAA;IAC5B,GAUgB+I,GAAAzH,UAAhB,SAAwB2G,IAAAA;AACpB,UAAwB,MAApBA,GAAIrJ,KAAKY,UAAgByI,GAAIrJ,SAASiK;AACtC,eAAOZ;AAEX,UAAIrJ,KAAOgK,EAAUtH,QAAQ2G,GAAIrJ,IAAAA;AAIjC,aAHoB,MAAhBA,GAAKY,UAAuC,OAAvBZ,GAAKa,WAAW,CAAA,MACrCb,KAAO,KAEJqJ,GAAInC,KAAK,EAAElH,MAAAA,GAAAA,CAAAA;IACtB,GAUgBmK,GAAArH,WAAhB,SAAyBuG,IAAAA;AACrB,aAAOW,EAAUlH,SAASuG,GAAIrJ,IAAAA;IAClC,GAUgBmK,GAAAhH,UAAhB,SAAwBkG,IAAAA;AACpB,aAAOW,EAAU7G,QAAQkG,GAAIrJ,IAAAA;IACjC;EACH,EAzFgBkK,MAAAA,IAAK,CAAA,EAAA,GAAA,MAAA;AAAA,GAAA;AAAA,IAAA,EAAA,KAAA,MAAA,IAAA;;;ACkBhB,SAAU,iBAAiB,UAAgC;AAC/D,MAAI,aAAc,GAAiD;AACjE,WAAO;EACT;AACA,MAAI,aAAc,GAAwD;AACxE,WAAO;EACT;AACA,MAAI,aAAc,GAAoD;AACpE,WAAO;EACT;AACA,SAAO;AACT;;;ACpBM,SAAU,YAAY,KAAsB;AAChD,SAAO;AACT;;;ACdM,SAAU,UAAU,OAAoB;AAC5C,SAAO;IACL,OAAO,EAAE,MAAM,MAAM,kBAAkB,GAAG,WAAW,MAAM,cAAc,EAAC;IAC1E,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAC;;AAExE;AAUM,SAAU,QAAQ,OAAgB;AACtC,SAAO;IACL,iBAAiB,MAAM,MAAM,OAAO;IACpC,aAAa,MAAM,MAAM,YAAY;IACrC,eAAe,MAAM,IAAI,OAAO;IAChC,WAAW,MAAM,IAAI,YAAY;;AAErC;;;ACCM,SAAU,qBACd,oBAAoD;AAEpD,SAAO;IACL,GAAG,QAAQ,mBAAmB,SAAS,KAAK;IAC5C,SAAS,mBAAmB;IAC5B,UAAU,IAAI,MAAM,mBAAmB,SAAS,GAAG;;AAEvD;;;ACYM,SAAU,aAAa,YAA0B;AACrD,QAAM,aAAwC;IAC5C,GAAG,QAAQ,WAAW,KAAK;IAC3B,SAAS,WAAW;IACpB,UAAU,WAAW,WACjB,iBAAiB,WAAW,QAAQ,IACnC;;AAGP,MAAI,WAAW,QAAQ,MAAM;AAC3B,eAAW,OACT,WAAW,mBAAmB,OAC1B,OAAO,WAAW,IAAI,IACtB,EAAE,OAAO,OAAO,WAAW,IAAI,GAAG,QAAQ,IAAI,MAAM,WAAW,gBAAgB,IAAI,EAAC;EAC5F;AAEA,MAAI,WAAW,oBAAoB;AACjC,eAAW,qBAAqB,WAAW,mBAAmB,IAAI,oBAAoB;EACxF;AAEA,MAAI,WAAW,MAAM;AACnB,eAAW,OAAO,WAAW,KAAK,IAAI,WAAW;EACnD;AAEA,MAAI,WAAW,UAAU,MAAM;AAC7B,eAAW,SAAS,WAAW;EACjC;AAEA,SAAO;AACT;;;ACvDM,SAAU,WAAW,UAAsB;AAC/C,SAAO;IACL,OAAO,QAAQ,SAAS,KAAK;IAC7B,MAAM,SAAS;;AAEnB;;;ACSM,SAAU,2BACd,SAA2B;AAE3B,QAAM,SAAoD,CAAA;AAE1D,MAAI,QAAQ,kBAAkB,MAAM;AAClC,WAAO,iBAAiB,QAAQ;EAClC;AACA,MAAI,QAAQ,qBAAqB,MAAM;AACrC,WAAO,oBAAoB,QAAQ;EACrC;AACA,MAAI,QAAQ,aAAa,MAAM;AAC7B,WAAO,YAAY,QAAQ;EAC7B;AACA,MAAI,QAAQ,aAAa,MAAM;AAC7B,WAAO,YAAY,QAAQ;EAC7B;AAEA,SAAO;AACT;;;ACAM,SAAU,oBACd,mBAAoC;AAEpC,QAAM,SACJ,kBAAkB,SAAS,WACvB,EAAE,aAAa,IAAI,MAAM,kBAAkB,GAAG,EAAC,IAC/C,kBAAkB,SAAS,WACzB,EAAE,aAAa,IAAI,MAAM,kBAAkB,GAAG,EAAC,IAC/C;IACE,aAAa,IAAI,MAAM,kBAAkB,MAAM;IAC/C,aAAa,IAAI,MAAM,kBAAkB,MAAM;;AAGzD,MAAI,kBAAkB,SAAS;AAC7B,WAAO,UAAU,2BAA2B,kBAAkB,OAAO;EACvE;AAEA,SAAO;AACT;;;ACNA,SAAS,oBACP,UACA,KACA,WAAkB;AAElB,SAAO;IACL,UAAU,IAAI,MAAM,GAAG;IACvB;IACA,UAAU,WAAW,QAAQ;;AAEjC;AAUM,SAAU,gBAAgB,eAAgC;;AAC9D,QAAM,QAAiD,CAAA;AAEvD,MAAI,cAAc,SAAS;AACzB,eAAW,CAAC,KAAK,SAAS,KAAK,OAAO,QAAQ,cAAc,OAAO,GAAG;AACpE,iBAAW,YAAY,WAAW;AAChC,cAAM,KAAK,oBAAoB,UAAU,GAAG,CAAC;MAC/C;IACF;EACF;AAEA,MAAI,cAAc,iBAAiB;AACjC,eAAW,kBAAkB,cAAc,iBAAiB;AAC1D,UAAI,kBAAkB,gBAAgB;AACpC,mBAAW,YAAY,eAAe,OAAO;AAC3C,gBAAM,KACJ,oBACE,UACA,eAAe,aAAa,MAC5B,KAAA,eAAe,aAAa,aAAO,QAAA,OAAA,SAAA,KAAI,MAAS,CACjD;QAEL;MACF,OAAO;AACL,cAAM,KAAK,oBAAoB,cAAc,CAAC;MAChD;IACF;EACF;AAEA,SAAO;IACL;;AAEJ;;;AC3EM,SAAU,aAAa,YAA0B;AACrD,QAAM,SAAsC;IAC1C,OAAO,WAAW;IAClB,aAAa,WAAW;;AAG1B,MAAI,WAAW,aAAa;AAC1B,WAAO,cAAc,WAAW,YAAY,IAAI,YAAY;EAC9D;AAEA,MAAI,WAAW,UAAU;AACvB,WAAO,WAAW,WAAW,SAAS;EACxC;AAEA,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO,gBAAgB,WAAW,IAAI;EAC/C;AAEA,MAAI,WAAW,eAAe,MAAM;AAClC,WAAO,cAAc,WAAW;EAClC;AAEA,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO,WAAW;EAC3B;AAEA,SAAO;AACT;;;AClEM,SAAU,0BACd,MAA4C;AAE5C,SAAO;AACT;;;ACiBM,SAAU,UAAU,SAAoB;AAC5C,QAAM,SAAmC;IACvC,OAAO,QAAQ;IACf,IAAI,QAAQ;;AAGd,MAAI,QAAQ,WAAW;AACrB,WAAO,YAAY,QAAQ;EAC7B;AAEA,SAAO;AACT;;;ACwDM,SAAU,qBACd,MAA4B;AAE5B,MAAI,SAAU,GAAiD;AAC7D,WAAO;EACT;AACA,MAAI,SAAU,GAAmD;AAC/D,WAAO;EACT;AACA,MAAI,SAAU,GAAqD;AACjE,WAAO;EACT;AACA,MAAI,SAAU,GAAwD;AACpE,WAAO;EACT;AACA,MAAI,SAAU,GAAkD;AAC9D,WAAO;EACT;AACA,MAAI,SAAU,GAAqD;AACjE,WAAO;EACT;AACA,MAAI,SAAU,GAAkD;AAC9D,WAAO;EACT;AACA,MAAI,SAAU,GAAsD;AAClE,WAAO;EACT;AACA,MAAI,SAAU,GAAmD;AAC/D,WAAO;EACT;AACA,MAAI,SAAU,IAAsD;AAClE,WAAO;EACT;AACA,MAAI,SAAU,IAAkD;AAC9D,WAAO;EACT;AACA,MAAI,SAAU,IAAmD;AAC/D,WAAO;EACT;AACA,MAAI,SAAU,IAAkD;AAC9D,WAAO;EACT;AACA,MAAI,SAAU,IAAqD;AACjE,WAAO;EACT;AACA,MAAI,SAAU,IAAqD;AACjE,WAAO;EACT;AACA,MAAI,SAAU,IAAmD;AAC/D,WAAO;EACT;AACA,MAAI,SAAU,IAAkD;AAC9D,WAAO;EACT;AACA,MAAI,SAAU,IAAuD;AACnE,WAAO;EACT;AACA,MAAI,SAAU,IAAoD;AAChE,WAAO;EACT;AACA,MAAI,SAAU,IAAwD;AACpE,WAAO;EACT;AACA,MAAI,SAAU,IAAsD;AAClE,WAAO;EACT;AACA,MAAI,SAAU,IAAoD;AAChE,WAAO;EACT;AACA,MAAI,SAAU,IAAmD;AAC/D,WAAO;EACT;AACA,MAAI,SAAU,IAAsD;AAClE,WAAO;EACT;AAGA,SAAO;AACT;;;ACxJM,SAAU,oBACd,KAA0B;AAE1B,SAAO;AACT;;;ACHM,SAAU,iBAAiB,eAAgC;AAC/D,SAAO;IACL,OAAO,cAAc;;AAEzB;;;ACAM,SAAU,sBAAsB,UAAsB;AAC1D,SAAO;IACL,OAAO,QAAQ,SAAS,KAAK;IAC7B,MAAM,SAAS;;AAEnB;;;AC8FA,SAAS,sBACP,MAAuE;AAEvE,MAAI,WAAW,MAAM;AACnB,WAAO,QAAQ,KAAK,KAAK;EAC3B;AAEA,MAAI,YAAY,QAAQ,aAAa,MAAM;AACzC,WAAO;MACL,QAAQ,QAAQ,KAAK,MAAM;MAC3B,SAAS,QAAQ,KAAK,OAAO;;EAEjC;AAEA,SAAO,QAAQ,IAAI;AACrB;AAYM,SAAU,iBACd,gBACA,SAAgC;;AAEhC,QAAM,gBAAe,KAAA,QAAQ,kBAAY,QAAA,OAAA,SAAA,KAAI,CAAA;AAC7C,QAAM,YAAW,KAAA,eAAe,cAAQ,QAAA,OAAA,SAAA,KAAI,aAAa;AACzD,QAAM,oBAAmB,KAAA,eAAe,sBAAgB,QAAA,OAAA,SAAA,KAAI,aAAa;AACzE,QAAM,oBAAmB,KAAA,eAAe,sBAAgB,QAAA,OAAA,SAAA,KAAI,aAAa;AACzE,QAAM,kBAAiB,KAAA,eAAe,oBAAc,QAAA,OAAA,SAAA,KAAI,aAAa;AAErE,MAAI,OAAO,eAAe;AAC1B,MAAI;AAEJ,MAAI,UAAU;AACZ,YAAQ,sBAAsB,QAAQ;AACtC,QAAI,aAAa,UAAU;AACzB,aAAO,SAAS;IAClB;EACF,OAAO;AACL,YAAQ,EAAE,GAAG,QAAQ,MAAK;EAC5B;AAEA,QAAM,SAA0C;IAC9C,YAAY,SAAI,QAAJ,SAAI,SAAJ,OAAQ,eAAe;IACnC,MACE,eAAe,QAAQ,OAClB,KACD,qBAAqB,eAAe,IAAI;IAC9C,OAAO,eAAe;IACtB;;AAGF,MAAI,eAAe,qBAAqB;AACtC,WAAO,sBAAsB,eAAe,oBAAoB,IAAI,qBAAqB;EAC3F;AAEA,MAAI,eAAe,SAAS;AAC1B,WAAO,UAAU,UAAU,eAAe,OAAO;EACnD;AAEA,MAAI,kBAAkB;AACpB,WAAO,mBAAmB;EAC5B;AAEA,MAAI,eAAe,UAAU,MAAM;AACjC,WAAO,SAAS,eAAe;EACjC;AAEA,MAAI,OAAO,eAAe,kBAAkB,UAAU;AACpD,WAAO,gBAAgB,eAAe;EACxC,WAAW,eAAe,eAAe;AACvC,WAAO,gBAAgB,iBAAiB,eAAe,aAAa;EACtE;AAEA,MAAI,eAAe,cAAc,MAAM;AACrC,WAAO,aAAa,eAAe;EACrC;AAEA,MAAI,qBAAqB,GAAG;AAC1B,WAAO,kBACL;EACJ,WAAW,mBAAmB,GAAG;AAC/B,WAAO,kBACL;EACJ;AAEA,MAAI,eAAe,aAAa,MAAM;AACpC,WAAO,YAAY,eAAe;EACpC;AAEA,MAAI,eAAe,YAAY,MAAM;AACnC,WAAO,WAAW,eAAe;EACnC;AAEA,MAAI,eAAe,MAAM;AACvB,WAAO,OAAO,eAAe,KAAK,IAAI,mBAAmB;EAC3D;AAEA,SAAO;AACT;;;ACpMM,SAAU,iBACd,gBACA,SAAgC;AAEhC,SAAO;IACL,YAAY,QAAQ,eAAe,YAAY;IAC/C,aAAa,eAAe,MAAM,IAAI,CAAC,SACrC,iBAAiB,MAAM,EAAE,OAAO,QAAQ,OAAO,cAAc,eAAe,aAAY,CAAE,CAAC;;AAGjG;;;ACkDM,SAAU,aAAa,YAA0B;AACrD,MAAI,eAAgB,GAAyC;AAC3D,WAAO;EACT;AACA,MAAI,eAAgB,GAA2C;AAC7D,WAAO;EACT;AACA,MAAI,eAAgB,GAA8C;AAChE,WAAO;EACT;AACA,MAAI,eAAgB,GAA4C;AAC9D,WAAO;EACT;AACA,MAAI,eAAgB,GAA0C;AAC5D,WAAO;EACT;AACA,MAAI,eAAgB,GAA2C;AAC7D,WAAO;EACT;AACA,MAAI,eAAgB,GAA6C;AAC/D,WAAO;EACT;AACA,MAAI,eAAgB,GAA0C;AAC5D,WAAO;EACT;AACA,MAAI,eAAgB,GAAgD;AAClE,WAAO;EACT;AACA,MAAI,eAAgB,IAA0C;AAC5D,WAAO;EACT;AACA,MAAI,eAAgB,IAA+C;AACjE,WAAO;EACT;AACA,MAAI,eAAgB,IAA8C;AAChE,WAAO;EACT;AACA,MAAI,eAAgB,IAA8C;AAChE,WAAO;EACT;AACA,MAAI,eAAgB,IAA8C;AAChE,WAAO;EACT;AACA,MAAI,eAAgB,IAA4C;AAC9D,WAAO;EACT;AACA,MAAI,eAAgB,IAA4C;AAC9D,WAAO;EACT;AACA,MAAI,eAAgB,IAA6C;AAC/D,WAAO;EACT;AACA,MAAI,eAAgB,IAA2C;AAC7D,WAAO;EACT;AACA,MAAI,eAAgB,IAA4C;AAC9D,WAAO;EACT;AACA,MAAI,eAAgB,IAAyC;AAC3D,WAAO;EACT;AACA,MAAI,eAAgB,IAA0C;AAC5D,WAAO;EACT;AACA,MAAI,eAAgB,IAAgD;AAClE,WAAO;EACT;AACA,MAAI,eAAgB,IAA4C;AAC9D,WAAO;EACT;AACA,MAAI,eAAgB,IAA2C;AAC7D,WAAO;EACT;AACA,MAAI,eAAgB,IAA8C;AAChE,WAAO;EACT;AAEA,SAAO;AACT;;;AC1JM,SAAU,YAAY,WAAwB;AAClD,SAAO;AACT;;;ACiBM,SAAU,iBACd,gBAAkC;;AAElC,QAAM,SAA0C;IAC9C,SAAQ,KAAA,eAAe,YAAM,QAAA,OAAA,SAAA,KAAI;IACjC,MAAM,aAAa,eAAe,IAAI;IACtC,MAAM,eAAe;IACrB,OAAO,QAAQ,eAAe,KAAK;IACnC,gBAAgB,QAAQ,eAAe,cAAc;IACrD,OAAM,MAAA,KAAA,eAAe,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,WAAW,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;;AAGjD,MAAI,eAAe,UAAU;AAC3B,WAAO,WAAW,eAAe,SAAS,IAAI,gBAAgB;EAChE;AAEA,SAAO;AACT;;;AC3BM,SAAU,eAAe,cAA8B;AAC3D,QAAM,SAAwC;IAC5C,OAAO,aAAa,YAAY;IAChC,KAAK,aAAa,UAAU;;AAG9B,MAAI,aAAa,QAAQ,MAAM;AAC7B,WAAO,OAAO,EAAE,OAAO,aAAa,KAAI;EAC1C;AAEA,SAAO;AACT;;;AChCM,SAAU,sBACd,mBAAqD;AAErD,SAAO;IACL,cAAc,kBAAkB;IAChC,SAAS,kBAAkB;;AAE/B;;;ACgBA,SAAS,yBAAyB,OAAuB;AACvD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,EAAE,MAAK;EAChB;AAEA,SAAO,EAAE,OAAO,SAAS,MAAM,QAAQ;EAAK,MAAM,KAAK;QAAU;AACnE;AAUA,SAAS,gBACP,UAAmE;AAEnE,MAAI,OAAO,aAAa,YAAY,cAAc,UAAU;AAC1D,WAAO,CAAC,yBAAyB,QAAQ,CAAC;EAC5C;AAEA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,SAAS,IAAI,wBAAwB;EAC9C;AAEA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACpC;AAUM,SAAU,QAAQ,OAAgB;AACtC,QAAM,SAAiC;IACrC,UAAU,gBAAgB,MAAM,QAAQ;;AAG1C,MAAI,MAAM,OAAO;AACf,WAAO,QAAQ,QAAQ,MAAM,KAAK;EACpC;AAEA,SAAO;AACT;;;ACvEM,SAAU,aAAa,UAA0B;AACrD,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAC;AACxE;;;ACyBM,SAAU,OAAO,cAA8B;AACnD,QAAM,SAAiC;IACrC,OAAO,QAAQ,aAAa,KAAK;;AAGnC,MAAI,aAAa,WAAW,MAAM;AAChC,WAAO,UAAU,aAAa;EAChC;AAEA,MAAI,aAAa,UAAU,MAAM;AAC/B,WAAO,MAAM,IAAI,MAAM,aAAa,MAAM;EAC5C;AAEA,SAAO;AACT;;;ACdM,SAAU,eAAe,cAA8B;AAC3D,QAAM,SAAwC;IAC5C,OAAO,QAAQ,aAAa,WAAW;IACvC,sBAAsB,QAAQ,aAAa,oBAAoB;IAC/D,KAAK,IAAI,MAAM,aAAa,SAAS;;AAGvC,MAAI,aAAa,sBAAsB;AACrC,WAAO,uBAAuB,QAAQ,aAAa,oBAAoB;EACzE;AAEA,SAAO;AACT;;;ACTM,SAAU,kBACd,gBAA8C;AAE9C,QAAM,SAA4C,CAAA;AAClD,MAAI,UAAU;AAEd,SAAO,SAAS;AACd,WAAO,KAAK;MACV,OAAO,QAAQ,QAAQ,KAAK;KAC7B;AACD,cAAU,QAAQ;EACpB;AAEA,SAAO;AACT;;;ACPM,SAAU,2BACd,QACA,kBACA,UAA4B;AAE5B,QAAM,YAAY,oBAAI,IAAG;AAEzB,QAAM,kBAAkB,CAAC,UAAqC;AAC5D,QAAI,qBAAqB,KAAK;AAC5B,aAAO;IACT;AACA,UAAM,aAAa,MAAM,cAAa;AACtC,WAAO,MAAM,QAAQ,gBAAgB,IACjC,iBAAiB,SAAS,UAAU,IACpC,qBAAqB;EAC3B;AAEA,QAAM,aAAa,OAAO,UAAgD;AACxE,UAAM,YAAY,MAAM,aAAY;AACpC,UAAM,UAAU,MAAM,SAAS,kBAAkB,KAAK;AAEtD,QAAI,CAAC,MAAM,WAAU,KAAM,cAAc,MAAM,aAAY,KAAM,gBAAgB,KAAK,GAAG;AACvF,aAAO,OAAO,gBAAgB,OAAO,SAAS,OAAO,WAAW,CAAA,CAAE;IACpE;EACF;AAEA,QAAM,aAAa,CAAC,UAAuC;AACzD,QAAI,CAAC,gBAAgB,KAAK,GAAG;AAC3B;IACF;AAEA,QAAI;AACJ,UAAM,qBAAqB,MAAM,mBAAmB,MAAK;AACvD,mBAAa,MAAM;AACnB,eAAS,WAAW,MAAK;AACvB,mBAAW,KAAK;MAClB,GAAG,GAAG;IACR,CAAC;AAED,cAAU,IAAI,OAAO;MACnB,UAAO;AACL,qBAAa,MAAM;AACnB,2BAAmB,QAAO;MAC5B;KACD;AAED,eAAW,KAAK;EAClB;AAEA,QAAM,iBAAiB,CAAC,UAAuC;AAC7D,WAAO,OAAO,gBAAgB,OAAO,SAAS,OAAO,CAAA,CAAE;AACvD,UAAM,WAAW,UAAU,IAAI,KAAK;AACpC,QAAI,UAAU;AACZ,eAAS,QAAO;AAChB,gBAAU,OAAO,KAAK;IACxB;EACF;AAEA,QAAM,mBAAmB,OAAO,OAAO,iBAAiB,UAAU;AAClE,QAAM,qBAAqB,OAAO,OAAO,mBAAmB,CAAC,UAAS;AAvExE;AAwEI,mBAAe,KAAK;AACpB,mBAAS,YAAT,kCAAmB;EACrB,CAAC;AACD,QAAM,2BAA2B,OAAO,OAAO,yBAAyB,CAAC,EAAE,MAAK,MAAM;AA3ExF;AA4EI,mBAAe,KAAK;AACpB,eAAW,KAAK;AAChB,mBAAS,YAAT,kCAAmB;EACrB,CAAC;AAED,aAAW,SAAS,OAAO,OAAO,UAAS,GAAI;AAC7C,eAAW,KAAK;EAClB;AAEA,SAAO;IACL,UAAO;AACL,iBAAW,SAAS,UAAU,KAAI,GAAI;AACpC,uBAAe,KAAK;MACtB;AACA,uBAAiB,QAAO;AACxB,yBAAmB,QAAO;AAC1B,+BAAyB,QAAO;IAClC;IAEA,MAAM,aAAU;AACd,YAAM,QAAQ,IAAI,OAAO,OAAO,UAAS,EAAG,IAAI,UAAU,CAAC;IAC7D;;AAEJ;;;AC7HO,SAAS,oBAAoB,QAAQ,SAAS;AACjD,MAAI,EAAE,YAAY,WAAW,KAAQ,OAAO,UAAU,kBAAkB,KAAQ,IAAI;AACpF,MAAI;AACJ,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,QAAM,aAAa,MAAM;AACrB,QAAI,QAAQ;AACR,aAAO,QAAQ;AACf,eAAS;AAAA,IACb;AAAA,EACJ;AACA,QAAM,aAAa,YAAY,MAAM;AACjC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,UAAM,0BAA0B,KAAK,IAAI,IAAI;AAC7C,QAAI,0BAA0B,iBAAiB;AAC3C,iBAAW;AAAA,IACf;AAAA,EACJ,GAAG,QAAQ;AACX,SAAO;AAAA,IACH,UAAU;AACN,iBAAW;AACX,oBAAc,UAAU;AACxB,iBAAW;AAAA,IACf;AAAA,IACA,aAAa,WAAW;AACpB,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACtD;AACA,qBAAe,KAAK,IAAI;AACxB,UAAI,CAAC,QAAQ;AACT,iBAAS,OAAO,OAAO,gBAAgB;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AACA,aAAO,OAAO,oBAAoB,SAAS;AAAA,IAC/C;AAAA,IACA,iBAAiB,eAAe;AAC5B,mBAAa;AACb,iBAAW;AAAA,IACf;AAAA,EACJ;AACJ;;;ACsJO,SAAS,oBAAoB,QAAsB,SAAyC;AACjG,QAAM,aAAgC;IACpC,YAAY;IACZ,YAAY,CAAC;IACb,qBAAqB;IACrB,QAAQ;IACR,cAAc;IACd,OAAO;IACP,SAAS,CAAC;IACV,UAAU;IACV,aAAa;IACb,GAAG;EACL;AAEA,SAAO,UAAU,SAAS;IACxB,IAAI;IACJ,YAAY,CAAC,SAAS,MAAM;IAC5B,SAAS,CAAC,QAAQ,QAAQ,OAAO,KAAK;IACtC,WAAW,CAAC,oBAAoB;EAClC,CAAC;AAED,QAAM,gBAAgB,oBAAmD,QAAQ;IAC/E,OAAO;IACP,UAAU;IACV;EACF,CAAC;AAED,QAAM,gBAAgB,oBAAI,QAAqD;AAE/E,QAAM,qBAAqB,2BAA2B,QAAQ,QAAQ;IACpE,OAAO;IAEP,MAAM,kBAAkB,OAAO;AAC7B,YAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,YAAM,cAAc,MAAM,OAAO,aAAa,OAAO,MAAM,GAAG,CAAC;AAE/D,oBAAc,IAAI,OAAO,WAAW;AAEpC,aAAO,eAAA,OAAA,SAAA,YAAa,IAAI,YAAA;IAC1B;IAEA,MAAM,QAAQ,OAAO;AACnB,YAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,YAAM,OAAO,YAAY,OAAO,MAAM,GAAG,CAAC;IAC5C;EACF,CAAC;AAED,QAAM,cAAc;IAClB;IACA;IAEA,OAAO,UAAU,+BAA+B,QAAQ;MACtD,mBAAmB,CAAC,KAAK,GAAG;MAE5B,MAAM,uBAAuB,OAAO,UAAU;AAC5C,cAAM,WAAW,MAAM,qBAAqB,QAAQ;AACpD,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,OAAO,MAAM,OAAO,WAAW,OAAO,MAAM,GAAG,GAAG,aAAa,QAAQ,CAAC;AAE9E,YAAI,MAAM;AACR,iBAAO,iBAAiB,MAAM;YAC5B,OAAO;cACL,iBAAiB,SAAS;cAC1B,aAAa,SAAS;cACtB,eAAe,SAAS;cACxB,WAAW,SAAS;YACtB;UACF,CAAC;QACH;MACF;IACF,CAAC;IAED,OAAO,UAAU,sBAAsB,QAAQ;MAC7C,MAAM,aAAa,OAAO,UAAU;AAClC,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,MAAM,GAAG,GAAG,aAAa,QAAQ,CAAC;AAE3E,YAAI,MAAM;AACR,iBAAO,QAAQ,IAAI;QACrB;MACF;IACF,CAAC;IAED,OAAO,UAAU,2BAA2B,QAAQ;MAClD,MAAM,kBAAkB,OAAO,UAAU;AACvC,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,gBAAgB,MAAM,OAAO,aAAa,OAAO,MAAM,GAAG,GAAG,aAAa,QAAQ,CAAC;AAEzF,eAAO,iBAAA,OAAA,SAAA,cAAe,IAAI,cAAA;MAC5B;IACF,CAAC;IAED,OAAO,UAAU,+BAA+B,QAAQ;MACtD,aAAa;MAEb,MAAM,uBAAuB,OAAO;AAClC,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,QAAQ,MAAM,OAAO,oBAAoB,OAAO,MAAM,GAAG,CAAC;AAEhE,eAAO,SAAA,OAAA,SAAA,MAAO,IAAI,gBAAA;MACpB;IACF,CAAC;IAED,OAAO,UAAU,uCAAuC,QAAQ;MAC9D,aAAa;MAEb,MAAM,+BAA+B,OAAO;AAC1C,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,QAAQ,MAAM,OAAO,OAAO,OAAO,MAAM,GAAG,CAAC;AAEnD,eAAO,SAAA,OAAA,SAAA,MAAO,IAAI,UAAA;MACpB;IACF,CAAC;IAED,OAAO,UAAU,qBAAqB,QAAQ;MAC5C,MAAM,aAAa,OAAO;AACxB,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,QAAQ,MAAM,OAAO,UAAU,OAAO,MAAM,GAAG,CAAC;AAEtD,YAAI,OAAO;AACT,iBAAO;YACL,OAAO,MAAM,IAAI,MAAM;UACzB;QACF;MACF;IACF,CAAC;IAED,OAAO,UAAU,2BAA2B,QAAQ;MAClD,MAAM,mBAAmB,OAAO,OAAO,SAAS;AA7UtD,YAAA;AA8UQ,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,cAAc,MAAM,OAAO,cAAc,OAAO,MAAM,GAAG,GAAG,UAAU,KAAK,GAAG;UAClF,eACE,KAAA,cACG,IAAI,KAAK,MADZ,OAAA,SAAA,GAEI,OAAO,CAAC,eAAe,MAAM,gBAAgB,QAAQ,WAAW,KAAK,CAAC,CAAA,MAAM,CAAC;UACnF,MAAM,QAAQ,OAAO,CAAC,QAAQ,IAAI,IAAI;UACtC,aAAa,0BAA0B,QAAQ,OAAO;QACxD,CAAC;AAED,YAAI,aAAa;AACf,iBAAO;YACL,SAAS,YAAY,IAAI,YAAY;YACrC,UAAU;YAEV;UACF;QACF;MACF;IACF,CAAC;IAED,OAAO,UAAU,6BAA6B,QAAQ;MACpD,MAAM,qBAAqB,OAAO;AAChC,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,gBAAgB,MAAM,OAAO,iBAAiB,OAAO,MAAM,GAAG,CAAC;AAErE,eAAO,iBAAA,OAAA,SAAA,cAAe,IAAI,cAAA;MAC5B;IACF,CAAC;IAED,OAAO,UAAU,yBAAyB,QAAQ;MAChD,UAAU;QACR,aAAa;MACf;MACA,UAAU;QACR,CAAC,KAAK,GAAG;QACT,CAAC,KAAK,GAAG;QACT,CAAC,KAAK,GAAG;MACX;MACA,kBAAkB;QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;MAC1B;MACA,kBAAkB;QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;MAC1B;IACF,CAAC;IAED,OAAO,UAAU,qCAAqC,QAAQ;MAC5D,6BAA6B,CAAC,IAAI;MAElC,MAAM,6BAA6B,OAAO,UAAU,IAAI,mBAAmB;AACzE,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,QAAQ,MAAM,OAAO;UACzB,OAAO,MAAM,GAAG;UAChB,aAAa,QAAQ;UACrB;UACA,sBAAsB,iBAAiB;QACzC;AAEA,eAAO,SAAA,OAAA,SAAA,MAAO,IAAI,UAAA;MACpB;IACF,CAAC;IAED,OAAO,UAAU,+BAA+B,QAAQ;MACtD,MAAM,uBAAuB,OAAO,WAAW;AAC7C,cAAM,SAAS,MAAM,cAAc,UAAU,MAAM,GAAG;AACtD,cAAM,kBAAkB,MAAM,OAAO;UACnC,OAAO,MAAM,GAAG;UAChB,UAAU,IAAI,YAAY;QAC5B;AAEA,eAAO,mBAAA,OAAA,SAAA,gBAAiB,IAAI,iBAAA;MAC9B;IACF,CAAC;EACH;AAEA,SAAO;IACL,UAAU;AACR,iBAAW,cAAc,aAAa;AACpC,mBAAW,QAAQ;MACrB;IACF;IAEA,MAAM,OAAO,YAAY;AACvB,oBAAc,iBAAiB,OAAO,OAAO,YAAY,UAAU,CAAC;AACpE,YAAM,mBAAmB,WAAW;IACtC;EACF;AACF;", "names": ["assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "root", "base", "name", "parse", "ret", "delimiter", "win32", "module", "exports", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "d", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "isWindows", "platform", "navigator", "userAgent", "indexOf", "_schemePattern", "_singleSlashStart", "_doubleSlashStart", "_validateUri", "_strict", "scheme", "Error", "authority", "query", "fragment", "test", "_empty", "_slash", "_regexp", "URI", "schemeOrData", "this", "thing", "fsPath", "with", "toString", "uriToFsPath", "change", "<PERSON><PERSON>", "match", "exec", "percentDecode", "replace", "idx", "substring", "components", "result", "skip<PERSON><PERSON><PERSON>", "_asFormatted", "toJSON", "data", "_formatted", "external", "_fsPath", "_sep", "_pathSepMarker", "$mid", "encodeTable", "encodeURIComponentFast", "uriComponent", "isPath", "isAuthority", "nativeEncodePos", "pos", "encodeURIComponent", "char<PERSON>t", "substr", "escaped", "encodeURIComponentMinimal", "uri", "keepDriveLetterCasing", "toLowerCase", "encoder", "userinfo", "String", "fromCharCode", "decodeURIComponentGraceful", "str", "decodeURIComponent", "_rEncodedAsHex", "posixPath", "slash", "Utils", "t", "joinPath", "paths", "<PERSON><PERSON><PERSON>", "slashAdded"]}