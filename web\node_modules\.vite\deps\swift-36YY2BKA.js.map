{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/swift/swift.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/swift/swift.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".swift\",\n  // TODO(owensd): Support the full range of unicode valid identifiers.\n  identifier: /[a-zA-Z_][\\w$]*/,\n  attributes: [\n    \"@GKInspectable\",\n    \"@IBAction\",\n    \"@IBDesignable\",\n    \"@IBInspectable\",\n    \"@IBOutlet\",\n    \"@IBSegueAction\",\n    \"@NSApplicationMain\",\n    \"@NSCopying\",\n    \"@NSManaged\",\n    \"@Sendable\",\n    \"@UIApplicationMain\",\n    \"@autoclosure\",\n    \"@actorIndependent\",\n    \"@asyncHandler\",\n    \"@available\",\n    \"@convention\",\n    \"@derivative\",\n    \"@differentiable\",\n    \"@discardableResult\",\n    \"@dynamicCallable\",\n    \"@dynamicMemberLookup\",\n    \"@escaping\",\n    \"@frozen\",\n    \"@globalActor\",\n    \"@inlinable\",\n    \"@inline\",\n    \"@main\",\n    \"@noDerivative\",\n    \"@nonobjc\",\n    \"@noreturn\",\n    \"@objc\",\n    \"@objcMembers\",\n    \"@preconcurrency\",\n    \"@propertyWrapper\",\n    \"@requires_stored_property_inits\",\n    \"@resultBuilder\",\n    \"@testable\",\n    \"@unchecked\",\n    \"@unknown\",\n    \"@usableFromInline\",\n    \"@warn_unqualified_access\"\n  ],\n  accessmodifiers: [\"open\", \"public\", \"internal\", \"fileprivate\", \"private\"],\n  keywords: [\n    \"#available\",\n    \"#colorLiteral\",\n    \"#column\",\n    \"#dsohandle\",\n    \"#else\",\n    \"#elseif\",\n    \"#endif\",\n    \"#error\",\n    \"#file\",\n    \"#fileID\",\n    \"#fileLiteral\",\n    \"#filePath\",\n    \"#function\",\n    \"#if\",\n    \"#imageLiteral\",\n    \"#keyPath\",\n    \"#line\",\n    \"#selector\",\n    \"#sourceLocation\",\n    \"#warning\",\n    \"Any\",\n    \"Protocol\",\n    \"Self\",\n    \"Type\",\n    \"actor\",\n    \"as\",\n    \"assignment\",\n    \"associatedtype\",\n    \"associativity\",\n    \"async\",\n    \"await\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"convenience\",\n    \"default\",\n    \"defer\",\n    \"deinit\",\n    \"didSet\",\n    \"do\",\n    \"dynamic\",\n    \"dynamicType\",\n    \"else\",\n    \"enum\",\n    \"extension\",\n    \"fallthrough\",\n    \"false\",\n    \"fileprivate\",\n    \"final\",\n    \"for\",\n    \"func\",\n    \"get\",\n    \"guard\",\n    \"higherThan\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"indirect\",\n    \"infix\",\n    \"init\",\n    \"inout\",\n    \"internal\",\n    \"is\",\n    \"isolated\",\n    \"lazy\",\n    \"left\",\n    \"let\",\n    \"lowerThan\",\n    \"mutating\",\n    \"nil\",\n    \"none\",\n    \"nonisolated\",\n    \"nonmutating\",\n    \"open\",\n    \"operator\",\n    \"optional\",\n    \"override\",\n    \"postfix\",\n    \"precedence\",\n    \"precedencegroup\",\n    \"prefix\",\n    \"private\",\n    \"protocol\",\n    \"public\",\n    \"repeat\",\n    \"required\",\n    \"rethrows\",\n    \"return\",\n    \"right\",\n    \"safe\",\n    \"self\",\n    \"set\",\n    \"some\",\n    \"static\",\n    \"struct\",\n    \"subscript\",\n    \"super\",\n    \"switch\",\n    \"throw\",\n    \"throws\",\n    \"true\",\n    \"try\",\n    \"typealias\",\n    \"unowned\",\n    \"unsafe\",\n    \"var\",\n    \"weak\",\n    \"where\",\n    \"while\",\n    \"willSet\",\n    \"__consuming\",\n    \"__owned\"\n  ],\n  symbols: /[=(){}\\[\\].,:;@#\\_&\\-<>`?!+*\\\\\\/]/,\n  // Moved . to operatorstart so it can be a delimiter\n  operatorstart: /[\\/=\\-+!*%<>&|^~?\\u00A1-\\u00A7\\u00A9\\u00AB\\u00AC\\u00AE\\u00B0-\\u00B1\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7\\u2016-\\u2017\\u2020-\\u2027\\u2030-\\u203E\\u2041-\\u2053\\u2055-\\u205E\\u2190-\\u23FF\\u2500-\\u2775\\u2794-\\u2BFF\\u2E00-\\u2E7F\\u3001-\\u3003\\u3008-\\u3030]/,\n  operatorend: /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uE0100-\\uE01EF]/,\n  operators: /(@operatorstart)((@operatorstart)|(@operatorend))*/,\n  // TODO(owensd): These are borrowed from C#; need to validate correctness for Swift.\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      { include: \"@attribute\" },\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@invokedmethod\" },\n      { include: \"@symbol\" }\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/\"\"\"/, \"string.quote\", \"@endDblDocString\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string.quote\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    symbol: [\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/[.]/, \"delimiter\"],\n      [/@operators/, \"operator\"],\n      [/@symbols/, \"operator\"]\n    ],\n    comment: [\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\*\\*/, \"comment.doc\", \"@commentdocbody\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@commentbody\"]\n    ],\n    commentdocbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/\\:[a-zA-Z]+\\:/, \"comment.doc.param\"],\n      [/./, \"comment.doc\"]\n    ],\n    commentbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    attribute: [\n      [\n        /@@@identifier/,\n        {\n          cases: {\n            \"@attributes\": \"keyword.control\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    literal: [\n      [/\"/, { token: \"string.quote\", next: \"@stringlit\" }],\n      [/0[b]([01]_?)+/, \"number.binary\"],\n      [/0[o]([0-7]_?)+/, \"number.octal\"],\n      [/0[x]([0-9a-fA-F]_?)+([pP][\\-+](\\d_?)+)?/, \"number.hex\"],\n      [/(\\d_?)*\\.(\\d_?)+([eE][\\-+]?(\\d_?)+)?/, \"number.float\"],\n      [/(\\d_?)+/, \"number\"]\n    ],\n    stringlit: [\n      [/\\\\\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }],\n      [/./, \"string\"]\n    ],\n    interpolatedexpression: [\n      [/\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/\\)/, { token: \"operator\", next: \"@pop\" }],\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@symbol\" }\n    ],\n    keyword: [\n      [/`/, { token: \"operator\", next: \"@escapedkeyword\" }],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"[A-Z][a-zA-Z0-9$]*\": \"type.identifier\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    escapedkeyword: [\n      [/`/, { token: \"operator\", next: \"@pop\" }],\n      [/./, \"identifier\"]\n    ],\n    invokedmethod: [\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimeter\", \"type.identifier\"],\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (C) David Owens II, owensd.io. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,YAAY;AAAA,EACZ,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,iBAAiB,CAAC,QAAQ,UAAU,YAAY,eAAe,SAAS;AAAA,EACxE,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA;AAAA,EAET,eAAe;AAAA,EACf,aAAa;AAAA,EACb,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,OAAO,gBAAgB,kBAAkB;AAAA,IAC5C;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,OAAO,gBAAgB,SAAS;AAAA,MACjC,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,OAAO,WAAW;AAAA,MACnB,CAAC,cAAc,UAAU;AAAA,MACzB,CAAC,YAAY,UAAU;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,UAAU,eAAe,iBAAiB;AAAA,MAC3C,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,cAAc;AAAA,IACpC;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,QAAQ,WAAW,cAAc;AAAA,MAClC,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,iBAAiB,mBAAmB;AAAA,MACrC,CAAC,KAAK,aAAa;AAAA,IACrB;AAAA,IACA,aAAa;AAAA,MACX,CAAC,QAAQ,WAAW,cAAc;AAAA,MAClC,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,MACT;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,eAAe;AAAA,YACf,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,MACnD,CAAC,iBAAiB,eAAe;AAAA,MACjC,CAAC,kBAAkB,cAAc;AAAA,MACjC,CAAC,2CAA2C,YAAY;AAAA,MACxD,CAAC,wCAAwC,cAAc;AAAA,MACvD,CAAC,WAAW,QAAQ;AAAA,IACtB;AAAA,IACA,WAAW;AAAA,MACT,CAAC,QAAQ,EAAE,OAAO,YAAY,MAAM,0BAA0B,CAAC;AAAA,MAC/D,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,MAC7C,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,MAAM,EAAE,OAAO,YAAY,MAAM,0BAA0B,CAAC;AAAA,MAC7D,CAAC,MAAM,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MAC1C,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,MACpD;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,sBAAsB;AAAA,YACtB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MACzC,CAAC,KAAK,YAAY;AAAA,IACpB;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,IAAI,CAAC,aAAa,iBAAiB;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}