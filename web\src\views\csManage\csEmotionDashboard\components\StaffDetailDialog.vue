<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="95%"
    class="staff-detail-dialog"
    destroy-on-close
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <div class="dialog-content" v-loading="loading">
      <template v-if="staffData">
        <!-- 客服基本信息 -->
        <div class="staff-info-section">
          <el-card class="info-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><User /></el-icon>
                <span>客服基本信息</span>
                <div class="date-range">
                  <el-tag type="info" size="small">
                    {{ formatDateRange(staffData.date_range) }}
                  </el-tag>
                </div>
              </div>
            </template>
            
            <div class="staff-info-grid">
              <div class="info-item">
                <div class="info-avatar">
                  <el-avatar :size="60" class="staff-avatar">
                    {{ staffData.staff_info?.staff_name?.charAt(0) || '?' }}
                  </el-avatar>
                </div>
                <div class="info-details">
                  <h3 class="staff-name">{{ staffData.staff_info?.staff_name || '未知客服' }}</h3>
                  <p class="staff-account">{{ staffData.staff_info?.staff_account || '-' }}</p>
                  <div class="staff-tags">
                    <el-tag type="primary" size="small">ID: {{ staffData.staff_info?.staff_id }}</el-tag>
                    <el-tag type="info" size="small" v-if="staffData.staff_info?.dept_name">{{ staffData.staff_info.dept_name }}</el-tag>
                    <el-tag type="warning" size="small" v-if="staffData.staff_info?.email">{{ staffData.staff_info.email }}</el-tag>
                  </div>
                </div>
              </div>
              
              <div class="score-summary">
                <div class="score-item">
                  <el-progress
                    type="circle"
                    :width="80"
                    :percentage="Math.round(staffData.staff_info?.comprehensive_score || 0)"
                    :color="getScoreColor(staffData.staff_info?.comprehensive_score || 0)"
                  />
                  <div class="score-label">综合绩效</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 核心指标卡片 -->
        <div class="core-metrics-section">
          <div class="metrics-grid">
            <div class="metric-card satisfaction">
              <div class="metric-icon">
                <el-icon size="32"><Star /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ (staffData.staff_info?.satisfaction_ratio || 0).toFixed(1) }}%</div>
                <div class="metric-label">满意度</div>
                <div class="metric-detail">
                  参评率: {{ (staffData.staff_info?.eva_ratio || 0).toFixed(1) }}%<br>
                  邀评率: {{ (staffData.staff_info?.invitation_ratio || 0).toFixed(1) }}%
                </div>
              </div>
            </div>

            <div class="metric-card efficiency">
              <div class="metric-icon">
                <el-icon size="32"><Lightning /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ (staffData.staff_info?.hourly_efficiency || 0).toFixed(1) }}/h</div>
                <div class="metric-label">小时效率</div>
                <div class="metric-detail">
                  应答率: {{ (staffData.staff_info?.reply_ratio || 0).toFixed(1) }}%<br>
                  解决率: {{ (staffData.staff_info?.one_off_ratio || 0).toFixed(1) }}%
                </div>
              </div>
            </div>

            <div class="metric-card emotion">
              <div class="metric-icon">
                <el-icon size="32"><Sunny /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ (staffData.staff_info?.emotion_contribution || 0).toFixed(3) }}</div>
                <div class="metric-label">情绪贡献度</div>
                <div class="metric-detail">
                  分析数: {{ staffData.emotion_summary?.total_records || 0 }}条<br>
                  正向: {{ staffData.emotion_summary?.positive_changes || 0 }}次
                </div>
              </div>
            </div>

            <div class="metric-card workload">
              <div class="metric-icon">
                <el-icon size="32"><ChatRound /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(staffData.staff_info?.total_sessions || 0) }}</div>
                <div class="metric-label">会话总量</div>
                <div class="metric-detail">
                  有效: {{ formatNumber(staffData.staff_info?.valid_sessions || 0) }}<br>
                  时长: {{ (staffData.staff_info?.online_duration || 0).toFixed(1) }}h
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细数据分析 -->
        <div class="detailed-analysis-section">
          <el-tabs v-model="activeTab" class="detail-tabs">
            <!-- 工作量分析 -->
            <el-tab-pane label="工作量分析" name="workload">
              <div class="analysis-content">
                <div class="stats-row">
                  <div class="stats-group">
                    <h4>会话统计</h4>
                    <div class="stats-items">
                      <div class="stats-item">
                        <span class="label">会话总数:</span>
                        <span class="value primary">{{ formatNumber(staffData.staff_info?.total_sessions || 0) }}</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">有效会话:</span>
                        <span class="value success">{{ formatNumber(staffData.staff_info?.valid_sessions || 0) }}</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">有效率:</span>
                        <span class="value">{{ getValidRatio(staffData.staff_info?.valid_sessions, staffData.staff_info?.total_sessions) }}%</span>
                      </div>
                    </div>
                  </div>

                  <div class="stats-group">
                    <h4>时间统计</h4>
                    <div class="stats-items">
                      <div class="stats-item">
                        <span class="label">登录时长:</span>
                        <span class="value info">{{ (staffData.staff_info?.login_duration || 0).toFixed(1) }}h</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">在线时长:</span>
                        <span class="value primary">{{ (staffData.staff_info?.online_duration || 0).toFixed(1) }}h</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">小休时长:</span>
                        <span class="value warning">{{ (staffData.staff_info?.rest_duration || 0).toFixed(1) }}h</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">挂起时长:</span>
                        <span class="value danger">{{ (staffData.staff_info?.pend_duration || 0).toFixed(1) }}h</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">空闲时长:</span>
                        <span class="value">{{ (staffData.staff_info?.resume_free_duration || 0).toFixed(1) }}h</span>
                      </div>
                    </div>
                  </div>

                  <div class="stats-group">
                    <h4>效率统计</h4>
                    <div class="stats-items">
                      <div class="stats-item">
                        <span class="label">小时效率:</span>
                        <span class="value primary">{{ (staffData.staff_info?.hourly_efficiency || 0).toFixed(2) }}单/小时</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">会话有效率:</span>
                        <span class="value info">{{ getValidRatio(staffData.staff_info?.valid_sessions, staffData.staff_info?.total_sessions) }}%</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">平均首响时间:</span>
                        <span class="value">{{ ((staffData.staff_info?.avg_first_resp_time || 0) / 1000).toFixed(1) }}s</span>
                      </div>
                      <div class="stats-item">
                        <span class="label">平均响应时间:</span>
                        <span class="value">{{ ((staffData.staff_info?.avg_resp_time || 0) / 1000).toFixed(1) }}s</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 质量分析 -->
            <el-tab-pane label="质量分析" name="quality">
              <div class="analysis-content">
                <div class="quality-section">
                  <el-card class="quality-card" shadow="hover">
                    <template #header>
                      <div class="card-header">
                        <el-icon class="header-icon"><TrendCharts /></el-icon>
                        <span>质量分析雷达图</span>
                      </div>
                    </template>
                    
                    <div class="quality-content">
                      <div class="radar-container">
                        <div ref="radarChart" class="radar-chart"></div>
                      </div>
                      
                      <!-- 质量指标详情 -->
                      <div class="quality-details">
                        <div class="detail-section">
                          <h4 class="section-title">个人指标</h4>
                          <div class="detail-row">
                            <div class="detail-item">
                              <span class="label">满意度:</span>
                              <span class="value success">{{ (staffData.staff_info?.satisfaction_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">应答率:</span>
                              <span class="value primary">{{ (staffData.staff_info?.reply_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">解决率:</span>
                              <span class="value warning">{{ (staffData.staff_info?.one_off_ratio || 0).toFixed(1) }}%</span>
                            </div>
                          </div>
                          <div class="detail-row">
                            <div class="detail-item">
                              <span class="label">参评率:</span>
                              <span class="value info">{{ (staffData.staff_info?.eva_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">邀评率:</span>
                              <span class="value">{{ (staffData.staff_info?.invitation_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">小时效率:</span>
                              <span class="value primary">{{ (staffData.staff_info?.hourly_efficiency || 0).toFixed(1) }}/h</span>
                            </div>
                          </div>
                        </div>

                        <!-- 部门平均值对比 -->
                        <div class="detail-section" v-if="staffData.department_averages">
                          <h4 class="section-title">{{ staffData.department_averages.dept_name }}部门平均 ({{ staffData.department_averages.staff_count }}人)</h4>
                          <div class="detail-row">
                            <div class="detail-item">
                              <span class="label">满意度:</span>
                              <span class="value success dept-avg">{{ (staffData.department_averages.satisfaction_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">应答率:</span>
                              <span class="value primary dept-avg">{{ (staffData.department_averages.reply_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">解决率:</span>
                              <span class="value warning dept-avg">{{ (staffData.department_averages.one_off_ratio || 0).toFixed(1) }}%</span>
                            </div>
                          </div>
                          <div class="detail-row">
                            <div class="detail-item">
                              <span class="label">参评率:</span>
                              <span class="value info dept-avg">{{ (staffData.department_averages.eva_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">邀评率:</span>
                              <span class="value dept-avg">{{ (staffData.department_averages.invitation_ratio || 0).toFixed(1) }}%</span>
                            </div>
                            <div class="detail-item">
                              <span class="label">小时效率:</span>
                              <span class="value primary dept-avg">{{ (staffData.department_averages.hourly_efficiency || 0).toFixed(1) }}/h</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
              </div>
            </el-tab-pane>

            <!-- 情绪分析 -->
            <el-tab-pane label="情绪分析" name="emotion">
              <div class="analysis-content">
                <!-- 情绪概览统计 -->
                <div class="emotion-overview">
                  <div class="emotion-stats">
                    <div class="emotion-card positive">
                      <div class="emotion-icon">📈</div>
                      <div class="emotion-content">
                        <div class="emotion-number">{{ staffData.emotion_summary?.positive_changes || 0 }}</div>
                        <div class="emotion-label">正向变化</div>
                      </div>
                    </div>

                    <div class="emotion-card negative">
                      <div class="emotion-icon">📉</div>
                      <div class="emotion-content">
                        <div class="emotion-number">{{ staffData.emotion_summary?.negative_changes || 0 }}</div>
                        <div class="emotion-label">负向变化</div>
                      </div>
                    </div>

                    <div class="emotion-card neutral">
                      <div class="emotion-icon">😐</div>
                      <div class="emotion-content">
                        <div class="emotion-number">{{ staffData.emotion_summary?.neutral_changes || 0 }}</div>
                        <div class="emotion-label">稳定变化</div>
                      </div>
                    </div>

                    <div class="emotion-card contribution">
                      <div class="emotion-icon">⚡</div>
                      <div class="emotion-content">
                        <div class="emotion-number">{{ (staffData.emotion_summary?.total_emotion_contribution || 0).toFixed(4) }}</div>
                        <div class="emotion-label">总计贡献度</div>
                      </div>
                    </div>
                  </div>

                  <div class="emotion-description">
                    <h4>情绪贡献度说明</h4>
                    <p><strong>总贡献度:</strong> {{ (staffData.staff_info?.emotion_contribution || 0).toFixed(4) }}</p>
                    <p><strong>每小时贡献度:</strong> {{ (staffData.staff_info?.emotion_contribution_per_hour || 0).toFixed(4) }}</p>
                    <p><strong>总分析记录:</strong> {{ staffData.emotion_summary?.total_records || 0 }}条</p>
                    <p><strong>平均情绪改善:</strong> {{ (staffData.emotion_summary?.avg_emotion_change || 0).toFixed(4) }}</p>
                    <div class="calculation-example">
                      <p><strong>计算说明：</strong></p>
                      <p>• 情绪贡献度 = 平均情绪改善分数</p>
                      <p>• 每小时贡献度 = 情绪贡献度 ÷ 在线时长</p>
                    </div>
                  </div>
                </div>

                <!-- 情绪分析记录列表 -->
                <div class="emotion-records-section" v-if="staffData.emotion_records && staffData.emotion_records.length > 0">
                  <el-card class="records-card" shadow="hover">
                    <template #header>
                      <div class="card-header">
                        <el-icon class="header-icon"><DataAnalysis /></el-icon>
                        <span>最近情绪分析记录</span>
                        <el-tag type="info" size="small">最多显示20条</el-tag>
                      </div>
                    </template>
                    
                    <div class="records-table">
                      <el-table :data="staffData.emotion_records" stripe style="width: 100%" max-height="400">
                        <el-table-column prop="create_datetime" label="分析时间" width="160" />
                        <el-table-column prop="session_id" label="会话ID" width="120" />
                        <el-table-column label="情绪变化" width="120" align="center">
                          <template #default="scope">
                            <el-tag 
                              :type="getEmotionChangeType(scope.row.emotion_change_score)"
                              size="small"
                            >
                              {{ (scope.row.emotion_change_score || 0).toFixed(3) }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="初始情绪" width="100" align="center">
                          <template #default="scope">
                            {{ (scope.row.initial_emotion_score || 0).toFixed(2) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="结束情绪" width="100" align="center">
                          <template #default="scope">
                            {{ (scope.row.final_emotion_score || 0).toFixed(2) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="会话时长" width="100" align="center">
                          <template #default="scope">
                            {{ scope.row.session_duration_minutes || 0 }}分钟
                          </template>
                        </el-table-column>
                        <el-table-column label="单次贡献度" width="120" align="center">
                          <template #default="scope">
                            <span class="contribution-value">
                              {{ (scope.row.single_emotion_contribution || 0).toFixed(4) }}
                            </span>
                          </template>
                        </el-table-column>

                        <el-table-column label="消息数" width="100" align="center">
                          <template #default="scope">
                            <div class="message-count">
                              <div>总: {{ scope.row.message_count || 0 }}</div>
                              <div class="sub-count">
                                客服: {{ scope.row.service_message_count || 0 }} | 
                                用户: {{ scope.row.user_message_count || 0 }}
                              </div>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="conversation_summary" label="对话摘要" min-width="200">
                          <template #default="scope">
                            <el-tooltip :content="scope.row.conversation_summary" placement="top" v-if="scope.row.conversation_summary">
                              <div class="conversation-summary">
                                {{ truncateText(scope.row.conversation_summary, 50) }}
                              </div>
                            </el-tooltip>
                            <span v-else class="no-summary">暂无摘要</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-card>
                </div>

                <!-- 无情绪记录提示 -->
                <div v-else class="no-emotion-records">
                  <el-empty description="该时间段内暂无情绪分析记录" />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>

      <!-- 无数据状态 -->
      <el-empty v-else-if="!loading" description="暂无数据" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { 
  User, Star, Lightning, Sunny, ChatRound, TrendCharts, 
  Timer, DataAnalysis, Warning, InfoFilled, Refresh 
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 更新类型定义以匹配后端返回的数据结构
interface StaffInfo {
  staff_id: string;
  staff_name: string;
  staff_account: string;
  email?: string;
  dept_name?: string;
  satisfaction_ratio: number;
  reply_ratio: number;
  one_off_ratio: number;
  eva_ratio: number;
  invitation_ratio: number;
  user_resolved_ratio: number;
  hourly_efficiency: number;
  emotion_contribution: number;
  emotion_contribution_per_hour: number;
  total_sessions: number;
  valid_sessions: number;
  online_duration: number;
  login_duration: number;
  rest_duration: number;
  pend_duration: number;
  resume_free_duration: number;
  hang_session_duration: number;
  avg_first_resp_time: number;
  avg_resp_time: number;
  comprehensive_score: number;
  emotion_analysis_count: number;
  avg_emotion_change: number;
  positive_emotion_changes: number;
  negative_emotion_changes: number;
}

interface EmotionRecord {
  session_id: string;
  create_datetime: string;
  initial_emotion_score: number;
  final_emotion_score: number;
  emotion_change_score: number;
  conversation_summary: string;
  session_duration: number;
  session_duration_minutes: number;
  single_emotion_contribution: number;
  message_count: number;
  user_message_count: number;
  service_message_count: number;
}

interface EmotionSummary {
  total_records: number;
  avg_emotion_change: number;
  total_emotion_contribution: number;
  positive_changes: number;
  negative_changes: number;
  neutral_changes: number;
}

interface DateRange {
  start_date: string;
  end_date: string;
}

  interface StaffDetailData {
    staff_info: StaffInfo;
    emotion_records: EmotionRecord[];
    emotion_summary: EmotionSummary;
    date_range: DateRange;
    department_averages?: DepartmentAverages;
  }

  interface DepartmentAverages {
    satisfaction_ratio: number;
    reply_ratio: number;
    one_off_ratio: number;
    eva_ratio: number;
    invitation_ratio: number;
    hourly_efficiency: number;
    dept_name: string;
    staff_count: number;
  }

interface Props {
  visible: boolean;
  staffData: StaffDetailData | null;
  loading: boolean;
  dateRange: DateRange;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh', staffId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const activeTab = ref('workload');
const radarChart = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

const dialogTitle = computed(() => {
  if (props.staffData?.staff_info) {
    return `${props.staffData.staff_info.staff_name} - 数据分析详情`;
  }
  return '客服数据详情';
});

// 方法
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

const getScoreColor = (score: number) => {
  if (score >= 80) return '#67C23A';
  if (score >= 60) return '#E6A23C';
  return '#F56C6C';
};

const formatDateRange = (dateRange: DateRange) => {
  if (!dateRange) return '-';
  return `${dateRange.start_date} 至 ${dateRange.end_date}`;
};

const getValidRatio = (valid: number, total: number) => {
  if (!total) return '0.0';
  return ((valid / total) * 100).toFixed(1);
};

const getEmotionChangeType = (score: number) => {
  if (!score) return '';
  if (score > 0) return 'success';
  if (score < 0) return 'danger';
  return 'info';
};

const truncateText = (text: string, length: number) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

const handleClose = () => {
  emit('update:visible', false);
};

const handleRefresh = () => {
  if (props.staffData?.staff_info?.staff_id) {
    emit('refresh', props.staffData.staff_info.staff_id);
  }
};

// 监听弹窗显示，重置选项卡
watch(() => props.visible, (newVal) => {
  if (newVal) {
    activeTab.value = 'workload';
  }
});

// 初始化雷达图
const initRadarChart = () => {
  if (!radarChart.value || !props.staffData?.staff_info) return;
  
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  chartInstance = echarts.init(radarChart.value);
  
  const staffInfo = props.staffData.staff_info;
  const deptAverages = props.staffData.department_averages;
  
  // 计算最大值，用于雷达图范围
  const maxEfficiency = Math.max(
    staffInfo.hourly_efficiency || 0,
    deptAverages?.hourly_efficiency || 0,
    20  // 最小值
  ) * 1.2;
  
  const option = {
    title: {
      text: '质量分析雷达图',
      left: 'center',
      top: '5%',
      textStyle: {
        color: '#333',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    legend: {
      data: ['当前客服', deptAverages ? `${deptAverages.dept_name}部门平均` : '团队平均'],
      top: '15%',
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data;
        const indicators = ['满意度', '应答率', '解决率', '参评率', '邀评率', '效率'];
        let result = `<div style="text-align: left;"><strong>${params.seriesName}</strong><br/>`;
        data.value.forEach((value: number, index: number) => {
          const unit = index === 5 ? '/h' : '%';
          result += `${indicators[index]}: ${value.toFixed(1)}${unit}<br/>`;
        });
        result += `</div>`;
        return result;
      }
    },
    radar: {
      indicator: [
        { name: '满意度', max: 100, color: '#67C23A' },
        { name: '应答率', max: 100, color: '#409EFF' },
        { name: '解决率', max: 100, color: '#E6A23C' },
        { name: '参评率', max: 100, color: '#F56C6C' },
        { name: '邀评率', max: 100, color: '#909399' },
        { name: '效率', max: maxEfficiency, color: '#8B5CF6' }
      ],
      shape: 'polygon',
      radius: '60%',
      center: ['50%', '60%'],
      splitNumber: 4,
      splitArea: {
        areaStyle: {
          color: ['rgba(64, 158, 255, 0.1)', 'rgba(64, 158, 255, 0.05)']
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(64, 158, 255, 0.3)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(64, 158, 255, 0.5)'
        }
      },
      name: {
        textStyle: {
          color: '#333',
          fontSize: 12
        }
      }
    },
    series: [
      {
        name: '质量指标对比',
        type: 'radar',
        data: [
          // 当前客服数据
          {
            value: [
              staffInfo.satisfaction_ratio || 0,
              staffInfo.reply_ratio || 0,
              staffInfo.one_off_ratio || 0,
              staffInfo.eva_ratio || 0,
              staffInfo.invitation_ratio || 0,
              staffInfo.hourly_efficiency || 0
            ],
            name: '当前客服',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.2)'
            },
            lineStyle: {
              color: '#409EFF',
              width: 3
            }
          },
          // 部门平均值数据
          ...(deptAverages ? [{
            value: [
              deptAverages.satisfaction_ratio || 0,
              deptAverages.reply_ratio || 0,
              deptAverages.one_off_ratio || 0,
              deptAverages.eva_ratio || 0,
              deptAverages.invitation_ratio || 0,
              deptAverages.hourly_efficiency || 0
            ],
            name: `${deptAverages.dept_name}部门平均`,
            symbol: 'diamond',
            symbolSize: 6,
            itemStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: 'rgba(103, 194, 58, 0.15)'
            },
            lineStyle: {
              color: '#67C23A',
              width: 2,
              type: 'dashed'
            }
          }] : [])
        ]
      }
    ]
  };
  
  chartInstance.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
};

// 监听数据变化
watch(() => props.staffData, (newData) => {
  if (newData && activeTab.value === 'quality') {
    nextTick(() => {
      initRadarChart();
    });
  }
}, { deep: true });

// 监听tab切换
watch(activeTab, (newTab) => {
  if (newTab === 'quality' && props.staffData) {
    nextTick(() => {
      initRadarChart();
    });
  }
});

onMounted(() => {
  if (props.staffData && activeTab.value === 'quality') {
    nextTick(() => {
      initRadarChart();
    });
  }
});
</script>

<style scoped>
.staff-detail-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.staff-detail-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid #eee;
  padding: 20px 24px 16px;
}

.staff-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.dialog-content {
  padding: 24px;
}

/* 客服信息部分 */
.staff-info-section {
  margin-bottom: 24px;
}

.info-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.card-header .date-range {
  margin-left: auto;
}

.header-icon {
  color: #409EFF;
}

.staff-info-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.staff-avatar {
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  color: white;
  font-weight: bold;
  font-size: 24px;
}

.staff-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.staff-account {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.staff-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.score-summary {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-item {
  text-align: center;
}

.score-label {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

/* 核心指标卡片 */
.core-metrics-section {
  margin-bottom: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.metric-card {
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  color: white;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background: linear-gradient(135deg, currentColor 0%, transparent 100%);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-card.satisfaction { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.metric-card.efficiency { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.metric-card.emotion { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.metric-card.workload { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.metric-detail {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

/* 详细分析部分 */
.detailed-analysis-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.detail-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  padding: 0 24px;
}

.detail-tabs :deep(.el-tabs__content) {
  padding: 24px;
}

.analysis-content {
  min-height: 300px;
}

/* 工作量分析样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stats-group h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.stats-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-item .label {
  color: #606266;
  font-size: 14px;
}

.stats-item .value {
  font-weight: 600;
  font-size: 14px;
}

.stats-item .value.primary { color: #409EFF; }
.stats-item .value.success { color: #67C23A; }
.stats-item .value.warning { color: #E6A23C; }
.stats-item .value.danger { color: #F56C6C; }
.stats-item .value.info { color: #909399; }

/* 质量分析样式 */
.quality-section {
  margin-bottom: 24px;
}

.quality-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.quality-content {
  padding: 24px;
}

.quality-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.quality-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  flex: 1;
}

.progress-value {
  font-weight: 600;
  font-size: 14px;
}

.quality-scores {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scores-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.score-circle {
  text-align: center;
}

.score-info {
  margin-top: 8px;
}

.score-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.score-desc {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 情绪分析样式 */
.emotion-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.emotion-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.emotion-card {
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.emotion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.emotion-card.positive { background: linear-gradient(135deg, #67C23A, #85CE61); color: white; }
.emotion-card.negative { background: linear-gradient(135deg, #F56C6C, #F78989); color: white; }
.emotion-card.neutral { background: linear-gradient(135deg, #909399, #B1B3B8); color: white; }
.emotion-card.contribution { background: linear-gradient(135deg, #E6A23C, #EEBE77); color: white; }

.emotion-icon {
  font-size: 32px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.emotion-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.emotion-label {
  font-size: 14px;
  opacity: 0.9;
}

.emotion-description {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.emotion-description h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.emotion-description p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
}

.calculation-example {
  margin-top: 16px;
  padding: 12px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  border-left: 3px solid #409EFF;
}

.calculation-example p {
  margin: 4px 0;
  font-size: 13px;
}

/* 情绪记录表格样式 */
.emotion-records-section {
  margin-top: 32px;
}

.records-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.records-table {
  padding: 16px;
}

.records-table :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.records-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.records-table :deep(.el-table__header th) {
  background: #f8f9fa;
  color: #303133;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.records-table :deep(.el-table__row:hover) {
  background: #f0f9ff;
}

.contribution-value {
  color: #409EFF;
  font-weight: 600;
}

.contribution-per-hour {
  color: #67C23A;
  font-weight: 600;
}

.message-count {
  text-align: center;
}

.message-count .sub-count {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.conversation-summary {
  cursor: pointer;
  color: #606266;
  line-height: 1.4;
}

.conversation-summary:hover {
  color: #409EFF;
}

.no-summary {
  color: #c0c4cc;
  font-style: italic;
}

.no-emotion-records {
  padding: 40px 0;
  text-align: center;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #eee;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .staff-info-grid {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .quality-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .emotion-overview {
    grid-template-columns: 1fr;
  }
  
  .emotion-stats {
    grid-template-columns: 1fr;
  }
}

.quality-progress {
  margin-top: 8px;
}

.quality-progress :deep(.el-progress-bar__outer) {
  border-radius: 6px;
}

.quality-progress :deep(.el-progress-bar__inner) {
  border-radius: 6px;
}

/* 雷达图样式 */
.radar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.radar-chart {
  width: 100%;
  height: 400px;
}

.quality-details {
  margin-top: 20px;
}

.detail-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.detail-section:last-child {
  margin-bottom: 0;
  border-left-color: #67C23A;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-item .label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  font-weight: 600;
}

.detail-item .value.dept-avg {
  position: relative;
}

.detail-item .value.dept-avg::after {
  content: '(平均)';
  font-size: 11px;
  color: #909399;
  font-weight: normal;
  margin-left: 4px;
}

.detail-item .value.success { color: #67C23A; }
.detail-item .value.primary { color: #409EFF; }
.detail-item .value.warning { color: #E6A23C; }
.detail-item .value.info { color: #909399; }
.detail-item .value.danger { color: #F56C6C; }
</style> 