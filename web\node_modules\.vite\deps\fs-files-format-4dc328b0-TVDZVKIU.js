import {
  he
} from "./chunk-OFUQC3BW.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import {
  B
} from "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  defineComponent,
  mergeProps,
  openBlock,
  ref,
  renderList,
  resolveDynamicComponent,
  toDisplayString,
  watch,
  withCtx
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/fs-files-format-4dc328b0.mjs
var $ = defineComponent({
  name: "FsFilesFormat",
  props: {
    /**
     * 文件列表
     * 支持格式： `url , {url} , [url1,url2] ,  [{url:url1},{url:url2}]`
     */
    modelValue: {},
    // tag颜色，`【primary, success, warning, danger ,info】`
    color: {
      default: ""
    },
    // 展示类型`【text, tag】`
    type: {
      default: "tag"
      // `可选【text,tag】`
    },
    // 链接配置
    a: {},
    // tag配置
    tag: {},
    // 构建下载url方法，支持异步
    buildUrl: {},
    // 批量构建下载url方法，支持异步
    buildUrls: {},
    // 根据value构建文件名
    getFileName: {}
  },
  setup(t, F) {
    const { ui: i } = B(), f = computed(() => (
      // @ts-ignore
      t.getFileName || function(e) {
        return typeof e != "string" ? (console.warn("获取文件名失败，请配置getFileName"), e) : (e == null ? void 0 : e.lastIndexOf("/")) >= 0 ? e.substring(e.lastIndexOf("/") + 1) : e;
      }
    ));
    function u(e) {
      return {
        url: void 0,
        value: e,
        name: f.value(e),
        // @ts-ignore
        color: t.color
      };
    }
    async function m(e) {
      if (t.buildUrls) {
        const r = e.map((a) => a.value), b = await t.buildUrls(r);
        for (let a = 0; a < e.length; a++)
          e[a].url = b[a];
      } else if (t.buildUrl)
        for (let r of e)
          r.url = await t.buildUrl(r.value);
      else
        for (let r = 0; r < e.length; r++)
          e[r].url = e[r].value;
    }
    async function o() {
      if (t.modelValue == null || t.modelValue === "")
        return [];
      let e = [];
      if (typeof t.modelValue == "string")
        e = [u(t.modelValue)];
      else if (t.modelValue instanceof Array) {
        e = [];
        for (const r of t.modelValue)
          e.push(u(r));
      }
      return await m(e), e;
    }
    const c = ref([]);
    watch(
      () => t.modelValue,
      async () => {
        c.value = await o();
      },
      {
        immediate: true
      }
    );
    const v = computed(() => ({
      ...t
    }));
    return {
      ui: i,
      itemsRef: c,
      computedProps: v
    };
  }
});
var B2 = { class: "fs-files-format" };
var C = ["href"];
var R = ["href"];
function x(t, F, i, f, u, m) {
  return openBlock(), createElementBlock("div", B2, [
    t.computedProps.type === "text" ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(t.itemsRef, (o) => (openBlock(), createElementBlock("span", {
      key: o.url,
      class: "fs-files-item"
    }, [
      createBaseVNode("a", mergeProps({
        href: o.url,
        target: "_blank",
        ref_for: true
      }, t.computedProps.a), toDisplayString(o.name), 17, C)
    ]))), 128)) : (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(t.itemsRef, (o) => (openBlock(), createBlock(resolveDynamicComponent(t.ui.tag.name), mergeProps({
      key: o.url,
      class: "fs-tag-item",
      color: o.color,
      ref_for: true
    }, t.computedProps.tag), {
      default: withCtx(() => [
        createBaseVNode("a", mergeProps({
          href: o.url,
          target: "_blank",
          ref_for: true
        }, t.computedProps.a), toDisplayString(o.name), 17, R)
      ]),
      _: 2
    }, 1040, ["color"]))), 128))
  ]);
}
var O = he($, [["render", x]]);
export {
  O as default
};
//# sourceMappingURL=fs-files-format-4dc328b0-TVDZVKIU.js.map
