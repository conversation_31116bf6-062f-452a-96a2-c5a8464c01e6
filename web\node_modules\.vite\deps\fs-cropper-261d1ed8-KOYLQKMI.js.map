{"version": 3, "sources": ["../../node_modules/.pnpm/cropperjs@1.6.2/node_modules/cropperjs/dist/cropper.esm.js", "../../@fast-crud/fast-extends/src/uploader/components/utils/vue-cropperjs.js", "../../@fast-crud/fast-extends/src/uploader/components/fs-cropper.vue", "../../@fast-crud/fast-extends/src/uploader/components/fs-cropper.vue"], "sourcesContent": ["/*!\n * Cropper.js v1.6.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2024-04-21T07:43:05.335Z\n */\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'cropper';\n\n// Actions\nvar ACTION_ALL = 'all';\nvar ACTION_CROP = 'crop';\nvar ACTION_MOVE = 'move';\nvar ACTION_ZOOM = 'zoom';\nvar ACTION_EAST = 'e';\nvar ACTION_WEST = 'w';\nvar ACTION_SOUTH = 's';\nvar ACTION_NORTH = 'n';\nvar ACTION_NORTH_EAST = 'ne';\nvar ACTION_NORTH_WEST = 'nw';\nvar ACTION_SOUTH_EAST = 'se';\nvar ACTION_SOUTH_WEST = 'sw';\n\n// Classes\nvar CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\nvar CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\nvar CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\nvar DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\");\n\n// Drag modes\nvar DRAG_MODE_CROP = 'crop';\nvar DRAG_MODE_MOVE = 'move';\nvar DRAG_MODE_NONE = 'none';\n\n// Events\nvar EVENT_CROP = 'crop';\nvar EVENT_CROP_END = 'cropend';\nvar EVENT_CROP_MOVE = 'cropmove';\nvar EVENT_CROP_START = 'cropstart';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_READY = 'ready';\nvar EVENT_RESIZE = 'resize';\nvar EVENT_WHEEL = 'wheel';\nvar EVENT_ZOOM = 'zoom';\n\n// Mime types\nvar MIME_TYPE_JPEG = 'image/jpeg';\n\n// RegExps\nvar REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\nvar REGEXP_DATA_URL = /^data:/;\nvar REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\nvar REGEXP_TAG_NAME = /^img|canvas$/i;\n\n// Misc\n// Inspired by the default width and height of a canvas element.\nvar MIN_CONTAINER_WIDTH = 200;\nvar MIN_CONTAINER_HEIGHT = 100;\n\nvar DEFAULTS = {\n  // Define the view mode of the cropper\n  viewMode: 0,\n  // 0, 1, 2, 3\n\n  // Define the dragging mode of the cropper\n  dragMode: DRAG_MODE_CROP,\n  // 'crop', 'move' or 'none'\n\n  // Define the initial aspect ratio of the crop box\n  initialAspectRatio: NaN,\n  // Define the aspect ratio of the crop box\n  aspectRatio: NaN,\n  // An object with the previous cropping result data\n  data: null,\n  // A selector for adding extra containers to preview\n  preview: '',\n  // Re-render the cropper when resize the window\n  responsive: true,\n  // Restore the cropped area after resize the window\n  restore: true,\n  // Check if the current image is a cross-origin image\n  checkCrossOrigin: true,\n  // Check the current image's Exif Orientation information\n  checkOrientation: true,\n  // Show the black modal\n  modal: true,\n  // Show the dashed lines for guiding\n  guides: true,\n  // Show the center indicator for guiding\n  center: true,\n  // Show the white modal to highlight the crop box\n  highlight: true,\n  // Show the grid background\n  background: true,\n  // Enable to crop the image automatically when initialize\n  autoCrop: true,\n  // Define the percentage of automatic cropping area when initializes\n  autoCropArea: 0.8,\n  // Enable to move the image\n  movable: true,\n  // Enable to rotate the image\n  rotatable: true,\n  // Enable to scale the image\n  scalable: true,\n  // Enable to zoom the image\n  zoomable: true,\n  // Enable to zoom the image by dragging touch\n  zoomOnTouch: true,\n  // Enable to zoom the image by wheeling mouse\n  zoomOnWheel: true,\n  // Define zoom ratio when zoom the image by wheeling mouse\n  wheelZoomRatio: 0.1,\n  // Enable to move the crop box\n  cropBoxMovable: true,\n  // Enable to resize the crop box\n  cropBoxResizable: true,\n  // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n  toggleDragModeOnDblclick: true,\n  // Size limitation\n  minCanvasWidth: 0,\n  minCanvasHeight: 0,\n  minCropBoxWidth: 0,\n  minCropBoxHeight: 0,\n  minContainerWidth: MIN_CONTAINER_WIDTH,\n  minContainerHeight: MIN_CONTAINER_HEIGHT,\n  // Shortcuts of events\n  ready: null,\n  cropstart: null,\n  cropmove: null,\n  cropend: null,\n  crop: null,\n  zoom: null\n};\n\nvar TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is a positive number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n */\nvar isPositiveNumber = function isPositiveNumber(value) {\n  return value > 0 && value < Infinity;\n};\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nvar slice = Array.prototype.slice;\n\n/**\n * Convert array-like or iterable object to an array.\n * @param {*} value - The value to convert.\n * @returns {Array} Returns a new array.\n */\nfunction toArray(value) {\n  return Array.from ? Array.from(value) : slice.call(value);\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      toArray(data).forEach(function (value, key) {\n        callback.call(data, value, key, data);\n      });\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} target - The target object to extend.\n * @param {*} args - The rest objects for merging to the target object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(target) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(target) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          target[key] = arg[key];\n        });\n      }\n    });\n  }\n  return target;\n};\nvar REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n\n/**\n * Normalize decimal number.\n * Check out {@link https://0.30000000000000004.com/}\n * @param {number} value - The value to normalize.\n * @param {number} [times=100000000000] - The times for normalizing.\n * @returns {number} Returns the normalized number.\n */\nfunction normalizeDecimalNumber(value) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n  return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n}\nvar REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value = \"\".concat(value, \"px\");\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction toParamCase(value) {\n  return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(toParamCase(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n  }\n}\n\n/**\n * Remove data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to remove.\n */\nfunction removeData(element, name) {\n  if (isObject(element[name])) {\n    try {\n      delete element[name];\n    } catch (error) {\n      element[name] = undefined;\n    }\n  } else if (element.dataset) {\n    // #128 Safari not allows to delete dataset property\n    try {\n      delete element.dataset[name];\n    } catch (error) {\n      element.dataset[name] = undefined;\n    }\n  } else {\n    element.removeAttribute(\"data-\".concat(toParamCase(name)));\n  }\n}\nvar REGEXP_SPACES = /\\s\\s*/;\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, {\n      detail: data,\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\nvar location = WINDOW.location;\nvar REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n\n/**\n * Check if the given URL is a cross origin URL.\n * @param {string} url - The target URL.\n * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n */\nfunction isCrossOriginURL(url) {\n  var parts = url.match(REGEXP_ORIGINS);\n  return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n}\n\n/**\n * Add timestamp to the given URL.\n * @param {string} url - The target URL.\n * @returns {string} The result URL.\n */\nfunction addTimestamp(url) {\n  var timestamp = \"timestamp=\".concat(new Date().getTime());\n  return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var maxRatio = 0;\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      if (Math.abs(ratio) > Math.abs(maxRatio)) {\n        maxRatio = ratio;\n      }\n    });\n  });\n  return maxRatio;\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\n/**\n * Get the max sizes in a rectangle under the given aspect ratio.\n * @param {Object} data - The original sizes.\n * @param {string} [type='contain'] - The adjust type.\n * @returns {Object} The result sizes.\n */\nfunction getAdjustedSizes(_ref4) {\n  var aspectRatio = _ref4.aspectRatio,\n    height = _ref4.height,\n    width = _ref4.width;\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n  var isValidWidth = isPositiveNumber(width);\n  var isValidHeight = isPositiveNumber(height);\n  if (isValidWidth && isValidHeight) {\n    var adjustedWidth = height * aspectRatio;\n    if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n      height = width / aspectRatio;\n    } else {\n      width = height * aspectRatio;\n    }\n  } else if (isValidWidth) {\n    height = width / aspectRatio;\n  } else if (isValidHeight) {\n    width = height * aspectRatio;\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\n\n/**\n * Get the new sizes of a rectangle after rotated.\n * @param {Object} data - The original sizes.\n * @returns {Object} The result sizes.\n */\nfunction getRotatedSizes(_ref5) {\n  var width = _ref5.width,\n    height = _ref5.height,\n    degree = _ref5.degree;\n  degree = Math.abs(degree) % 180;\n  if (degree === 90) {\n    return {\n      width: height,\n      height: width\n    };\n  }\n  var arc = degree % 90 * Math.PI / 180;\n  var sinArc = Math.sin(arc);\n  var cosArc = Math.cos(arc);\n  var newWidth = width * cosArc + height * sinArc;\n  var newHeight = width * sinArc + height * cosArc;\n  return degree > 90 ? {\n    width: newHeight,\n    height: newWidth\n  } : {\n    width: newWidth,\n    height: newHeight\n  };\n}\n\n/**\n * Get a canvas which drew the given image.\n * @param {HTMLImageElement} image - The image for drawing.\n * @param {Object} imageData - The image data.\n * @param {Object} canvasData - The canvas data.\n * @param {Object} options - The options.\n * @returns {HTMLCanvasElement} The result canvas.\n */\nfunction getSourceCanvas(image, _ref6, _ref7, _ref8) {\n  var imageAspectRatio = _ref6.aspectRatio,\n    imageNaturalWidth = _ref6.naturalWidth,\n    imageNaturalHeight = _ref6.naturalHeight,\n    _ref6$rotate = _ref6.rotate,\n    rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n    _ref6$scaleX = _ref6.scaleX,\n    scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n    _ref6$scaleY = _ref6.scaleY,\n    scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n  var aspectRatio = _ref7.aspectRatio,\n    naturalWidth = _ref7.naturalWidth,\n    naturalHeight = _ref7.naturalHeight;\n  var _ref8$fillColor = _ref8.fillColor,\n    fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n    _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n    imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n    _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n    imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n    _ref8$maxWidth = _ref8.maxWidth,\n    maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n    _ref8$maxHeight = _ref8.maxHeight,\n    maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n    _ref8$minWidth = _ref8.minWidth,\n    minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n    _ref8$minHeight = _ref8.minHeight,\n    minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var maxSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var minSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n  var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight));\n\n  // Note: should always use image's natural sizes for drawing as\n  // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n  var destMaxSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var destMinSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n  var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n  var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n  canvas.width = normalizeDecimalNumber(width);\n  canvas.height = normalizeDecimalNumber(height);\n  context.fillStyle = fillColor;\n  context.fillRect(0, 0, width, height);\n  context.save();\n  context.translate(width / 2, height / 2);\n  context.rotate(rotate * Math.PI / 180);\n  context.scale(scaleX, scaleY);\n  context.imageSmoothingEnabled = imageSmoothingEnabled;\n  context.imageSmoothingQuality = imageSmoothingQuality;\n  context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n    return Math.floor(normalizeDecimalNumber(param));\n  }))));\n  context.restore();\n  return canvas;\n}\nvar fromCharCode = String.fromCharCode;\n\n/**\n * Get string from char code in data view.\n * @param {DataView} dataView - The data view for read.\n * @param {number} start - The start index.\n * @param {number} length - The read length.\n * @returns {string} The read result.\n */\nfunction getStringFromCharCode(dataView, start, length) {\n  var str = '';\n  length += start;\n  for (var i = start; i < length; i += 1) {\n    str += fromCharCode(dataView.getUint8(i));\n  }\n  return str;\n}\nvar REGEXP_DATA_URL_HEAD = /^data:.*,/;\n\n/**\n * Transform Data URL to array buffer.\n * @param {string} dataURL - The Data URL to transform.\n * @returns {ArrayBuffer} The result array buffer.\n */\nfunction dataURLToArrayBuffer(dataURL) {\n  var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n  var binary = atob(base64);\n  var arrayBuffer = new ArrayBuffer(binary.length);\n  var uint8 = new Uint8Array(arrayBuffer);\n  forEach(uint8, function (value, i) {\n    uint8[i] = binary.charCodeAt(i);\n  });\n  return arrayBuffer;\n}\n\n/**\n * Transform array buffer to Data URL.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n * @param {string} mimeType - The mime type of the Data URL.\n * @returns {string} The result Data URL.\n */\nfunction arrayBufferToDataURL(arrayBuffer, mimeType) {\n  var chunks = [];\n\n  // Chunk Typed Array for better performance (#435)\n  var chunkSize = 8192;\n  var uint8 = new Uint8Array(arrayBuffer);\n  while (uint8.length > 0) {\n    // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n    // eslint-disable-next-line prefer-spread\n    chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n    uint8 = uint8.subarray(chunkSize);\n  }\n  return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n}\n\n/**\n * Get orientation value from given array buffer.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n * @returns {number} The read orientation value.\n */\nfunction resetAndGetOrientation(arrayBuffer) {\n  var dataView = new DataView(arrayBuffer);\n  var orientation;\n\n  // Ignores range error when the image does not have correct Exif information\n  try {\n    var littleEndian;\n    var app1Start;\n    var ifdStart;\n\n    // Only handle JPEG image (start by 0xFFD8)\n    if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n      var length = dataView.byteLength;\n      var offset = 2;\n      while (offset + 1 < length) {\n        if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n          app1Start = offset;\n          break;\n        }\n        offset += 1;\n      }\n    }\n    if (app1Start) {\n      var exifIDCode = app1Start + 4;\n      var tiffOffset = app1Start + 10;\n      if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n        var endianness = dataView.getUint16(tiffOffset);\n        littleEndian = endianness === 0x4949;\n        if (littleEndian || endianness === 0x4D4D /* bigEndian */) {\n          if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n            var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n            if (firstIFDOffset >= 0x00000008) {\n              ifdStart = tiffOffset + firstIFDOffset;\n            }\n          }\n        }\n      }\n    }\n    if (ifdStart) {\n      var _length = dataView.getUint16(ifdStart, littleEndian);\n      var _offset;\n      var i;\n      for (i = 0; i < _length; i += 1) {\n        _offset = ifdStart + i * 12 + 2;\n        if (dataView.getUint16(_offset, littleEndian) === 0x0112 /* Orientation */) {\n          // 8 is the offset of the current tag's value\n          _offset += 8;\n\n          // Get the original orientation value\n          orientation = dataView.getUint16(_offset, littleEndian);\n\n          // Override the orientation with its default value\n          dataView.setUint16(_offset, 1, littleEndian);\n          break;\n        }\n      }\n    }\n  } catch (error) {\n    orientation = 1;\n  }\n  return orientation;\n}\n\n/**\n * Parse Exif Orientation value.\n * @param {number} orientation - The orientation to parse.\n * @returns {Object} The parsed result.\n */\nfunction parseOrientation(orientation) {\n  var rotate = 0;\n  var scaleX = 1;\n  var scaleY = 1;\n  switch (orientation) {\n    // Flip horizontal\n    case 2:\n      scaleX = -1;\n      break;\n\n    // Rotate left 180°\n    case 3:\n      rotate = -180;\n      break;\n\n    // Flip vertical\n    case 4:\n      scaleY = -1;\n      break;\n\n    // Flip vertical and rotate right 90°\n    case 5:\n      rotate = 90;\n      scaleY = -1;\n      break;\n\n    // Rotate right 90°\n    case 6:\n      rotate = 90;\n      break;\n\n    // Flip horizontal and rotate right 90°\n    case 7:\n      rotate = 90;\n      scaleX = -1;\n      break;\n\n    // Rotate left 90°\n    case 8:\n      rotate = -90;\n      break;\n  }\n  return {\n    rotate: rotate,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initCanvas();\n    this.initCropBox();\n    this.renderCanvas();\n    if (this.cropped) {\n      this.renderCropBox();\n    }\n  },\n  initContainer: function initContainer() {\n    var element = this.element,\n      options = this.options,\n      container = this.container,\n      cropper = this.cropper;\n    var minWidth = Number(options.minContainerWidth);\n    var minHeight = Number(options.minContainerHeight);\n    addClass(cropper, CLASS_HIDDEN);\n    removeClass(element, CLASS_HIDDEN);\n    var containerData = {\n      width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n      height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n    };\n    this.containerData = containerData;\n    setStyle(cropper, {\n      width: containerData.width,\n      height: containerData.height\n    });\n    addClass(element, CLASS_HIDDEN);\n    removeClass(cropper, CLASS_HIDDEN);\n  },\n  // Canvas (image wrapper)\n  initCanvas: function initCanvas() {\n    var containerData = this.containerData,\n      imageData = this.imageData;\n    var viewMode = this.options.viewMode;\n    var rotated = Math.abs(imageData.rotate) % 180 === 90;\n    var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n    var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n    var aspectRatio = naturalWidth / naturalHeight;\n    var canvasWidth = containerData.width;\n    var canvasHeight = containerData.height;\n    if (containerData.height * aspectRatio > containerData.width) {\n      if (viewMode === 3) {\n        canvasWidth = containerData.height * aspectRatio;\n      } else {\n        canvasHeight = containerData.width / aspectRatio;\n      }\n    } else if (viewMode === 3) {\n      canvasHeight = containerData.width / aspectRatio;\n    } else {\n      canvasWidth = containerData.height * aspectRatio;\n    }\n    var canvasData = {\n      aspectRatio: aspectRatio,\n      naturalWidth: naturalWidth,\n      naturalHeight: naturalHeight,\n      width: canvasWidth,\n      height: canvasHeight\n    };\n    this.canvasData = canvasData;\n    this.limited = viewMode === 1 || viewMode === 2;\n    this.limitCanvas(true, true);\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    canvasData.left = (containerData.width - canvasData.width) / 2;\n    canvasData.top = (containerData.height - canvasData.height) / 2;\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    this.initialCanvasData = assign({}, canvasData);\n  },\n  limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var viewMode = options.viewMode;\n    var aspectRatio = canvasData.aspectRatio;\n    var cropped = this.cropped && cropBoxData;\n    if (sizeLimited) {\n      var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n      var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n      if (viewMode > 1) {\n        minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n        minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n        if (viewMode === 3) {\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      } else if (viewMode > 0) {\n        if (minCanvasWidth) {\n          minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n        } else if (minCanvasHeight) {\n          minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n        } else if (cropped) {\n          minCanvasWidth = cropBoxData.width;\n          minCanvasHeight = cropBoxData.height;\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      }\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: minCanvasWidth,\n        height: minCanvasHeight\n      });\n      minCanvasWidth = _getAdjustedSizes.width;\n      minCanvasHeight = _getAdjustedSizes.height;\n      canvasData.minWidth = minCanvasWidth;\n      canvasData.minHeight = minCanvasHeight;\n      canvasData.maxWidth = Infinity;\n      canvasData.maxHeight = Infinity;\n    }\n    if (positionLimited) {\n      if (viewMode > (cropped ? 0 : 1)) {\n        var newCanvasLeft = containerData.width - canvasData.width;\n        var newCanvasTop = containerData.height - canvasData.height;\n        canvasData.minLeft = Math.min(0, newCanvasLeft);\n        canvasData.minTop = Math.min(0, newCanvasTop);\n        canvasData.maxLeft = Math.max(0, newCanvasLeft);\n        canvasData.maxTop = Math.max(0, newCanvasTop);\n        if (cropped && this.limited) {\n          canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n          canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n          canvasData.maxLeft = cropBoxData.left;\n          canvasData.maxTop = cropBoxData.top;\n          if (viewMode === 2) {\n            if (canvasData.width >= containerData.width) {\n              canvasData.minLeft = Math.min(0, newCanvasLeft);\n              canvasData.maxLeft = Math.max(0, newCanvasLeft);\n            }\n            if (canvasData.height >= containerData.height) {\n              canvasData.minTop = Math.min(0, newCanvasTop);\n              canvasData.maxTop = Math.max(0, newCanvasTop);\n            }\n          }\n        }\n      } else {\n        canvasData.minLeft = -canvasData.width;\n        canvasData.minTop = -canvasData.height;\n        canvasData.maxLeft = containerData.width;\n        canvasData.maxTop = containerData.height;\n      }\n    }\n  },\n  renderCanvas: function renderCanvas(changed, transformed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    if (transformed) {\n      var _getRotatedSizes = getRotatedSizes({\n          width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n          height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n          degree: imageData.rotate || 0\n        }),\n        naturalWidth = _getRotatedSizes.width,\n        naturalHeight = _getRotatedSizes.height;\n      var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n      var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n      canvasData.left -= (width - canvasData.width) / 2;\n      canvasData.top -= (height - canvasData.height) / 2;\n      canvasData.width = width;\n      canvasData.height = height;\n      canvasData.aspectRatio = naturalWidth / naturalHeight;\n      canvasData.naturalWidth = naturalWidth;\n      canvasData.naturalHeight = naturalHeight;\n      this.limitCanvas(true, false);\n    }\n    if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n      canvasData.left = canvasData.oldLeft;\n    }\n    if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n      canvasData.top = canvasData.oldTop;\n    }\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    this.limitCanvas(false, true);\n    canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n    canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    setStyle(this.canvas, assign({\n      width: canvasData.width,\n      height: canvasData.height\n    }, getTransforms({\n      translateX: canvasData.left,\n      translateY: canvasData.top\n    })));\n    this.renderImage(changed);\n    if (this.cropped && this.limited) {\n      this.limitCropBox(true, true);\n    }\n  },\n  renderImage: function renderImage(changed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n    var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n    assign(imageData, {\n      width: width,\n      height: height,\n      left: (canvasData.width - width) / 2,\n      top: (canvasData.height - height) / 2\n    });\n    setStyle(this.image, assign({\n      width: imageData.width,\n      height: imageData.height\n    }, getTransforms(assign({\n      translateX: imageData.left,\n      translateY: imageData.top\n    }, imageData))));\n    if (changed) {\n      this.output();\n    }\n  },\n  initCropBox: function initCropBox() {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n    var autoCropArea = Number(options.autoCropArea) || 0.8;\n    var cropBoxData = {\n      width: canvasData.width,\n      height: canvasData.height\n    };\n    if (aspectRatio) {\n      if (canvasData.height * aspectRatio > canvasData.width) {\n        cropBoxData.height = cropBoxData.width / aspectRatio;\n      } else {\n        cropBoxData.width = cropBoxData.height * aspectRatio;\n      }\n    }\n    this.cropBoxData = cropBoxData;\n    this.limitCropBox(true, true);\n\n    // Initialize auto crop area\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n\n    // The width/height of auto crop area must large than \"minWidth/Height\"\n    cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n    cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n    cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n    cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    this.initialCropBoxData = assign({}, cropBoxData);\n  },\n  limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData,\n      limited = this.limited;\n    var aspectRatio = options.aspectRatio;\n    if (sizeLimited) {\n      var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n      var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n      var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n      var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height;\n\n      // The min/maxCropBoxWidth/Height must be less than container's width/height\n      minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n      minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n      if (aspectRatio) {\n        if (minCropBoxWidth && minCropBoxHeight) {\n          if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n        } else if (minCropBoxWidth) {\n          minCropBoxHeight = minCropBoxWidth / aspectRatio;\n        } else if (minCropBoxHeight) {\n          minCropBoxWidth = minCropBoxHeight * aspectRatio;\n        }\n        if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n          maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n        } else {\n          maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n        }\n      }\n\n      // The minWidth/Height must be less than maxWidth/Height\n      cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n      cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n      cropBoxData.maxWidth = maxCropBoxWidth;\n      cropBoxData.maxHeight = maxCropBoxHeight;\n    }\n    if (positionLimited) {\n      if (limited) {\n        cropBoxData.minLeft = Math.max(0, canvasData.left);\n        cropBoxData.minTop = Math.max(0, canvasData.top);\n        cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n        cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n      } else {\n        cropBoxData.minLeft = 0;\n        cropBoxData.minTop = 0;\n        cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n        cropBoxData.maxTop = containerData.height - cropBoxData.height;\n      }\n    }\n  },\n  renderCropBox: function renderCropBox() {\n    var options = this.options,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData;\n    if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n      cropBoxData.left = cropBoxData.oldLeft;\n    }\n    if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n      cropBoxData.top = cropBoxData.oldTop;\n    }\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n    this.limitCropBox(false, true);\n    cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n    cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    if (options.movable && options.cropBoxMovable) {\n      // Turn to move the canvas when the crop box is equal to the container\n      setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n    }\n    setStyle(this.cropBox, assign({\n      width: cropBoxData.width,\n      height: cropBoxData.height\n    }, getTransforms({\n      translateX: cropBoxData.left,\n      translateY: cropBoxData.top\n    })));\n    if (this.cropped && this.limited) {\n      this.limitCanvas(true, true);\n    }\n    if (!this.disabled) {\n      this.output();\n    }\n  },\n  output: function output() {\n    this.preview();\n    dispatchEvent(this.element, EVENT_CROP, this.getData());\n  }\n};\n\nvar preview = {\n  initPreview: function initPreview() {\n    var element = this.element,\n      crossOrigin = this.crossOrigin;\n    var preview = this.options.preview;\n    var url = crossOrigin ? this.crossOriginUrl : this.url;\n    var alt = element.alt || 'The image to preview';\n    var image = document.createElement('img');\n    if (crossOrigin) {\n      image.crossOrigin = crossOrigin;\n    }\n    image.src = url;\n    image.alt = alt;\n    this.viewBox.appendChild(image);\n    this.viewBoxImage = image;\n    if (!preview) {\n      return;\n    }\n    var previews = preview;\n    if (typeof preview === 'string') {\n      previews = element.ownerDocument.querySelectorAll(preview);\n    } else if (preview.querySelector) {\n      previews = [preview];\n    }\n    this.previews = previews;\n    forEach(previews, function (el) {\n      var img = document.createElement('img');\n\n      // Save the original size for recover\n      setData(el, DATA_PREVIEW, {\n        width: el.offsetWidth,\n        height: el.offsetHeight,\n        html: el.innerHTML\n      });\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      }\n      img.src = url;\n      img.alt = alt;\n\n      /**\n       * Override img element styles\n       * Add `display:block` to avoid margin top issue\n       * Add `height:auto` to override `height` attribute on IE8\n       * (Occur only when margin-top <= -height)\n       */\n      img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n      el.innerHTML = '';\n      el.appendChild(img);\n    });\n  },\n  resetPreview: function resetPreview() {\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      setStyle(element, {\n        width: data.width,\n        height: data.height\n      });\n      element.innerHTML = data.html;\n      removeData(element, DATA_PREVIEW);\n    });\n  },\n  preview: function preview() {\n    var imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var cropBoxWidth = cropBoxData.width,\n      cropBoxHeight = cropBoxData.height;\n    var width = imageData.width,\n      height = imageData.height;\n    var left = cropBoxData.left - canvasData.left - imageData.left;\n    var top = cropBoxData.top - canvasData.top - imageData.top;\n    if (!this.cropped || this.disabled) {\n      return;\n    }\n    setStyle(this.viewBoxImage, assign({\n      width: width,\n      height: height\n    }, getTransforms(assign({\n      translateX: -left,\n      translateY: -top\n    }, imageData))));\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      var originalWidth = data.width;\n      var originalHeight = data.height;\n      var newWidth = originalWidth;\n      var newHeight = originalHeight;\n      var ratio = 1;\n      if (cropBoxWidth) {\n        ratio = originalWidth / cropBoxWidth;\n        newHeight = cropBoxHeight * ratio;\n      }\n      if (cropBoxHeight && newHeight > originalHeight) {\n        ratio = originalHeight / cropBoxHeight;\n        newWidth = cropBoxWidth * ratio;\n        newHeight = originalHeight;\n      }\n      setStyle(element, {\n        width: newWidth,\n        height: newHeight\n      });\n      setStyle(element.getElementsByTagName('img')[0], assign({\n        width: width * ratio,\n        height: height * ratio\n      }, getTransforms(assign({\n        translateX: -left * ratio,\n        translateY: -top * ratio\n      }, imageData))));\n    });\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      addListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      addListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      addListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      addListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      addListener(element, EVENT_ZOOM, options.zoom);\n    }\n    addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n    addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n    addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n    if (options.responsive) {\n      addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      removeListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      removeListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      removeListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      removeListener(element, EVENT_ZOOM, options.zoom);\n    }\n    removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n    }\n    removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n    removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n    if (options.responsive) {\n      removeListener(window, EVENT_RESIZE, this.onResize);\n    }\n  }\n};\n\nvar handlers = {\n  resize: function resize() {\n    if (this.disabled) {\n      return;\n    }\n    var options = this.options,\n      container = this.container,\n      containerData = this.containerData;\n    var ratioX = container.offsetWidth / containerData.width;\n    var ratioY = container.offsetHeight / containerData.height;\n    var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY;\n\n    // Resize when width changed or height changed\n    if (ratio !== 1) {\n      var canvasData;\n      var cropBoxData;\n      if (options.restore) {\n        canvasData = this.getCanvasData();\n        cropBoxData = this.getCropBoxData();\n      }\n      this.render();\n      if (options.restore) {\n        this.setCanvasData(forEach(canvasData, function (n, i) {\n          canvasData[i] = n * ratio;\n        }));\n        this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n          cropBoxData[i] = n * ratio;\n        }));\n      }\n    }\n  },\n  dblclick: function dblclick() {\n    if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n      return;\n    }\n    this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n  },\n  wheel: function wheel(event) {\n    var _this = this;\n    var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n    var delta = 1;\n    if (this.disabled) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast (#21)\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this.wheeling = false;\n    }, 50);\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, event);\n  },\n  cropStart: function cropStart(event) {\n    var buttons = event.buttons,\n      button = event.button;\n    if (this.disabled\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n    var options = this.options,\n      pointers = this.pointers;\n    var action;\n    if (event.changedTouches) {\n      // Handle touch event\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      // Handle mouse event and pointer event\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n      action = ACTION_ZOOM;\n    } else {\n      action = getData(event.target, DATA_ACTION);\n    }\n    if (!REGEXP_ACTIONS.test(action)) {\n      return;\n    }\n    if (dispatchEvent(this.element, EVENT_CROP_START, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n\n    // This line is required for preventing page zooming in iOS browsers\n    event.preventDefault();\n    this.action = action;\n    this.cropping = false;\n    if (action === ACTION_CROP) {\n      this.cropping = true;\n      addClass(this.dragBox, CLASS_MODAL);\n    }\n  },\n  cropMove: function cropMove(event) {\n    var action = this.action;\n    if (this.disabled || !action) {\n      return;\n    }\n    var pointers = this.pointers;\n    event.preventDefault();\n    if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        // The first parameter should not be undefined (#432)\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  cropEnd: function cropEnd(event) {\n    if (this.disabled) {\n      return;\n    }\n    var action = this.action,\n      pointers = this.pointers;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        delete pointers[touch.identifier];\n      });\n    } else {\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (!Object.keys(pointers).length) {\n      this.action = '';\n    }\n    if (this.cropping) {\n      this.cropping = false;\n      toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n    }\n    dispatchEvent(this.element, EVENT_CROP_END, {\n      originalEvent: event,\n      action: action\n    });\n  }\n};\n\nvar change = {\n  change: function change(event) {\n    var options = this.options,\n      canvasData = this.canvasData,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData,\n      pointers = this.pointers;\n    var action = this.action;\n    var aspectRatio = options.aspectRatio;\n    var left = cropBoxData.left,\n      top = cropBoxData.top,\n      width = cropBoxData.width,\n      height = cropBoxData.height;\n    var right = left + width;\n    var bottom = top + height;\n    var minLeft = 0;\n    var minTop = 0;\n    var maxWidth = containerData.width;\n    var maxHeight = containerData.height;\n    var renderable = true;\n    var offset;\n\n    // Locking aspect ratio in \"free mode\" by holding shift key\n    if (!aspectRatio && event.shiftKey) {\n      aspectRatio = width && height ? width / height : 1;\n    }\n    if (this.limited) {\n      minLeft = cropBoxData.minLeft;\n      minTop = cropBoxData.minTop;\n      maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n      maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n    }\n    var pointer = pointers[Object.keys(pointers)[0]];\n    var range = {\n      x: pointer.endX - pointer.startX,\n      y: pointer.endY - pointer.startY\n    };\n    var check = function check(side) {\n      switch (side) {\n        case ACTION_EAST:\n          if (right + range.x > maxWidth) {\n            range.x = maxWidth - right;\n          }\n          break;\n        case ACTION_WEST:\n          if (left + range.x < minLeft) {\n            range.x = minLeft - left;\n          }\n          break;\n        case ACTION_NORTH:\n          if (top + range.y < minTop) {\n            range.y = minTop - top;\n          }\n          break;\n        case ACTION_SOUTH:\n          if (bottom + range.y > maxHeight) {\n            range.y = maxHeight - bottom;\n          }\n          break;\n      }\n    };\n    switch (action) {\n      // Move crop box\n      case ACTION_ALL:\n        left += range.x;\n        top += range.y;\n        break;\n\n      // Resize crop box\n      case ACTION_EAST:\n        if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_EAST);\n        width += range.x;\n        if (width < 0) {\n          action = ACTION_WEST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_NORTH:\n        if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_NORTH);\n        height -= range.y;\n        top += range.y;\n        if (height < 0) {\n          action = ACTION_SOUTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_WEST:\n        if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_WEST);\n        width -= range.x;\n        left += range.x;\n        if (width < 0) {\n          action = ACTION_EAST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_SOUTH:\n        if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_SOUTH);\n        height += range.y;\n        if (height < 0) {\n          action = ACTION_NORTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_NORTH_EAST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_NORTH_WEST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n          left += cropBoxData.width - width;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_WEST:\n        if (aspectRatio) {\n          if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_EAST:\n        if (aspectRatio) {\n          if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_EAST);\n          width += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n\n      // Move canvas\n      case ACTION_MOVE:\n        this.move(range.x, range.y);\n        renderable = false;\n        break;\n\n      // Zoom canvas\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), event);\n        renderable = false;\n        break;\n\n      // Create crop box\n      case ACTION_CROP:\n        if (!range.x || !range.y) {\n          renderable = false;\n          break;\n        }\n        offset = getOffset(this.cropper);\n        left = pointer.startX - offset.left;\n        top = pointer.startY - offset.top;\n        width = cropBoxData.minWidth;\n        height = cropBoxData.minHeight;\n        if (range.x > 0) {\n          action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n        } else if (range.x < 0) {\n          left -= width;\n          action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n        }\n        if (range.y < 0) {\n          top -= height;\n        }\n\n        // Show the crop box if is hidden\n        if (!this.cropped) {\n          removeClass(this.cropBox, CLASS_HIDDEN);\n          this.cropped = true;\n          if (this.limited) {\n            this.limitCropBox(true, true);\n          }\n        }\n        break;\n    }\n    if (renderable) {\n      cropBoxData.width = width;\n      cropBoxData.height = height;\n      cropBoxData.left = left;\n      cropBoxData.top = top;\n      this.action = action;\n      this.renderCropBox();\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  }\n};\n\nvar methods = {\n  // Show the crop box manually\n  crop: function crop() {\n    if (this.ready && !this.cropped && !this.disabled) {\n      this.cropped = true;\n      this.limitCropBox(true, true);\n      if (this.options.modal) {\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n      removeClass(this.cropBox, CLASS_HIDDEN);\n      this.setCropBoxData(this.initialCropBoxData);\n    }\n    return this;\n  },\n  // Reset the image and crop box to their initial states\n  reset: function reset() {\n    if (this.ready && !this.disabled) {\n      this.imageData = assign({}, this.initialImageData);\n      this.canvasData = assign({}, this.initialCanvasData);\n      this.cropBoxData = assign({}, this.initialCropBoxData);\n      this.renderCanvas();\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    }\n    return this;\n  },\n  // Clear the crop box\n  clear: function clear() {\n    if (this.cropped && !this.disabled) {\n      assign(this.cropBoxData, {\n        left: 0,\n        top: 0,\n        width: 0,\n        height: 0\n      });\n      this.cropped = false;\n      this.renderCropBox();\n      this.limitCanvas(true, true);\n\n      // Render canvas after crop box rendered\n      this.renderCanvas();\n      removeClass(this.dragBox, CLASS_MODAL);\n      addClass(this.cropBox, CLASS_HIDDEN);\n    }\n    return this;\n  },\n  /**\n   * Replace the image's src and rebuild the cropper\n   * @param {string} url - The new URL.\n   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n   * @returns {Cropper} this\n   */\n  replace: function replace(url) {\n    var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!this.disabled && url) {\n      if (this.isImg) {\n        this.element.src = url;\n      }\n      if (hasSameSize) {\n        this.url = url;\n        this.image.src = url;\n        if (this.ready) {\n          this.viewBoxImage.src = url;\n          forEach(this.previews, function (element) {\n            element.getElementsByTagName('img')[0].src = url;\n          });\n        }\n      } else {\n        if (this.isImg) {\n          this.replaced = true;\n        }\n        this.options.data = null;\n        this.uncreate();\n        this.load(url);\n      }\n    }\n    return this;\n  },\n  // Enable (unfreeze) the cropper\n  enable: function enable() {\n    if (this.ready && this.disabled) {\n      this.disabled = false;\n      removeClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  // Disable (freeze) the cropper\n  disable: function disable() {\n    if (this.ready && !this.disabled) {\n      this.disabled = true;\n      addClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  /**\n   * Destroy the cropper and remove the instance from the image\n   * @returns {Cropper} this\n   */\n  destroy: function destroy() {\n    var element = this.element;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    element[NAMESPACE] = undefined;\n    if (this.isImg && this.replaced) {\n      element.src = this.originalUrl;\n    }\n    this.uncreate();\n    return this;\n  },\n  /**\n   * Move the canvas with relative offsets\n   * @param {number} offsetX - The relative offset distance on the x-axis.\n   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n   * @returns {Cropper} this\n   */\n  move: function move(offsetX) {\n    var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n    var _this$canvasData = this.canvasData,\n      left = _this$canvasData.left,\n      top = _this$canvasData.top;\n    return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n  },\n  /**\n   * Move the canvas to an absolute point\n   * @param {number} x - The x-axis coordinate.\n   * @param {number} [y=x] - The y-axis coordinate.\n   * @returns {Cropper} this\n   */\n  moveTo: function moveTo(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var canvasData = this.canvasData;\n    var changed = false;\n    x = Number(x);\n    y = Number(y);\n    if (this.ready && !this.disabled && this.options.movable) {\n      if (isNumber(x)) {\n        canvasData.left = x;\n        changed = true;\n      }\n      if (isNumber(y)) {\n        canvasData.top = y;\n        changed = true;\n      }\n      if (changed) {\n        this.renderCanvas(true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the canvas with a relative ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoom: function zoom(ratio, _originalEvent) {\n    var canvasData = this.canvasData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n  },\n  /**\n   * Zoom the canvas to an absolute ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Object} pivot - The zoom pivot point coordinate.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var width = canvasData.width,\n      height = canvasData.height,\n      naturalWidth = canvasData.naturalWidth,\n      naturalHeight = canvasData.naturalHeight;\n    ratio = Number(ratio);\n    if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      if (dispatchEvent(this.element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: width / naturalWidth,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      if (_originalEvent) {\n        var pointers = this.pointers;\n        var offset = getOffset(this.cropper);\n        var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n      } else {\n        // Zoom from the center of the canvas\n        canvasData.left -= (newWidth - width) / 2;\n        canvasData.top -= (newHeight - height) / 2;\n      }\n      canvasData.width = newWidth;\n      canvasData.height = newHeight;\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Rotate the canvas with a relative degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotate: function rotate(degree) {\n    return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n  },\n  /**\n   * Rotate the canvas to an absolute degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotateTo: function rotateTo(degree) {\n    degree = Number(degree);\n    if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n      this.imageData.rotate = degree % 360;\n      this.renderCanvas(true, true);\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Cropper} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    var scaleY = this.imageData.scaleY;\n    return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    var scaleX = this.imageData.scaleX;\n    return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n  },\n  /**\n   * Scale the image\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scale: function scale(scaleX) {\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var imageData = this.imageData;\n    var transformed = false;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.ready && !this.disabled && this.options.scalable) {\n      if (isNumber(scaleX)) {\n        imageData.scaleX = scaleX;\n        transformed = true;\n      }\n      if (isNumber(scaleY)) {\n        imageData.scaleY = scaleY;\n        transformed = true;\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Get the cropped area position and size data (base on the original image)\n   * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n   * @returns {Object} The result cropped data.\n   */\n  getData: function getData() {\n    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        x: cropBoxData.left - canvasData.left,\n        y: cropBoxData.top - canvasData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n      var ratio = imageData.width / imageData.naturalWidth;\n      forEach(data, function (n, i) {\n        data[i] = n / ratio;\n      });\n      if (rounded) {\n        // In case rounding off leads to extra 1px in right or bottom border\n        // we should round the top-left corner and the dimension (#343).\n        var bottom = Math.round(data.y + data.height);\n        var right = Math.round(data.x + data.width);\n        data.x = Math.round(data.x);\n        data.y = Math.round(data.y);\n        data.width = right - data.x;\n        data.height = bottom - data.y;\n      }\n    } else {\n      data = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n    }\n    if (options.rotatable) {\n      data.rotate = imageData.rotate || 0;\n    }\n    if (options.scalable) {\n      data.scaleX = imageData.scaleX || 1;\n      data.scaleY = imageData.scaleY || 1;\n    }\n    return data;\n  },\n  /**\n   * Set the cropped area position and size with new data\n   * @param {Object} data - The new data.\n   * @returns {Cropper} this\n   */\n  setData: function setData(data) {\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData;\n    var cropBoxData = {};\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      var transformed = false;\n      if (options.rotatable) {\n        if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n          imageData.rotate = data.rotate;\n          transformed = true;\n        }\n      }\n      if (options.scalable) {\n        if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n          imageData.scaleX = data.scaleX;\n          transformed = true;\n        }\n        if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n          imageData.scaleY = data.scaleY;\n          transformed = true;\n        }\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n      var ratio = imageData.width / imageData.naturalWidth;\n      if (isNumber(data.x)) {\n        cropBoxData.left = data.x * ratio + canvasData.left;\n      }\n      if (isNumber(data.y)) {\n        cropBoxData.top = data.y * ratio + canvasData.top;\n      }\n      if (isNumber(data.width)) {\n        cropBoxData.width = data.width * ratio;\n      }\n      if (isNumber(data.height)) {\n        cropBoxData.height = data.height * ratio;\n      }\n      this.setCropBoxData(cropBoxData);\n    }\n    return this;\n  },\n  /**\n   * Get the container size data.\n   * @returns {Object} The result container data.\n   */\n  getContainerData: function getContainerData() {\n    return this.ready ? assign({}, this.containerData) : {};\n  },\n  /**\n   * Get the image position and size data.\n   * @returns {Object} The result image data.\n   */\n  getImageData: function getImageData() {\n    return this.sized ? assign({}, this.imageData) : {};\n  },\n  /**\n   * Get the canvas position and size data.\n   * @returns {Object} The result canvas data.\n   */\n  getCanvasData: function getCanvasData() {\n    var canvasData = this.canvasData;\n    var data = {};\n    if (this.ready) {\n      forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n        data[n] = canvasData[n];\n      });\n    }\n    return data;\n  },\n  /**\n   * Set the canvas position and size with new data.\n   * @param {Object} data - The new canvas data.\n   * @returns {Cropper} this\n   */\n  setCanvasData: function setCanvasData(data) {\n    var canvasData = this.canvasData;\n    var aspectRatio = canvasData.aspectRatio;\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        canvasData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        canvasData.top = data.top;\n      }\n      if (isNumber(data.width)) {\n        canvasData.width = data.width;\n        canvasData.height = data.width / aspectRatio;\n      } else if (isNumber(data.height)) {\n        canvasData.height = data.height;\n        canvasData.width = data.height * aspectRatio;\n      }\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Get the crop box position and size data.\n   * @returns {Object} The result crop box data.\n   */\n  getCropBoxData: function getCropBoxData() {\n    var cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        left: cropBoxData.left,\n        top: cropBoxData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n    }\n    return data || {};\n  },\n  /**\n   * Set the crop box position and size with new data.\n   * @param {Object} data - The new crop box data.\n   * @returns {Cropper} this\n   */\n  setCropBoxData: function setCropBoxData(data) {\n    var cropBoxData = this.cropBoxData;\n    var aspectRatio = this.options.aspectRatio;\n    var widthChanged;\n    var heightChanged;\n    if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        cropBoxData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        cropBoxData.top = data.top;\n      }\n      if (isNumber(data.width) && data.width !== cropBoxData.width) {\n        widthChanged = true;\n        cropBoxData.width = data.width;\n      }\n      if (isNumber(data.height) && data.height !== cropBoxData.height) {\n        heightChanged = true;\n        cropBoxData.height = data.height;\n      }\n      if (aspectRatio) {\n        if (widthChanged) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else if (heightChanged) {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n      this.renderCropBox();\n    }\n    return this;\n  },\n  /**\n   * Get a canvas drawn the cropped image.\n   * @param {Object} [options={}] - The config options.\n   * @returns {HTMLCanvasElement} - The result canvas.\n   */\n  getCroppedCanvas: function getCroppedCanvas() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!this.ready || !window.HTMLCanvasElement) {\n      return null;\n    }\n    var canvasData = this.canvasData;\n    var source = getSourceCanvas(this.image, this.imageData, canvasData, options);\n\n    // Returns the source canvas if it is not cropped.\n    if (!this.cropped) {\n      return source;\n    }\n    var _this$getData = this.getData(options.rounded),\n      initialX = _this$getData.x,\n      initialY = _this$getData.y,\n      initialWidth = _this$getData.width,\n      initialHeight = _this$getData.height;\n    var ratio = source.width / Math.floor(canvasData.naturalWidth);\n    if (ratio !== 1) {\n      initialX *= ratio;\n      initialY *= ratio;\n      initialWidth *= ratio;\n      initialHeight *= ratio;\n    }\n    var aspectRatio = initialWidth / initialHeight;\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.maxWidth || Infinity,\n      height: options.maxHeight || Infinity\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.minWidth || 0,\n      height: options.minHeight || 0\n    }, 'cover');\n    var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.width || (ratio !== 1 ? source.width : initialWidth),\n        height: options.height || (ratio !== 1 ? source.height : initialHeight)\n      }),\n      width = _getAdjustedSizes.width,\n      height = _getAdjustedSizes.height;\n    width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n    height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = options.fillColor || 'transparent';\n    context.fillRect(0, 0, width, height);\n    var _options$imageSmoothi = options.imageSmoothingEnabled,\n      imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n      imageSmoothingQuality = options.imageSmoothingQuality;\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n    if (imageSmoothingQuality) {\n      context.imageSmoothingQuality = imageSmoothingQuality;\n    }\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n    var sourceWidth = source.width;\n    var sourceHeight = source.height;\n\n    // Source canvas parameters\n    var srcX = initialX;\n    var srcY = initialY;\n    var srcWidth;\n    var srcHeight;\n\n    // Destination canvas parameters\n    var dstX;\n    var dstY;\n    var dstWidth;\n    var dstHeight;\n    if (srcX <= -initialWidth || srcX > sourceWidth) {\n      srcX = 0;\n      srcWidth = 0;\n      dstX = 0;\n      dstWidth = 0;\n    } else if (srcX <= 0) {\n      dstX = -srcX;\n      srcX = 0;\n      srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n      dstWidth = srcWidth;\n    } else if (srcX <= sourceWidth) {\n      dstX = 0;\n      srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n      dstWidth = srcWidth;\n    }\n    if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n      srcY = 0;\n      srcHeight = 0;\n      dstY = 0;\n      dstHeight = 0;\n    } else if (srcY <= 0) {\n      dstY = -srcY;\n      srcY = 0;\n      srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n      dstHeight = srcHeight;\n    } else if (srcY <= sourceHeight) {\n      dstY = 0;\n      srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n      dstHeight = srcHeight;\n    }\n    var params = [srcX, srcY, srcWidth, srcHeight];\n\n    // Avoid \"IndexSizeError\"\n    if (dstWidth > 0 && dstHeight > 0) {\n      var scale = width / initialWidth;\n      params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n    }\n\n    // All the numerical parameters should be integer for `drawImage`\n    // https://github.com/fengyuanchen/cropper/issues/476\n    context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    return canvas;\n  },\n  /**\n   * Change the aspect ratio of the crop box.\n   * @param {number} aspectRatio - The new aspect ratio.\n   * @returns {Cropper} this\n   */\n  setAspectRatio: function setAspectRatio(aspectRatio) {\n    var options = this.options;\n    if (!this.disabled && !isUndefined(aspectRatio)) {\n      // 0 -> NaN\n      options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n      if (this.ready) {\n        this.initCropBox();\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n    }\n    return this;\n  },\n  /**\n   * Change the drag mode.\n   * @param {string} mode - The new drag mode.\n   * @returns {Cropper} this\n   */\n  setDragMode: function setDragMode(mode) {\n    var options = this.options,\n      dragBox = this.dragBox,\n      face = this.face;\n    if (this.ready && !this.disabled) {\n      var croppable = mode === DRAG_MODE_CROP;\n      var movable = options.movable && mode === DRAG_MODE_MOVE;\n      mode = croppable || movable ? mode : DRAG_MODE_NONE;\n      options.dragMode = mode;\n      setData(dragBox, DATA_ACTION, mode);\n      toggleClass(dragBox, CLASS_CROP, croppable);\n      toggleClass(dragBox, CLASS_MOVE, movable);\n      if (!options.cropBoxMovable) {\n        // Sync drag mode to crop box when it is not movable\n        setData(face, DATA_ACTION, mode);\n        toggleClass(face, CLASS_CROP, croppable);\n        toggleClass(face, CLASS_MOVE, movable);\n      }\n    }\n    return this;\n  }\n};\n\nvar AnotherCropper = WINDOW.Cropper;\nvar Cropper = /*#__PURE__*/function () {\n  /**\n   * Create a new Cropper.\n   * @param {Element} element - The target element for cropping.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Cropper(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Cropper);\n    if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n      throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.cropped = false;\n    this.disabled = false;\n    this.pointers = {};\n    this.ready = false;\n    this.reloading = false;\n    this.replaced = false;\n    this.sized = false;\n    this.sizing = false;\n    this.init();\n  }\n  return _createClass(Cropper, [{\n    key: \"init\",\n    value: function init() {\n      var element = this.element;\n      var tagName = element.tagName.toLowerCase();\n      var url;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n      if (tagName === 'img') {\n        this.isImg = true;\n\n        // e.g.: \"img/picture.jpg\"\n        url = element.getAttribute('src') || '';\n        this.originalUrl = url;\n\n        // Stop when it's a blank image\n        if (!url) {\n          return;\n        }\n\n        // e.g.: \"https://example.com/img/picture.jpg\"\n        url = element.src;\n      } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n        url = element.toDataURL();\n      }\n      this.load(url);\n    }\n  }, {\n    key: \"load\",\n    value: function load(url) {\n      var _this = this;\n      if (!url) {\n        return;\n      }\n      this.url = url;\n      this.imageData = {};\n      var element = this.element,\n        options = this.options;\n      if (!options.rotatable && !options.scalable) {\n        options.checkOrientation = false;\n      }\n\n      // Only IE10+ supports Typed Arrays\n      if (!options.checkOrientation || !window.ArrayBuffer) {\n        this.clone();\n        return;\n      }\n\n      // Detect the mime type of the image directly if it is a Data URL\n      if (REGEXP_DATA_URL.test(url)) {\n        // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n        if (REGEXP_DATA_URL_JPEG.test(url)) {\n          this.read(dataURLToArrayBuffer(url));\n        } else {\n          // Only a JPEG image may contains Exif Orientation information,\n          // the rest types of Data URLs are not necessary to check orientation at all.\n          this.clone();\n        }\n        return;\n      }\n\n      // 1. Detect the mime type of the image by a XMLHttpRequest.\n      // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n      var xhr = new XMLHttpRequest();\n      var clone = this.clone.bind(this);\n      this.reloading = true;\n      this.xhr = xhr;\n\n      // 1. Cross origin requests are only supported for protocol schemes:\n      // http, https, data, chrome, chrome-extension.\n      // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n      // in some browsers as IE11 and Safari.\n      xhr.onabort = clone;\n      xhr.onerror = clone;\n      xhr.ontimeout = clone;\n      xhr.onprogress = function () {\n        // Abort the request directly if it not a JPEG image for better performance\n        if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n          xhr.abort();\n        }\n      };\n      xhr.onload = function () {\n        _this.read(xhr.response);\n      };\n      xhr.onloadend = function () {\n        _this.reloading = false;\n        _this.xhr = null;\n      };\n\n      // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n      if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n        url = addTimestamp(url);\n      }\n\n      // The third parameter is required for avoiding side-effect (#682)\n      xhr.open('GET', url, true);\n      xhr.responseType = 'arraybuffer';\n      xhr.withCredentials = element.crossOrigin === 'use-credentials';\n      xhr.send();\n    }\n  }, {\n    key: \"read\",\n    value: function read(arrayBuffer) {\n      var options = this.options,\n        imageData = this.imageData;\n\n      // Reset the orientation value to its default value 1\n      // as some iOS browsers will render image with its orientation\n      var orientation = resetAndGetOrientation(arrayBuffer);\n      var rotate = 0;\n      var scaleX = 1;\n      var scaleY = 1;\n      if (orientation > 1) {\n        // Generate a new URL which has the default orientation value\n        this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n        var _parseOrientation = parseOrientation(orientation);\n        rotate = _parseOrientation.rotate;\n        scaleX = _parseOrientation.scaleX;\n        scaleY = _parseOrientation.scaleY;\n      }\n      if (options.rotatable) {\n        imageData.rotate = rotate;\n      }\n      if (options.scalable) {\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n      }\n      this.clone();\n    }\n  }, {\n    key: \"clone\",\n    value: function clone() {\n      var element = this.element,\n        url = this.url;\n      var crossOrigin = element.crossOrigin;\n      var crossOriginUrl = url;\n      if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n        if (!crossOrigin) {\n          crossOrigin = 'anonymous';\n        }\n\n        // Bust cache when there is not a \"crossOrigin\" property (#519)\n        crossOriginUrl = addTimestamp(url);\n      }\n      this.crossOrigin = crossOrigin;\n      this.crossOriginUrl = crossOriginUrl;\n      var image = document.createElement('img');\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n      image.src = crossOriginUrl || url;\n      image.alt = element.alt || 'The image to crop';\n      this.image = image;\n      image.onload = this.start.bind(this);\n      image.onerror = this.stop.bind(this);\n      addClass(image, CLASS_HIDE);\n      element.parentNode.insertBefore(image, element.nextSibling);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      this.sizing = true;\n\n      // Match all browsers that use WebKit as the layout engine in iOS devices,\n      // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n      var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n      var done = function done(naturalWidth, naturalHeight) {\n        assign(_this2.imageData, {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight,\n          aspectRatio: naturalWidth / naturalHeight\n        });\n        _this2.initialImageData = assign({}, _this2.imageData);\n        _this2.sizing = false;\n        _this2.sized = true;\n        _this2.build();\n      };\n\n      // Most modern browsers (excepts iOS WebKit)\n      if (image.naturalWidth && !isIOSWebKit) {\n        done(image.naturalWidth, image.naturalHeight);\n        return;\n      }\n      var sizingImage = document.createElement('img');\n      var body = document.body || document.documentElement;\n      this.sizingImage = sizingImage;\n      sizingImage.onload = function () {\n        done(sizingImage.width, sizingImage.height);\n        if (!isIOSWebKit) {\n          body.removeChild(sizingImage);\n        }\n      };\n      sizingImage.src = image.src;\n\n      // iOS WebKit will convert the image automatically\n      // with its orientation once append it into DOM (#279)\n      if (!isIOSWebKit) {\n        sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n        body.appendChild(sizingImage);\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      image.parentNode.removeChild(image);\n      this.image = null;\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (!this.sized || this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options,\n        image = this.image;\n\n      // Create cropper elements\n      var container = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n      var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n      var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n      this.container = container;\n      this.cropper = cropper;\n      this.canvas = canvas;\n      this.dragBox = dragBox;\n      this.cropBox = cropBox;\n      this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n      this.face = face;\n      canvas.appendChild(image);\n\n      // Hide the original image\n      addClass(element, CLASS_HIDDEN);\n\n      // Inserts the cropper after to the current image\n      container.insertBefore(cropper, element.nextSibling);\n\n      // Show the hidden image\n      removeClass(image, CLASS_HIDE);\n      this.initPreview();\n      this.bind();\n      options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n      options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n      options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n      addClass(cropBox, CLASS_HIDDEN);\n      if (!options.guides) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n      }\n      if (!options.center) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n      }\n      if (options.background) {\n        addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n      }\n      if (!options.highlight) {\n        addClass(face, CLASS_INVISIBLE);\n      }\n      if (options.cropBoxMovable) {\n        addClass(face, CLASS_MOVE);\n        setData(face, DATA_ACTION, ACTION_ALL);\n      }\n      if (!options.cropBoxResizable) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n      }\n      this.render();\n      this.ready = true;\n      this.setDragMode(options.dragMode);\n      if (options.autoCrop) {\n        this.crop();\n      }\n      this.setData(options.data);\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_READY);\n    }\n  }, {\n    key: \"unbuild\",\n    value: function unbuild() {\n      if (!this.ready) {\n        return;\n      }\n      this.ready = false;\n      this.unbind();\n      this.resetPreview();\n      var parentNode = this.cropper.parentNode;\n      if (parentNode) {\n        parentNode.removeChild(this.cropper);\n      }\n      removeClass(this.element, CLASS_HIDDEN);\n    }\n  }, {\n    key: \"uncreate\",\n    value: function uncreate() {\n      if (this.ready) {\n        this.unbuild();\n        this.ready = false;\n        this.cropped = false;\n      } else if (this.sizing) {\n        this.sizingImage.onload = null;\n        this.sizing = false;\n        this.sized = false;\n      } else if (this.reloading) {\n        this.xhr.onabort = null;\n        this.xhr.abort();\n      } else if (this.image) {\n        this.stop();\n      }\n    }\n\n    /**\n     * Get the no conflict cropper class.\n     * @returns {Cropper} The cropper class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Cropper = AnotherCropper;\n      return Cropper;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n}();\nassign(Cropper.prototype, render, preview, events, handlers, change, methods);\n\nexport { Cropper as default };\n", "import { h } from \"vue\";\nimport Cropper from \"cropperjs\";\n\nconst previewPropType = typeof window === \"undefined\" ? [String, Array] : [String, Array, Element, NodeList];\n\nexport default {\n  render() {\n    const crossorigin = this.crossorigin || undefined;\n\n    return h(\"div\", { style: this.containerStyle }, [\n      h(\"img\", {\n        ref: \"img\",\n        src: this.src,\n        alt: this.alt || \"image\",\n        style: [{ \"max-width\": \"100%\" }, this.imgStyle],\n        crossorigin\n      })\n    ]);\n  },\n  props: {\n    // Library props\n    containerStyle: Object,\n    src: {\n      type: String,\n      default: \"\"\n    },\n    alt: String,\n    imgStyle: Object,\n\n    // CropperJS props\n    viewMode: Number,\n    dragMode: String,\n    initialAspectRatio: Number,\n    aspectRatio: Number,\n    data: Object,\n    preview: previewPropType,\n    responsive: {\n      type: Boolean,\n      default: true\n    },\n    restore: {\n      type: Boolean,\n      default: true\n    },\n    checkCrossOrigin: {\n      type: Boolean,\n      default: true\n    },\n    checkOrientation: {\n      type: Boolean,\n      default: true\n    },\n    crossorigin: {\n      type: String\n    },\n    modal: {\n      type: Boolean,\n      default: true\n    },\n    guides: {\n      type: Boolean,\n      default: true\n    },\n    center: {\n      type: Boolean,\n      default: true\n    },\n    highlight: {\n      type: Boolean,\n      default: true\n    },\n    background: {\n      type: Boolean,\n      default: true\n    },\n    autoCrop: {\n      type: Boolean,\n      default: true\n    },\n    autoCropArea: Number,\n    movable: {\n      type: Boolean,\n      default: true\n    },\n    rotatable: {\n      type: Boolean,\n      default: true\n    },\n    scalable: {\n      type: Boolean,\n      default: true\n    },\n    zoomable: {\n      type: Boolean,\n      default: true\n    },\n    zoomOnTouch: {\n      type: Boolean,\n      default: true\n    },\n    zoomOnWheel: {\n      type: Boolean,\n      default: true\n    },\n    wheelZoomRatio: Number,\n    cropBoxMovable: {\n      type: Boolean,\n      default: true\n    },\n    cropBoxResizable: {\n      type: Boolean,\n      default: true\n    },\n    toggleDragModeOnDblclick: {\n      type: Boolean,\n      default: true\n    },\n\n    // Size limitation\n    minCanvasWidth: Number,\n    minCanvasHeight: Number,\n    minCropBoxWidth: Number,\n    minCropBoxHeight: Number,\n    minContainerWidth: Number,\n    minContainerHeight: Number,\n\n    // callbacks\n    ready: Function,\n    cropstart: Function,\n    cropmove: Function,\n    cropend: Function,\n    crop: Function,\n    zoom: Function\n  },\n  mounted() {\n    const { containerStyle, src, alt, imgStyle, ...data } = this.$options.props;\n    const props = {};\n\n    for (const key in data) {\n      if (this[key] !== undefined) {\n        props[key] = this[key];\n      }\n    }\n\n    this.cropper = new Cropper(this.$refs.img, props);\n  },\n  methods: {\n    // Reset the image and crop box to their initial states\n    reset() {\n      return this.cropper.reset();\n    },\n\n    // Clear the crop box\n    clear() {\n      return this.cropper.clear();\n    },\n\n    // Init crop box manually\n    initCrop() {\n      return this.cropper.crop();\n    },\n\n    /**\n     * Replace the image's src and rebuild the cropper\n     * @param {string} url - The new URL.\n     * @param {boolean} [onlyColorChanged] - Indicate if the new image only changed color.\n     * @returns {Object} this\n     */\n    replace(url, onlyColorChanged = false) {\n      return this.cropper.replace(url, onlyColorChanged);\n    },\n\n    // Enable (unfreeze) the cropper\n    enable() {\n      return this.cropper.enable();\n    },\n\n    // Disable (freeze) the cropper\n    disable() {\n      return this.cropper.disable();\n    },\n\n    // Destroy the cropper and remove the instance from the image\n    destroy() {\n      return this.cropper.destroy();\n    },\n\n    /**\n     * Move the canvas with relative offsets\n     * @param {number} offsetX - The relative offset distance on the x-axis.\n     * @param {number} offsetY - The relative offset distance on the y-axis.\n     * @returns {Object} this\n     */\n    move(offsetX, offsetY) {\n      return this.cropper.move(offsetX, offsetY);\n    },\n\n    /**\n     * Move the canvas to an absolute point\n     * @param {number} x - The x-axis coordinate.\n     * @param {number} [y=x] - The y-axis coordinate.\n     * @returns {Object} this\n     */\n    moveTo(x, y = x) {\n      return this.cropper.moveTo(x, y);\n    },\n\n    /**\n     * Zoom the canvas with a relative ratio\n     * @param {number} ratio - The target ratio.\n     * @param {Event} _originalEvent - The original event if any.\n     * @returns {Object} this\n     */\n    relativeZoom(ratio, _originalEvent) {\n      return this.cropper.zoom(ratio, _originalEvent);\n    },\n\n    /**\n     * Zoom the canvas to an absolute ratio\n     * @param {number} ratio - The target ratio.\n     * @param {Event} _originalEvent - The original event if any.\n     * @returns {Object} this\n     */\n    zoomTo(ratio, _originalEvent) {\n      return this.cropper.zoomTo(ratio, _originalEvent);\n    },\n\n    /**\n     * Rotate the canvas with a relative degree\n     * @param {number} degree - The rotate degree.\n     * @returns {Object} this\n     */\n    rotate(degree) {\n      return this.cropper.rotate(degree);\n    },\n\n    /**\n     * Rotate the canvas to an absolute degree\n     * @param {number} degree - The rotate degree.\n     * @returns {Object} this\n     */\n    rotateTo(degree) {\n      return this.cropper.rotateTo(degree);\n    },\n\n    /**\n     * Scale the image on the x-axis.\n     * @param {number} scaleX - The scale ratio on the x-axis.\n     * @returns {Object} this\n     */\n    scaleX(scaleX) {\n      return this.cropper.scaleX(scaleX);\n    },\n\n    /**\n     * Scale the image on the y-axis.\n     * @param {number} scaleY - The scale ratio on the y-axis.\n     * @returns {Object} this\n     */\n    scaleY(scaleY) {\n      return this.cropper.scaleY(scaleY);\n    },\n\n    /**\n     * Scale the image\n     * @param {number} scaleX - The scale ratio on the x-axis.\n     * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n     * @returns {Object} this\n     */\n    scale(scaleX, scaleY = scaleX) {\n      return this.cropper.scale(scaleX, scaleY);\n    },\n\n    /**\n     * Get the cropped area position and size data (base on the original image)\n     * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n     * @returns {Object} The result cropped data.\n     */\n    getData(rounded = false) {\n      return this.cropper.getData(rounded);\n    },\n\n    /**\n     * Set the cropped area position and size with new data\n     * @param {Object} data - The new data.\n     * @returns {Object} this\n     */\n    setData(data) {\n      return this.cropper.setData(data);\n    },\n\n    /**\n     * Get the container size data.\n     * @returns {Object} The result container data.\n     */\n    getContainerData() {\n      return this.cropper.getContainerData();\n    },\n\n    /**\n     * Get the image position and size data.\n     * @returns {Object} The result image data.\n     */\n    getImageData() {\n      return this.cropper.getImageData();\n    },\n\n    /**\n     * Get the canvas position and size data.\n     * @returns {Object} The result canvas data.\n     */\n    getCanvasData() {\n      return this.cropper.getCanvasData();\n    },\n\n    /**\n     * Set the canvas position and size with new data.\n     * @param {Object} data - The new canvas data.\n     * @returns {Object} this\n     */\n    setCanvasData(data) {\n      return this.cropper.setCanvasData(data);\n    },\n\n    /**\n     * Get the crop box position and size data.\n     * @returns {Object} The result crop box data.\n     */\n    getCropBoxData() {\n      return this.cropper.getCropBoxData();\n    },\n\n    /**\n     * Set the crop box position and size with new data.\n     * @param {Object} data - The new crop box data.\n     * @returns {Object} this\n     */\n    setCropBoxData(data) {\n      return this.cropper.setCropBoxData(data);\n    },\n\n    /**\n     * Get a canvas drawn the cropped image.\n     * @param {Object} [options={}] - The config options.\n     * @returns {HTMLCanvasElement} - The result canvas.\n     */\n    getCroppedCanvas(options = {}) {\n      return this.cropper.getCroppedCanvas(options);\n    },\n\n    /**\n     * Change the aspect ratio of the crop box.\n     * @param {number} aspectRatio - The new aspect ratio.\n     * @returns {Object} this\n     */\n    setAspectRatio(aspectRatio) {\n      return this.cropper.setAspectRatio(aspectRatio);\n    },\n\n    /**\n     * Change the drag mode.\n     * @param {string} mode - The new drag mode.\n     * @returns {Object} this\n     */\n    setDragMode(mode) {\n      return this.cropper.setDragMode(mode);\n    }\n  }\n};\n", "<template>\n  <component\n    :is=\"ui.dialog.name\"\n    ref=\"cropperDialogRef\"\n    v-model:[ui.dialog.visible]=\"dialogVisible\"\n    append-to-body\n    width=\"900px\"\n    :close-on-click-modal=\"true\"\n    v-bind=\"dialogBinding\"\n    :destroy-on-close=\"false\"\n  >\n    <div class=\"fs-cropper-dialog-wrap\">\n      <input v-show=\"false\" ref=\"fileInputRef\" type=\"file\" :accept=\"accept\" @change=\"handleChange\" />\n      <!-- step1 -->\n      <div v-show=\"!isLoaded\" class=\"fs-cropper-dialog__choose fs-cropper-dialog_left\">\n        <fs-button round :text=\"computedTexts.chooseImage\" @click=\"showFileChooser\" />\n        <p>{{ computedUploadTip }}</p>\n      </div>\n      <!-- step2 -->\n      <div v-show=\"isLoaded\" class=\"fs-cropper-dialog__edit fs-cropper-dialog_left\">\n        <div class=\"fs-cropper-dialog__edit-area\">\n          <vue-cropper\n            ref=\"cropperRef\"\n            :src=\"imgSrc\"\n            preview=\".preview\"\n            :style=\"{ height: _cropperHeight }\"\n            v-bind=\"_cropper\"\n          />\n        </div>\n        <div class=\"tool-bar\">\n          <component :is=\"ui.buttonGroup.name\">\n            <fs-button v-for=\"(item, index) of computedButtons\" :key=\"index\" v-bind=\"item\" />\n          </component>\n        </div>\n      </div>\n      <div class=\"fs-cropper-dialog__preview\">\n        <span class=\"fs-cropper-dialog__preview-title\">{{ computedTexts.preview }}</span>\n        <div class=\"fs-cropper-dialog__preview-120 preview\"></div>\n        <div class=\"fs-cropper-dialog__preview-65 preview\" :class=\"{ round: _cropper.aspectRatio === 1 }\"></div>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <fs-button size=\"small\" :text=\"computedTexts.cancel\" @click=\"handleClose\" />\n        <fs-button type=\"primary\" size=\"small\" :text=\"computedTexts.confirm\" @click=\"doCropper()\" />\n      </div>\n    </template>\n  </component>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, ref, Ref } from \"vue\";\nimport VueCropper from \"./utils/vue-cropperjs.js\";\nimport \"cropperjs/dist/cropper.css\";\nimport { useI18n, useUi } from \"@fast-crud/fast-crud\";\n\n/**\n * 图片裁剪对话框\n * 内部封装了[cropperjs](https://github.com/fengyuanchen/cropperjs)\n */\nexport default defineComponent({\n  name: \"FsCropper\",\n  components: {\n    VueCropper\n  },\n  props: {\n    // 对话框标题\n    title: {\n      type: String\n    },\n    // cropper的高度，默认为浏览器可视窗口高度的40%，最小270\n    cropperHeight: {\n      type: [String, Number]\n    },\n    // 对话框宽度，默认50%\n    dialogWidth: {\n      type: [String, Number],\n      default: \"50%\"\n    },\n    // 图片大小限制，单位MB，0为不限制\n    maxSize: {\n      type: Number,\n      default: 5\n    },\n    // 上传提示\n    uploadTip: {\n      type: String\n    },\n    // cropperjs的参数，详见：https://github.com/fengyuanchen/cropperjs\n    cropper: {\n      type: Object\n    },\n    // 可接收的文件后缀\n    accept: {\n      type: String,\n      default: \".jpg, .jpeg, .png, .gif, .webp\"\n    },\n    // 输出类型，blob,dataUrl,all\n    output: {\n      type: String,\n      default: \"blob\" // blob\n    },\n    compressQuality: {\n      type: Number,\n      default: 0.8\n    }\n  },\n  emits: [\"cancel\", \"done\", \"ready\"],\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    const { t } = useI18n();\n    const dialogVisible: Ref = ref(false);\n    const cropperRef: Ref = ref();\n    const fileInputRef: Ref = ref();\n    const isLoaded: Ref = ref(false);\n    const imgSrc: Ref = ref();\n    const data: Ref = ref();\n    const file: Ref = ref();\n    const scale: Ref = ref({\n      x: 1,\n      y: 1\n    });\n    // 点击关闭弹窗\n    function handleClose() {\n      dialogVisible.value = false;\n    }\n\n    function handleClosed() {\n      clear();\n      ctx.emit(\"cancel\");\n    }\n    const vClosed = ui.dialog.buildOnClosedBind(handleClosed);\n    const customClass = ui.dialog.customClass;\n    const dialogBinding: Ref = computed(() => {\n      return {\n        ...vClosed,\n        [customClass]: \"fs-cropper-dialog\",\n        ...ui.formWrapper.buildWidthBind(ui.dialog.name, \"960px\"),\n        ...ui.formWrapper.buildInitBind(ui.dialog.name),\n        title: props.title || t(\"fs.extends.cropper.title\")\n      };\n    });\n\n    function open(url: string) {\n      dialogVisible.value = true;\n      if (url != null && url !== \"\") {\n        imgSrc.value = url;\n      }\n    }\n    function close() {\n      dialogVisible.value = false;\n    }\n    function clear() {\n      isLoaded.value = false;\n      if (fileInputRef.value != null) {\n        fileInputRef.value.value = null;\n        fileInputRef.value = null;\n      }\n      if (cropperRef.value != null) {\n        cropperRef.value.clear();\n      }\n    }\n    // 获取vue-cropper组件对象\n    function getCropperRef() {\n      return cropperRef.value;\n    }\n    const scope = {\n      cropper: getCropperRef(),\n      zoom,\n      clear,\n      close,\n      open\n    };\n\n    function ready(event: any) {\n      // this.zoom(-0.3)\n      ctx.emit(\"ready\", {\n        event,\n        ...scope\n      });\n    }\n    function preventDefault(e: any) {\n      e.preventDefault();\n      return false;\n    }\n    // 点击按钮打开文件资源窗口\n    function handleClick() {\n      fileInputRef.value.click();\n    }\n\n    // 检测选择的文件是否合适\n    function checkFile(file: File) {\n      // 仅限图片\n      if (file.type.indexOf(\"image\") === -1) {\n        ui.message.warn(\"请选择合适的文件类型\");\n        return false;\n      }\n      // 超出大小\n      if (props.maxSize > 0 && file.size / 1024 / 1024 > props.maxSize) {\n        ui.message.warn(`图片大小超出最大限制（${props.maxSize}MB），请重新选择.`);\n        return false;\n      }\n      return true;\n    }\n\n    function setImage(e: any) {\n      const selectFile = e.target.files[0];\n      if (selectFile.type.indexOf(\"image/\") === -1) {\n        ui.message.warn(\"Please select an image file\");\n        return;\n      }\n      if (typeof FileReader === \"function\") {\n        const reader = new FileReader();\n        reader.onload = (event) => {\n          imgSrc.value = event.target.result;\n          // rebuild cropperjs with the updated source\n          cropperRef.value.replace(event.target.result);\n        };\n        reader.readAsDataURL(selectFile);\n      } else {\n        ui.message.error(\"Sorry, FileReader API not supported\");\n      }\n    }\n    // 触发input框的change事件选择图片\n    function handleChange(e: any) {\n      e.preventDefault();\n      const files = e.target.files || e.dataTransfer.files;\n      if (files == null) {\n        return;\n      }\n      isLoaded.value = true;\n      const selectedFile = files[0];\n      if (checkFile(selectedFile)) {\n        file.value = selectedFile;\n        setImage(e);\n        // setTimeout(() => {\n        //   this.zoom(-0.3)\n        // }, 1)\n      }\n    }\n\n    function getCropImageDataUrl(fileType: string, quality?: any) {\n      // get image data for post processing, e.g. upload or setting image src\n      if (quality == null) {\n        quality = props.compressQuality;\n      }\n      return cropperRef.value.getCroppedCanvas().toDataURL(fileType, quality);\n    }\n    async function getCropImageBlob(type: string, quality?: any) {\n      if (quality == null) {\n        quality = props.compressQuality;\n      }\n\n      return new Promise((resolve, reject) => {\n        function callback(blob: any) {\n          resolve(blob);\n        }\n        cropperRef.value.getCroppedCanvas().toBlob(callback, type, quality);\n      });\n    }\n    function emit(result: any) {\n      ctx.emit(\"done\", result);\n    }\n    async function doOutput(file: File) {\n      const ret: any = { file };\n      if (props.output === \"all\") {\n        const blob = await getCropImageBlob(file.type);\n        const dataUrl = getCropImageDataUrl(file.type);\n        ret.blob = blob;\n        ret.dataUrl = dataUrl;\n        emit(ret);\n        return;\n      }\n\n      if (props.output === \"blob\") {\n        ret.blob = await getCropImageBlob(file.type);\n        emit(ret);\n        return;\n      }\n      if (props.output === \"dataUrl\") {\n        ret.dataUrl = getCropImageDataUrl(file.type);\n        emit(ret);\n      }\n    }\n\n    async function doCropper() {\n      if (!isLoaded.value) {\n        ui.message.warn(\"请先选择图片\");\n        return;\n      }\n      await doOutput(file.value);\n      dialogVisible.value = false;\n    }\n\n    function flipX() {\n      cropperRef.value.scaleX((scale.value.x *= -1));\n    }\n    function flipY() {\n      cropperRef.value.scaleY((scale.value.y *= -1));\n    }\n    function getCropBoxData() {\n      data.value = JSON.stringify(cropperRef.value.getCropBoxData(), null, 4);\n    }\n    function getData() {\n      data.value = JSON.stringify(cropperRef.value.getData(), null, 4);\n    }\n    function move(offsetX: any, offsetY: any) {\n      cropperRef.value.move(offsetX, offsetY);\n    }\n    function reset() {\n      cropperRef.value.reset();\n    }\n    function rotate(deg: any) {\n      cropperRef.value.rotate(deg);\n    }\n    function setCropBoxData() {\n      cropperRef.value.setCropBoxData(JSON.parse(data.value));\n    }\n    function setData() {\n      cropperRef.value.setData(JSON.parse(data.value));\n    }\n\n    function showFileChooser() {\n      fileInputRef.value.click();\n    }\n    function zoom(percent: any) {\n      cropperRef.value.relativeZoom(percent);\n    }\n\n    const computedButtons = computed(() => {\n      const size = \"small\";\n      const round = true;\n      const buttons = [\n        {\n          size,\n          round,\n          icon: ui.icons.edit,\n          text: t(\"fs.extends.cropper.reChoose\"),\n          onClick() {\n            handleClick();\n          }\n        },\n        {\n          size,\n          round,\n          text: t(\"fs.extends.cropper.flipX\"),\n          onClick() {\n            flipX();\n          }\n        },\n        {\n          size,\n          round,\n          text: t(\"fs.extends.cropper.flipY\"),\n          onClick() {\n            flipY();\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.zoomIn,\n          onClick() {\n            zoom(0.1);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.zoomOut,\n          onClick() {\n            zoom(-0.1);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refreshLeft,\n          onClick() {\n            rotate(90);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refreshRight,\n          onClick() {\n            rotate(-90);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refresh,\n          text: t(\"fs.extends.cropper.reset\"),\n          onClick() {\n            reset();\n          }\n        }\n      ];\n\n      return buttons;\n    });\n\n    const computedTexts = computed(() => {\n      return {\n        title: t(\"fs.extends.cropper.title\"),\n        preview: t(\"fs.extends.cropper.preview\"),\n        cancel: t(\"fs.extends.cropper.cancel\"),\n        confirm: t(\"fs.extends.cropper.confirm\"),\n        chooseImage: t(\"fs.extends.cropper.chooseImage\")\n      };\n    });\n\n    const computedUploadTip = computed(() => {\n      if (props.uploadTip != null && props.uploadTip !== \"\") {\n        return props.uploadTip;\n      }\n      if (props.maxSize > 0) {\n        return `${t(\"fs.extends.cropper.onlySupport\")} ${props.accept.replace(/,/g, \"、\")},\n        ${t(\"fs.extends.cropper.sizeLimit\")} ${props.maxSize}M`;\n      } else {\n        return `${t(\"fs.extends.cropper.onlySupport\")}${props.accept},${t(\"fs.extends.cropper.sizeNoLimit\")}`;\n      }\n    });\n    return {\n      ui,\n      cropperRef,\n      fileInputRef,\n      dialogVisible,\n      dialogBinding,\n      isLoaded,\n      imgSrc,\n      data,\n      file,\n      scale,\n      computedButtons,\n      handleClose,\n      setData,\n      handleClosed,\n      close,\n      showFileChooser,\n      zoom,\n      setCropBoxData,\n      rotate,\n      reset,\n      move,\n      getData,\n      getCropBoxData,\n      flipY,\n      flipX,\n      doCropper,\n      doOutput,\n      getCropImageBlob,\n      getCropImageDataUrl,\n      handleChange,\n      setImage,\n      checkFile,\n      handleClick,\n      preventDefault,\n      open,\n      clear,\n      getCropperRef,\n      ready,\n      computedTexts,\n      computedUploadTip\n    };\n  },\n  data() {\n    return {};\n  },\n  computed: {\n    _cropper() {\n      const def: any = {\n        aspectRatio: 1,\n        ready: this.ready\n      };\n      if (this.cropper == null) {\n        return def;\n      }\n      const assign = Object.assign(def, this.cropper);\n      return assign;\n    },\n    _cropperHeight() {\n      let height = this.cropperHeight;\n      if (height == null) {\n        height = document.documentElement.clientHeight * 0.55;\n        if (height < 270) {\n          height = 270;\n        }\n      }\n      if (typeof height === \"number\") {\n        return height + \"px\";\n      }\n      return height;\n    },\n    _dialogWidth() {\n      let width = this.dialogWidth;\n      if (width == null) {\n        width = \"50%\";\n      }\n      if (typeof width === \"number\") {\n        return width + \"px\";\n      }\n      return width;\n    }\n  }\n});\n</script>\n\n<style lang=\"less\">\n//@width: 10px;\n//@height: @width + 10px;\n.fs-cropper-dialog {\n  n-button-group {\n    display: flex;\n    button {\n      margin: 0 2px;\n    }\n  }\n  &-wrap {\n    display: flex;\n    justify-content: space-between;\n    width: 100%;\n    height: 100%;\n    padding-bottom: 20px;\n  }\n  .dialog-footer {\n    > .fs-button {\n      margin: 2px;\n    }\n  }\n\n  &_left {\n    font-size: 13px;\n    color: #999999;\n    position: relative;\n    background: #ecf2f6;\n    flex-grow: 5;\n    margin: 10px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex-direction: column;\n  }\n\n  &__choose {\n    p {\n      width: 100%;\n      text-align: center;\n    }\n  }\n\n  &__edit {\n    &-area {\n      width: 100%;\n      overflow: hidden;\n\n      &-img {\n        object-fit: cover;\n      }\n    }\n    .tool-bar {\n      margin: 10px;\n      position: absolute;\n      bottom: -50px;\n    }\n  }\n\n  &__preview {\n    background: #ecf2f6;\n    text-align: center;\n    width: 200px;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    font-size: 13px;\n    margin: 10px;\n    padding: 10px;\n    &-title {\n      color: #999999;\n    }\n    .preview {\n      overflow: hidden;\n      margin: 10px;\n      border: 1px #cacaca solid;\n    }\n    .round {\n      border-radius: 500px;\n    }\n\n    img {\n      background: #fff;\n      margin: auto !important;\n      border-radius: 0 !important;\n    }\n\n    &-120 {\n      height: 120px;\n      width: 120px;\n    }\n\n    &-65 {\n      height: 65px;\n      width: 65px;\n    }\n\n    &-40 {\n      height: 30px;\n      width: 30px;\n    }\n  }\n}\n</style>\n", "<template>\n  <component\n    :is=\"ui.dialog.name\"\n    ref=\"cropperDialogRef\"\n    v-model:[ui.dialog.visible]=\"dialogVisible\"\n    append-to-body\n    width=\"900px\"\n    :close-on-click-modal=\"true\"\n    v-bind=\"dialogBinding\"\n    :destroy-on-close=\"false\"\n  >\n    <div class=\"fs-cropper-dialog-wrap\">\n      <input v-show=\"false\" ref=\"fileInputRef\" type=\"file\" :accept=\"accept\" @change=\"handleChange\" />\n      <!-- step1 -->\n      <div v-show=\"!isLoaded\" class=\"fs-cropper-dialog__choose fs-cropper-dialog_left\">\n        <fs-button round :text=\"computedTexts.chooseImage\" @click=\"showFileChooser\" />\n        <p>{{ computedUploadTip }}</p>\n      </div>\n      <!-- step2 -->\n      <div v-show=\"isLoaded\" class=\"fs-cropper-dialog__edit fs-cropper-dialog_left\">\n        <div class=\"fs-cropper-dialog__edit-area\">\n          <vue-cropper\n            ref=\"cropperRef\"\n            :src=\"imgSrc\"\n            preview=\".preview\"\n            :style=\"{ height: _cropperHeight }\"\n            v-bind=\"_cropper\"\n          />\n        </div>\n        <div class=\"tool-bar\">\n          <component :is=\"ui.buttonGroup.name\">\n            <fs-button v-for=\"(item, index) of computedButtons\" :key=\"index\" v-bind=\"item\" />\n          </component>\n        </div>\n      </div>\n      <div class=\"fs-cropper-dialog__preview\">\n        <span class=\"fs-cropper-dialog__preview-title\">{{ computedTexts.preview }}</span>\n        <div class=\"fs-cropper-dialog__preview-120 preview\"></div>\n        <div class=\"fs-cropper-dialog__preview-65 preview\" :class=\"{ round: _cropper.aspectRatio === 1 }\"></div>\n      </div>\n    </div>\n\n    <template #footer>\n      <div class=\"dialog-footer\">\n        <fs-button size=\"small\" :text=\"computedTexts.cancel\" @click=\"handleClose\" />\n        <fs-button type=\"primary\" size=\"small\" :text=\"computedTexts.confirm\" @click=\"doCropper()\" />\n      </div>\n    </template>\n  </component>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, ref, Ref } from \"vue\";\nimport VueCropper from \"./utils/vue-cropperjs.js\";\nimport \"cropperjs/dist/cropper.css\";\nimport { useI18n, useUi } from \"@fast-crud/fast-crud\";\n\n/**\n * 图片裁剪对话框\n * 内部封装了[cropperjs](https://github.com/fengyuanchen/cropperjs)\n */\nexport default defineComponent({\n  name: \"FsCropper\",\n  components: {\n    VueCropper\n  },\n  props: {\n    // 对话框标题\n    title: {\n      type: String\n    },\n    // cropper的高度，默认为浏览器可视窗口高度的40%，最小270\n    cropperHeight: {\n      type: [String, Number]\n    },\n    // 对话框宽度，默认50%\n    dialogWidth: {\n      type: [String, Number],\n      default: \"50%\"\n    },\n    // 图片大小限制，单位MB，0为不限制\n    maxSize: {\n      type: Number,\n      default: 5\n    },\n    // 上传提示\n    uploadTip: {\n      type: String\n    },\n    // cropperjs的参数，详见：https://github.com/fengyuanchen/cropperjs\n    cropper: {\n      type: Object\n    },\n    // 可接收的文件后缀\n    accept: {\n      type: String,\n      default: \".jpg, .jpeg, .png, .gif, .webp\"\n    },\n    // 输出类型，blob,dataUrl,all\n    output: {\n      type: String,\n      default: \"blob\" // blob\n    },\n    compressQuality: {\n      type: Number,\n      default: 0.8\n    }\n  },\n  emits: [\"cancel\", \"done\", \"ready\"],\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    const { t } = useI18n();\n    const dialogVisible: Ref = ref(false);\n    const cropperRef: Ref = ref();\n    const fileInputRef: Ref = ref();\n    const isLoaded: Ref = ref(false);\n    const imgSrc: Ref = ref();\n    const data: Ref = ref();\n    const file: Ref = ref();\n    const scale: Ref = ref({\n      x: 1,\n      y: 1\n    });\n    // 点击关闭弹窗\n    function handleClose() {\n      dialogVisible.value = false;\n    }\n\n    function handleClosed() {\n      clear();\n      ctx.emit(\"cancel\");\n    }\n    const vClosed = ui.dialog.buildOnClosedBind(handleClosed);\n    const customClass = ui.dialog.customClass;\n    const dialogBinding: Ref = computed(() => {\n      return {\n        ...vClosed,\n        [customClass]: \"fs-cropper-dialog\",\n        ...ui.formWrapper.buildWidthBind(ui.dialog.name, \"960px\"),\n        ...ui.formWrapper.buildInitBind(ui.dialog.name),\n        title: props.title || t(\"fs.extends.cropper.title\")\n      };\n    });\n\n    function open(url: string) {\n      dialogVisible.value = true;\n      if (url != null && url !== \"\") {\n        imgSrc.value = url;\n      }\n    }\n    function close() {\n      dialogVisible.value = false;\n    }\n    function clear() {\n      isLoaded.value = false;\n      if (fileInputRef.value != null) {\n        fileInputRef.value.value = null;\n        fileInputRef.value = null;\n      }\n      if (cropperRef.value != null) {\n        cropperRef.value.clear();\n      }\n    }\n    // 获取vue-cropper组件对象\n    function getCropperRef() {\n      return cropperRef.value;\n    }\n    const scope = {\n      cropper: getCropperRef(),\n      zoom,\n      clear,\n      close,\n      open\n    };\n\n    function ready(event: any) {\n      // this.zoom(-0.3)\n      ctx.emit(\"ready\", {\n        event,\n        ...scope\n      });\n    }\n    function preventDefault(e: any) {\n      e.preventDefault();\n      return false;\n    }\n    // 点击按钮打开文件资源窗口\n    function handleClick() {\n      fileInputRef.value.click();\n    }\n\n    // 检测选择的文件是否合适\n    function checkFile(file: File) {\n      // 仅限图片\n      if (file.type.indexOf(\"image\") === -1) {\n        ui.message.warn(\"请选择合适的文件类型\");\n        return false;\n      }\n      // 超出大小\n      if (props.maxSize > 0 && file.size / 1024 / 1024 > props.maxSize) {\n        ui.message.warn(`图片大小超出最大限制（${props.maxSize}MB），请重新选择.`);\n        return false;\n      }\n      return true;\n    }\n\n    function setImage(e: any) {\n      const selectFile = e.target.files[0];\n      if (selectFile.type.indexOf(\"image/\") === -1) {\n        ui.message.warn(\"Please select an image file\");\n        return;\n      }\n      if (typeof FileReader === \"function\") {\n        const reader = new FileReader();\n        reader.onload = (event) => {\n          imgSrc.value = event.target.result;\n          // rebuild cropperjs with the updated source\n          cropperRef.value.replace(event.target.result);\n        };\n        reader.readAsDataURL(selectFile);\n      } else {\n        ui.message.error(\"Sorry, FileReader API not supported\");\n      }\n    }\n    // 触发input框的change事件选择图片\n    function handleChange(e: any) {\n      e.preventDefault();\n      const files = e.target.files || e.dataTransfer.files;\n      if (files == null) {\n        return;\n      }\n      isLoaded.value = true;\n      const selectedFile = files[0];\n      if (checkFile(selectedFile)) {\n        file.value = selectedFile;\n        setImage(e);\n        // setTimeout(() => {\n        //   this.zoom(-0.3)\n        // }, 1)\n      }\n    }\n\n    function getCropImageDataUrl(fileType: string, quality?: any) {\n      // get image data for post processing, e.g. upload or setting image src\n      if (quality == null) {\n        quality = props.compressQuality;\n      }\n      return cropperRef.value.getCroppedCanvas().toDataURL(fileType, quality);\n    }\n    async function getCropImageBlob(type: string, quality?: any) {\n      if (quality == null) {\n        quality = props.compressQuality;\n      }\n\n      return new Promise((resolve, reject) => {\n        function callback(blob: any) {\n          resolve(blob);\n        }\n        cropperRef.value.getCroppedCanvas().toBlob(callback, type, quality);\n      });\n    }\n    function emit(result: any) {\n      ctx.emit(\"done\", result);\n    }\n    async function doOutput(file: File) {\n      const ret: any = { file };\n      if (props.output === \"all\") {\n        const blob = await getCropImageBlob(file.type);\n        const dataUrl = getCropImageDataUrl(file.type);\n        ret.blob = blob;\n        ret.dataUrl = dataUrl;\n        emit(ret);\n        return;\n      }\n\n      if (props.output === \"blob\") {\n        ret.blob = await getCropImageBlob(file.type);\n        emit(ret);\n        return;\n      }\n      if (props.output === \"dataUrl\") {\n        ret.dataUrl = getCropImageDataUrl(file.type);\n        emit(ret);\n      }\n    }\n\n    async function doCropper() {\n      if (!isLoaded.value) {\n        ui.message.warn(\"请先选择图片\");\n        return;\n      }\n      await doOutput(file.value);\n      dialogVisible.value = false;\n    }\n\n    function flipX() {\n      cropperRef.value.scaleX((scale.value.x *= -1));\n    }\n    function flipY() {\n      cropperRef.value.scaleY((scale.value.y *= -1));\n    }\n    function getCropBoxData() {\n      data.value = JSON.stringify(cropperRef.value.getCropBoxData(), null, 4);\n    }\n    function getData() {\n      data.value = JSON.stringify(cropperRef.value.getData(), null, 4);\n    }\n    function move(offsetX: any, offsetY: any) {\n      cropperRef.value.move(offsetX, offsetY);\n    }\n    function reset() {\n      cropperRef.value.reset();\n    }\n    function rotate(deg: any) {\n      cropperRef.value.rotate(deg);\n    }\n    function setCropBoxData() {\n      cropperRef.value.setCropBoxData(JSON.parse(data.value));\n    }\n    function setData() {\n      cropperRef.value.setData(JSON.parse(data.value));\n    }\n\n    function showFileChooser() {\n      fileInputRef.value.click();\n    }\n    function zoom(percent: any) {\n      cropperRef.value.relativeZoom(percent);\n    }\n\n    const computedButtons = computed(() => {\n      const size = \"small\";\n      const round = true;\n      const buttons = [\n        {\n          size,\n          round,\n          icon: ui.icons.edit,\n          text: t(\"fs.extends.cropper.reChoose\"),\n          onClick() {\n            handleClick();\n          }\n        },\n        {\n          size,\n          round,\n          text: t(\"fs.extends.cropper.flipX\"),\n          onClick() {\n            flipX();\n          }\n        },\n        {\n          size,\n          round,\n          text: t(\"fs.extends.cropper.flipY\"),\n          onClick() {\n            flipY();\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.zoomIn,\n          onClick() {\n            zoom(0.1);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.zoomOut,\n          onClick() {\n            zoom(-0.1);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refreshLeft,\n          onClick() {\n            rotate(90);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refreshRight,\n          onClick() {\n            rotate(-90);\n          }\n        },\n        {\n          size,\n          round,\n          icon: ui.icons.refresh,\n          text: t(\"fs.extends.cropper.reset\"),\n          onClick() {\n            reset();\n          }\n        }\n      ];\n\n      return buttons;\n    });\n\n    const computedTexts = computed(() => {\n      return {\n        title: t(\"fs.extends.cropper.title\"),\n        preview: t(\"fs.extends.cropper.preview\"),\n        cancel: t(\"fs.extends.cropper.cancel\"),\n        confirm: t(\"fs.extends.cropper.confirm\"),\n        chooseImage: t(\"fs.extends.cropper.chooseImage\")\n      };\n    });\n\n    const computedUploadTip = computed(() => {\n      if (props.uploadTip != null && props.uploadTip !== \"\") {\n        return props.uploadTip;\n      }\n      if (props.maxSize > 0) {\n        return `${t(\"fs.extends.cropper.onlySupport\")} ${props.accept.replace(/,/g, \"、\")},\n        ${t(\"fs.extends.cropper.sizeLimit\")} ${props.maxSize}M`;\n      } else {\n        return `${t(\"fs.extends.cropper.onlySupport\")}${props.accept},${t(\"fs.extends.cropper.sizeNoLimit\")}`;\n      }\n    });\n    return {\n      ui,\n      cropperRef,\n      fileInputRef,\n      dialogVisible,\n      dialogBinding,\n      isLoaded,\n      imgSrc,\n      data,\n      file,\n      scale,\n      computedButtons,\n      handleClose,\n      setData,\n      handleClosed,\n      close,\n      showFileChooser,\n      zoom,\n      setCropBoxData,\n      rotate,\n      reset,\n      move,\n      getData,\n      getCropBoxData,\n      flipY,\n      flipX,\n      doCropper,\n      doOutput,\n      getCropImageBlob,\n      getCropImageDataUrl,\n      handleChange,\n      setImage,\n      checkFile,\n      handleClick,\n      preventDefault,\n      open,\n      clear,\n      getCropperRef,\n      ready,\n      computedTexts,\n      computedUploadTip\n    };\n  },\n  data() {\n    return {};\n  },\n  computed: {\n    _cropper() {\n      const def: any = {\n        aspectRatio: 1,\n        ready: this.ready\n      };\n      if (this.cropper == null) {\n        return def;\n      }\n      const assign = Object.assign(def, this.cropper);\n      return assign;\n    },\n    _cropperHeight() {\n      let height = this.cropperHeight;\n      if (height == null) {\n        height = document.documentElement.clientHeight * 0.55;\n        if (height < 270) {\n          height = 270;\n        }\n      }\n      if (typeof height === \"number\") {\n        return height + \"px\";\n      }\n      return height;\n    },\n    _dialogWidth() {\n      let width = this.dialogWidth;\n      if (width == null) {\n        width = \"50%\";\n      }\n      if (typeof width === \"number\") {\n        return width + \"px\";\n      }\n      return width;\n    }\n  }\n});\n</script>\n\n<style lang=\"less\">\n//@width: 10px;\n//@height: @width + 10px;\n.fs-cropper-dialog {\n  n-button-group {\n    display: flex;\n    button {\n      margin: 0 2px;\n    }\n  }\n  &-wrap {\n    display: flex;\n    justify-content: space-between;\n    width: 100%;\n    height: 100%;\n    padding-bottom: 20px;\n  }\n  .dialog-footer {\n    > .fs-button {\n      margin: 2px;\n    }\n  }\n\n  &_left {\n    font-size: 13px;\n    color: #999999;\n    position: relative;\n    background: #ecf2f6;\n    flex-grow: 5;\n    margin: 10px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex-direction: column;\n  }\n\n  &__choose {\n    p {\n      width: 100%;\n      text-align: center;\n    }\n  }\n\n  &__edit {\n    &-area {\n      width: 100%;\n      overflow: hidden;\n\n      &-img {\n        object-fit: cover;\n      }\n    }\n    .tool-bar {\n      margin: 10px;\n      position: absolute;\n      bottom: -50px;\n    }\n  }\n\n  &__preview {\n    background: #ecf2f6;\n    text-align: center;\n    width: 200px;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    font-size: 13px;\n    margin: 10px;\n    padding: 10px;\n    &-title {\n      color: #999999;\n    }\n    .preview {\n      overflow: hidden;\n      margin: 10px;\n      border: 1px #cacaca solid;\n    }\n    .round {\n      border-radius: 500px;\n    }\n\n    img {\n      background: #fff;\n      margin: auto !important;\n      border-radius: 0 !important;\n    }\n\n    &-120 {\n      height: 120px;\n      width: 120px;\n    }\n\n    &-65 {\n      height: 65px;\n      width: 65px;\n    }\n\n    &-40 {\n      height: 30px;\n      width: 30px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAASA,GAAQC,GAAGC,GAAG;AACrB,MAAIC,IAAI,OAAO,KAAKF,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIG,IAAI,OAAO,sBAAsBH,CAAC;AACtCC,UAAME,IAAIA,EAAE,OAAO,SAAUF,GAAG;AAC9B,aAAO,OAAO,yBAAyBD,GAAGC,CAAC,EAAE;IACnD,CAAK,IAAIC,EAAE,KAAK,MAAMA,GAAGC,CAAC;EACvB;AACD,SAAOD;AACT;AACA,SAASE,GAAeJ,GAAG;AACzB,WAASC,IAAI,GAAGA,IAAI,UAAU,QAAQA,KAAK;AACzC,QAAIC,IAAY,UAAUD,CAAC,KAAnB,OAAuB,UAAUA,CAAC,IAAI,CAAA;AAC9CA,QAAI,IAAIF,GAAQ,OAAOG,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUD,GAAG;AAClDI,SAAgBL,GAAGC,GAAGC,EAAED,CAAC,CAAC;IAChC,CAAK,IAAI,OAAO,4BAA4B,OAAO,iBAAiBD,GAAG,OAAO,0BAA0BE,CAAC,CAAC,IAAIH,GAAQ,OAAOG,CAAC,CAAC,EAAE,QAAQ,SAAUD,GAAG;AAChJ,aAAO,eAAeD,GAAGC,GAAG,OAAO,yBAAyBC,GAAGD,CAAC,CAAC;IACvE,CAAK;EACF;AACD,SAAOD;AACT;AACA,SAASM,GAAaJ,GAAGD,GAAG;AAC1B,MAAgB,OAAOC,KAAnB,YAAwB,CAACA;AAAG,WAAOA;AACvC,MAAIF,IAAIE,EAAE,OAAO,WAAW;AAC5B,MAAeF,MAAX,QAAc;AAChB,QAAIO,IAAIP,EAAE,KAAKE,GAAGD,KAAK,SAAS;AAChC,QAAgB,OAAOM,KAAnB;AAAsB,aAAOA;AACjC,UAAM,IAAI,UAAU,8CAA8C;EACnE;AACD,UAAqBN,MAAb,WAAiB,SAAS,QAAQC,CAAC;AAC7C;AACA,SAASM,GAAeN,GAAG;AACzB,MAAIK,IAAID,GAAaJ,GAAG,QAAQ;AAChC,SAAmB,OAAOK,KAAnB,WAAuBA,IAAIA,IAAI;AACxC;AACA,SAASE,GAAQN,GAAG;AAClB;AAEA,SAAOM,KAAwB,OAAO,UAArB,cAA2C,OAAO,OAAO,YAA1B,WAAqC,SAAUN,GAAG;AAChG,WAAO,OAAOA;EACf,IAAG,SAAUA,GAAG;AACf,WAAOA,KAAmB,OAAO,UAArB,cAA+BA,EAAE,gBAAgB,UAAUA,MAAM,OAAO,YAAY,WAAW,OAAOA;EACtH,GAAKM,GAAQN,CAAC;AACd;AACA,SAASO,GAAgBC,GAAUC,GAAa;AAC9C,MAAI,EAAED,aAAoBC;AACxB,UAAM,IAAI,UAAU,mCAAmC;AAE3D;AACA,SAASC,GAAkBC,GAAQC,GAAO;AACxC,WAAS,IAAI,GAAG,IAAIA,EAAM,QAAQ,KAAK;AACrC,QAAIC,IAAaD,EAAM,CAAC;AACxBC,MAAW,aAAaA,EAAW,cAAc,OACjDA,EAAW,eAAe,MACtB,WAAWA,MAAYA,EAAW,WAAW,OACjD,OAAO,eAAeF,GAAQN,GAAeQ,EAAW,GAAG,GAAGA,CAAU;EACzE;AACH;AACA,SAASC,GAAaL,GAAaM,GAAYC,GAAa;AAC1D,SAAID,KAAYL,GAAkBD,EAAY,WAAWM,CAAU,GAC/DC,KAAaN,GAAkBD,GAAaO,CAAW,GAC3D,OAAO,eAAeP,GAAa,aAAa;IAC9C,UAAU;EACd,CAAG,GACMA;AACT;AACA,SAASP,GAAgBe,GAAKC,GAAKC,GAAO;AACxC,SAAAD,IAAMb,GAAea,CAAG,GACpBA,KAAOD,IACT,OAAO,eAAeA,GAAKC,GAAK;IAC9B,OAAOC;IACP,YAAY;IACZ,cAAc;IACd,UAAU;EAChB,CAAK,IAEDF,EAAIC,CAAG,IAAIC,GAENF;AACT;AACA,SAASG,GAAmBC,GAAK;AAC/B,SAAOC,GAAmBD,CAAG,KAAKE,GAAiBF,CAAG,KAAKG,GAA4BH,CAAG,KAAKI,GAAAA;AACjG;AACA,SAASH,GAAmBD,GAAK;AAC/B,MAAI,MAAM,QAAQA,CAAG;AAAG,WAAOK,GAAkBL,CAAG;AACtD;AACA,SAASE,GAAiBI,GAAM;AAC9B,MAAI,OAAO,SAAW,OAAeA,EAAK,OAAO,QAAQ,KAAK,QAAQA,EAAK,YAAY,KAAK;AAAM,WAAO,MAAM,KAAKA,CAAI;AAC1H;AACA,SAASH,GAA4BxB,GAAG4B,GAAQ;AAC9C,MAAK5B,GACL;AAAA,QAAI,OAAOA,KAAM;AAAU,aAAO0B,GAAkB1B,GAAG4B,CAAM;AAC7D,QAAIC,IAAI,OAAO,UAAU,SAAS,KAAK7B,CAAC,EAAE,MAAM,GAAG,EAAE;AAErD,QADI6B,MAAM,YAAY7B,EAAE,gBAAa6B,IAAI7B,EAAE,YAAY,OACnD6B,MAAM,SAASA,MAAM;AAAO,aAAO,MAAM,KAAK7B,CAAC;AACnD,QAAI6B,MAAM,eAAe,2CAA2C,KAAKA,CAAC;AAAG,aAAOH,GAAkB1B,GAAG4B,CAAM;EAAA;AACjH;AACA,SAASF,GAAkBL,GAAKS,GAAK;AACnC,GAAIA,KAAO,QAAQA,IAAMT,EAAI,YAAQS,IAAMT,EAAI;AAC/C,WAAS,IAAI,GAAGU,IAAO,IAAI,MAAMD,CAAG,GAAG,IAAIA,GAAK;AAAKC,MAAK,CAAC,IAAIV,EAAI,CAAC;AACpE,SAAOU;AACT;AACA,SAASN,KAAqB;AAC5B,QAAM,IAAI,UAAU;mFAAsI;AAC5J;AAEA,IAAIO,KAAa,OAAO,SAAW,OAAe,OAAO,OAAO,WAAa;AAA7E,IACIC,IAASD,KAAa,SAAS,CAAA;AADnC,IAEIE,KAAkBF,MAAcC,EAAO,SAAS,kBAAkB,kBAAkBA,EAAO,SAAS,kBAAkB;AAF1H,IAGIE,KAAoBH,KAAa,kBAAkBC,IAAS;AAHhE,IAIIG,IAAY;AAJhB,IAOIC,KAAa;AAPjB,IAQIC,KAAc;AARlB,IASIC,KAAc;AATlB,IAUIC,KAAc;AAVlB,IAWIC,IAAc;AAXlB,IAYIC,KAAc;AAZlB,IAaIC,KAAe;AAbnB,IAcIC,IAAe;AAdnB,IAeIC,KAAoB;AAfxB,IAgBIC,KAAoB;AAhBxB,IAiBIC,KAAoB;AAjBxB,IAkBIC,KAAoB;AAlBxB,IAqBIC,KAAa,GAAG,OAAOb,GAAW,OAAO;AArB7C,IAsBIc,KAAiB,GAAG,OAAOd,GAAW,WAAW;AAtBrD,IAuBIe,IAAe,GAAG,OAAOf,GAAW,SAAS;AAvBjD,IAwBIgB,KAAa,GAAG,OAAOhB,GAAW,OAAO;AAxB7C,IAyBIiB,KAAkB,GAAG,OAAOjB,GAAW,YAAY;AAzBvD,IA0BIkB,KAAc,GAAG,OAAOlB,GAAW,QAAQ;AA1B/C,IA2BImB,KAAa,GAAG,OAAOnB,GAAW,OAAO;AA3B7C,IA8BIoB,KAAc,GAAG,OAAOpB,GAAW,QAAQ;AA9B/C,IA+BIqB,KAAe,GAAG,OAAOrB,GAAW,SAAS;AA/BjD,IAkCIsB,KAAiB;AAlCrB,IAmCIC,KAAiB;AAnCrB,IAoCIC,KAAiB;AApCrB,IAuCIC,KAAa;AAvCjB,IAwCIC,KAAiB;AAxCrB,IAyCIC,KAAkB;AAzCtB,IA0CIC,KAAmB;AA1CvB,IA2CIC,KAAiB;AA3CrB,IA4CIC,KAAoBhC,KAAkB,eAAe;AA5CzD,IA6CIiC,KAAmBjC,KAAkB,cAAc;AA7CvD,IA8CIkC,KAAkBlC,KAAkB,yBAAyB;AA9CjE,IA+CImC,MAAqBlC,KAAoB,gBAAgB+B;AA/C7D,IAgDII,KAAqBnC,KAAoB,gBAAgBgC;AAhD7D,IAiDII,KAAmBpC,KAAoB,4BAA4BiC;AAjDvE,IAkDII,KAAc;AAlDlB,IAmDIC,KAAe;AAnDnB,IAoDIC,KAAc;AApDlB,IAqDIC,KAAa;AArDjB,IAwDIC,KAAiB;AAxDrB,IA2DIC,KAAiB;AA3DrB,IA4DIC,KAAkB;AA5DtB,IA6DIC,KAAuB;AA7D3B,IA8DIC,KAAkB;AA9DtB,IAkEIC,KAAsB;AAlE1B,IAmEIC,KAAuB;AAnE3B,IAqEIC,KAAW;;EAEb,UAAU;;;EAIV,UAAUzB;;;EAIV,oBAAoB;;EAEpB,aAAa;;EAEb,MAAM;;EAEN,SAAS;;EAET,YAAY;;EAEZ,SAAS;;EAET,kBAAkB;;EAElB,kBAAkB;;EAElB,OAAO;;EAEP,QAAQ;;EAER,QAAQ;;EAER,WAAW;;EAEX,YAAY;;EAEZ,UAAU;;EAEV,cAAc;;EAEd,SAAS;;EAET,WAAW;;EAEX,UAAU;;EAEV,UAAU;;EAEV,aAAa;;EAEb,aAAa;;EAEb,gBAAgB;;EAEhB,gBAAgB;;EAEhB,kBAAkB;;EAElB,0BAA0B;;EAE1B,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmBuB;EACnB,oBAAoBC;;EAEpB,OAAO;EACP,WAAW;EACX,UAAU;EACV,SAAS;EACT,MAAM;EACN,MAAM;AACR;AA9IA,IAgJIE,KAAW;AAhJf,IAqJIC,KAAQ,OAAO,SAASpD,EAAO;AAOnC,SAASqD,EAASnE,GAAO;AACvB,SAAO,OAAOA,KAAU,YAAY,CAACkE,GAAMlE,CAAK;AAClD;AAOA,IAAIoE,KAAmB,SAA0BpE,GAAO;AACtD,SAAOA,IAAQ,KAAKA,IAAQ,IAAA;AAC9B;AAOA,SAASqE,GAAYrE,GAAO;AAC1B,SAAO,OAAOA,IAAU;AAC1B;AAOA,SAASsE,GAAStE,GAAO;AACvB,SAAOb,GAAQa,CAAK,MAAM,YAAYA,MAAU;AAClD;AACA,IAAIuE,KAAiB,OAAO,UAAU;AAOtC,SAASC,IAAcxE,GAAO;AAC5B,MAAI,CAACsE,GAAStE,CAAK;AACjB,WAAO;AAET,MAAI;AACF,QAAIyE,IAAezE,EAAM,aACrB0E,IAAYD,EAAa;AAC7B,WAAOA,KAAgBC,KAAaH,GAAe,KAAKG,GAAW,eAAe;EACnF,QAAe;AACd,WAAO;EACR;AACH;AAOA,SAASC,EAAW3E,GAAO;AACzB,SAAO,OAAOA,KAAU;AAC1B;AACA,IAAI4E,KAAQ,MAAM,UAAU;AAO5B,SAASC,GAAQ7E,GAAO;AACtB,SAAO,MAAM,OAAO,MAAM,KAAKA,CAAK,IAAI4E,GAAM,KAAK5E,CAAK;AAC1D;AAQA,SAAS8E,EAAQC,GAAMC,GAAU;AAC/B,SAAID,KAAQJ,EAAWK,CAAQ,MACzB,MAAM,QAAQD,CAAI,KAAKZ,EAASY,EAAK,MAAM,IAC7CF,GAAQE,CAAI,EAAE,QAAQ,SAAU/E,GAAOD,GAAK;AAC1CiF,MAAS,KAAKD,GAAM/E,GAAOD,GAAKgF,CAAI;EAC5C,CAAO,IACQT,GAASS,CAAI,KACtB,OAAO,KAAKA,CAAI,EAAE,QAAQ,SAAUhF,GAAK;AACvCiF,MAAS,KAAKD,GAAMA,EAAKhF,CAAG,GAAGA,GAAKgF,CAAI;EAChD,CAAO,IAGEA;AACT;AAQA,IAAIE,IAAS,OAAO,UAAU,SAAgBzF,GAAQ;AACpD,WAAS0F,IAAO,UAAU,QAAQC,IAAO,IAAI,MAAMD,IAAO,IAAIA,IAAO,IAAI,CAAC,GAAGE,IAAO,GAAGA,IAAOF,GAAME;AAClGD,MAAKC,IAAO,CAAC,IAAI,UAAUA,CAAI;AAEjC,SAAId,GAAS9E,CAAM,KAAK2F,EAAK,SAAS,KACpCA,EAAK,QAAQ,SAAUE,GAAK;AACtBf,OAASe,CAAG,KACd,OAAO,KAAKA,CAAG,EAAE,QAAQ,SAAUtF,GAAK;AACtCP,QAAOO,CAAG,IAAIsF,EAAItF,CAAG;IAC/B,CAAS;EAET,CAAK,GAEIP;AACT;AAdA,IAeI8F,KAAkB;AAStB,SAASC,GAAuBvF,GAAO;AACrC,MAAIwF,IAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,SAAOF,GAAgB,KAAKtF,CAAK,IAAI,KAAK,MAAMA,IAAQwF,CAAK,IAAIA,IAAQxF;AAC3E;AACA,IAAIyF,KAAgB;AAOpB,SAASC,EAASC,GAASC,GAAQ;AACjC,MAAIC,IAAQF,EAAQ;AACpBb,IAAQc,GAAQ,SAAU5F,GAAO8F,GAAU;AACrCL,OAAc,KAAKK,CAAQ,KAAK3B,EAASnE,CAAK,MAChDA,IAAQ,GAAG,OAAOA,GAAO,IAAI,IAE/B6F,EAAMC,CAAQ,IAAI9F;EACtB,CAAG;AACH;AAQA,SAAS+F,GAASJ,GAAS3F,GAAO;AAChC,SAAO2F,EAAQ,YAAYA,EAAQ,UAAU,SAAS3F,CAAK,IAAI2F,EAAQ,UAAU,QAAQ3F,CAAK,IAAI;AACpG;AAOA,SAASgG,EAASL,GAAS3F,GAAO;AAChC,MAAKA,GAGL;AAAA,QAAImE,EAASwB,EAAQ,MAAM,GAAG;AAC5Bb,QAAQa,GAAS,SAAUM,GAAM;AAC/BD,UAASC,GAAMjG,CAAK;MAC1B,CAAK;AACD;IACD;AACD,QAAI2F,EAAQ,WAAW;AACrBA,QAAQ,UAAU,IAAI3F,CAAK;AAC3B;IACD;AACD,QAAIkG,IAAYP,EAAQ,UAAU,KAAI;AACjCO,QAEMA,EAAU,QAAQlG,CAAK,IAAI,MACpC2F,EAAQ,YAAY,GAAG,OAAOO,GAAW,GAAG,EAAE,OAAOlG,CAAK,KAF1D2F,EAAQ,YAAY3F;EAAA;AAIxB;AAOA,SAASmG,EAAYR,GAAS3F,GAAO;AACnC,MAAKA,GAGL;AAAA,QAAImE,EAASwB,EAAQ,MAAM,GAAG;AAC5Bb,QAAQa,GAAS,SAAUM,GAAM;AAC/BE,UAAYF,GAAMjG,CAAK;MAC7B,CAAK;AACD;IACD;AACD,QAAI2F,EAAQ,WAAW;AACrBA,QAAQ,UAAU,OAAO3F,CAAK;AAC9B;IACD;AACG2F,MAAQ,UAAU,QAAQ3F,CAAK,KAAK,MACtC2F,EAAQ,YAAYA,EAAQ,UAAU,QAAQ3F,GAAO,EAAE;EAAA;AAE3D;AAQA,SAASoG,GAAYT,GAAS3F,GAAOqG,GAAO;AAC1C,MAAKrG,GAGL;AAAA,QAAImE,EAASwB,EAAQ,MAAM,GAAG;AAC5Bb,QAAQa,GAAS,SAAUM,GAAM;AAC/BG,WAAYH,GAAMjG,GAAOqG,CAAK;MACpC,CAAK;AACD;IACD;AAGGA,QACFL,EAASL,GAAS3F,CAAK,IAEvBmG,EAAYR,GAAS3F,CAAK;EAAA;AAE9B;AACA,IAAIsG,KAAoB;AAOxB,SAASC,GAAYvG,GAAO;AAC1B,SAAOA,EAAM,QAAQsG,IAAmB,OAAO,EAAE,YAAW;AAC9D;AAQA,SAASE,GAAQb,GAASc,GAAM;AAC9B,SAAInC,GAASqB,EAAQc,CAAI,CAAC,IACjBd,EAAQc,CAAI,IAEjBd,EAAQ,UACHA,EAAQ,QAAQc,CAAI,IAEtBd,EAAQ,aAAa,QAAQ,OAAOY,GAAYE,CAAI,CAAC,CAAC;AAC/D;AAQA,SAASC,GAAQf,GAASc,GAAM1B,GAAM;AAChCT,KAASS,CAAI,IACfY,EAAQc,CAAI,IAAI1B,IACPY,EAAQ,UACjBA,EAAQ,QAAQc,CAAI,IAAI1B,IAExBY,EAAQ,aAAa,QAAQ,OAAOY,GAAYE,CAAI,CAAC,GAAG1B,CAAI;AAEhE;AAOA,SAAS4B,GAAWhB,GAASc,GAAM;AACjC,MAAInC,GAASqB,EAAQc,CAAI,CAAC;AACxB,QAAI;AACF,aAAOd,EAAQc,CAAI;IACpB,QAAe;AACdd,QAAQc,CAAI,IAAI;IACjB;WACQd,EAAQ;AAEjB,QAAI;AACF,aAAOA,EAAQ,QAAQc,CAAI;IAC5B,QAAe;AACdd,QAAQ,QAAQc,CAAI,IAAI;IACzB;;AAEDd,MAAQ,gBAAgB,QAAQ,OAAOY,GAAYE,CAAI,CAAC,CAAC;AAE7D;AACA,IAAIG,KAAgB;AAApB,IACIC,KAAgB,WAAY;AAC9B,MAAIC,IAAY;AAChB,MAAIjG,IAAY;AACd,QAAIkG,IAAO,OACPC,IAAW,WAAoB;IAAA,GAC/BC,IAAU,OAAO,eAAe,CAAA,GAAI,QAAQ;MAC9C,KAAK,WAAe;AAClB,eAAAH,IAAY,MACLC;MACR;;;;;;MAMD,KAAK,SAAa/G,GAAO;AACvB+G,YAAO/G;MACR;IACP,CAAK;AACDc,MAAO,iBAAiB,QAAQkG,GAAUC,CAAO,GACjDnG,EAAO,oBAAoB,QAAQkG,GAAUC,CAAO;EACrD;AACD,SAAOH;AACT,EAAA;AASA,SAASI,EAAevB,GAASwB,GAAMH,GAAU;AAC/C,MAAIC,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA,GAC9EG,IAAUJ;AACdG,IAAK,KAAI,EAAG,MAAMP,EAAa,EAAE,QAAQ,SAAUS,GAAO;AACxD,QAAI,CAACR,IAAe;AAClB,UAAIS,IAAY3B,EAAQ;AACpB2B,WAAaA,EAAUD,CAAK,KAAKC,EAAUD,CAAK,EAAEL,CAAQ,MAC5DI,IAAUE,EAAUD,CAAK,EAAEL,CAAQ,GACnC,OAAOM,EAAUD,CAAK,EAAEL,CAAQ,GAC5B,OAAO,KAAKM,EAAUD,CAAK,CAAC,EAAE,WAAW,KAC3C,OAAOC,EAAUD,CAAK,GAEpB,OAAO,KAAKC,CAAS,EAAE,WAAW,KACpC,OAAO3B,EAAQ;IAGpB;AACDA,MAAQ,oBAAoB0B,GAAOD,GAASH,CAAO;EACvD,CAAG;AACH;AASA,SAASM,EAAY5B,GAASwB,GAAMH,GAAU;AAC5C,MAAIC,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA,GAC9EO,IAAWR;AACfG,IAAK,KAAI,EAAG,MAAMP,EAAa,EAAE,QAAQ,SAAUS,GAAO;AACxD,QAAIJ,EAAQ,QAAQ,CAACJ,IAAe;AAClC,UAAIY,IAAqB9B,EAAQ,WAC/B2B,IAAYG,MAAuB,SAAS,CAAA,IAAKA;AACnDD,UAAW,WAAmB;AAC5B,eAAOF,EAAUD,CAAK,EAAEL,CAAQ,GAChCrB,EAAQ,oBAAoB0B,GAAOG,GAAUP,CAAO;AACpD,iBAASS,IAAQ,UAAU,QAAQvC,KAAO,IAAI,MAAMuC,CAAK,GAAGC,IAAQ,GAAGA,IAAQD,GAAOC;AACpFxC,UAAAA,GAAKwC,CAAK,IAAI,UAAUA,CAAK;AAE/BX,UAAS,MAAMrB,GAASR,EAAI;MACpC,GACWmC,EAAUD,CAAK,MAClBC,EAAUD,CAAK,IAAI,CAAA,IAEjBC,EAAUD,CAAK,EAAEL,CAAQ,KAC3BrB,EAAQ,oBAAoB0B,GAAOC,EAAUD,CAAK,EAAEL,CAAQ,GAAGC,CAAO,GAExEK,EAAUD,CAAK,EAAEL,CAAQ,IAAIQ,GAC7B7B,EAAQ,YAAY2B;IACrB;AACD3B,MAAQ,iBAAiB0B,GAAOG,GAAUP,CAAO;EACrD,CAAG;AACH;AASA,SAASW,GAAcjC,GAASwB,GAAMpC,GAAM;AAC1C,MAAIsC;AAGJ,SAAI1C,EAAW,KAAK,KAAKA,EAAW,WAAW,IAC7C0C,IAAQ,IAAI,YAAYF,GAAM;IAC5B,QAAQpC;IACR,SAAS;IACT,YAAY;EAClB,CAAK,KAEDsC,IAAQ,SAAS,YAAY,aAAa,GAC1CA,EAAM,gBAAgBF,GAAM,MAAM,MAAMpC,CAAI,IAEvCY,EAAQ,cAAc0B,CAAK;AACpC;AAOA,SAASQ,GAAUlC,GAAS;AAC1B,MAAImC,IAAMnC,EAAQ,sBAAA;AAClB,SAAO;IACL,MAAMmC,EAAI,QAAQ,OAAO,cAAc,SAAS,gBAAgB;IAChE,KAAKA,EAAI,OAAO,OAAO,cAAc,SAAS,gBAAgB;EAClE;AACA;AACA,IAAIC,KAAWjH,EAAO;AAAtB,IACIkH,KAAiB;AAOrB,SAASC,GAAiBC,GAAK;AAC7B,MAAIC,IAAQD,EAAI,MAAMF,EAAc;AACpC,SAAOG,MAAU,SAASA,EAAM,CAAC,MAAMJ,GAAS,YAAYI,EAAM,CAAC,MAAMJ,GAAS,YAAYI,EAAM,CAAC,MAAMJ,GAAS;AACtH;AAOA,SAASK,GAAaF,GAAK;AACzB,MAAIG,IAAY,aAAa,QAAO,oBAAI,KAAM,GAAC,QAAO,CAAE;AACxD,SAAOH,KAAOA,EAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAOG;AACvD;AAOA,SAASC,GAAcC,GAAM;AAC3B,MAAIC,IAASD,EAAK,QAChBE,IAASF,EAAK,QACdG,IAASH,EAAK,QACdI,IAAaJ,EAAK,YAClBK,IAAaL,EAAK,YAChBM,IAAS,CAAA;AACT1E,IAASwE,CAAU,KAAKA,MAAe,KACzCE,EAAO,KAAK,cAAc,OAAOF,GAAY,KAAK,CAAC,GAEjDxE,EAASyE,CAAU,KAAKA,MAAe,KACzCC,EAAO,KAAK,cAAc,OAAOD,GAAY,KAAK,CAAC,GAIjDzE,EAASqE,CAAM,KAAKA,MAAW,KACjCK,EAAO,KAAK,UAAU,OAAOL,GAAQ,MAAM,CAAC,GAE1CrE,EAASsE,CAAM,KAAKA,MAAW,KACjCI,EAAO,KAAK,UAAU,OAAOJ,GAAQ,GAAG,CAAC,GAEvCtE,EAASuE,CAAM,KAAKA,MAAW,KACjCG,EAAO,KAAK,UAAU,OAAOH,GAAQ,GAAG,CAAC;AAE3C,MAAII,IAAYD,EAAO,SAASA,EAAO,KAAK,GAAG,IAAI;AACnD,SAAO;IACL,iBAAiBC;IACjB,aAAaA;IACb,WAAWA;EACf;AACA;AAOA,SAASC,GAAgBC,GAAU;AACjC,MAAIC,IAAYnK,GAAe,CAAE,GAAEkK,CAAQ,GACvCE,IAAW;AACf,SAAApE,EAAQkE,GAAU,SAAUG,GAASC,GAAW;AAC9C,WAAOH,EAAUG,CAAS,GAC1BtE,EAAQmE,GAAW,SAAUI,GAAU;AACrC,UAAIC,IAAK,KAAK,IAAIH,EAAQ,SAASE,EAAS,MAAM,GAC9CE,IAAK,KAAK,IAAIJ,EAAQ,SAASE,EAAS,MAAM,GAC9CG,IAAK,KAAK,IAAIL,EAAQ,OAAOE,EAAS,IAAI,GAC1CI,IAAK,KAAK,IAAIN,EAAQ,OAAOE,EAAS,IAAI,GAC1CK,KAAK,KAAK,KAAKJ,IAAKA,IAAKC,IAAKA,CAAE,GAChCI,IAAK,KAAK,KAAKH,IAAKA,IAAKC,IAAKA,CAAE,GAChCG,KAASD,IAAKD,MAAMA;AACpB,WAAK,IAAIE,CAAK,IAAI,KAAK,IAAIV,CAAQ,MACrCA,IAAWU;IAEnB,CAAK;EACL,CAAG,GACMV;AACT;AAQA,SAASW,GAAWC,GAAOC,GAAS;AAClC,MAAIC,IAAQF,EAAM,OAChBG,IAAQH,EAAM,OACZI,IAAM;IACR,MAAMF;IACN,MAAMC;EACV;AACE,SAAOF,IAAUG,IAAMpL,GAAe;IACpC,QAAQkL;IACR,QAAQC;EACT,GAAEC,CAAG;AACR;AAOA,SAASC,GAAkBnB,GAAU;AACnC,MAAIgB,IAAQ,GACRC,IAAQ,GACRG,IAAQ;AACZ,SAAAtF,EAAQkE,GAAU,SAAUqB,GAAO;AACjC,QAAIC,IAASD,EAAM,QACjBE,IAASF,EAAM;AACjBL,SAASM,GACTL,KAASM,GACTH,KAAS;EACb,CAAG,GACDJ,KAASI,GACTH,KAASG,GACF;IACL,OAAOJ;IACP,OAAOC;EACX;AACA;AAQA,SAASO,EAAiBC,GAAO;AAC/B,MAAIC,IAAcD,EAAM,aACtBE,IAASF,EAAM,QACfG,IAAQH,EAAM,OACZtD,IAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAC3E0D,IAAezG,GAAiBwG,CAAK,GACrCE,IAAgB1G,GAAiBuG,CAAM;AAC3C,MAAIE,KAAgBC,GAAe;AACjC,QAAIC,IAAgBJ,IAASD;AACzBvD,UAAS,aAAa4D,IAAgBH,KAASzD,MAAS,WAAW4D,IAAgBH,IACrFD,IAASC,IAAQF,IAEjBE,IAAQD,IAASD;EAEpB;AAAUG,QACTF,IAASC,IAAQF,IACRI,MACTF,IAAQD,IAASD;AAEnB,SAAO;IACL,OAAOE;IACP,QAAQD;EACZ;AACA;AAOA,SAASK,GAAgBC,GAAO;AAC9B,MAAIL,IAAQK,EAAM,OAChBN,IAASM,EAAM,QACfC,IAASD,EAAM;AAEjB,MADAC,IAAS,KAAK,IAAIA,CAAM,IAAI,KACxBA,MAAW;AACb,WAAO;MACL,OAAOP;MACP,QAAQC;IACd;AAEE,MAAIO,IAAMD,IAAS,KAAK,KAAK,KAAK,KAC9BE,IAAS,KAAK,IAAID,CAAG,GACrBE,IAAS,KAAK,IAAIF,CAAG,GACrBG,IAAWV,IAAQS,IAASV,IAASS,GACrCG,IAAYX,IAAQQ,IAAST,IAASU;AAC1C,SAAOH,IAAS,KAAK;IACnB,OAAOK;IACP,QAAQD;EACZ,IAAM;IACF,OAAOA;IACP,QAAQC;EACZ;AACA;AAUA,SAASC,GAAgBC,GAAOC,GAAOC,GAAOC,GAAO;AACnD,MAAIC,IAAmBH,EAAM,aAC3BI,IAAoBJ,EAAM,cAC1BK,IAAqBL,EAAM,eAC3BM,IAAeN,EAAM,QACrBlD,IAASwD,MAAiB,SAAS,IAAIA,GACvCC,IAAeP,EAAM,QACrBjD,KAASwD,MAAiB,SAAS,IAAIA,GACvCC,IAAeR,EAAM,QACrBhD,IAASwD,MAAiB,SAAS,IAAIA,GACrCxB,IAAciB,EAAM,aACtBQ,IAAeR,EAAM,cACrBS,IAAgBT,EAAM,eACpBU,IAAkBT,EAAM,WAC1BU,IAAYD,MAAoB,SAAS,gBAAgBA,GACzDE,IAAwBX,EAAM,uBAC9BY,IAAwBD,MAA0B,SAAS,OAAOA,GAClEE,IAAwBb,EAAM,uBAC9Bc,KAAwBD,MAA0B,SAAS,QAAQA,GACnEE,IAAiBf,EAAM,UACvBgB,IAAWD,MAAmB,SAAS,IAAA,IAAWA,GAClDE,IAAkBjB,EAAM,WACxBkB,IAAYD,MAAoB,SAAS,IAAA,IAAWA,GACpDE,IAAiBnB,EAAM,UACvBoB,IAAWD,MAAmB,SAAS,IAAIA,GAC3CE,IAAkBrB,EAAM,WACxBsB,IAAYD,MAAoB,SAAS,IAAIA,GAC3CE,IAAS,SAAS,cAAc,QAAQ,GACxCC,IAAUD,EAAO,WAAW,IAAI,GAChCE,IAAW7C,EAAiB;IAC9B,aAAaE;IACb,OAAOkC;IACP,QAAQE;EACZ,CAAG,GACGQ,IAAW9C,EAAiB;IAC9B,aAAaE;IACb,OAAOsC;IACP,QAAQE;EACT,GAAE,OAAO,GACNtC,KAAQ,KAAK,IAAIyC,EAAS,OAAO,KAAK,IAAIC,EAAS,OAAOnB,CAAY,CAAC,GACvExB,KAAS,KAAK,IAAI0C,EAAS,QAAQ,KAAK,IAAIC,EAAS,QAAQlB,CAAa,CAAC,GAI3EmB,KAAe/C,EAAiB;IAClC,aAAaqB;IACb,OAAOe;IACP,QAAQE;EACZ,CAAG,GACGU,KAAehD,EAAiB;IAClC,aAAaqB;IACb,OAAOmB;IACP,QAAQE;EACT,GAAE,OAAO,GACNO,KAAY,KAAK,IAAIF,GAAa,OAAO,KAAK,IAAIC,GAAa,OAAO1B,CAAiB,CAAC,GACxF4B,KAAa,KAAK,IAAIH,GAAa,QAAQ,KAAK,IAAIC,GAAa,QAAQzB,CAAkB,CAAC,GAC5F4B,KAAS,CAAC,CAACF,KAAY,GAAG,CAACC,KAAa,GAAGD,IAAWC,EAAU;AACpE,SAAAP,EAAO,QAAQ5H,GAAuBqF,EAAK,GAC3CuC,EAAO,SAAS5H,GAAuBoF,EAAM,GAC7CyC,EAAQ,YAAYd,GACpBc,EAAQ,SAAS,GAAG,GAAGxC,IAAOD,EAAM,GACpCyC,EAAQ,KAAI,GACZA,EAAQ,UAAUxC,KAAQ,GAAGD,KAAS,CAAC,GACvCyC,EAAQ,OAAO5E,IAAS,KAAK,KAAK,GAAG,GACrC4E,EAAQ,MAAM3E,IAAQC,CAAM,GAC5B0E,EAAQ,wBAAwBZ,GAChCY,EAAQ,wBAAwBV,IAChCU,EAAQ,UAAU,MAAMA,GAAS,CAAC3B,CAAK,EAAE,OAAOxL,GAAmB0N,GAAO,IAAI,SAAUC,IAAO;AAC7F,WAAO,KAAK,MAAMrI,GAAuBqI,EAAK,CAAC;EACnD,CAAG,CAAC,CAAC,CAAC,GACJR,EAAQ,QAAO,GACRD;AACT;AACA,IAAIU,KAAe,OAAO;AAS1B,SAASC,GAAsBC,GAAUC,GAAOC,GAAQ;AACtD,MAAIC,IAAM;AACVD,OAAUD;AACV,WAAS/O,IAAI+O,GAAO/O,IAAIgP,GAAQhP,KAAK;AACnCiP,SAAOL,GAAaE,EAAS,SAAS9O,CAAC,CAAC;AAE1C,SAAOiP;AACT;AACA,IAAIC,KAAuB;AAO3B,SAASC,GAAqBC,GAAS;AACrC,MAAIC,IAASD,EAAQ,QAAQF,IAAsB,EAAE,GACjDI,IAAS,KAAKD,CAAM,GACpBE,IAAc,IAAI,YAAYD,EAAO,MAAM,GAC3CE,IAAQ,IAAI,WAAWD,CAAW;AACtC,SAAA1J,EAAQ2J,GAAO,SAAUzO,GAAOf,GAAG;AACjCwP,MAAMxP,CAAC,IAAIsP,EAAO,WAAWtP,CAAC;EAClC,CAAG,GACMuP;AACT;AAQA,SAASE,GAAqBF,GAAaG,GAAU;AAMnD,WALIC,IAAS,CAAA,GAGTC,IAAY,MACZJ,IAAQ,IAAI,WAAWD,CAAW,GAC/BC,EAAM,SAAS;AAGpBG,MAAO,KAAKf,GAAa,MAAM,MAAMhJ,GAAQ4J,EAAM,SAAS,GAAGI,CAAS,CAAC,CAAC,CAAC,GAC3EJ,IAAQA,EAAM,SAASI,CAAS;AAElC,SAAO,QAAQ,OAAOF,GAAU,UAAU,EAAE,OAAO,KAAKC,EAAO,KAAK,EAAE,CAAC,CAAC;AAC1E;AAOA,SAASE,GAAuBN,GAAa;AAC3C,MAAIT,IAAW,IAAI,SAASS,CAAW,GACnCO;AAGJ,MAAI;AACF,QAAIC,GACAC,GACAC;AAGJ,QAAInB,EAAS,SAAS,CAAC,MAAM,OAAQA,EAAS,SAAS,CAAC,MAAM;AAG5D,eAFIE,IAASF,EAAS,YAClBoB,IAAS,GACNA,IAAS,IAAIlB,KAAQ;AAC1B,YAAIF,EAAS,SAASoB,CAAM,MAAM,OAAQpB,EAAS,SAASoB,IAAS,CAAC,MAAM,KAAM;AAChFF,cAAYE;AACZ;QACD;AACDA,aAAU;MACX;AAEH,QAAIF,GAAW;AACb,UAAIG,IAAaH,IAAY,GACzBI,IAAaJ,IAAY;AAC7B,UAAInB,GAAsBC,GAAUqB,GAAY,CAAC,MAAM,QAAQ;AAC7D,YAAIE,KAAavB,EAAS,UAAUsB,CAAU;AAE9C,YADAL,IAAeM,OAAe,QAC1BN,KAAgBM,OAAe,UAC7BvB,EAAS,UAAUsB,IAAa,GAAGL,CAAY,MAAM,IAAQ;AAC/D,cAAIO,IAAiBxB,EAAS,UAAUsB,IAAa,GAAGL,CAAY;AAChEO,eAAkB,MACpBL,IAAWG,IAAaE;QAE3B;MAEJ;IACF;AACD,QAAIL,GAAU;AACZ,UAAIM,IAAUzB,EAAS,UAAUmB,GAAUF,CAAY,GACnDS,GACAxQ;AACJ,WAAKA,IAAI,GAAGA,IAAIuQ,GAASvQ,KAAK;AAE5B,YADAwQ,IAAUP,IAAWjQ,IAAI,KAAK,GAC1B8O,EAAS,UAAU0B,GAAST,CAAY,MAAM,KAA0B;AAE1ES,eAAW,GAGXV,IAAchB,EAAS,UAAU0B,GAAST,CAAY,GAGtDjB,EAAS,UAAU0B,GAAS,GAAGT,CAAY;AAC3C;QACD;IAEJ;EACF,QAAe;AACdD,QAAc;EACf;AACD,SAAOA;AACT;AAOA,SAASW,GAAiBX,GAAa;AACrC,MAAIvG,IAAS,GACTC,IAAS,GACTC,IAAS;AACb,UAAQqG,GAAW;IAEjB,KAAK;AACHtG,UAAS;AACT;IAGF,KAAK;AACHD,UAAS;AACT;IAGF,KAAK;AACHE,UAAS;AACT;IAGF,KAAK;AACHF,UAAS,IACTE,IAAS;AACT;IAGF,KAAK;AACHF,UAAS;AACT;IAGF,KAAK;AACHA,UAAS,IACTC,IAAS;AACT;IAGF,KAAK;AACHD,UAAS;AACT;EACH;AACD,SAAO;IACL,QAAQA;IACR,QAAQC;IACR,QAAQC;EACZ;AACA;AAEA,IAAIiH,KAAS;EACX,QAAQ,WAAkB;AACxB,SAAK,cAAa,GAClB,KAAK,WAAU,GACf,KAAK,YAAW,GAChB,KAAK,aAAY,GACb,KAAK,WACP,KAAK,cAAa;EAErB;EACD,eAAe,WAAyB;AACtC,QAAIhK,IAAU,KAAK,SACjBsB,IAAU,KAAK,SACf2I,IAAY,KAAK,WACjBC,IAAU,KAAK,SACb7C,IAAW,OAAO/F,EAAQ,iBAAiB,GAC3CiG,IAAY,OAAOjG,EAAQ,kBAAkB;AACjDjB,MAAS6J,GAAS7N,CAAY,GAC9BmE,EAAYR,GAAS3D,CAAY;AACjC,QAAI8N,IAAgB;MAClB,OAAO,KAAK,IAAIF,EAAU,aAAa5C,KAAY,IAAIA,IAAWlJ,EAAmB;MACrF,QAAQ,KAAK,IAAI8L,EAAU,cAAc1C,KAAa,IAAIA,IAAYnJ,EAAoB;IAChG;AACI,SAAK,gBAAgB+L,GACrBpK,EAASmK,GAAS;MAChB,OAAOC,EAAc;MACrB,QAAQA,EAAc;IAC5B,CAAK,GACD9J,EAASL,GAAS3D,CAAY,GAC9BmE,EAAY0J,GAAS7N,CAAY;EAClC;;EAED,YAAY,WAAsB;AAChC,QAAI8N,IAAgB,KAAK,eACvBC,IAAY,KAAK,WACfC,IAAW,KAAK,QAAQ,UACxBC,IAAU,KAAK,IAAIF,EAAU,MAAM,IAAI,QAAQ,IAC/C5D,IAAe8D,IAAUF,EAAU,gBAAgBA,EAAU,cAC7D3D,IAAgB6D,IAAUF,EAAU,eAAeA,EAAU,eAC7DrF,IAAcyB,IAAeC,GAC7B8D,IAAcJ,EAAc,OAC5BK,IAAeL,EAAc;AAC7BA,MAAc,SAASpF,IAAcoF,EAAc,QACjDE,MAAa,IACfE,IAAcJ,EAAc,SAASpF,IAErCyF,IAAeL,EAAc,QAAQpF,IAE9BsF,MAAa,IACtBG,IAAeL,EAAc,QAAQpF,IAErCwF,IAAcJ,EAAc,SAASpF;AAEvC,QAAI0F,KAAa;MACf,aAAa1F;MACb,cAAcyB;MACd,eAAeC;MACf,OAAO8D;MACP,QAAQC;IACd;AACI,SAAK,aAAaC,IAClB,KAAK,UAAUJ,MAAa,KAAKA,MAAa,GAC9C,KAAK,YAAY,MAAM,IAAI,GAC3BI,GAAW,QAAQ,KAAK,IAAI,KAAK,IAAIA,GAAW,OAAOA,GAAW,QAAQ,GAAGA,GAAW,QAAQ,GAChGA,GAAW,SAAS,KAAK,IAAI,KAAK,IAAIA,GAAW,QAAQA,GAAW,SAAS,GAAGA,GAAW,SAAS,GACpGA,GAAW,QAAQN,EAAc,QAAQM,GAAW,SAAS,GAC7DA,GAAW,OAAON,EAAc,SAASM,GAAW,UAAU,GAC9DA,GAAW,UAAUA,GAAW,MAChCA,GAAW,SAASA,GAAW,KAC/B,KAAK,oBAAoBnL,EAAO,CAAE,GAAEmL,EAAU;EAC/C;EACD,aAAa,SAAqBC,GAAaC,GAAiB;AAC9D,QAAIrJ,IAAU,KAAK,SACjB6I,IAAgB,KAAK,eACrBM,IAAa,KAAK,YAClBG,IAAc,KAAK,aACjBP,IAAW/I,EAAQ,UACnByD,IAAc0F,EAAW,aACzBI,IAAU,KAAK,WAAWD;AAC9B,QAAIF,GAAa;AACf,UAAII,KAAiB,OAAOxJ,EAAQ,cAAc,KAAK,GACnDyJ,IAAkB,OAAOzJ,EAAQ,eAAe,KAAK;AACrD+I,UAAW,KACbS,KAAiB,KAAK,IAAIA,IAAgBX,EAAc,KAAK,GAC7DY,IAAkB,KAAK,IAAIA,GAAiBZ,EAAc,MAAM,GAC5DE,MAAa,MACXU,IAAkBhG,IAAc+F,KAClCA,KAAiBC,IAAkBhG,IAEnCgG,IAAkBD,KAAiB/F,MAG9BsF,IAAW,MAChBS,KACFA,KAAiB,KAAK,IAAIA,IAAgBD,IAAUD,EAAY,QAAQ,CAAC,IAChEG,IACTA,IAAkB,KAAK,IAAIA,GAAiBF,IAAUD,EAAY,SAAS,CAAC,IACnEC,MACTC,KAAiBF,EAAY,OAC7BG,IAAkBH,EAAY,QAC1BG,IAAkBhG,IAAc+F,KAClCA,KAAiBC,IAAkBhG,IAEnCgG,IAAkBD,KAAiB/F;AAIzC,UAAIiG,IAAoBnG,EAAiB;QACvC,aAAaE;QACb,OAAO+F;QACP,QAAQC;MAChB,CAAO;AACDD,MAAAA,KAAiBE,EAAkB,OACnCD,IAAkBC,EAAkB,QACpCP,EAAW,WAAWK,IACtBL,EAAW,YAAYM,GACvBN,EAAW,WAAW,IAAA,GACtBA,EAAW,YAAY,IAAA;IACxB;AACD,QAAIE;AACF,UAAIN,KAAYQ,IAAU,IAAI,IAAI;AAChC,YAAII,IAAgBd,EAAc,QAAQM,EAAW,OACjDS,IAAef,EAAc,SAASM,EAAW;AACrDA,UAAW,UAAU,KAAK,IAAI,GAAGQ,CAAa,GAC9CR,EAAW,SAAS,KAAK,IAAI,GAAGS,CAAY,GAC5CT,EAAW,UAAU,KAAK,IAAI,GAAGQ,CAAa,GAC9CR,EAAW,SAAS,KAAK,IAAI,GAAGS,CAAY,GACxCL,KAAW,KAAK,YAClBJ,EAAW,UAAU,KAAK,IAAIG,EAAY,MAAMA,EAAY,QAAQA,EAAY,QAAQH,EAAW,MAAM,GACzGA,EAAW,SAAS,KAAK,IAAIG,EAAY,KAAKA,EAAY,OAAOA,EAAY,SAASH,EAAW,OAAO,GACxGA,EAAW,UAAUG,EAAY,MACjCH,EAAW,SAASG,EAAY,KAC5BP,MAAa,MACXI,EAAW,SAASN,EAAc,UACpCM,EAAW,UAAU,KAAK,IAAI,GAAGQ,CAAa,GAC9CR,EAAW,UAAU,KAAK,IAAI,GAAGQ,CAAa,IAE5CR,EAAW,UAAUN,EAAc,WACrCM,EAAW,SAAS,KAAK,IAAI,GAAGS,CAAY,GAC5CT,EAAW,SAAS,KAAK,IAAI,GAAGS,CAAY;MAI1D;AACQT,UAAW,UAAU,CAACA,EAAW,OACjCA,EAAW,SAAS,CAACA,EAAW,QAChCA,EAAW,UAAUN,EAAc,OACnCM,EAAW,SAASN,EAAc;EAGvC;EACD,cAAc,SAAsBgB,GAASC,GAAa;AACxD,QAAIX,IAAa,KAAK,YACpBL,IAAY,KAAK;AACnB,QAAIgB,GAAa;AACf,UAAIC,IAAmBhG,GAAgB;QACnC,OAAO+E,EAAU,eAAe,KAAK,IAAIA,EAAU,UAAU,CAAC;QAC9D,QAAQA,EAAU,gBAAgB,KAAK,IAAIA,EAAU,UAAU,CAAC;QAChE,QAAQA,EAAU,UAAU;MACtC,CAAS,GACD5D,IAAe6E,EAAiB,OAChC5E,IAAgB4E,EAAiB,QAC/BpG,IAAQwF,EAAW,SAASjE,IAAeiE,EAAW,eACtDzF,IAASyF,EAAW,UAAUhE,IAAgBgE,EAAW;AAC7DA,QAAW,SAASxF,IAAQwF,EAAW,SAAS,GAChDA,EAAW,QAAQzF,IAASyF,EAAW,UAAU,GACjDA,EAAW,QAAQxF,GACnBwF,EAAW,SAASzF,GACpByF,EAAW,cAAcjE,IAAeC,GACxCgE,EAAW,eAAejE,GAC1BiE,EAAW,gBAAgBhE,GAC3B,KAAK,YAAY,MAAM,KAAK;IAC7B;AACD,KAAIgE,EAAW,QAAQA,EAAW,YAAYA,EAAW,QAAQA,EAAW,cAC1EA,EAAW,OAAOA,EAAW,WAE3BA,EAAW,SAASA,EAAW,aAAaA,EAAW,SAASA,EAAW,eAC7EA,EAAW,MAAMA,EAAW,SAE9BA,EAAW,QAAQ,KAAK,IAAI,KAAK,IAAIA,EAAW,OAAOA,EAAW,QAAQ,GAAGA,EAAW,QAAQ,GAChGA,EAAW,SAAS,KAAK,IAAI,KAAK,IAAIA,EAAW,QAAQA,EAAW,SAAS,GAAGA,EAAW,SAAS,GACpG,KAAK,YAAY,OAAO,IAAI,GAC5BA,EAAW,OAAO,KAAK,IAAI,KAAK,IAAIA,EAAW,MAAMA,EAAW,OAAO,GAAGA,EAAW,OAAO,GAC5FA,EAAW,MAAM,KAAK,IAAI,KAAK,IAAIA,EAAW,KAAKA,EAAW,MAAM,GAAGA,EAAW,MAAM,GACxFA,EAAW,UAAUA,EAAW,MAChCA,EAAW,SAASA,EAAW,KAC/B1K,EAAS,KAAK,QAAQT,EAAO;MAC3B,OAAOmL,EAAW;MAClB,QAAQA,EAAW;IACpB,GAAE9H,GAAc;MACf,YAAY8H,EAAW;MACvB,YAAYA,EAAW;IACxB,CAAA,CAAC,CAAC,GACH,KAAK,YAAYU,CAAO,GACpB,KAAK,WAAW,KAAK,WACvB,KAAK,aAAa,MAAM,IAAI;EAE/B;EACD,aAAa,SAAqBA,GAAS;AACzC,QAAIV,IAAa,KAAK,YACpBL,IAAY,KAAK,WACfnF,IAAQmF,EAAU,gBAAgBK,EAAW,QAAQA,EAAW,eAChEzF,IAASoF,EAAU,iBAAiBK,EAAW,SAASA,EAAW;AACvEnL,MAAO8K,GAAW;MAChB,OAAOnF;MACP,QAAQD;MACR,OAAOyF,EAAW,QAAQxF,KAAS;MACnC,MAAMwF,EAAW,SAASzF,KAAU;IAC1C,CAAK,GACDjF,EAAS,KAAK,OAAOT,EAAO;MAC1B,OAAO8K,EAAU;MACjB,QAAQA,EAAU;IACxB,GAAOzH,GAAcrD,EAAO;MACtB,YAAY8K,EAAU;MACtB,YAAYA,EAAU;IAC5B,GAAOA,CAAS,CAAC,CAAC,CAAC,GACXe,KACF,KAAK,OAAM;EAEd;EACD,aAAa,WAAuB;AAClC,QAAI7J,IAAU,KAAK,SACjBmJ,IAAa,KAAK,YAChB1F,IAAczD,EAAQ,eAAeA,EAAQ,oBAC7CgK,IAAe,OAAOhK,EAAQ,YAAY,KAAK,KAC/CsJ,IAAc;MAChB,OAAOH,EAAW;MAClB,QAAQA,EAAW;IACzB;AACQ1F,UACE0F,EAAW,SAAS1F,IAAc0F,EAAW,QAC/CG,EAAY,SAASA,EAAY,QAAQ7F,IAEzC6F,EAAY,QAAQA,EAAY,SAAS7F,IAG7C,KAAK,cAAc6F,GACnB,KAAK,aAAa,MAAM,IAAI,GAG5BA,EAAY,QAAQ,KAAK,IAAI,KAAK,IAAIA,EAAY,OAAOA,EAAY,QAAQ,GAAGA,EAAY,QAAQ,GACpGA,EAAY,SAAS,KAAK,IAAI,KAAK,IAAIA,EAAY,QAAQA,EAAY,SAAS,GAAGA,EAAY,SAAS,GAGxGA,EAAY,QAAQ,KAAK,IAAIA,EAAY,UAAUA,EAAY,QAAQU,CAAY,GACnFV,EAAY,SAAS,KAAK,IAAIA,EAAY,WAAWA,EAAY,SAASU,CAAY,GACtFV,EAAY,OAAOH,EAAW,QAAQA,EAAW,QAAQG,EAAY,SAAS,GAC9EA,EAAY,MAAMH,EAAW,OAAOA,EAAW,SAASG,EAAY,UAAU,GAC9EA,EAAY,UAAUA,EAAY,MAClCA,EAAY,SAASA,EAAY,KACjC,KAAK,qBAAqBtL,EAAO,CAAE,GAAEsL,CAAW;EACjD;EACD,cAAc,SAAsBF,GAAaC,GAAiB;AAChE,QAAIrJ,IAAU,KAAK,SACjB6I,IAAgB,KAAK,eACrBM,IAAa,KAAK,YAClBG,IAAc,KAAK,aACnBW,IAAU,KAAK,SACbxG,IAAczD,EAAQ;AAC1B,QAAIoJ,GAAa;AACf,UAAIc,IAAkB,OAAOlK,EAAQ,eAAe,KAAK,GACrDmK,KAAmB,OAAOnK,EAAQ,gBAAgB,KAAK,GACvDoK,IAAkBH,IAAU,KAAK,IAAIpB,EAAc,OAAOM,EAAW,OAAOA,EAAW,QAAQA,EAAW,MAAMN,EAAc,QAAQM,EAAW,IAAI,IAAIN,EAAc,OACvKwB,IAAmBJ,IAAU,KAAK,IAAIpB,EAAc,QAAQM,EAAW,QAAQA,EAAW,SAASA,EAAW,KAAKN,EAAc,SAASM,EAAW,GAAG,IAAIN,EAAc;AAG9KqB,UAAkB,KAAK,IAAIA,GAAiBrB,EAAc,KAAK,GAC/DsB,KAAmB,KAAK,IAAIA,IAAkBtB,EAAc,MAAM,GAC9DpF,MACEyG,KAAmBC,KACjBA,KAAmB1G,IAAcyG,IACnCC,KAAmBD,IAAkBzG,IAErCyG,IAAkBC,KAAmB1G,IAE9ByG,IACTC,KAAmBD,IAAkBzG,IAC5B0G,OACTD,IAAkBC,KAAmB1G,IAEnC4G,IAAmB5G,IAAc2G,IACnCC,IAAmBD,IAAkB3G,IAErC2G,IAAkBC,IAAmB5G,IAKzC6F,EAAY,WAAW,KAAK,IAAIY,GAAiBE,CAAe,GAChEd,EAAY,YAAY,KAAK,IAAIa,IAAkBE,CAAgB,GACnEf,EAAY,WAAWc,GACvBd,EAAY,YAAYe;IACzB;AACGhB,UACEY,KACFX,EAAY,UAAU,KAAK,IAAI,GAAGH,EAAW,IAAI,GACjDG,EAAY,SAAS,KAAK,IAAI,GAAGH,EAAW,GAAG,GAC/CG,EAAY,UAAU,KAAK,IAAIT,EAAc,OAAOM,EAAW,OAAOA,EAAW,KAAK,IAAIG,EAAY,OACtGA,EAAY,SAAS,KAAK,IAAIT,EAAc,QAAQM,EAAW,MAAMA,EAAW,MAAM,IAAIG,EAAY,WAEtGA,EAAY,UAAU,GACtBA,EAAY,SAAS,GACrBA,EAAY,UAAUT,EAAc,QAAQS,EAAY,OACxDA,EAAY,SAAST,EAAc,SAASS,EAAY;EAG7D;EACD,eAAe,WAAyB;AACtC,QAAItJ,IAAU,KAAK,SACjB6I,IAAgB,KAAK,eACrBS,IAAc,KAAK;AACrB,KAAIA,EAAY,QAAQA,EAAY,YAAYA,EAAY,QAAQA,EAAY,cAC9EA,EAAY,OAAOA,EAAY,WAE7BA,EAAY,SAASA,EAAY,aAAaA,EAAY,SAASA,EAAY,eACjFA,EAAY,MAAMA,EAAY,SAEhCA,EAAY,QAAQ,KAAK,IAAI,KAAK,IAAIA,EAAY,OAAOA,EAAY,QAAQ,GAAGA,EAAY,QAAQ,GACpGA,EAAY,SAAS,KAAK,IAAI,KAAK,IAAIA,EAAY,QAAQA,EAAY,SAAS,GAAGA,EAAY,SAAS,GACxG,KAAK,aAAa,OAAO,IAAI,GAC7BA,EAAY,OAAO,KAAK,IAAI,KAAK,IAAIA,EAAY,MAAMA,EAAY,OAAO,GAAGA,EAAY,OAAO,GAChGA,EAAY,MAAM,KAAK,IAAI,KAAK,IAAIA,EAAY,KAAKA,EAAY,MAAM,GAAGA,EAAY,MAAM,GAC5FA,EAAY,UAAUA,EAAY,MAClCA,EAAY,SAASA,EAAY,KAC7BtJ,EAAQ,WAAWA,EAAQ,kBAE7BP,GAAQ,KAAK,MAAMrE,IAAakO,EAAY,SAAST,EAAc,SAASS,EAAY,UAAUT,EAAc,SAAS1O,KAAcF,EAAU,GAEnJwE,EAAS,KAAK,SAAST,EAAO;MAC5B,OAAOsL,EAAY;MACnB,QAAQA,EAAY;IACrB,GAAEjI,GAAc;MACf,YAAYiI,EAAY;MACxB,YAAYA,EAAY;IACzB,CAAA,CAAC,CAAC,GACC,KAAK,WAAW,KAAK,WACvB,KAAK,YAAY,MAAM,IAAI,GAExB,KAAK,YACR,KAAK,OAAM;EAEd;EACD,QAAQ,WAAkB;AACxB,SAAK,QAAO,GACZ3I,GAAc,KAAK,SAASlF,IAAY,KAAK,QAAO,CAAE;EACvD;AACH;AA1VA,IA4VI6O,KAAU;EACZ,aAAa,WAAuB;AAClC,QAAI5L,IAAU,KAAK,SACjB6L,IAAc,KAAK,aACjBD,IAAU,KAAK,QAAQ,SACvBrJ,IAAMsJ,IAAc,KAAK,iBAAiB,KAAK,KAC/CC,IAAM9L,EAAQ,OAAO,wBACrB8F,IAAQ,SAAS,cAAc,KAAK;AAQxC,QAPI+F,MACF/F,EAAM,cAAc+F,IAEtB/F,EAAM,MAAMvD,GACZuD,EAAM,MAAMgG,GACZ,KAAK,QAAQ,YAAYhG,CAAK,GAC9B,KAAK,eAAeA,GAChB,CAAA,CAAC8F,GAGL;AAAA,UAAIG,IAAWH;AACX,aAAOA,KAAY,WACrBG,IAAW/L,EAAQ,cAAc,iBAAiB4L,CAAO,IAChDA,EAAQ,kBACjBG,IAAW,CAACH,CAAO,IAErB,KAAK,WAAWG,GAChB5M,EAAQ4M,GAAU,SAAUC,GAAI;AAC9B,YAAIC,IAAM,SAAS,cAAc,KAAK;AAGtClL,WAAQiL,GAAIrP,IAAc;UACxB,OAAOqP,EAAG;UACV,QAAQA,EAAG;UACX,MAAMA,EAAG;QACjB,CAAO,GACGH,MACFI,EAAI,cAAcJ,IAEpBI,EAAI,MAAM1J,GACV0J,EAAI,MAAMH,GAQVG,EAAI,MAAM,UAAU,2KACpBD,EAAG,YAAY,IACfA,EAAG,YAAYC,CAAG;MACxB,CAAK;IAAA;EACF;EACD,cAAc,WAAwB;AACpC9M,MAAQ,KAAK,UAAU,SAAUa,GAAS;AACxC,UAAIZ,IAAOyB,GAAQb,GAASrD,EAAY;AACxCoD,QAASC,GAAS;QAChB,OAAOZ,EAAK;QACZ,QAAQA,EAAK;MACrB,CAAO,GACDY,EAAQ,YAAYZ,EAAK,MACzB4B,GAAWhB,GAASrD,EAAY;IACtC,CAAK;EACF;EACD,SAAS,WAAmB;AAC1B,QAAIyN,IAAY,KAAK,WACnBK,IAAa,KAAK,YAClBG,IAAc,KAAK,aACjBsB,IAAetB,EAAY,OAC7BuB,IAAgBvB,EAAY,QAC1B3F,IAAQmF,EAAU,OACpBpF,IAASoF,EAAU,QACjBgC,IAAOxB,EAAY,OAAOH,EAAW,OAAOL,EAAU,MACtDiC,IAAMzB,EAAY,MAAMH,EAAW,MAAML,EAAU;AACnD,KAAC,KAAK,WAAW,KAAK,aAG1BrK,EAAS,KAAK,cAAcT,EAAO;MACjC,OAAO2F;MACP,QAAQD;IACd,GAAOrC,GAAcrD,EAAO;MACtB,YAAY,CAAC8M;MACb,YAAY,CAACC;IACnB,GAAOjC,CAAS,CAAC,CAAC,CAAC,GACfjL,EAAQ,KAAK,UAAU,SAAUa,IAAS;AACxC,UAAIZ,IAAOyB,GAAQb,IAASrD,EAAY,GACpC2P,IAAgBlN,EAAK,OACrBmN,IAAiBnN,EAAK,QACtBuG,IAAW2G,GACX1G,IAAY2G,GACZtI,IAAQ;AACRiI,YACFjI,IAAQqI,IAAgBJ,GACxBtG,IAAYuG,IAAgBlI,IAE1BkI,KAAiBvG,IAAY2G,MAC/BtI,IAAQsI,IAAiBJ,GACzBxG,IAAWuG,IAAejI,GAC1B2B,IAAY2G,IAEdxM,EAASC,IAAS;QAChB,OAAO2F;QACP,QAAQC;MAChB,CAAO,GACD7F,EAASC,GAAQ,qBAAqB,KAAK,EAAE,CAAC,GAAGV,EAAO;QACtD,OAAO2F,IAAQhB;QACf,QAAQe,IAASf;MACzB,GAAStB,GAAcrD,EAAO;QACtB,YAAY,CAAC8M,IAAOnI;QACpB,YAAY,CAACoI,IAAMpI;MAC3B,GAASmG,CAAS,CAAC,CAAC,CAAC;IACrB,CAAK;EACF;AACH;AA3cA,IA6cIoC,KAAS;EACX,MAAM,WAAgB;AACpB,QAAIxM,IAAU,KAAK,SACjBsB,IAAU,KAAK,SACf4I,IAAU,KAAK;AACblL,MAAWsC,EAAQ,SAAS,KAC9BM,EAAY5B,GAAS9C,IAAkBoE,EAAQ,SAAS,GAEtDtC,EAAWsC,EAAQ,QAAQ,KAC7BM,EAAY5B,GAAS/C,IAAiBqE,EAAQ,QAAQ,GAEpDtC,EAAWsC,EAAQ,OAAO,KAC5BM,EAAY5B,GAAShD,IAAgBsE,EAAQ,OAAO,GAElDtC,EAAWsC,EAAQ,IAAI,KACzBM,EAAY5B,GAASjD,IAAYuE,EAAQ,IAAI,GAE3CtC,EAAWsC,EAAQ,IAAI,KACzBM,EAAY5B,GAASnC,IAAYyD,EAAQ,IAAI,GAE/CM,EAAYsI,GAAS3M,KAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,IAAI,CAAC,GACjF+D,EAAQ,YAAYA,EAAQ,eAC9BM,EAAYsI,GAAStM,IAAa,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,GAAG;MACtE,SAAS;MACT,SAAS;IACjB,CAAO,GAEC0D,EAAQ,4BACVM,EAAYsI,GAAS/M,IAAgB,KAAK,aAAa,KAAK,SAAS,KAAK,IAAI,CAAC,GAEjFyE,EAAY5B,EAAQ,eAAexC,IAAoB,KAAK,aAAa,KAAK,SAAS,KAAK,IAAI,CAAC,GACjGoE,EAAY5B,EAAQ,eAAevC,IAAkB,KAAK,YAAY,KAAK,QAAQ,KAAK,IAAI,CAAC,GACzF6D,EAAQ,cACVM,EAAY,QAAQjE,IAAc,KAAK,WAAW,KAAK,OAAO,KAAK,IAAI,CAAC;EAE3E;EACD,QAAQ,WAAkB;AACxB,QAAIqC,IAAU,KAAK,SACjBsB,IAAU,KAAK,SACf4I,IAAU,KAAK;AACblL,MAAWsC,EAAQ,SAAS,KAC9BC,EAAevB,GAAS9C,IAAkBoE,EAAQ,SAAS,GAEzDtC,EAAWsC,EAAQ,QAAQ,KAC7BC,EAAevB,GAAS/C,IAAiBqE,EAAQ,QAAQ,GAEvDtC,EAAWsC,EAAQ,OAAO,KAC5BC,EAAevB,GAAShD,IAAgBsE,EAAQ,OAAO,GAErDtC,EAAWsC,EAAQ,IAAI,KACzBC,EAAevB,GAASjD,IAAYuE,EAAQ,IAAI,GAE9CtC,EAAWsC,EAAQ,IAAI,KACzBC,EAAevB,GAASnC,IAAYyD,EAAQ,IAAI,GAElDC,EAAe2I,GAAS3M,KAAoB,KAAK,WAAW,GACxD+D,EAAQ,YAAYA,EAAQ,eAC9BC,EAAe2I,GAAStM,IAAa,KAAK,SAAS;MACjD,SAAS;MACT,SAAS;IACjB,CAAO,GAEC0D,EAAQ,4BACVC,EAAe2I,GAAS/M,IAAgB,KAAK,UAAU,GAEzDoE,EAAevB,EAAQ,eAAexC,IAAoB,KAAK,UAAU,GACzE+D,EAAevB,EAAQ,eAAevC,IAAkB,KAAK,SAAS,GAClE6D,EAAQ,cACVC,EAAe,QAAQ5D,IAAc,KAAK,QAAQ;EAErD;AACH;AAphBA,IAshBI8O,KAAW;EACb,QAAQ,WAAkB;AACxB,QAAI,CAAA,KAAK,UAGT;AAAA,UAAInL,IAAU,KAAK,SACjB2I,IAAY,KAAK,WACjBE,IAAgB,KAAK,eACnBuC,IAASzC,EAAU,cAAcE,EAAc,OAC/CwC,IAAS1C,EAAU,eAAeE,EAAc,QAChDlG,IAAQ,KAAK,IAAIyI,IAAS,CAAC,IAAI,KAAK,IAAIC,IAAS,CAAC,IAAID,IAASC;AAGnE,UAAI1I,MAAU,GAAG;AACf,YAAIwG,GACAG;AACAtJ,UAAQ,YACVmJ,IAAa,KAAK,cAAA,GAClBG,IAAc,KAAK,eAAA,IAErB,KAAK,OAAM,GACPtJ,EAAQ,YACV,KAAK,cAAcnC,EAAQsL,GAAY,SAAU1P,GAAGzB,IAAG;AACrDmR,YAAWnR,EAAC,IAAIyB,IAAIkJ;QACrB,CAAA,CAAC,GACF,KAAK,eAAe9E,EAAQyL,GAAa,SAAU7P,GAAGzB,IAAG;AACvDsR,YAAYtR,EAAC,IAAIyB,IAAIkJ;QACtB,CAAA,CAAC;MAEL;IAAA;EACF;EACD,UAAU,WAAoB;AACxB,SAAK,YAAY,KAAK,QAAQ,aAAanH,MAG/C,KAAK,YAAYsD,GAAS,KAAK,SAASjE,EAAU,IAAIU,KAAiBD,EAAc;EACtF;EACD,OAAO,SAAe8E,GAAO;AAC3B,QAAIkL,IAAQ,MACR3I,IAAQ,OAAO,KAAK,QAAQ,cAAc,KAAK,KAC/C4I,IAAQ;AACR,SAAK,aAGTnL,EAAM,eAAc,GAGhB,CAAA,KAAK,aAGT,KAAK,WAAW,MAChB,WAAW,WAAY;AACrBkL,QAAM,WAAW;IAClB,GAAE,EAAE,GACDlL,EAAM,SACRmL,IAAQnL,EAAM,SAAS,IAAI,IAAI,KACtBA,EAAM,aACfmL,IAAQ,CAACnL,EAAM,aAAa,MACnBA,EAAM,WACfmL,IAAQnL,EAAM,SAAS,IAAI,IAAI,KAEjC,KAAK,KAAK,CAACmL,IAAQ5I,GAAOvC,CAAK;EAChC;EACD,WAAW,SAAmBA,GAAO;AACnC,QAAIoL,IAAUpL,EAAM,SAClBqL,IAASrL,EAAM;AACjB,QAAI,EAAA,KAAK,aAGLA,EAAM,SAAS,eAAeA,EAAM,SAAS,iBAAiBA,EAAM,gBAAgB;KAExFlD,EAASsO,CAAO,KAAKA,MAAY,KAAKtO,EAASuO,CAAM,KAAKA,MAAW,KAGlErL,EAAM,WAGT;AAAA,UAAIJ,IAAU,KAAK,SACjB+B,IAAW,KAAK,UACd2J;AACAtL,QAAM,iBAERvC,EAAQuC,EAAM,gBAAgB,SAAUuL,GAAO;AAC7C5J,UAAS4J,EAAM,UAAU,IAAI/I,GAAW+I,CAAK;MACrD,CAAO,IAGD5J,EAAS3B,EAAM,aAAa,CAAC,IAAIwC,GAAWxC,CAAK,GAE/C,OAAO,KAAK2B,CAAQ,EAAE,SAAS,KAAK/B,EAAQ,YAAYA,EAAQ,cAClE0L,IAAStR,KAETsR,IAASnM,GAAQa,EAAM,QAAQhF,EAAW,GAEvCqB,GAAe,KAAKiP,CAAM,KAG3B/K,GAAc,KAAK,SAAS/E,IAAkB;QAChD,eAAewE;QACf,QAAQsL;MACT,CAAA,MAAM,UAKPtL,EAAM,eAAc,GACpB,KAAK,SAASsL,GACd,KAAK,WAAW,OACZA,MAAWxR,OACb,KAAK,WAAW,MAChB6E,EAAS,KAAK,SAAS7D,EAAW;IAAA;EAErC;EACD,UAAU,SAAkBkF,GAAO;AACjC,QAAIsL,IAAS,KAAK;AAClB,QAAI,EAAA,KAAK,YAAY,CAACA,IAGtB;AAAA,UAAI3J,IAAW,KAAK;AACpB3B,QAAM,eAAc,GAChBO,GAAc,KAAK,SAAShF,IAAiB;QAC/C,eAAeyE;QACf,QAAQsL;MACT,CAAA,MAAM,UAGHtL,EAAM,iBACRvC,EAAQuC,EAAM,gBAAgB,SAAUuL,GAAO;AAE7C3N,UAAO+D,EAAS4J,EAAM,UAAU,KAAK,CAAE,GAAE/I,GAAW+I,GAAO,IAAI,CAAC;MACxE,CAAO,IAED3N,EAAO+D,EAAS3B,EAAM,aAAa,CAAC,KAAK,CAAE,GAAEwC,GAAWxC,GAAO,IAAI,CAAC,GAEtE,KAAK,OAAOA,CAAK;IAAA;EAClB;EACD,SAAS,SAAiBA,GAAO;AAC/B,QAAI,CAAA,KAAK,UAGT;AAAA,UAAIsL,IAAS,KAAK,QAChB3J,IAAW,KAAK;AACd3B,QAAM,iBACRvC,EAAQuC,EAAM,gBAAgB,SAAUuL,GAAO;AAC7C,eAAO5J,EAAS4J,EAAM,UAAU;MACxC,CAAO,IAED,OAAO5J,EAAS3B,EAAM,aAAa,CAAC,GAEjCsL,MAGLtL,EAAM,eAAc,GACf,OAAO,KAAK2B,CAAQ,EAAE,WACzB,KAAK,SAAS,KAEZ,KAAK,aACP,KAAK,WAAW,OAChB5C,GAAY,KAAK,SAASjE,IAAa,KAAK,WAAW,KAAK,QAAQ,KAAK,IAE3EyF,GAAc,KAAK,SAASjF,IAAgB;QAC1C,eAAe0E;QACf,QAAQsL;MACd,CAAK;IAAA;EACF;AACH;AA3rBA,IA6rBIE,KAAS;EACX,QAAQ,SAAgBxL,GAAO;AAC7B,QAAIJ,IAAU,KAAK,SACjBmJ,IAAa,KAAK,YAClBN,IAAgB,KAAK,eACrBS,IAAc,KAAK,aACnBvH,IAAW,KAAK,UACd2J,IAAS,KAAK,QACdjI,IAAczD,EAAQ,aACtB8K,IAAOxB,EAAY,MACrByB,KAAMzB,EAAY,KAClB3F,IAAQ2F,EAAY,OACpB5F,IAAS4F,EAAY,QACnBuC,IAAQf,IAAOnH,GACfmI,IAASf,KAAMrH,GACfqI,IAAU,GACVC,IAAS,GACTrG,IAAWkD,EAAc,OACzBhD,IAAYgD,EAAc,QAC1BoD,IAAa,MACb/D;AAGA,KAACzE,KAAerD,EAAM,aACxBqD,IAAcE,KAASD,IAASC,IAAQD,IAAS,IAE/C,KAAK,YACPqI,IAAUzC,EAAY,SACtB0C,IAAS1C,EAAY,QACrB3D,IAAWoG,IAAU,KAAK,IAAIlD,EAAc,OAAOM,EAAW,OAAOA,EAAW,OAAOA,EAAW,KAAK,GACvGtD,IAAYmG,IAAS,KAAK,IAAInD,EAAc,QAAQM,EAAW,QAAQA,EAAW,MAAMA,EAAW,MAAM;AAE3G,QAAIjH,KAAUH,EAAS,OAAO,KAAKA,CAAQ,EAAE,CAAC,CAAC,GAC3CmK,IAAQ;MACV,GAAGhK,GAAQ,OAAOA,GAAQ;MAC1B,GAAGA,GAAQ,OAAOA,GAAQ;IAChC,GACQiK,IAAQ,SAAeC,GAAM;AAC/B,cAAQA,GAAI;QACV,KAAK/R;AACCwR,cAAQK,EAAM,IAAIvG,MACpBuG,EAAM,IAAIvG,IAAWkG;AAEvB;QACF,KAAKvR;AACCwQ,cAAOoB,EAAM,IAAIH,MACnBG,EAAM,IAAIH,IAAUjB;AAEtB;QACF,KAAKtQ;AACCuQ,UAAAA,KAAMmB,EAAM,IAAIF,MAClBE,EAAM,IAAIF,IAASjB;AAErB;QACF,KAAKxQ;AACCuR,cAASI,EAAM,IAAIrG,MACrBqG,EAAM,IAAIrG,IAAYiG;AAExB;MACH;IACP;AACI,YAAQJ,GAAM;MAEZ,KAAKzR;AACH6Q,aAAQoB,EAAM,GACdnB,MAAOmB,EAAM;AACb;MAGF,KAAK7R;AACH,YAAI6R,EAAM,KAAK,MAAML,KAASlG,KAAYlC,MAAgBsH,MAAOiB,KAAUF,KAAUjG,KAAa;AAChGoG,cAAa;AACb;QACD;AACDE,UAAM9R,CAAW,GACjBsJ,KAASuI,EAAM,GACXvI,IAAQ,MACV+H,IAASpR,IACTqJ,IAAQ,CAACA,GACTmH,KAAQnH,IAENF,MACFC,IAASC,IAAQF,GACjBsH,OAAQzB,EAAY,SAAS5F,KAAU;AAEzC;MACF,KAAKlJ;AACH,YAAI0R,EAAM,KAAK,MAAMnB,MAAOiB,KAAUvI,MAAgBqH,KAAQiB,KAAWF,KAASlG,KAAY;AAC5FsG,cAAa;AACb;QACD;AACDE,UAAM3R,CAAY,GAClBkJ,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM,GACTxI,IAAS,MACXgI,IAASnR,IACTmJ,IAAS,CAACA,GACVqH,MAAOrH,IAELD,MACFE,IAAQD,IAASD,GACjBqH,MAASxB,EAAY,QAAQ3F,KAAS;AAExC;MACF,KAAKrJ;AACH,YAAI4R,EAAM,KAAK,MAAMpB,KAAQiB,KAAWtI,MAAgBsH,MAAOiB,KAAUF,KAAUjG,KAAa;AAC9FoG,cAAa;AACb;QACD;AACDE,UAAM7R,EAAW,GACjBqJ,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,GACVvI,IAAQ,MACV+H,IAASrR,GACTsJ,IAAQ,CAACA,GACTmH,KAAQnH,IAENF,MACFC,IAASC,IAAQF,GACjBsH,OAAQzB,EAAY,SAAS5F,KAAU;AAEzC;MACF,KAAKnJ;AACH,YAAI2R,EAAM,KAAK,MAAMJ,KAAUjG,KAAapC,MAAgBqH,KAAQiB,KAAWF,KAASlG,KAAY;AAClGsG,cAAa;AACb;QACD;AACDE,UAAM5R,EAAY,GAClBmJ,KAAUwI,EAAM,GACZxI,IAAS,MACXgI,IAASlR,GACTkJ,IAAS,CAACA,GACVqH,MAAOrH,IAELD,MACFE,IAAQD,IAASD,GACjBqH,MAASxB,EAAY,QAAQ3F,KAAS;AAExC;MACF,KAAKlJ;AACH,YAAIgJ,GAAa;AACf,cAAIyI,EAAM,KAAK,MAAMnB,MAAOiB,KAAUH,KAASlG,IAAW;AACxDsG,gBAAa;AACb;UACD;AACDE,YAAM3R,CAAY,GAClBkJ,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM,GACbvI,IAAQD,IAASD;QAC3B;AACU0I,YAAM3R,CAAY,GAClB2R,EAAM9R,CAAW,GACb6R,EAAM,KAAK,IACTL,IAAQlG,IACVhC,KAASuI,EAAM,IACNA,EAAM,KAAK,KAAKnB,MAAOiB,MAChCC,IAAa,SAGftI,KAASuI,EAAM,GAEbA,EAAM,KAAK,IACTnB,KAAMiB,MACRtI,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM,MAGfxI,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM;AAGbvI,YAAQ,KAAKD,IAAS,KACxBgI,IAAS9Q,IACT8I,IAAS,CAACA,GACVC,IAAQ,CAACA,GACToH,MAAOrH,GACPoH,KAAQnH,KACCA,IAAQ,KACjB+H,IAAShR,IACTiJ,IAAQ,CAACA,GACTmH,KAAQnH,KACCD,IAAS,MAClBgI,IAAS/Q,IACT+I,IAAS,CAACA,GACVqH,MAAOrH;AAET;MACF,KAAKhJ;AACH,YAAI+I,GAAa;AACf,cAAIyI,EAAM,KAAK,MAAMnB,MAAOiB,KAAUlB,KAAQiB,IAAU;AACtDE,gBAAa;AACb;UACD;AACDE,YAAM3R,CAAY,GAClBkJ,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM,GACbvI,IAAQD,IAASD,GACjBqH,KAAQxB,EAAY,QAAQ3F;QACtC;AACUwI,YAAM3R,CAAY,GAClB2R,EAAM7R,EAAW,GACb4R,EAAM,KAAK,IACTpB,IAAOiB,KACTpI,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,KACLA,EAAM,KAAK,KAAKnB,MAAOiB,MAChCC,IAAa,UAGftI,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,IAEZA,EAAM,KAAK,IACTnB,KAAMiB,MACRtI,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM,MAGfxI,KAAUwI,EAAM,GAChBnB,MAAOmB,EAAM;AAGbvI,YAAQ,KAAKD,IAAS,KACxBgI,IAAS/Q,IACT+I,IAAS,CAACA,GACVC,IAAQ,CAACA,GACToH,MAAOrH,GACPoH,KAAQnH,KACCA,IAAQ,KACjB+H,IAASjR,IACTkJ,IAAQ,CAACA,GACTmH,KAAQnH,KACCD,IAAS,MAClBgI,IAAS9Q,IACT8I,IAAS,CAACA,GACVqH,MAAOrH;AAET;MACF,KAAK9I;AACH,YAAI6I,GAAa;AACf,cAAIyI,EAAM,KAAK,MAAMpB,KAAQiB,KAAWD,KAAUjG,IAAY;AAC5DoG,gBAAa;AACb;UACD;AACDE,YAAM7R,EAAW,GACjBqJ,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,GACdxI,IAASC,IAAQF;QAC3B;AACU0I,YAAM5R,EAAY,GAClB4R,EAAM7R,EAAW,GACb4R,EAAM,KAAK,IACTpB,IAAOiB,KACTpI,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,KACLA,EAAM,KAAK,KAAKJ,KAAUjG,MACnCoG,IAAa,UAGftI,KAASuI,EAAM,GACfpB,KAAQoB,EAAM,IAEZA,EAAM,KAAK,IACTJ,IAASjG,MACXnC,KAAUwI,EAAM,KAGlBxI,KAAUwI,EAAM;AAGhBvI,YAAQ,KAAKD,IAAS,KACxBgI,IAASjR,IACTiJ,IAAS,CAACA,GACVC,IAAQ,CAACA,GACToH,MAAOrH,GACPoH,KAAQnH,KACCA,IAAQ,KACjB+H,IAAS/Q,IACTgJ,IAAQ,CAACA,GACTmH,KAAQnH,KACCD,IAAS,MAClBgI,IAAShR,IACTgJ,IAAS,CAACA,GACVqH,MAAOrH;AAET;MACF,KAAK/I;AACH,YAAI8I,GAAa;AACf,cAAIyI,EAAM,KAAK,MAAML,KAASlG,KAAYmG,KAAUjG,IAAY;AAC9DoG,gBAAa;AACb;UACD;AACDE,YAAM9R,CAAW,GACjBsJ,KAASuI,EAAM,GACfxI,IAASC,IAAQF;QAC3B;AACU0I,YAAM5R,EAAY,GAClB4R,EAAM9R,CAAW,GACb6R,EAAM,KAAK,IACTL,IAAQlG,IACVhC,KAASuI,EAAM,IACNA,EAAM,KAAK,KAAKJ,KAAUjG,MACnCoG,IAAa,SAGftI,KAASuI,EAAM,GAEbA,EAAM,KAAK,IACTJ,IAASjG,MACXnC,KAAUwI,EAAM,KAGlBxI,KAAUwI,EAAM;AAGhBvI,YAAQ,KAAKD,IAAS,KACxBgI,IAAShR,IACTgJ,IAAS,CAACA,GACVC,IAAQ,CAACA,GACToH,MAAOrH,GACPoH,KAAQnH,KACCA,IAAQ,KACjB+H,IAAS9Q,IACT+I,IAAQ,CAACA,GACTmH,KAAQnH,KACCD,IAAS,MAClBgI,IAASjR,IACTiJ,IAAS,CAACA,GACVqH,MAAOrH;AAET;MAGF,KAAKvJ;AACH,aAAK,KAAK+R,EAAM,GAAGA,EAAM,CAAC,GAC1BD,IAAa;AACb;MAGF,KAAK7R;AACH,aAAK,KAAK0H,GAAgBC,CAAQ,GAAG3B,CAAK,GAC1C6L,IAAa;AACb;MAGF,KAAK/R;AACH,YAAI,CAACgS,EAAM,KAAK,CAACA,EAAM,GAAG;AACxBD,cAAa;AACb;QACD;AACD/D,YAAStH,GAAU,KAAK,OAAO,GAC/BkK,IAAO5I,GAAQ,SAASgG,EAAO,MAC/B6C,KAAM7I,GAAQ,SAASgG,EAAO,KAC9BvE,IAAQ2F,EAAY,UACpB5F,IAAS4F,EAAY,WACjB4C,EAAM,IAAI,IACZR,IAASQ,EAAM,IAAI,IAAIvR,KAAoBF,KAClCyR,EAAM,IAAI,MACnBpB,KAAQnH,GACR+H,IAASQ,EAAM,IAAI,IAAItR,KAAoBF,KAEzCwR,EAAM,IAAI,MACZnB,MAAOrH,IAIJ,KAAK,YACRxE,EAAY,KAAK,SAASnE,CAAY,GACtC,KAAK,UAAU,MACX,KAAK,WACP,KAAK,aAAa,MAAM,IAAI;AAGhC;IACH;AACGkR,UACF3C,EAAY,QAAQ3F,GACpB2F,EAAY,SAAS5F,GACrB4F,EAAY,OAAOwB,GACnBxB,EAAY,MAAMyB,IAClB,KAAK,SAASW,GACd,KAAK,cAAa,IAIpB7N,EAAQkE,GAAU,SAAUsK,GAAG;AAC7BA,QAAE,SAASA,EAAE,MACbA,EAAE,SAASA,EAAE;IACnB,CAAK;EACF;AACH;AAnkCA,IAqkCIC,KAAU;;EAEZ,MAAM,WAAgB;AACpB,WAAI,KAAK,SAAS,CAAC,KAAK,WAAW,CAAC,KAAK,aACvC,KAAK,UAAU,MACf,KAAK,aAAa,MAAM,IAAI,GACxB,KAAK,QAAQ,SACfvN,EAAS,KAAK,SAAS7D,EAAW,GAEpCgE,EAAY,KAAK,SAASnE,CAAY,GACtC,KAAK,eAAe,KAAK,kBAAkB,IAEtC;EACR;;EAED,OAAO,WAAiB;AACtB,WAAI,KAAK,SAAS,CAAC,KAAK,aACtB,KAAK,YAAYiD,EAAO,CAAE,GAAE,KAAK,gBAAgB,GACjD,KAAK,aAAaA,EAAO,CAAE,GAAE,KAAK,iBAAiB,GACnD,KAAK,cAAcA,EAAO,CAAE,GAAE,KAAK,kBAAkB,GACrD,KAAK,aAAY,GACb,KAAK,WACP,KAAK,cAAa,IAGf;EACR;;EAED,OAAO,WAAiB;AACtB,WAAI,KAAK,WAAW,CAAC,KAAK,aACxBA,EAAO,KAAK,aAAa;MACvB,MAAM;MACN,KAAK;MACL,OAAO;MACP,QAAQ;IAChB,CAAO,GACD,KAAK,UAAU,OACf,KAAK,cAAa,GAClB,KAAK,YAAY,MAAM,IAAI,GAG3B,KAAK,aAAY,GACjBkB,EAAY,KAAK,SAAShE,EAAW,GACrC6D,EAAS,KAAK,SAAShE,CAAY,IAE9B;EACR;;;;;;;EAOD,SAAS,SAAiBkG,GAAK;AAC7B,QAAIsL,IAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACtF,WAAI,CAAC,KAAK,YAAYtL,MAChB,KAAK,UACP,KAAK,QAAQ,MAAMA,IAEjBsL,KACF,KAAK,MAAMtL,GACX,KAAK,MAAM,MAAMA,GACb,KAAK,UACP,KAAK,aAAa,MAAMA,GACxBpD,EAAQ,KAAK,UAAU,SAAUa,GAAS;AACxCA,QAAQ,qBAAqB,KAAK,EAAE,CAAC,EAAE,MAAMuC;IACzD,CAAW,OAGC,KAAK,UACP,KAAK,WAAW,OAElB,KAAK,QAAQ,OAAO,MACpB,KAAK,SAAQ,GACb,KAAK,KAAKA,CAAG,KAGV;EACR;;EAED,QAAQ,WAAkB;AACxB,WAAI,KAAK,SAAS,KAAK,aACrB,KAAK,WAAW,OAChB/B,EAAY,KAAK,SAASpE,EAAc,IAEnC;EACR;;EAED,SAAS,WAAmB;AAC1B,WAAI,KAAK,SAAS,CAAC,KAAK,aACtB,KAAK,WAAW,MAChBiE,EAAS,KAAK,SAASjE,EAAc,IAEhC;EACR;;;;;EAKD,SAAS,WAAmB;AAC1B,QAAI4D,IAAU,KAAK;AACnB,WAAKA,EAAQ1E,CAAS,KAGtB0E,EAAQ1E,CAAS,IAAI,QACjB,KAAK,SAAS,KAAK,aACrB0E,EAAQ,MAAM,KAAK,cAErB,KAAK,SAAQ,GACN,QAPE;EAQV;;;;;;;EAOD,MAAM,SAAc8N,GAAS;AAC3B,QAAIC,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAID,GAC9EE,IAAmB,KAAK,YAC1B5B,IAAO4B,EAAiB,MACxB3B,IAAM2B,EAAiB;AACzB,WAAO,KAAK,OAAOtP,GAAYoP,CAAO,IAAIA,IAAU1B,IAAO,OAAO0B,CAAO,GAAGpP,GAAYqP,CAAO,IAAIA,IAAU1B,IAAM,OAAO0B,CAAO,CAAC;EACnI;;;;;;;EAOD,QAAQ,SAAgBE,GAAG;AACzB,QAAIC,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAID,GACxExD,IAAa,KAAK,YAClBU,IAAU;AACd,WAAA8C,IAAI,OAAOA,CAAC,GACZC,IAAI,OAAOA,CAAC,GACR,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,YAC3C1P,EAASyP,CAAC,MACZxD,EAAW,OAAOwD,GAClB9C,IAAU,OAER3M,EAAS0P,CAAC,MACZzD,EAAW,MAAMyD,GACjB/C,IAAU,OAERA,KACF,KAAK,aAAa,IAAI,IAGnB;EACR;;;;;;;EAOD,MAAM,SAAclH,GAAOkK,GAAgB;AACzC,QAAI1D,IAAa,KAAK;AACtB,WAAAxG,IAAQ,OAAOA,CAAK,GAChBA,IAAQ,IACVA,IAAQ,KAAK,IAAIA,KAEjBA,IAAQ,IAAIA,GAEP,KAAK,OAAOwG,EAAW,QAAQxG,IAAQwG,EAAW,cAAc,MAAM0D,CAAc;EAC5F;;;;;;;;EAQD,QAAQ,SAAgBlK,GAAOmK,GAAOD,GAAgB;AACpD,QAAI7M,IAAU,KAAK,SACjBmJ,IAAa,KAAK,YAChBxF,IAAQwF,EAAW,OACrBzF,IAASyF,EAAW,QACpBjE,IAAeiE,EAAW,cAC1BhE,IAAgBgE,EAAW;AAE7B,QADAxG,IAAQ,OAAOA,CAAK,GAChBA,KAAS,KAAK,KAAK,SAAS,CAAC,KAAK,YAAY3C,EAAQ,UAAU;AAClE,UAAIqE,KAAWa,IAAevC,GAC1B2B,IAAYa,IAAgBxC;AAChC,UAAIhC,GAAc,KAAK,SAASpE,IAAY;QAC1C,OAAOoG;QACP,UAAUgB,IAAQuB;QAClB,eAAe2H;MAChB,CAAA,MAAM;AACL,eAAO;AAET,UAAIA,GAAgB;AAClB,YAAI9K,IAAW,KAAK,UAChBmG,IAAStH,GAAU,KAAK,OAAO,GAC/BmM,IAAShL,KAAY,OAAO,KAAKA,CAAQ,EAAE,SAASmB,GAAkBnB,CAAQ,IAAI;UACpF,OAAO8K,EAAe;UACtB,OAAOA,EAAe;QAChC;AAGQ1D,UAAW,SAAS9E,KAAWV,OAAWoJ,EAAO,QAAQ7E,EAAO,OAAOiB,EAAW,QAAQxF,IAC1FwF,EAAW,QAAQ7E,IAAYZ,OAAYqJ,EAAO,QAAQ7E,EAAO,MAAMiB,EAAW,OAAOzF;MAC1F;AAAUnG,QAAAA,IAAcuP,CAAK,KAAK5P,EAAS4P,EAAM,CAAC,KAAK5P,EAAS4P,EAAM,CAAC,KACtE3D,EAAW,SAAS9E,KAAWV,OAAWmJ,EAAM,IAAI3D,EAAW,QAAQxF,IACvEwF,EAAW,QAAQ7E,IAAYZ,OAAYoJ,EAAM,IAAI3D,EAAW,OAAOzF,OAGvEyF,EAAW,SAAS9E,KAAWV,KAAS,GACxCwF,EAAW,QAAQ7E,IAAYZ,KAAU;AAE3CyF,QAAW,QAAQ9E,IACnB8E,EAAW,SAAS7E,GACpB,KAAK,aAAa,IAAI;IACvB;AACD,WAAO;EACR;;;;;;EAMD,QAAQ,SAAgBL,GAAQ;AAC9B,WAAO,KAAK,UAAU,KAAK,UAAU,UAAU,KAAK,OAAOA,CAAM,CAAC;EACnE;;;;;;EAMD,UAAU,SAAkBA,GAAQ;AAClC,WAAAA,IAAS,OAAOA,CAAM,GAClB/G,EAAS+G,CAAM,KAAK,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,cACnE,KAAK,UAAU,SAASA,IAAS,KACjC,KAAK,aAAa,MAAM,IAAI,IAEvB;EACR;;;;;;EAMD,QAAQ,SAAgB+I,GAAS;AAC/B,QAAIvL,IAAS,KAAK,UAAU;AAC5B,WAAO,KAAK,MAAMuL,GAAS9P,EAASuE,CAAM,IAAIA,IAAS,CAAC;EACzD;;;;;;EAMD,QAAQ,SAAgBwL,GAAS;AAC/B,QAAIzL,IAAS,KAAK,UAAU;AAC5B,WAAO,KAAK,MAAMtE,EAASsE,CAAM,IAAIA,IAAS,GAAGyL,CAAO;EACzD;;;;;;;EAOD,OAAO,SAAezL,GAAQ;AAC5B,QAAIC,IAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAID,GAC7EsH,IAAY,KAAK,WACjBgB,IAAc;AAClB,WAAAtI,IAAS,OAAOA,CAAM,GACtBC,IAAS,OAAOA,CAAM,GAClB,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,aAC3CvE,EAASsE,CAAM,MACjBsH,EAAU,SAAStH,GACnBsI,IAAc,OAEZ5M,EAASuE,CAAM,MACjBqH,EAAU,SAASrH,GACnBqI,IAAc,OAEZA,KACF,KAAK,aAAa,MAAM,IAAI,IAGzB;EACR;;;;;;EAMD,SAAS,WAAmB;AAC1B,QAAIoD,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,OAC9ElN,IAAU,KAAK,SACjB8I,IAAY,KAAK,WACjBK,IAAa,KAAK,YAClBG,IAAc,KAAK,aACjBxL;AACJ,QAAI,KAAK,SAAS,KAAK,SAAS;AAC9BA,UAAO;QACL,GAAGwL,EAAY,OAAOH,EAAW;QACjC,GAAGG,EAAY,MAAMH,EAAW;QAChC,OAAOG,EAAY;QACnB,QAAQA,EAAY;MAC5B;AACM,UAAI3G,IAAQmG,EAAU,QAAQA,EAAU;AAIxC,UAHAjL,EAAQC,GAAM,SAAUrE,IAAGzB,GAAG;AAC5B8F,UAAK9F,CAAC,IAAIyB,KAAIkJ;MACtB,CAAO,GACGuK,GAAS;AAGX,YAAIpB,IAAS,KAAK,MAAMhO,EAAK,IAAIA,EAAK,MAAM,GACxC+N,IAAQ,KAAK,MAAM/N,EAAK,IAAIA,EAAK,KAAK;AAC1CA,UAAK,IAAI,KAAK,MAAMA,EAAK,CAAC,GAC1BA,EAAK,IAAI,KAAK,MAAMA,EAAK,CAAC,GAC1BA,EAAK,QAAQ+N,IAAQ/N,EAAK,GAC1BA,EAAK,SAASgO,IAAShO,EAAK;MAC7B;IACP;AACMA,UAAO;QACL,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;MAChB;AAEI,WAAIkC,EAAQ,cACVlC,EAAK,SAASgL,EAAU,UAAU,IAEhC9I,EAAQ,aACVlC,EAAK,SAASgL,EAAU,UAAU,GAClChL,EAAK,SAASgL,EAAU,UAAU,IAE7BhL;EACR;;;;;;EAMD,SAAS,SAAiBA,GAAM;AAC9B,QAAIkC,IAAU,KAAK,SACjB8I,IAAY,KAAK,WACjBK,IAAa,KAAK,YAChBG,IAAc,CAAA;AAClB,QAAI,KAAK,SAAS,CAAC,KAAK,YAAY/L,IAAcO,CAAI,GAAG;AACvD,UAAIgM,IAAc;AACd9J,QAAQ,aACN9C,EAASY,EAAK,MAAM,KAAKA,EAAK,WAAWgL,EAAU,WACrDA,EAAU,SAAShL,EAAK,QACxBgM,IAAc,OAGd9J,EAAQ,aACN9C,EAASY,EAAK,MAAM,KAAKA,EAAK,WAAWgL,EAAU,WACrDA,EAAU,SAAShL,EAAK,QACxBgM,IAAc,OAEZ5M,EAASY,EAAK,MAAM,KAAKA,EAAK,WAAWgL,EAAU,WACrDA,EAAU,SAAShL,EAAK,QACxBgM,IAAc,QAGdA,KACF,KAAK,aAAa,MAAM,IAAI;AAE9B,UAAInH,IAAQmG,EAAU,QAAQA,EAAU;AACpC5L,QAASY,EAAK,CAAC,MACjBwL,EAAY,OAAOxL,EAAK,IAAI6E,IAAQwG,EAAW,OAE7CjM,EAASY,EAAK,CAAC,MACjBwL,EAAY,MAAMxL,EAAK,IAAI6E,IAAQwG,EAAW,MAE5CjM,EAASY,EAAK,KAAK,MACrBwL,EAAY,QAAQxL,EAAK,QAAQ6E,IAE/BzF,EAASY,EAAK,MAAM,MACtBwL,EAAY,SAASxL,EAAK,SAAS6E,IAErC,KAAK,eAAe2G,CAAW;IAChC;AACD,WAAO;EACR;;;;;EAKD,kBAAkB,WAA4B;AAC5C,WAAO,KAAK,QAAQtL,EAAO,CAAE,GAAE,KAAK,aAAa,IAAI,CAAA;EACtD;;;;;EAKD,cAAc,WAAwB;AACpC,WAAO,KAAK,QAAQA,EAAO,CAAE,GAAE,KAAK,SAAS,IAAI,CAAA;EAClD;;;;;EAKD,eAAe,WAAyB;AACtC,QAAImL,IAAa,KAAK,YAClBrL,IAAO,CAAA;AACX,WAAI,KAAK,SACPD,EAAQ,CAAC,QAAQ,OAAO,SAAS,UAAU,gBAAgB,eAAe,GAAG,SAAUpE,GAAG;AACxFqE,QAAKrE,CAAC,IAAI0P,EAAW1P,CAAC;IAC9B,CAAO,GAEIqE;EACR;;;;;;EAMD,eAAe,SAAuBA,GAAM;AAC1C,QAAIqL,IAAa,KAAK,YAClB1F,IAAc0F,EAAW;AAC7B,WAAI,KAAK,SAAS,CAAC,KAAK,YAAY5L,IAAcO,CAAI,MAChDZ,EAASY,EAAK,IAAI,MACpBqL,EAAW,OAAOrL,EAAK,OAErBZ,EAASY,EAAK,GAAG,MACnBqL,EAAW,MAAMrL,EAAK,MAEpBZ,EAASY,EAAK,KAAK,KACrBqL,EAAW,QAAQrL,EAAK,OACxBqL,EAAW,SAASrL,EAAK,QAAQ2F,KACxBvG,EAASY,EAAK,MAAM,MAC7BqL,EAAW,SAASrL,EAAK,QACzBqL,EAAW,QAAQrL,EAAK,SAAS2F,IAEnC,KAAK,aAAa,IAAI,IAEjB;EACR;;;;;EAKD,gBAAgB,WAA0B;AACxC,QAAI6F,IAAc,KAAK,aACnBxL;AACJ,WAAI,KAAK,SAAS,KAAK,YACrBA,IAAO;MACL,MAAMwL,EAAY;MAClB,KAAKA,EAAY;MACjB,OAAOA,EAAY;MACnB,QAAQA,EAAY;IAC5B,IAEWxL,KAAQ,CAAA;EAChB;;;;;;EAMD,gBAAgB,SAAwBA,GAAM;AAC5C,QAAIwL,IAAc,KAAK,aACnB7F,IAAc,KAAK,QAAQ,aAC3B0J,GACAC;AACJ,WAAI,KAAK,SAAS,KAAK,WAAW,CAAC,KAAK,YAAY7P,IAAcO,CAAI,MAChEZ,EAASY,EAAK,IAAI,MACpBwL,EAAY,OAAOxL,EAAK,OAEtBZ,EAASY,EAAK,GAAG,MACnBwL,EAAY,MAAMxL,EAAK,MAErBZ,EAASY,EAAK,KAAK,KAAKA,EAAK,UAAUwL,EAAY,UACrD6D,IAAe,MACf7D,EAAY,QAAQxL,EAAK,QAEvBZ,EAASY,EAAK,MAAM,KAAKA,EAAK,WAAWwL,EAAY,WACvD8D,IAAgB,MAChB9D,EAAY,SAASxL,EAAK,SAExB2F,MACE0J,IACF7D,EAAY,SAASA,EAAY,QAAQ7F,IAChC2J,MACT9D,EAAY,QAAQA,EAAY,SAAS7F,KAG7C,KAAK,cAAa,IAEb;EACR;;;;;;EAMD,kBAAkB,WAA4B;AAC5C,QAAIzD,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AAClF,QAAI,CAAC,KAAK,SAAS,CAAC,OAAO;AACzB,aAAO;AAET,QAAImJ,IAAa,KAAK,YAClBkE,IAAS9I,GAAgB,KAAK,OAAO,KAAK,WAAW4E,GAAYnJ,CAAO;AAG5E,QAAI,CAAC,KAAK;AACR,aAAOqN;AAET,QAAIC,IAAgB,KAAK,QAAQtN,EAAQ,OAAO,GAC9CuN,IAAWD,EAAc,GACzBE,IAAWF,EAAc,GACzBG,IAAeH,EAAc,OAC7BI,IAAgBJ,EAAc,QAC5B3K,IAAQ0K,EAAO,QAAQ,KAAK,MAAMlE,EAAW,YAAY;AACzDxG,UAAU,MACZ4K,KAAY5K,GACZ6K,KAAY7K,GACZ8K,KAAgB9K,GAChB+K,KAAiB/K;AAEnB,QAAIc,KAAcgK,IAAeC,GAC7BtH,IAAW7C,EAAiB;MAC9B,aAAaE;MACb,OAAOzD,EAAQ,YAAY,IAAA;MAC3B,QAAQA,EAAQ,aAAa,IAAA;IACnC,CAAK,GACGqG,IAAW9C,EAAiB;MAC9B,aAAaE;MACb,OAAOzD,EAAQ,YAAY;MAC3B,QAAQA,EAAQ,aAAa;IAC9B,GAAE,OAAO,GACN0J,IAAoBnG,EAAiB;MACrC,aAAaE;MACb,OAAOzD,EAAQ,UAAU2C,MAAU,IAAI0K,EAAO,QAAQI;MACtD,QAAQzN,EAAQ,WAAW2C,MAAU,IAAI0K,EAAO,SAASK;IACjE,CAAO,GACD/J,IAAQ+F,EAAkB,OAC1BhG,IAASgG,EAAkB;AAC7B/F,QAAQ,KAAK,IAAIyC,EAAS,OAAO,KAAK,IAAIC,EAAS,OAAO1C,CAAK,CAAC,GAChED,IAAS,KAAK,IAAI0C,EAAS,QAAQ,KAAK,IAAIC,EAAS,QAAQ3C,CAAM,CAAC;AACpE,QAAIwC,IAAS,SAAS,cAAc,QAAQ,GACxCC,IAAUD,EAAO,WAAW,IAAI;AACpCA,MAAO,QAAQ5H,GAAuBqF,CAAK,GAC3CuC,EAAO,SAAS5H,GAAuBoF,CAAM,GAC7CyC,EAAQ,YAAYnG,EAAQ,aAAa,eACzCmG,EAAQ,SAAS,GAAG,GAAGxC,GAAOD,CAAM;AACpC,QAAIiK,IAAwB3N,EAAQ,uBAClCuF,IAAwBoI,MAA0B,SAAS,OAAOA,GAClElI,IAAwBzF,EAAQ;AAClCmG,MAAQ,wBAAwBZ,GAC5BE,MACFU,EAAQ,wBAAwBV;AAIlC,QAAImI,KAAcP,EAAO,OACrBQ,IAAeR,EAAO,QAGtBS,IAAOP,GACPQ,IAAOP,GACPQ,GACAC,GAGAC,GACAC,GACAC,GACAC;AACAP,SAAQ,CAACL,KAAgBK,IAAOF,MAClCE,IAAO,GACPE,IAAW,GACXE,IAAO,GACPE,IAAW,KACFN,KAAQ,KACjBI,IAAO,CAACJ,GACRA,IAAO,GACPE,IAAW,KAAK,IAAIJ,IAAaH,IAAeK,CAAI,GACpDM,IAAWJ,KACFF,KAAQF,OACjBM,IAAO,GACPF,IAAW,KAAK,IAAIP,GAAcG,KAAcE,CAAI,GACpDM,IAAWJ,IAETA,KAAY,KAAKD,KAAQ,CAACL,KAAiBK,IAAOF,KACpDE,IAAO,GACPE,IAAY,GACZE,IAAO,GACPE,IAAY,KACHN,KAAQ,KACjBI,IAAO,CAACJ,GACRA,IAAO,GACPE,IAAY,KAAK,IAAIJ,GAAcH,IAAgBK,CAAI,GACvDM,IAAYJ,KACHF,KAAQF,MACjBM,IAAO,GACPF,IAAY,KAAK,IAAIP,GAAeG,IAAeE,CAAI,GACvDM,IAAYJ;AAEd,QAAIvH,IAAS,CAACoH,GAAMC,GAAMC,GAAUC,CAAS;AAG7C,QAAIG,IAAW,KAAKC,IAAY,GAAG;AACjC,UAAIC,IAAQ3K,IAAQ8J;AACpB/G,QAAO,KAAKwH,IAAOI,GAAOH,IAAOG,GAAOF,IAAWE,GAAOD,IAAYC,CAAK;IAC5E;AAID,WAAAnI,EAAQ,UAAU,MAAMA,GAAS,CAACkH,CAAM,EAAE,OAAOrU,GAAmB0N,EAAO,IAAI,SAAUC,GAAO;AAC9F,aAAO,KAAK,MAAMrI,GAAuBqI,CAAK,CAAC;IACrD,CAAK,CAAC,CAAC,CAAC,GACGT;EACR;;;;;;EAMD,gBAAgB,SAAwBzC,GAAa;AACnD,QAAIzD,IAAU,KAAK;AACnB,WAAI,CAAC,KAAK,YAAY,CAAC5C,GAAYqG,CAAW,MAE5CzD,EAAQ,cAAc,KAAK,IAAI,GAAGyD,CAAW,KAAK,KAC9C,KAAK,UACP,KAAK,YAAW,GACZ,KAAK,WACP,KAAK,cAAa,KAIjB;EACR;;;;;;EAMD,aAAa,SAAqB8K,GAAM;AACtC,QAAIvO,IAAU,KAAK,SACjBwO,IAAU,KAAK,SACfC,IAAO,KAAK;AACd,QAAI,KAAK,SAAS,CAAC,KAAK,UAAU;AAChC,UAAIC,IAAYH,MAASjT,IACrBqT,IAAU3O,EAAQ,WAAWuO,MAAShT;AAC1CgT,UAAOG,KAAaC,IAAUJ,IAAO/S,IACrCwE,EAAQ,WAAWuO,GACnB9O,GAAQ+O,GAASpT,IAAamT,CAAI,GAClCpP,GAAYqP,GAAS3T,IAAY6T,CAAS,GAC1CvP,GAAYqP,GAASrT,IAAYwT,CAAO,GACnC3O,EAAQ,mBAEXP,GAAQgP,GAAMrT,IAAamT,CAAI,GAC/BpP,GAAYsP,GAAM5T,IAAY6T,CAAS,GACvCvP,GAAYsP,GAAMtT,IAAYwT,CAAO;IAExC;AACD,WAAO;EACR;AACH;AArtDA,IAutDIC,KAAiB/U,EAAO;AAvtD5B,IAwtDIgV,KAAuB,WAAY;AAMrC,WAASA,EAAQnQ,GAAS;AACxB,QAAIsB,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AAElF,QADA7H,GAAgB,MAAM0W,CAAO,GACzB,CAACnQ,KAAW,CAAC9B,GAAgB,KAAK8B,EAAQ,OAAO;AACnD,YAAM,IAAI,MAAM,0EAA0E;AAE5F,SAAK,UAAUA,GACf,KAAK,UAAUV,EAAO,CAAE,GAAEjB,IAAUQ,IAAcyC,CAAO,KAAKA,CAAO,GACrE,KAAK,UAAU,OACf,KAAK,WAAW,OAChB,KAAK,WAAW,CAAA,GAChB,KAAK,QAAQ,OACb,KAAK,YAAY,OACjB,KAAK,WAAW,OAChB,KAAK,QAAQ,OACb,KAAK,SAAS,OACd,KAAK,KAAI;EACV;AACD,SAAOtH,GAAamW,GAAS,CAAC;IAC5B,KAAK;IACL,OAAO,WAAgB;AACrB,UAAInQ,IAAU,KAAK,SACfoQ,IAAUpQ,EAAQ,QAAQ,YAAW,GACrCuC;AACJ,UAAI,CAAAvC,EAAQ1E,CAAS,GAIrB;AAAA,YADA0E,EAAQ1E,CAAS,IAAI,MACjB8U,MAAY,OAAO;AAQrB,cAPA,KAAK,QAAQ,MAGb7N,IAAMvC,EAAQ,aAAa,KAAK,KAAK,IACrC,KAAK,cAAcuC,GAGf,CAACA;AACH;AAIFA,cAAMvC,EAAQ;QACf;AAAUoQ,gBAAY,YAAY,OAAO,sBACxC7N,IAAMvC,EAAQ,UAAA;AAEhB,aAAK,KAAKuC,CAAG;MAAA;IACd;EACL,GAAK;IACD,KAAK;IACL,OAAO,SAAcA,GAAK;AACxB,UAAIqK,IAAQ;AACZ,UAAKrK,GAGL;AAAA,aAAK,MAAMA,GACX,KAAK,YAAY,CAAA;AACjB,YAAIvC,IAAU,KAAK,SACjBsB,IAAU,KAAK;AAMjB,YALI,CAACA,EAAQ,aAAa,CAACA,EAAQ,aACjCA,EAAQ,mBAAmB,QAIzB,CAACA,EAAQ,oBAAoB,CAAC,OAAO,aAAa;AACpD,eAAK,MAAK;AACV;QACD;AAGD,YAAItD,GAAgB,KAAKuE,CAAG,GAAG;AAEzBtE,aAAqB,KAAKsE,CAAG,IAC/B,KAAK,KAAKkG,GAAqBlG,CAAG,CAAC,IAInC,KAAK,MAAK;AAEZ;QACD;AAID,YAAI8N,IAAM,IAAI,eAAA,GACVC,IAAQ,KAAK,MAAM,KAAK,IAAI;AAChC,aAAK,YAAY,MACjB,KAAK,MAAMD,GAMXA,EAAI,UAAUC,GACdD,EAAI,UAAUC,GACdD,EAAI,YAAYC,GAChBD,EAAI,aAAa,WAAY;AAEvBA,YAAI,kBAAkB,cAAc,MAAMvS,MAC5CuS,EAAI,MAAK;QAEnB,GACMA,EAAI,SAAS,WAAY;AACvBzD,YAAM,KAAKyD,EAAI,QAAQ;QAC/B,GACMA,EAAI,YAAY,WAAY;AAC1BzD,YAAM,YAAY,OAClBA,EAAM,MAAM;QACpB,GAGUtL,EAAQ,oBAAoBgB,GAAiBC,CAAG,KAAKvC,EAAQ,gBAC/DuC,IAAME,GAAaF,CAAG,IAIxB8N,EAAI,KAAK,OAAO9N,GAAK,IAAI,GACzB8N,EAAI,eAAe,eACnBA,EAAI,kBAAkBrQ,EAAQ,gBAAgB,mBAC9CqQ,EAAI,KAAI;MAAA;IACT;EACL,GAAK;IACD,KAAK;IACL,OAAO,SAAcxH,GAAa;AAChC,UAAIvH,IAAU,KAAK,SACjB8I,IAAY,KAAK,WAIfhB,IAAcD,GAAuBN,CAAW,GAChDhG,IAAS,GACTC,IAAS,GACTC,IAAS;AACb,UAAIqG,IAAc,GAAG;AAEnB,aAAK,MAAML,GAAqBF,GAAa/K,EAAc;AAC3D,YAAIyS,IAAoBxG,GAAiBX,CAAW;AACpDvG,YAAS0N,EAAkB,QAC3BzN,IAASyN,EAAkB,QAC3BxN,IAASwN,EAAkB;MAC5B;AACGjP,QAAQ,cACV8I,EAAU,SAASvH,IAEjBvB,EAAQ,aACV8I,EAAU,SAAStH,GACnBsH,EAAU,SAASrH,IAErB,KAAK,MAAK;IACX;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAiB;AACtB,UAAI/C,IAAU,KAAK,SACjBuC,IAAM,KAAK,KACTsJ,IAAc7L,EAAQ,aACtBwQ,IAAiBjO;AACjB,WAAK,QAAQ,oBAAoBD,GAAiBC,CAAG,MAClDsJ,MACHA,IAAc,cAIhB2E,IAAiB/N,GAAaF,CAAG,IAEnC,KAAK,cAAcsJ,GACnB,KAAK,iBAAiB2E;AACtB,UAAI1K,IAAQ,SAAS,cAAc,KAAK;AACpC+F,YACF/F,EAAM,cAAc+F,IAEtB/F,EAAM,MAAM0K,KAAkBjO,GAC9BuD,EAAM,MAAM9F,EAAQ,OAAO,qBAC3B,KAAK,QAAQ8F,GACbA,EAAM,SAAS,KAAK,MAAM,KAAK,IAAI,GACnCA,EAAM,UAAU,KAAK,KAAK,KAAK,IAAI,GACnCzF,EAASyF,GAAOxJ,EAAU,GAC1B0D,EAAQ,WAAW,aAAa8F,GAAO9F,EAAQ,WAAW;IAC3D;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAiB;AACtB,UAAIyQ,IAAS,MACT3K,IAAQ,KAAK;AACjBA,QAAM,SAAS,MACfA,EAAM,UAAU,MAChB,KAAK,SAAS;AAId,UAAI4K,IAAcvV,EAAO,aAAa,sCAAsC,KAAKA,EAAO,UAAU,SAAS,GACvGwV,IAAO,SAAcnK,GAAcC,IAAe;AACpDnH,UAAOmR,EAAO,WAAW;UACvB,cAAcjK;UACd,eAAeC;UACf,aAAaD,IAAeC;QACtC,CAAS,GACDgK,EAAO,mBAAmBnR,EAAO,CAAE,GAAEmR,EAAO,SAAS,GACrDA,EAAO,SAAS,OAChBA,EAAO,QAAQ,MACfA,EAAO,MAAK;MACpB;AAGM,UAAI3K,EAAM,gBAAgB,CAAC4K,GAAa;AACtCC,UAAK7K,EAAM,cAAcA,EAAM,aAAa;AAC5C;MACD;AACD,UAAI8K,IAAc,SAAS,cAAc,KAAK,GAC1CC,IAAO,SAAS,QAAQ,SAAS;AACrC,WAAK,cAAcD,GACnBA,EAAY,SAAS,WAAY;AAC/BD,UAAKC,EAAY,OAAOA,EAAY,MAAM,GACrCF,KACHG,EAAK,YAAYD,CAAW;MAEtC,GACMA,EAAY,MAAM9K,EAAM,KAInB4K,MACHE,EAAY,MAAM,UAAU,wJAC5BC,EAAK,YAAYD,CAAW;IAE/B;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAgB;AACrB,UAAI9K,IAAQ,KAAK;AACjBA,QAAM,SAAS,MACfA,EAAM,UAAU,MAChBA,EAAM,WAAW,YAAYA,CAAK,GAClC,KAAK,QAAQ;IACd;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAiB;AACtB,UAAI,EAAA,CAAC,KAAK,SAAS,KAAK,QAGxB;AAAA,YAAI9F,IAAU,KAAK,SACjBsB,IAAU,KAAK,SACfwE,IAAQ,KAAK,OAGXmE,IAAYjK,EAAQ,YACpB8Q,IAAW,SAAS,cAAc,KAAK;AAC3CA,UAAS,YAAYxS;AACrB,YAAI4L,IAAU4G,EAAS,cAAc,IAAI,OAAOxV,GAAW,YAAY,CAAC,GACpEkM,IAAS0C,EAAQ,cAAc,IAAI,OAAO5O,GAAW,SAAS,CAAC,GAC/DwU,IAAU5F,EAAQ,cAAc,IAAI,OAAO5O,GAAW,WAAW,CAAC,GAClEyV,KAAU7G,EAAQ,cAAc,IAAI,OAAO5O,GAAW,WAAW,CAAC,GAClEyU,IAAOgB,GAAQ,cAAc,IAAI,OAAOzV,GAAW,OAAO,CAAC;AAC/D,aAAK,YAAY2O,GACjB,KAAK,UAAUC,GACf,KAAK,SAAS1C,GACd,KAAK,UAAUsI,GACf,KAAK,UAAUiB,IACf,KAAK,UAAU7G,EAAQ,cAAc,IAAI,OAAO5O,GAAW,WAAW,CAAC,GACvE,KAAK,OAAOyU,GACZvI,EAAO,YAAY1B,CAAK,GAGxBzF,EAASL,GAAS3D,CAAY,GAG9B4N,EAAU,aAAaC,GAASlK,EAAQ,WAAW,GAGnDQ,EAAYsF,GAAOxJ,EAAU,GAC7B,KAAK,YAAW,GAChB,KAAK,KAAI,GACTgF,EAAQ,qBAAqB,KAAK,IAAI,GAAGA,EAAQ,kBAAkB,KAAK,KACxEA,EAAQ,cAAc,KAAK,IAAI,GAAGA,EAAQ,WAAW,KAAK,KAC1DA,EAAQ,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,MAAMA,EAAQ,QAAQ,CAAC,CAAC,KAAK,GAC7EjB,EAAS0Q,IAAS1U,CAAY,GACzBiF,EAAQ,UACXjB,EAAS0Q,GAAQ,uBAAuB,GAAG,OAAOzV,GAAW,SAAS,CAAC,GAAGe,CAAY,GAEnFiF,EAAQ,UACXjB,EAAS0Q,GAAQ,uBAAuB,GAAG,OAAOzV,GAAW,SAAS,CAAC,GAAGe,CAAY,GAEpFiF,EAAQ,cACVjB,EAAS6J,GAAS,GAAG,OAAO5O,GAAW,KAAK,CAAC,GAE1CgG,EAAQ,aACXjB,EAAS0P,GAAMxT,EAAe,GAE5B+E,EAAQ,mBACVjB,EAAS0P,GAAMtT,EAAU,GACzBsE,GAAQgP,GAAMrT,IAAanB,EAAU,IAElC+F,EAAQ,qBACXjB,EAAS0Q,GAAQ,uBAAuB,GAAG,OAAOzV,GAAW,OAAO,CAAC,GAAGe,CAAY,GACpFgE,EAAS0Q,GAAQ,uBAAuB,GAAG,OAAOzV,GAAW,QAAQ,CAAC,GAAGe,CAAY,IAEvF,KAAK,OAAM,GACX,KAAK,QAAQ,MACb,KAAK,YAAYiF,EAAQ,QAAQ,GAC7BA,EAAQ,YACV,KAAK,KAAI,GAEX,KAAK,QAAQA,EAAQ,IAAI,GACrBtC,EAAWsC,EAAQ,KAAK,KAC1BM,EAAY5B,GAAStC,IAAa4D,EAAQ,OAAO;UAC/C,MAAM;QAChB,CAAS,GAEHW,GAAcjC,GAAStC,EAAW;MAAA;IACnC;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAmB;AACxB,UAAK,KAAK,OAGV;AAAA,aAAK,QAAQ,OACb,KAAK,OAAM,GACX,KAAK,aAAY;AACjB,YAAIsT,IAAa,KAAK,QAAQ;AAC1BA,aACFA,EAAW,YAAY,KAAK,OAAO,GAErCxQ,EAAY,KAAK,SAASnE,CAAY;MAAA;IACvC;EACL,GAAK;IACD,KAAK;IACL,OAAO,WAAoB;AACrB,WAAK,SACP,KAAK,QAAO,GACZ,KAAK,QAAQ,OACb,KAAK,UAAU,SACN,KAAK,UACd,KAAK,YAAY,SAAS,MAC1B,KAAK,SAAS,OACd,KAAK,QAAQ,SACJ,KAAK,aACd,KAAK,IAAI,UAAU,MACnB,KAAK,IAAI,MAAA,KACA,KAAK,SACd,KAAK,KAAI;IAEZ;;;;;EAMF,CAAA,GAAG,CAAC;IACH,KAAK;IACL,OAAO,WAAsB;AAC3B,aAAA,OAAO,UAAU6T,IACVC;IACR;;;;;EAML,GAAK;IACD,KAAK;IACL,OAAO,SAAqB7O,GAAS;AACnChC,QAAOjB,IAAUQ,IAAcyC,CAAO,KAAKA,CAAO;IACnD;EACF,CAAA,CAAC;AACJ,EAAA;AACAhC,EAAO6Q,GAAQ,WAAWnG,IAAQ4B,IAASY,IAAQC,IAAUS,IAAQU,EAAO;AC3rG5E,IAAMqD,KAAkB,OAAO,SAAW,MAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO,SAAS,QAAQ;AAA3G,IAEeC,KAAA;EACb,SAAS;AACP,UAAMC,IAAc,KAAK,eAAe;AAExC,WAAOC,EAAE,OAAO,EAAE,OAAO,KAAK,eAAc,GAAI;MAC9CA,EAAE,OAAO;QACP,KAAK;QACL,KAAK,KAAK;QACV,KAAK,KAAK,OAAO;QACjB,OAAO,CAAC,EAAE,aAAa,OAAM,GAAI,KAAK,QAAQ;QAC9C,aAAAD;MACR,CAAO;IACP,CAAK;EACF;EACD,OAAO;;IAEL,gBAAgB;IAChB,KAAK;MACH,MAAM;MACN,SAAS;IACV;IACD,KAAK;IACL,UAAU;;IAGV,UAAU;IACV,UAAU;IACV,oBAAoB;IACpB,aAAa;IACb,MAAM;IACN,SAASF;IACT,YAAY;MACV,MAAM;MACN,SAAS;IACV;IACD,SAAS;MACP,MAAM;MACN,SAAS;IACV;IACD,kBAAkB;MAChB,MAAM;MACN,SAAS;IACV;IACD,kBAAkB;MAChB,MAAM;MACN,SAAS;IACV;IACD,aAAa;MACX,MAAM;IACP;IACD,OAAO;MACL,MAAM;MACN,SAAS;IACV;IACD,QAAQ;MACN,MAAM;MACN,SAAS;IACV;IACD,QAAQ;MACN,MAAM;MACN,SAAS;IACV;IACD,WAAW;MACT,MAAM;MACN,SAAS;IACV;IACD,YAAY;MACV,MAAM;MACN,SAAS;IACV;IACD,UAAU;MACR,MAAM;MACN,SAAS;IACV;IACD,cAAc;IACd,SAAS;MACP,MAAM;MACN,SAAS;IACV;IACD,WAAW;MACT,MAAM;MACN,SAAS;IACV;IACD,UAAU;MACR,MAAM;MACN,SAAS;IACV;IACD,UAAU;MACR,MAAM;MACN,SAAS;IACV;IACD,aAAa;MACX,MAAM;MACN,SAAS;IACV;IACD,aAAa;MACX,MAAM;MACN,SAAS;IACV;IACD,gBAAgB;IAChB,gBAAgB;MACd,MAAM;MACN,SAAS;IACV;IACD,kBAAkB;MAChB,MAAM;MACN,SAAS;IACV;IACD,0BAA0B;MACxB,MAAM;MACN,SAAS;IACV;;IAGD,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;;IAGpB,OAAO;IACP,WAAW;IACX,UAAU;IACV,SAAS;IACT,MAAM;IACN,MAAM;EACP;EACD,UAAU;AACR,UAAM,EAAE,gBAAAI,GAAgB,KAAAC,GAAK,KAAAxF,GAAK,UAAAyF,GAAU,GAAGnS,EAAAA,IAAS,KAAK,SAAS,OAChEtF,IAAQ,CAAA;AAEd,eAAWM,KAAOgF;AACZ,WAAKhF,CAAG,MAAM,WAChBN,EAAMM,CAAG,IAAI,KAAKA,CAAG;AAIzB,SAAK,UAAU,IAAI+V,GAAQ,KAAK,MAAM,KAAKrW,CAAK;EACjD;EACD,SAAS;;IAEP,QAAQ;AACN,aAAO,KAAK,QAAQ,MAAA;IACrB;;IAGD,QAAQ;AACN,aAAO,KAAK,QAAQ,MAAA;IACrB;;IAGD,WAAW;AACT,aAAO,KAAK,QAAQ,KAAA;IACrB;;;;;;;IAQD,QAAQyI,GAAKiP,IAAmB,OAAO;AACrC,aAAO,KAAK,QAAQ,QAAQjP,GAAKiP,CAAgB;IAClD;;IAGD,SAAS;AACP,aAAO,KAAK,QAAQ,OAAA;IACrB;;IAGD,UAAU;AACR,aAAO,KAAK,QAAQ,QAAA;IACrB;;IAGD,UAAU;AACR,aAAO,KAAK,QAAQ,QAAA;IACrB;;;;;;;IAQD,KAAK1D,GAASC,GAAS;AACrB,aAAO,KAAK,QAAQ,KAAKD,GAASC,CAAO;IAC1C;;;;;;;IAQD,OAAOE,GAAGC,IAAID,GAAG;AACf,aAAO,KAAK,QAAQ,OAAOA,GAAGC,CAAC;IAChC;;;;;;;IAQD,aAAajK,GAAOkK,GAAgB;AAClC,aAAO,KAAK,QAAQ,KAAKlK,GAAOkK,CAAc;IAC/C;;;;;;;IAQD,OAAOlK,GAAOkK,GAAgB;AAC5B,aAAO,KAAK,QAAQ,OAAOlK,GAAOkK,CAAc;IACjD;;;;;;IAOD,OAAO5I,GAAQ;AACb,aAAO,KAAK,QAAQ,OAAOA,CAAM;IAClC;;;;;;IAOD,SAASA,GAAQ;AACf,aAAO,KAAK,QAAQ,SAASA,CAAM;IACpC;;;;;;IAOD,OAAOzC,GAAQ;AACb,aAAO,KAAK,QAAQ,OAAOA,CAAM;IAClC;;;;;;IAOD,OAAOC,GAAQ;AACb,aAAO,KAAK,QAAQ,OAAOA,CAAM;IAClC;;;;;;;IAQD,MAAMD,GAAQC,IAASD,GAAQ;AAC7B,aAAO,KAAK,QAAQ,MAAMA,GAAQC,CAAM;IACzC;;;;;;IAOD,QAAQyL,IAAU,OAAO;AACvB,aAAO,KAAK,QAAQ,QAAQA,CAAO;IACpC;;;;;;IAOD,QAAQpP,GAAM;AACZ,aAAO,KAAK,QAAQ,QAAQA,CAAI;IACjC;;;;;IAMD,mBAAmB;AACjB,aAAO,KAAK,QAAQ,iBAAA;IACrB;;;;;IAMD,eAAe;AACb,aAAO,KAAK,QAAQ,aAAA;IACrB;;;;;IAMD,gBAAgB;AACd,aAAO,KAAK,QAAQ,cAAA;IACrB;;;;;;IAOD,cAAcA,GAAM;AAClB,aAAO,KAAK,QAAQ,cAAcA,CAAI;IACvC;;;;;IAMD,iBAAiB;AACf,aAAO,KAAK,QAAQ,eAAA;IACrB;;;;;;IAOD,eAAeA,GAAM;AACnB,aAAO,KAAK,QAAQ,eAAeA,CAAI;IACxC;;;;;;IAOD,iBAAiBkC,IAAU,CAAA,GAAI;AAC7B,aAAO,KAAK,QAAQ,iBAAiBA,CAAO;IAC7C;;;;;;IAOD,eAAeyD,GAAa;AAC1B,aAAO,KAAK,QAAQ,eAAeA,CAAW;IAC/C;;;;;;IAOD,YAAY8K,GAAM;AAChB,aAAO,KAAK,QAAQ,YAAYA,CAAI;IACrC;EACF;AACH;ACnTA,IAAA4B,KAAeC,gBAAgB;EAC7B,MAAM;EACN,YAAY;IACV,YAAAR;EACF;EACA,OAAO;;IAEL,OAAO;MACL,MAAM;IACR;;IAEA,eAAe;MACb,MAAM,CAAC,QAAQ,MAAM;IACvB;;IAEA,aAAa;MACX,MAAM,CAAC,QAAQ,MAAM;MACrB,SAAS;IACX;;IAEA,SAAS;MACP,MAAM;MACN,SAAS;IACX;;IAEA,WAAW;MACT,MAAM;IACR;;IAEA,SAAS;MACP,MAAM;IACR;;IAEA,QAAQ;MACN,MAAM;MACN,SAAS;IACX;;IAEA,QAAQ;MACN,MAAM;MACN,SAAS;;IACX;IACA,iBAAiB;MACf,MAAM;MACN,SAAS;IACX;EACF;EACA,OAAO,CAAC,UAAU,QAAQ,OAAO;EACjC,MAAMpX,GAAY6X,GAAK;AACf,UAAA,EAAE,IAAAC,EAAAA,IAAOC,EAAAA,GACT,EAAE,GAAA5Y,EAAAA,IAAM6Y,GAAAA,GACRC,IAAqBC,IAAI,KAAK,GAC9BC,IAAkBD,IAAAA,GAClBE,IAAoBF,IAAAA,GACpBG,IAAgBH,IAAI,KAAK,GACzBI,IAAcJ,IAAAA,GACd5S,IAAY4S,IAAAA,GACZK,KAAYL,IAAAA,GACZpC,IAAaoC,IAAI;MACrB,GAAG;MACH,GAAG;IAAA,CACJ;AAED,aAASM,IAAc;AACrBP,QAAc,QAAQ;IACxB;AAEA,aAASQ,IAAe;AAChBC,QAAAA,GACNb,EAAI,KAAK,QAAQ;IACnB;AACA,UAAMc,IAAUb,EAAG,OAAO,kBAAkBW,CAAY,GAClDG,IAAcd,EAAG,OAAO,aACxBe,IAAqBC,SAAS,OAC3B;MACL,GAAGH;MACH,CAACC,CAAW,GAAG;MACf,GAAGd,EAAG,YAAY,eAAeA,EAAG,OAAO,MAAM,OAAO;MACxD,GAAGA,EAAG,YAAY,cAAcA,EAAG,OAAO,IAAI;MAC9C,OAAO9X,EAAM,SAASb,EAAE,0BAA0B;IAAA,EAErD;AAED,aAAS4Z,EAAKtQ,GAAa;AACzBwP,QAAc,QAAQ,MAClBxP,KAAO,QAAQA,MAAQ,OACzB6P,EAAO,QAAQ7P;IAEnB;AACA,aAASuQ,IAAQ;AACff,QAAc,QAAQ;IACxB;AACA,aAASS,IAAQ;AACfL,QAAS,QAAQ,OACbD,EAAa,SAAS,SACxBA,EAAa,MAAM,QAAQ,MAC3BA,EAAa,QAAQ,OAEnBD,EAAW,SAAS,QACtBA,EAAW,MAAM,MAAA;IAErB;AAEA,aAASc,IAAgB;AACvB,aAAOd,EAAW;IACpB;AACA,UAAMe,KAAQ;MACZ,SAASD,EAAc;MACvB,MAAAE;MACA,OAAAT;MACA,OAAAM;MACA,MAAAD;IAAA;AAGF,aAASK,EAAMxR,GAAY;AAEzBiQ,QAAI,KAAK,SAAS;QAChB,OAAAjQ;QACA,GAAGsR;MAAA,CACJ;IACH;AACA,aAASG,EAAepa,GAAQ;AAC9B,aAAAA,EAAE,eAAe,GACV;IACT;AAEA,aAASqa,IAAc;AACrBlB,QAAa,MAAM,MAAA;IACrB;AAGA,aAASmB,EAAUhB,GAAY;AAE7B,aAAIA,EAAK,KAAK,QAAQ,OAAO,MAAM,MAC9BT,EAAA,QAAQ,KAAK,YAAY,GACrB,SAGL9X,EAAM,UAAU,KAAKuY,EAAK,OAAO,OAAO,OAAOvY,EAAM,WACvD8X,EAAG,QAAQ,KAAK,cAAc9X,EAAM,OAAO,YAAY,GAChD,SAEF;IACT;AAEA,aAASwZ,EAASva,GAAQ;AACxB,YAAMwa,IAAaxa,EAAE,OAAO,MAAM,CAAC;AACnC,UAAIwa,EAAW,KAAK,QAAQ,QAAQ,MAAM,IAAI;AACzC3B,UAAA,QAAQ,KAAK,6BAA6B;AAC7C;MACF;AACI,UAAA,OAAO,cAAe,YAAY;AAC9B,cAAA4B,IAAS,IAAI,WAAA;AACZA,UAAA,SAAS,CAAC9R,OAAU;AAClB0Q,YAAA,QAAQ1Q,GAAM,OAAO,QAE5BuQ,EAAW,MAAM,QAAQvQ,GAAM,OAAO,MAAM;QAAA,GAE9C8R,EAAO,cAAcD,CAAU;MAAA;AAE5B3B,UAAA,QAAQ,MAAM,qCAAqC;IAE1D;AAEA,aAAS6B,EAAa1a,GAAQ;AAC5BA,QAAE,eAAe;AACjB,YAAM2a,IAAQ3a,EAAE,OAAO,SAASA,EAAE,aAAa;AAC/C,UAAI2a,KAAS;AACX;AAEFvB,QAAS,QAAQ;AACX,YAAAwB,IAAeD,EAAM,CAAC;AACxBL,QAAUM,CAAY,MACxBtB,GAAK,QAAQsB,GACbL,EAASva,CAAC;IAKd;AAES,aAAA6a,EAAoBC,GAAkBC,GAAe;AAE5D,aAAIA,KAAW,SACbA,IAAUha,EAAM,kBAEXmY,EAAW,MAAM,iBAAA,EAAmB,UAAU4B,GAAUC,CAAO;IACxE;AACe,mBAAAC,EAAiBvS,GAAcsS,GAAe;AAC3D,aAAIA,KAAW,SACbA,IAAUha,EAAM,kBAGX,IAAI,QAAQ,CAACka,GAASC,OAAW;AACtC,iBAAS5U,GAAS6U,IAAW;AAC3BF,YAAQE,EAAI;QACd;AACAjC,UAAW,MAAM,iBAAiB,EAAE,OAAO5S,IAAUmC,GAAMsS,CAAO;MAAA,CACnE;IACH;AACA,aAASK,EAAKC,GAAa;AACrBzC,QAAA,KAAK,QAAQyC,CAAM;IACzB;AACA,mBAAeC,EAAShC,GAAY;AAC5B,YAAAiC,IAAW,EAAE,MAAAjC,EAAAA;AACf,UAAAvY,EAAM,WAAW,OAAO;AAC1B,cAAMoa,IAAO,MAAMH,EAAiB1B,EAAK,IAAI,GACvCkC,KAAUX,EAAoBvB,EAAK,IAAI;AAC7CiC,UAAI,OAAOJ,GACXI,EAAI,UAAUC,IACdJ,EAAKG,CAAG;AACR;MACF;AAEI,UAAAxa,EAAM,WAAW,QAAQ;AAC3Bwa,UAAI,OAAO,MAAMP,EAAiB1B,EAAK,IAAI,GAC3C8B,EAAKG,CAAG;AACR;MACF;AACIxa,QAAM,WAAW,cACfwa,EAAA,UAAUV,EAAoBvB,EAAK,IAAI,GAC3C8B,EAAKG,CAAG;IAEZ;AAEA,mBAAeE,IAAY;AACrB,UAAA,CAACrC,EAAS,OAAO;AAChBP,UAAA,QAAQ,KAAK,QAAQ;AACxB;MACF;AACM,YAAAyC,EAAShC,GAAK,KAAK,GACzBN,EAAc,QAAQ;IACxB;AAEA,aAAS0C,IAAQ;AACfxC,QAAW,MAAM,OAAQrC,EAAM,MAAM,KAAK,EAAG;IAC/C;AACA,aAAS8E,KAAQ;AACfzC,QAAW,MAAM,OAAQrC,EAAM,MAAM,KAAK,EAAG;IAC/C;AACA,aAAS+E,KAAiB;AACnBvV,QAAA,QAAQ,KAAK,UAAU6S,EAAW,MAAM,eAAe,GAAG,MAAM,CAAC;IACxE;AACA,aAASpR,KAAU;AACZzB,QAAA,QAAQ,KAAK,UAAU6S,EAAW,MAAM,QAAQ,GAAG,MAAM,CAAC;IACjE;AACS,aAAA2C,GAAK9G,GAAcC,GAAc;AAC7BkE,QAAA,MAAM,KAAKnE,GAASC,CAAO;IACxC;AACA,aAAS8G,KAAQ;AACf5C,QAAW,MAAM,MAAA;IACnB;AACA,aAASpP,GAAOiS,GAAU;AACb7C,QAAA,MAAM,OAAO6C,CAAG;IAC7B;AACA,aAASC,KAAiB;AACxB9C,QAAW,MAAM,eAAe,KAAK,MAAM7S,EAAK,KAAK,CAAC;IACxD;AACA,aAAS2B,KAAU;AACjBkR,QAAW,MAAM,QAAQ,KAAK,MAAM7S,EAAK,KAAK,CAAC;IACjD;AAEA,aAAS4V,KAAkB;AACzB9C,QAAa,MAAM,MAAA;IACrB;AACA,aAASe,GAAKgC,GAAc;AACfhD,QAAA,MAAM,aAAagD,CAAO;IACvC;AAEM,UAAAC,KAAkBtC,SAAS,MAAM;AACrC,YAAMuC,IAAO;AAuEN,aArES;QACd;UACE,MAAAA;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,MAAM3Y,EAAE,6BAA6B;UACrC,UAAU;AACIma,cAAAA;UACd;QACF;QACA;UACE,MAAA+B;UACA,OAAA;UACA,MAAMlc,EAAE,0BAA0B;UAClC,UAAU;AACFwb,cAAAA;UACR;QACF;QACA;UACE,MAAAU;UACA,OAAA;UACA,MAAMlc,EAAE,0BAA0B;UAClC,UAAU;AACFyb,eAAAA;UACR;QACF;QACA;UACE,MAAAS;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,UAAU;AACRqB,eAAK,GAAG;UACV;QACF;QACA;UACE,MAAAkC;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,UAAU;AACRqB,eAAK,IAAI;UACX;QACF;QACA;UACE,MAAAkC;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,UAAU;AACR/O,eAAO,EAAE;UACX;QACF;QACA;UACE,MAAAsS;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,UAAU;AACR/O,eAAO,GAAG;UACZ;QACF;QACA;UACE,MAAAsS;UACA,OAAA;UACA,MAAMvD,EAAG,MAAM;UACf,MAAM3Y,EAAE,0BAA0B;UAClC,UAAU;AACF4b,eAAAA;UACR;QACF;MAAA;IAGK,CACR,GAEKO,KAAgBxC,SAAS,OACtB;MACL,OAAO3Z,EAAE,0BAA0B;MACnC,SAASA,EAAE,4BAA4B;MACvC,QAAQA,EAAE,2BAA2B;MACrC,SAASA,EAAE,4BAA4B;MACvC,aAAaA,EAAE,gCAAgC;IAAA,EAElD,GAEKoc,KAAoBzC,SAAS,MAC7B9Y,EAAM,aAAa,QAAQA,EAAM,cAAc,KAC1CA,EAAM,YAEXA,EAAM,UAAU,IACX,GAAGb,EAAE,gCAAgC,CAAC,IAAIa,EAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;UAC9Eb,EAAE,8BAA8B,CAAC,IAAIa,EAAM,OAAO,MAE7C,GAAGb,EAAE,gCAAgC,CAAC,GAAGa,EAAM,MAAM,IAAIb,EAAE,gCAAgC,CAAC,EAEtG;AACM,WAAA;MACL,IAAA2Y;MACA,YAAAK;MACA,cAAAC;MACA,eAAAH;MACA,eAAAY;MACA,UAAAR;MACA,QAAAC;MACA,MAAAhT;MACA,MAAAiT;MACA,OAAAzC;MACA,iBAAAsF;MACA,aAAA5C;MACA,SAAAvR;MACA,cAAAwR;MACA,OAAAO;MACA,iBAAAkC;MACA,MAAA/B;MACA,gBAAA8B;MACA,QAAAlS;MACA,OAAAgS;MACA,MAAAD;MACA,SAAA/T;MACA,gBAAA8T;MACA,OAAAD;MACA,OAAAD;MACA,WAAAD;MACA,UAAAH;MACA,kBAAAN;MACA,qBAAAH;MACA,cAAAH;MACA,UAAAH;MACA,WAAAD;MACA,aAAAD;MACA,gBAAAD;MACA,MAAAN;MACA,OAAAL;MACA,eAAAO;MACA,OAAAG;MACA,eAAAkC;MACA,mBAAAC;IAAA;EAEJ;EACA,OAAO;AACL,WAAO,CAAA;EACT;EACA,UAAU;IACR,WAAW;AACT,YAAMC,IAAW;QACf,aAAa;QACb,OAAO,KAAK;MAAA;AAEV,aAAA,KAAK,WAAW,OACXA,IAEM,OAAO,OAAOA,GAAK,KAAK,OAAO;IAEhD;IACA,iBAAiB;AACf,UAAItQ,IAAS,KAAK;AAOd,aANAA,KAAU,SACHA,IAAA,SAAS,gBAAgB,eAAe,MAC7CA,IAAS,QACFA,IAAA,OAGT,OAAOA,KAAW,WACbA,IAAS,OAEXA;IACT;IACA,eAAe;AACb,UAAIC,IAAQ,KAAK;AAIb,aAHAA,KAAS,SACHA,IAAA,QAEN,OAAOA,KAAU,WACZA,IAAQ,OAEVA;IACT;EACF;AACF,CAAC;WC9e6B,OAAM,yBAAA;IAKDsQ,KAAA,CAAA,QAAA;IACtBC,KAAA,EAAA,OAAM,mDAAA;IASNC,KAAA,EAAA,OAAM,iDAAA;IAMRC,KAAA,EAAA,OAAM,+BAAA;IAAA,KAAA,EACH,OAAM,WAAA;IAOTC,KAAA,EAAA,OAAM,6BAAA;IAAA,KAAA,EAAA,OAAA,mCAAA;IAAA,KAAA,EAAA,OAAA,gBAAA;;2CA1CfC,IAAAC,iBADF,aAESjE;AAEGA,SAAAA,UAAAA,GAAAA,YAAqBG,wBAAa+D,EAAA,GAAA,OAAA,IAAA,GAAAC,WAAA;IAAA,KAAA;IAC1C,CAAcD,EAAA,GAAA,OAAA,OAAA,GAAAA,EAAA;IACd,CAAa,cAAAA,EAAA,GAAA,OAAA,OAAA,GAAAE,EAAA,CAAA,MAAAA,EAAA,CAAA,IAAA,CAAAC,MAAAH,EAAA,gBAAAG;IACZ,kBAAA;IACOtD,OAAAA;IAkCG,wBAIH;EAAA,GAAAmD,EAHN,eAGM,EAAA,oBAAA,MAAA,CAAA,GAAA;IAAA,QAFJI,QAA4E,MAAA;MAArDC,gBAAA,OAAAC,IAAA;QAAAC,YAAQjB,GAAoB;UAAG,MAAA;UAAA,MAAAU,EAAA,cAAA;UACtD,SAA4FA,EAAA;QAAA,GAAjF,MAAK,GAAS,CAAA,QAAA,SAAA,CAAA;QAAAO,YAAMC,GAAO;UAAE,MAAMlB;UAAwB,MAAA;UAAA,MAAAU,EAAA,cAAA;UAAA,SAAA,EAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,MAAA,EAAA,UAAA;;MA7C9E,CAAA;IAAA,CAAA;IAYM,SAAAI,QAAA,MAAA;MAAAC,gBAA0B,OAAcI,IAAA;QAAYC,eAAAL,gBAAA,SAAA;UAAE,KAAA;UAAiB,MAAA;UAZ7E,QAAAL,EAAA;UAAA,UAYqBE,EAAK,CAAA,MAAAA,EAAA,CAAA,IAAA,IAAAxW,MAAAsW,EAAA,gBAAAA,EAAA,aAAA,GAAAtW,CAAA;QAAA,GAAA,MAAA,IAAA+V,EAAA,GAAA;UAEpB,CAAAkB,OAAA,KAAA;QAAA,CAAA;QACkBD,eAAAL,gBAAA,OAAAX,IAAA;UAAAa,YAAQjB,GAAc;YAAc,OAAK;YAAA,MAAAU,EAAA,cAAA;YACzD,SAAAA,EAAA;UAAA,GAAA,MAAA,GAAA,CAAA,QAAA,SAAA,CAAA;UAAA,gBAFY3D,KAAQ,MAAAuE,gBAAAZ,EAAA,iBAAA,GAAA,CAAA;QAAA,GAAA,GAAA,GAAA;UAKtB,CAAAW,OAAA,CAAAX,EAAA,QAAA;QAAA,CAAA;QAEIU,eAAAL,gBAME,OANFV,IAME;UAAAU,gBALI,OAAYT,IAAA;YAAAW,YACVjE,GAAM2D,WAAA;cACZ,KAAA;cACC,KAAKD,EAAA;cACEa,SAAAA;cAAAA,OAAAA,EAAAA,QAAAA,EAAAA,eAAAA;YAGZ,GAAAb,EAAA,QAAA,GAIM,MAJN,IAIM,CAAA,OAAA,OAAA,CAAA;UAAA,CAAA;UAAA,gBAjCd,OA+B+Dc,IAAA;aAAnDC,UAAA,GAAAC,YAAAC,wBAAiFjB,EAAAA,GA/B7F,YA+B+CZ,IAAAA,GAAAA,MAAAA;cAAAA,SAAAA,QAAAA,MAAAA;iBAAAA,UAAuB,IAAK,GAAA8B,mBAAAC,UAAA,MAAAC,WAAApB,EAAA,iBAAA,CAAAqB,GAAAC,OA/B3EP,UAAA,GAAAC,YAAAR,GAAAP,WAAA;kBA+ByF,KAAAqB;kBAAA,SAAA;gBAAA,GAAA,CAAA,GAAA,MAAA,EAAA,EA/BzF,GAAA,GAAA;cAAA,CAAA;cAAA,GAAA;;;;UAmCM,CAIMX,OAAAX,EAAA,QAAA;QAAA,CAAA;QAFJK,gBAAA,OAAAR,IAAA;UACAQ,gBAAwG,QAAAkB,IAAAX,gBAAAZ,EAAA,cAAA,OAAA,GAAA,CAAA;UAA9FE,EAAA,CAAA,MAtClBA,EAAAA,CAAAA,IAsCmBG,gBAA6E,OAAA,EAAA,OAAA,yCAAA,GAAA,MAAA,EAAA;UAAAA,gBAAA,OAAA;YAAA,OAAA,eAAA,CAAA,yCAAA,EAAA,OAAA,EAAA,SAAA,gBAAA,EAAA,CAAA,CAAA;;;MAtChG,CAAA;IAAA,CAAA;IAAA,GAAA;;;;", "names": ["ownKeys", "e", "r", "t", "o", "_objectSpread2", "_defineProperty", "_toPrimitive", "i", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_typeof", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "_createClass", "protoProps", "staticProps", "obj", "key", "value", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "iter", "minLen", "n", "len", "arr2", "IS_BROWSER", "WINDOW", "IS_TOUCH_DEVICE", "HAS_POINTER_EVENT", "NAMESPACE", "ACTION_ALL", "ACTION_CROP", "ACTION_MOVE", "ACTION_ZOOM", "ACTION_EAST", "ACTION_WEST", "ACTION_SOUTH", "ACTION_NORTH", "ACTION_NORTH_EAST", "ACTION_NORTH_WEST", "ACTION_SOUTH_EAST", "ACTION_SOUTH_WEST", "CLASS_CROP", "CLASS_DISABLED", "CLASS_HIDDEN", "CLASS_HIDE", "CLASS_INVISIBLE", "CLASS_MODAL", "CLASS_MOVE", "DATA_ACTION", "DATA_PREVIEW", "DRAG_MODE_CROP", "DRAG_MODE_MOVE", "DRAG_MODE_NONE", "EVENT_CROP", "EVENT_CROP_END", "EVENT_CROP_MOVE", "EVENT_CROP_START", "EVENT_DBLCLICK", "EVENT_TOUCH_START", "EVENT_TOUCH_MOVE", "EVENT_TOUCH_END", "EVENT_POINTER_DOWN", "EVENT_POINTER_MOVE", "EVENT_POINTER_UP", "EVENT_READY", "EVENT_RESIZE", "EVENT_WHEEL", "EVENT_ZOOM", "MIME_TYPE_JPEG", "REGEXP_ACTIONS", "REGEXP_DATA_URL", "REGEXP_DATA_URL_JPEG", "REGEXP_TAG_NAME", "MIN_CONTAINER_WIDTH", "MIN_CONTAINER_HEIGHT", "DEFAULTS", "TEMPLATE", "isNaN", "isNumber", "isPositiveNumber", "isUndefined", "isObject", "hasOwnProperty", "isPlainObject", "_constructor", "prototype", "isFunction", "slice", "toArray", "for<PERSON>ach", "data", "callback", "assign", "_len", "args", "_key", "arg", "REGEXP_DECIMALS", "normalizeDecimalNumber", "times", "REGEXP_SUFFIX", "setStyle", "element", "styles", "style", "property", "hasClass", "addClass", "elem", "className", "removeClass", "toggleClass", "added", "REGEXP_CAMEL_CASE", "toParamCase", "getData", "name", "setData", "removeData", "REGEXP_SPACES", "onceSupported", "supported", "once", "listener", "options", "removeListener", "type", "handler", "event", "listeners", "addListener", "_handler", "_element$listeners", "_len2", "_key2", "dispatchEvent", "getOffset", "box", "location", "REGEXP_ORIGINS", "isCrossOriginURL", "url", "parts", "addTimestamp", "timestamp", "getTransforms", "_ref", "rotate", "scaleX", "scaleY", "translateX", "translateY", "values", "transform", "getMaxZoomRatio", "pointers", "pointers2", "maxRatio", "pointer", "pointerId", "pointer2", "x1", "y1", "x2", "y2", "z1", "z2", "ratio", "getPointer", "_ref2", "endOnly", "pageX", "pageY", "end", "getPointersCenter", "count", "_ref3", "startX", "startY", "getAdjustedSizes", "_ref4", "aspectRatio", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidHeight", "adjustedWidth", "getRotatedSizes", "_ref5", "degree", "arc", "sinArc", "cosArc", "newWidth", "newHeight", "getSourceCanvas", "image", "_ref6", "_ref7", "_ref8", "imageAspectRatio", "imageNaturalWidth", "imageNaturalHeight", "_ref6$rotate", "_ref6$scaleX", "_ref6$scaleY", "naturalWidth", "naturalHeight", "_ref8$fillColor", "fillColor", "_ref8$imageSmoothingE", "imageSmoothingEnabled", "_ref8$imageSmoothingQ", "imageSmoothingQuality", "_ref8$maxWidth", "max<PERSON><PERSON><PERSON>", "_ref8$maxHeight", "maxHeight", "_ref8$minWidth", "min<PERSON><PERSON><PERSON>", "_ref8$minHeight", "minHeight", "canvas", "context", "maxSizes", "minSizes", "destMaxSizes", "destMinSizes", "destWidth", "destHeight", "params", "param", "fromCharCode", "getStringFromCharCode", "dataView", "start", "length", "str", "REGEXP_DATA_URL_HEAD", "dataURLToArrayBuffer", "dataURL", "base64", "binary", "arrayBuffer", "uint8", "arrayBufferToDataURL", "mimeType", "chunks", "chunkSize", "resetAndGetOrientation", "orientation", "littleEndian", "app1Start", "ifdStart", "offset", "exifIDCode", "tiffOffset", "endianness", "firstIFDOffset", "_length", "_offset", "parseOrientation", "render", "container", "cropper", "containerData", "imageData", "viewMode", "rotated", "canvasWidth", "canvasHeight", "canvasData", "sizeLimited", "positionLimited", "cropBoxData", "cropped", "minCanvasWidth", "minCanvasHeight", "_getAdjustedSizes", "newCanvasLeft", "newCanvasTop", "changed", "transformed", "_getRotatedSizes", "autoCropArea", "limited", "minCropBoxWidth", "minCropBoxHeight", "maxCropBox<PERSON>idth", "maxCropBoxHeight", "preview", "crossOrigin", "alt", "previews", "el", "img", "cropBoxWidth", "cropBoxHeight", "left", "top", "originalWidth", "originalHeight", "events", "handlers", "ratioX", "ratioY", "_this", "delta", "buttons", "button", "action", "touch", "change", "right", "bottom", "minLeft", "minTop", "renderable", "range", "check", "side", "p", "methods", "hasSameSize", "offsetX", "offsetY", "_this$canvasData", "x", "y", "_originalEvent", "pivot", "center", "_scaleX", "_scaleY", "rounded", "widthChanged", "heightChanged", "source", "_this$getData", "initialX", "initialY", "initialWidth", "initialHeight", "_options$imageSmoothi", "sourceWidth", "sourceHeight", "srcX", "srcY", "srcWidth", "srcHeight", "dstX", "dstY", "dstWidth", "dstHeight", "scale", "mode", "dragBox", "face", "croppable", "movable", "AnotherCropper", "C<PERSON>per", "tagName", "xhr", "clone", "_parseOrientation", "crossOriginUrl", "_this2", "isIOSWebKit", "done", "sizingImage", "body", "template", "cropBox", "parentNode", "previewPropType", "VueCropper", "crossorigin", "h", "containerStyle", "src", "imgStyle", "onlyColorChanged", "_sfc_main", "defineComponent", "ctx", "ui", "useUi", "useI18n", "dialogVisible", "ref", "cropperRef", "fileInputRef", "isLoaded", "imgSrc", "file", "handleClose", "handleClosed", "clear", "vClosed", "customClass", "dialogBinding", "computed", "open", "close", "getCropperRef", "scope", "zoom", "ready", "preventDefault", "handleClick", "checkFile", "setImage", "selectFile", "reader", "handleChange", "files", "selectedFile", "getCropImageDataUrl", "fileType", "quality", "getCropImageBlob", "resolve", "reject", "blob", "emit", "result", "doOutput", "ret", "dataUrl", "doCropper", "flipX", "flipY", "getCropBoxData", "move", "reset", "deg", "setCropBoxData", "showFileChooser", "percent", "computedButtons", "size", "computedTexts", "computedUploadTip", "def", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_7", "_component_vue_cropper", "_resolveComponent", "_ctx", "_mergeProps", "_cache", "$event", "_withCtx", "_createElementVNode", "_hoisted_9", "_createVNode", "_component_fs_button", "_hoisted_1", "_withDirectives", "_vShow", "_toDisplayString", "_cropper", "_hoisted_6", "_openBlock", "_createBlock", "_resolveDynamicComponent", "_createElementBlock", "_Fragment", "_renderList", "item", "index", "_hoisted_8"]}