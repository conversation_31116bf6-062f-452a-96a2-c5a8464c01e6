from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from apps.cs_manage.models import CsQualityCheck, CsQualityDeduction
import json


class CsQcExportSerializer(CustomModelSerializer):
    """
    客服质检导出序列化器 - 新版本
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_performance_text = serializers.SerializerMethodField()
    deductions_text = serializers.SerializerMethodField()
    session_start_time_str = serializers.SerializerMethodField()
    session_end_time_str = serializers.SerializerMethodField()
    create_datetime_str = serializers.SerializerMethodField()
    score_grade = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'session_id', 'service_name', 'service_id', 'service_account',
            'game_name', 'overall_score', 'score_grade', 'total_deductions', 
            'session_duration', 'session_start_time_str', 'session_end_time_str', 
            'create_datetime_str', 'analysis_summary', 'deductions_text', 
            'service_performance_text'
        ]
    
    def get_service_performance_text(self, obj):
        """获取客服表现文本"""
        try:
            if obj.service_performance:
                performance = json.loads(obj.service_performance)
                parts = []
                if performance.get('response_speed'):
                    parts.append(f"应答速度: {performance['response_speed']}")
                if performance.get('professionalism'):
                    parts.append(f"专业程度: {performance['professionalism']}")
                if performance.get('resolution_effectiveness'):
                    parts.append(f"解决效果: {performance['resolution_effectiveness']}")
                if performance.get('service_attitude'):
                    parts.append(f"服务态度: {performance['service_attitude']}")
                return '; '.join(parts)
        except (json.JSONDecodeError, TypeError):
            pass
        return ''
    
    def get_deductions_text(self, obj):
        """获取扣分项文本"""
        deductions = obj.deductions.all()
        return '; '.join([
            f"{d.item_name}(-{d.deduction_score}分): {d.description}"
            for d in deductions
        ])
    
    def get_session_start_time_str(self, obj):
        """格式化会话开始时间"""
        return obj.session_start_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_start_time else ''
    
    def get_session_end_time_str(self, obj):
        """格式化会话结束时间"""
        return obj.session_end_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_end_time else ''
    
    def get_create_datetime_str(self, obj):
        """格式化质检时间"""
        return obj.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_score_grade(self, obj):
        """获取分数等级"""
        score = obj.overall_score or 0
        if score >= 90:
            return 'A(优秀)'
        elif score >= 80:
            return 'B(良好)'
        elif score >= 70:
            return 'C(一般)'
        else:
            return 'D(待改进)'


class CsQcListSerializer(CustomModelSerializer):
    """
    客服质检列表序列化器 - 新版本
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    deduction_count = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    has_deductions = serializers.SerializerMethodField()
    session_start_time_str = serializers.SerializerMethodField()
    session_end_time_str = serializers.SerializerMethodField()
    create_datetime_str = serializers.SerializerMethodField()
    score_grade = serializers.SerializerMethodField()
    session_duration_str = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'id', 'session_id', 'service_id', 'service_name', 'service_account',
            'game_name', 'overall_score', 'score_grade', 'total_deductions', 
            'session_duration', 'session_duration_str', 'analysis_summary', 
            'session_summary', 'session_start_time', 'session_end_time',
            'create_datetime', 'deduction_count', 'user_info', 'has_deductions',
            'session_start_time_str', 'session_end_time_str', 'create_datetime_str'
        ]
    
    def get_deduction_count(self, obj):
        """获取扣分项数量"""
        return obj.deductions.count()

    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.service_user:
            return {
                'id': obj.service_user.id,
                'name': obj.service_user.name,
                'username': obj.service_user.username
            }
        return None

    def get_has_deductions(self, obj):
        """是否有扣分项"""
        return obj.total_deductions > 0

    def get_session_start_time_str(self, obj):
        """格式化会话开始时间"""
        return obj.session_start_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_start_time else ''

    def get_session_end_time_str(self, obj):
        """格式化会话结束时间"""
        return obj.session_end_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_end_time else ''

    def get_create_datetime_str(self, obj):
        """格式化质检时间"""
        return obj.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_score_grade(self, obj):
        """获取分数等级"""
        score = obj.overall_score or 0
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        else:
            return 'D'
    
    def get_session_duration_str(self, obj):
        """格式化会话时长"""
        if obj.session_duration:
            hours = int(obj.session_duration // 60)
            minutes = int(obj.session_duration % 60)
            if hours > 0:
                return f"{hours}小时{minutes}分钟"
            else:
                return f"{minutes}分钟"
        return ''


class CsQcDetailSerializer(CustomModelSerializer):
    """
    客服质检详情序列化器 - 新版本
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_performance_data = serializers.SerializerMethodField()
    deductions = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    score_grade = serializers.SerializerMethodField()
    session_duration_str = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'id', 'session_id', 'service_id', 'service_name', 'service_account',
            'game_name', 'overall_score', 'score_grade', 'total_deductions', 
            'session_duration', 'session_duration_str', 'analysis_summary', 
            'session_summary', 'service_performance_data', 'deductions',
            'user_info', 'session_start_time', 'session_end_time', 'create_datetime'
        ]
    
    def get_service_performance_data(self, obj):
        """获取客服表现数据"""
        try:
            return json.loads(obj.service_performance) if obj.service_performance else {}
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def get_deductions(self, obj):
        """获取扣分项详情"""
        return [
            {
                'id': d.id,
                'item_name': d.item_name,
                'category': d.category,
                'severity': d.severity,
                'description': d.description,
                'deduction_score': d.deduction_score,
                'message_index': d.message_index,
                'message_content': d.message_content,
                'suggestion': d.suggestion
            }
            for d in obj.deductions.all()
        ]

    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.service_user:
            return {
                'id': obj.service_user.id,
                'name': obj.service_user.name,
                'username': obj.service_user.username
            }
        return None
    
    def get_score_grade(self, obj):
        """获取分数等级"""
        score = obj.overall_score or 0
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        else:
            return 'D'
    
    def get_session_duration_str(self, obj):
        """格式化会话时长"""
        if obj.session_duration:
            hours = int(obj.session_duration // 60)
            minutes = int(obj.session_duration % 60)
            if hours > 0:
                return f"{hours}小时{minutes}分钟"
            else:
                return f"{minutes}分钟"
        return ''


class CsQualityDeductionSerializer(CustomModelSerializer):
    """
    客服质检扣分项序列化器 - 新版本
    """
    quality_check_info = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityDeduction
        fields = [
            'id', 'item_name', 'category', 'severity', 'description',
            'deduction_score', 'message_index', 'message_content', 
            'suggestion', 'quality_check_info', 'create_datetime'
        ]
    
    def get_quality_check_info(self, obj):
        """获取关联的质检记录信息"""
        qc = obj.quality_check
        return {
            'id': qc.id,
            'session_id': qc.session_id,
            'service_name': qc.service_name,
            'overall_score': qc.overall_score,
            'create_datetime': qc.create_datetime
        }