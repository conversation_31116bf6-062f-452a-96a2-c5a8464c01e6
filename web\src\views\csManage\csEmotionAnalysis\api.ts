import { request, downloadFile } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/cs_manage/cs_emotion_analysis'; // API 前缀指向情绪分析 ViewSet

/**
 * 查询列表
 * @param query
 * @returns {*}
 */
export function GetList(query: UserPageQuery) {
  return request({
    url: apiPrefix + '/', // 列表 API 通常是 ViewSet 的根路径
    method: 'get',
    params: query, // GET 请求使用 params
  });
}

/**
 * 获取详情
 * @param id
 * @returns {*}
 */
export function GetObj(id: InfoReq) {
  return request({
    url: apiPrefix + '/' + id + '/', // 详情 API
    method: 'get',
  });
}

/**
 * 新增
 * @param data
 * @returns {*}
 */
export function AddObj(data: AddReq) {
  return request({
    url: apiPrefix + '/',
    method: 'post',
    data: data,
  });
}

/**
 * 修改
 * @param data
 * @returns {*}
 */
export function UpdateObj(data: EditReq) {
  return request({
    url: apiPrefix + '/' + data.id + '/',
    method: 'put',
    data: data,
  });
}

/**
 * 删除
 * @param id
 * @returns {*}
 */
export function DelObj(id: DelReq) {
  return request({
    url: apiPrefix + '/' + id + '/',
    method: 'delete',
    data: { id },
  });
}

/**
 * 导出数据
 * @param params
 * @returns {*}
 */
export function exportData(params: any) {
  return downloadFile({
    url: apiPrefix + '/export_data/',
    params: params,
    method: 'get',
  });
}

/**
 * 手动触发情绪分析
 * @param data
 * @returns {*}
 */
export function manualAnalysis(data: { session_ids: string[], force_reprocess?: boolean }) {
  return request({
    url: apiPrefix + '/manual_analysis/',
    method: 'post',
    data: data,
  });
}

/**
 * 获取会话详细对话内容
 * @param id
 * @returns {*}
 */
export function getConversationDetail(id: string) {
  return request({
    url: apiPrefix + '/' + id + '/conversation_detail/',
    method: 'get',
  });
}

/**
 * 获取七鱼会话消息
 * @param data
 * @returns {*}
 */
export function QiyuSessionMessage(data) {
  return request({
    url: '/api/cs_manage/qiyu/session_message',
    method: 'post',
    data,
  });
}
