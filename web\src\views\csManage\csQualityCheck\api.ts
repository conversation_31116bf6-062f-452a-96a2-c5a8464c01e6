import { request, downloadFile } from '/@/utils/service';

// API基础路径 - 使用新的端点
const baseURL = '/api/cs_manage/cs_quality_check/';

// 获取质检看板数据
export function getDashboard(params: any) {
  return request({
    url: `${baseURL}dashboard/`,
    method: 'get',
    params
  });
}

// 获取质检记录列表
export function getQualityCheckList(params: any) {
  return request({
    url: baseURL,
    method: 'get',
    params
  });
}

// 获取质检记录详情
export function getQualityCheckDetail(id: number) {
  return request({
    url: `${baseURL}${id}/`,
    method: 'get'
  });
}

// 注意：以下接口已废弃，数据统一从dashboard接口获取
// getServiceList, getServiceDetail, getDeductionStats, getTrendData

// 获取用户列表
export function getUserList() {
  return request({
    url: `${baseURL}user_list/`,
    method: 'get'
  });
}

// 获取游戏列表
export function getGameList() {
  return request({
    url: `${baseURL}game_list/`,
    method: 'get'
  });
}

// 手动触发质检
export function manualCheck(data: any) {
  return request({
    url: `${baseURL}manual_check/`,
    method: 'post',
    data
  });
}

// 导出质检数据
export function exportData(params: any) {
  return downloadFile({
    url: `${baseURL}export/`,
    method: 'get',
    params
  });
}

// 兼容旧版本函数名
export function getDashboardData(params: any) {
  return getDashboard(params);
}

// 注意：以下接口已废弃，筛选功能通过质检记录列表接口的参数实现
// getQualityCheckByDeduction, getQualityCheckWithDeductions, getDeductionDistribution