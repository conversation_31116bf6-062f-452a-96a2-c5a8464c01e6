import {
  R,
  i
} from "./chunk-GQR6RJUV.js";
import {
  ElDialog,
  ElMessage,
  ElMessageBox,
  ElNotification,
  placeholderSign,
  useFormItem
} from "./chunk-HIOCDXBI.js";
import {
  dist_exports
} from "./chunk-FOTJEBUC.js";
import "./chunk-HYZ2CRGS.js";
import "./chunk-XBAZBRKF.js";
import {
  forEach_default,
  isFunction_default,
  union_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  computed,
  createTextVNode,
  createVNode,
  resolveComponent,
  unref
} from "./chunk-VL4YS5HC.js";
import "./chunk-XYY2WIPO.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/ui-element/dist/ui-element.mjs
var {
  buildBinding: b,
  creator: t
} = R();
var W = class {
  constructor(f) {
    this.type = "element", this.modelValue = "modelValue", this.switch = t({
      activeColor: "active-color",
      activeText: "active-text",
      activeValue: "active-value",
      inactiveColor: "inactive-color",
      inactiveText: "inactive-text",
      inactiveValue: "inactive-value",
      modelValue: "modelValue",
      name: "el-switch"
    }), this.formWrapper = t({
      visible: "modelValue",
      customClass: (e) => "class",
      titleSlotName: "header",
      buildOnClosedBind(e, a) {
        return {
          onClosed: a
        };
      },
      buildWidthBind(e, a) {
        return {
          width: a
        };
      },
      buildInitBind(e) {
        return {};
      },
      buildInnerBind() {
        return {};
      },
      name: "fs-form-wrapper"
    }), this.messageBox = t({
      name: "el-message-box",
      instance: void 0,
      open: async (e) => this.messageBox.instance(e),
      confirm: async (e) => this.messageBox.instance(e)
    }), this.message = t({
      instance: void 0,
      name: "el-message",
      open: (e) => {
        this.message.instance.open(e);
      },
      success: (e) => {
        this.message.instance.success(e);
      },
      error: (e) => {
        this.message.instance.error(e);
      },
      warn: (e) => {
        this.message.instance.warning(e);
      },
      info: (e) => {
        this.message.instance(e);
      }
    }), this.notification = t({
      instance: void 0,
      name: "el-notification",
      open: (e) => {
        this.notification.instance.open(e);
      },
      success: (e) => {
        this.notification.instance.success(e);
      },
      error: (e) => {
        this.notification.instance.error(e);
      },
      warn: (e) => {
        this.notification.instance.warn(e);
      },
      info: (e) => {
        this.notification.instance.success(e);
      }
    }), this.icon = t({
      name: "",
      isComponent: false
    }), this.icons = {
      add: "plus",
      columnsFilter: "set-up",
      compact: "rank",
      edit: "edit",
      remove: "delete",
      search: "search",
      refresh: "refresh",
      export: "upload",
      check: "check",
      sort: "sort",
      left: "arrow-left",
      right: "arrow-right",
      close: "close",
      arrowLeft: "left",
      arrowRight: "right",
      more: "more",
      plus: "plus",
      zoomIn: "zoom-in",
      zoomOut: "zoom-out",
      refreshLeft: "refresh-left",
      refreshRight: "refresh-right",
      upload: "upload",
      fullScreen: "full-screen",
      unFullScreen: "full-screen",
      question: "question-filled",
      caretUp: "CaretTop",
      caretDown: "CaretBottom",
      eye: "View",
      info: "warning"
    }, this.dialog = t({
      name: "el-dialog",
      visible: "modelValue",
      customClass: "class",
      titleSlotName: "header",
      footerSlotName: "footer",
      buildOnClosedBind(e) {
        return {
          onClosed: e
        };
      },
      footer() {
        return {};
      },
      open(e) {
        ElDialog.open(e);
      },
      builder(e) {
        return b(this, e, {
          props: {
            title: e.title,
            width: e.width
          },
          slots: {
            footer: e.footer
          }
        });
      }
    }), this.buttonGroup = t({
      name: "el-button-group"
    }), this.col = t({
      name: "el-col"
    }), this.row = t({
      name: "el-row"
    }), this.card = t({
      name: "el-card"
    }), this.checkboxGroup = t({
      name: "el-checkbox-group",
      modelValue: "modelValue"
    }), this.checkbox = t({
      name: "el-checkbox",
      resolveEvent(e) {
        return e;
      },
      modelValue: "modelValue",
      value: "value",
      onChange(e) {
        return {
          "onUpdate:modelValue": e
        };
      }
    }), this.drawer = t({
      name: "el-drawer",
      visible: "modelValue",
      customClass: "class",
      width: "size"
    }), this.collapseTransition = t({
      name: "el-collapse-transition"
    }), this.option = t({
      name: "el-option",
      value: "value",
      label: "label"
    }), this.select = t({
      name: "el-select",
      modelValue: "modelValue",
      clearable: "clearable",
      filterable: "filterable",
      buildMultiBinding(e) {
        return {
          multiple: e
        };
      }
    }), this.treeSelect = t({
      name: "el-tree-select",
      modelValue: "modelValue",
      clearable: "select.clearable",
      options: "data",
      value: "tree.value",
      label: "tree.label",
      children: "tree.children",
      buildOptionKeysNameBinding(e) {
        return {
          props: {
            label: e.label,
            value: e.value,
            children: e.children
          }
        };
      }
    }), this.radio = t({
      name: "el-radio",
      value: "value",
      builder(e) {
        return b(this, e, {
          props: {
            [this.value]: e.value
          }
        });
      }
    }), this.radioButton = t({
      name: "el-radio-button",
      value: "value",
      builder(e) {
        return b(this, e, {
          props: {
            [this.value]: e.value
          }
        });
      }
    }), this.radioGroup = t({
      name: "el-radio-group",
      modelValue: "modelValue"
    }), this.cascader = t({
      name: "el-cascader",
      modelValue: "modelValue",
      clearable: "clearable",
      fieldNames(e) {
        return {
          props: e
        };
      }
    }), this.form = t({
      name: "el-form",
      inlineLayout: {
        layout: "inline",
        inline: true
      },
      validateWrap: async (e) => e.validate(),
      transformValidateErrors: (e) => {
        const a = {};
        return forEach_default(e, (n, l) => {
          a[l] = true;
        }), a;
      }
    }), this.formItem = t({
      name: "el-form-item",
      prop: "prop",
      label: "label",
      rules: "rules",
      skipValidationWrapper: "div",
      injectFormItemContext() {
        const {
          formItem: e
        } = useFormItem();
        return {
          async onChange() {
            await (e == null ? void 0 : e.validate("change"));
          },
          async onBlur() {
            await (e == null ? void 0 : e.validate("blur"));
          }
        };
      },
      builder(e) {
        return b(this, e, {});
      }
    }), this.button = t({
      name: "el-button",
      textType: {
        text: true
      },
      linkType: {
        link: true,
        type: "primary"
      },
      circle: {
        circle: true
      },
      colors: (e) => ({
        type: e
      })
    }), this.pagination = t({
      name: "el-pagination",
      currentPage: "currentPage",
      total: "total",
      pageCount: null,
      onChange({
        setCurrentPage: e,
        setPageSize: a,
        doAfterChange: n
      }) {
        return {
          // element 页码改动回调
          onCurrentChange(l) {
            e(l), n();
          },
          onSizeChange(l) {
            a(l), n();
          }
        };
      }
    }), this.tableColumn = t({
      name: "el-table-column",
      label: "label",
      prop: "prop",
      row: "row",
      index: "$index"
    }), this.tableColumnGroup = t({
      name: "el-table-column",
      label: "label",
      prop: "prop",
      row: "row",
      index: "$index"
    }), this.table = t({
      name: "el-table",
      data: "data",
      renderMode: "slot",
      defaultRowKey: "id",
      fixedHeaderNeedComputeBodyHeight: false,
      buildMaxHeight: (e) => ({
        maxHeight: e
      }),
      hasMaxHeight: (e) => (e == null ? void 0 : e.maxHeight) != null,
      headerDomSelector: "",
      vLoading: "loading",
      // 没太大用
      setSelectedRows({
        multiple: e,
        selectedRowKeys: a,
        tableRef: n,
        getRowKey: l
      }) {
        const o = l(), s = [];
        for (const i2 of a.value)
          for (const r of n.data)
            r[o] === i2 && s.push(r);
        if (e)
          for (const i2 of s)
            n.toggleRowSelection(i2, true);
        else
          a.value.length > 0 && n.setCurrentRow(s[0]);
      },
      buildSelectionCrudOptions(e) {
        const {
          compute: a
        } = e.useCompute();
        function n(l) {
          const o = e.getRowKey(), s = e.getPageData();
          let i2 = o;
          isFunction_default(o) || (i2 = (c) => c[o]);
          const r = s.map(i2), u = e.selectedRowKeys instanceof Function ? e.selectedRowKeys() : e.selectedRowKeys;
          u.value || (u.value = []);
          const d = u.value.filter((c) => !r.includes(c));
          return union_default(d, l);
        }
        if (e.multiple)
          return {
            table: {
              onSelectionChange: (o = []) => {
                const s = e.getRowKey();
                let i2 = o.map((r) => r[s]);
                e.crossPage && (i2 = n(i2)), e.onSelectedKeysChanged(i2);
              }
            },
            columns: {
              $checked: {
                form: {
                  show: false
                },
                column: {
                  type: "selection",
                  align: "center",
                  width: "55px",
                  order: -9999,
                  reserveSelection: e.crossPage,
                  columnSetDisabled: true
                  //禁止在列设置中选择
                }
              }
            }
          };
        {
          const l = (i2) => {
            if (i2 == null) {
              e.onSelectedKeysChanged([]);
              return;
            }
            const r = e.getRowKey(), u = [i2[r]];
            e.onSelectedKeysChanged(u);
          }, o = e.selectedRowKeys instanceof Function ? e.selectedRowKeys() : e.selectedRowKeys, s = computed(() => o.value.length > 0 ? o.value[0] : null);
          return {
            table: {
              highlightCurrentRow: true,
              onCurrentChange: l
            },
            columns: {
              $selected: {
                form: {
                  show: false
                },
                column: {
                  align: "center",
                  width: "55px",
                  order: -9999,
                  component: {
                    name: "el-radio",
                    label: a((i2) => {
                      if (i2.form)
                        return i2.form[e.getRowKey()];
                    }),
                    props: {
                      modelValue: s
                    },
                    slots: {
                      default() {
                        return "";
                      }
                    }
                  },
                  conditionalRender: {
                    match() {
                      return false;
                    }
                  }
                }
              }
            }
          };
        }
      },
      rebuildRenderScope: (e) => e,
      scrollTo(e) {
        var a, n;
        (n = (a = e.tableRef) == null ? void 0 : a.value) == null || n.setScrollTop(e.top);
      },
      onChange({
        onSortChange: e,
        onFilterChange: a,
        bubbleUp: n
      }) {
        return {
          onSortChange: (l) => {
            const {
              column: o,
              prop: s,
              order: i2
            } = l;
            e && e({
              isServerSort: s && o.sortable === "custom",
              prop: s,
              order: i2,
              asc: i2 === "ascending"
            }), n((r) => {
              r.onSortChange && r.onSortChange(l);
            });
          },
          onFilterChange: (l) => {
            a(l), n((o) => {
              o.onFilterChange && o.onFilterChange(l);
            });
          }
        };
      }
    }), this.tableColumnV2 = t({
      name: "el-table-column",
      label: "label",
      prop: "prop",
      row: "row",
      index: "$index"
    }), this.tableColumnGroupV2 = t({
      name: "el-table-column",
      label: "label",
      prop: "prop",
      row: "row",
      index: "$index"
    }), this.tableV2 = t({
      name: "el-table-v2",
      data: "data",
      renderMode: "jsx",
      defaultRowKey: "id",
      fixedHeaderNeedComputeBodyHeight: false,
      renderMethod: "cellRenderer",
      columnsIsFlat: true,
      //构建自定义表头插槽方法，element-table-v2需要自己写多级表头
      buildMultiHeadersBind(e) {
        const a = e.flatColumns;
        e.treeColumns;
        function n(u, d = 1) {
          let c = 0;
          for (const v of u)
            if (v._parent == null)
              c = Math.max(c, d);
            else {
              const y = n([v._parent], d + 1);
              c = Math.max(c, y);
            }
          return c;
        }
        let l = 50;
        const o = n(a);
        o > 1 && (l = l - (o - 1) * 10, l = Math.max(30, l));
        const s = o * l;
        function i2(u) {
          const d = [];
          for (const c of u)
            c.children && c.children.length > 0 ? d.push(...i2(c.children)) : d.push(c);
          return d;
        }
        function r(u) {
          const d = i2(u);
          let c = 0;
          for (const v of d)
            c += v.width;
          return c;
        }
        return {
          bind: {
            headerHeight: s
          },
          slots: {
            header: ({
              cells: u,
              columns: d,
              headerIndex: c
            }) => {
              const v = {};
              d.forEach((m, h) => {
                v[m.key] = {
                  column: m,
                  index: h
                };
              });
              const y = [], S = [];
              function V(m, h) {
                const C = [];
                for (const p of m)
                  if (!p.children || p.children.length == 0)
                    C.push(createVNode("div", {
                      class: "custom-header-cell fs-multi-head-text el-table-v2__header-cell-text",
                      style: {
                        width: p.width + "px",
                        height: l * h + "px",
                        justifyContent: p.align
                      }
                    }, [p.title])), S.push(p.key);
                  else {
                    const x = r(p.children);
                    C.push(createVNode("div", {
                      class: "fs-multi-head-group "
                    }, [createVNode("div", {
                      class: "custom-header-cell fs-multi-head-text el-table-v2__header-cell-text",
                      style: {
                        width: x + "px",
                        height: l + "px",
                        justifyContent: p.align
                      }
                    }, [p.title]), createVNode("div", {
                      class: "fs-multi-head-sub "
                    }, [V(p.children, h - 1)])]));
                  }
                return C;
              }
              function R2(m, h = 1) {
                return m._parent ? (h = h + 1, R2(m._parent, h)) : {
                  parent: m,
                  deep: h
                };
              }
              return d.forEach((m, h) => {
                if ((m == null ? void 0 : m.placeholderSign) === placeholderSign) {
                  y.push(u[h]);
                  return;
                }
                if (!S.includes(m.key))
                  if (m._parent) {
                    const {
                      parent: C,
                      deep: p
                    } = R2(m), x = V([C], o);
                    y.push(...x);
                  } else
                    y.push(u[h]);
              }), y;
            }
          }
        };
      },
      rebuildRenderScope: (e) => ({
        ...e,
        index: e.rowIndex,
        row: e.rowData
      }),
      buildMaxHeight: (e) => ({
        maxHeight: e
      }),
      hasMaxHeight: (e) => false,
      headerDomSelector: "",
      vLoading: "loading",
      // 没太大用
      setSelectedRows({
        multiple: e,
        selectedRowKeys: a,
        tableRef: n,
        getRowKey: l
      }) {
        const o = l(), s = [];
        for (const i2 of a.value)
          for (const r of n.data)
            r[o] === i2 && s.push(r);
        if (e)
          for (const i2 of s)
            n.toggleRowSelection(i2, true);
        else
          a.value.length > 0 && n.setCurrentRow(s[0]);
      },
      buildSelectionCrudOptions(e) {
        const a = (n = []) => {
          e.onSelectedKeysChanged(n);
        };
        return unref(e), {
          table: {
            // checkedRowKeys: req.selectedRowKeys,
            // "onUpdate:checkedRowKeys": onSelectionChange
          },
          columns: {
            $checked: {
              form: {
                show: false
              },
              column: {
                multiple: !!e.multiple,
                align: "center",
                width: 80,
                order: -9999,
                fixed: e.selectionFixed,
                columnSetDisabled: true,
                cellRenderer: ({
                  rowData: n
                }) => {
                  const l = e.selectedRowKeys instanceof Function ? e.selectedRowKeys() : e.selectedRowKeys;
                  l.value || (l.value = []);
                  const o = (i2) => {
                    i2 ? l.value.push(n[e.getRowKey()]) : l.value = l.value.filter((r) => r !== n[e.getRowKey()]), a(l.value);
                  }, s = l.value.includes(n[e.getRowKey()]);
                  return createVNode(resolveComponent("ElCheckbox"), {
                    onChange: o,
                    modelValue: s
                  }, null);
                },
                headerCellRenderer: (n) => {
                  const l = e.getPageData() || [], o = e.selectedRowKeys instanceof Function ? e.selectedRowKeys() : e.selectedRowKeys, s = (u) => {
                    u ? o.value = l.map((d) => d[e.getRowKey()]) : o.value = [];
                  }, i2 = l.length > 0 && l.every((u) => o.value.includes(u[e.getRowKey()])), r = l.some((u) => o.value.includes(u[e.getRowKey()]));
                  return createVNode(resolveComponent("el-checkbox"), {
                    onChange: s,
                    modelValue: i2,
                    indeterminate: r && !i2
                  }, null);
                }
              }
            }
          }
        };
      },
      scrollTo(e) {
        var a, n;
        (n = (a = e.tableRef) == null ? void 0 : a.value) == null || n.scrollToTop(e.top);
      },
      onChange({
        onSortChange: e,
        onFilterChange: a,
        bubbleUp: n
      }) {
        return {
          onSortChange: (l) => {
            const {
              column: o,
              prop: s,
              order: i2
            } = l;
            e && e({
              isServerSort: s && o.sortable === "custom",
              prop: s,
              order: i2,
              asc: i2 === "ascending"
            }), n((r) => {
              r.onSortChange && r.onSortChange(l);
            });
          },
          onFilterChange: (l) => {
            a(l), n((o) => {
              o.onFilterChange && o.onFilterChange(l);
            });
          }
        };
      }
    }), this.textArea = t({
      name: "el-input",
      type: "textarea",
      modelValue: "modelValue",
      clearable: "clearable"
    }), this.tag = t({
      name: "el-tag",
      type: "type",
      colors: ["info", "success", "warning", "danger"]
    }), this.inputGroup = t({
      name: "el-input-group"
    }), this.input = t({
      name: "el-input",
      clearable: "clearable",
      modelValue: "modelValue"
    }), this.inputPassword = t({
      name: "el-input",
      clearable: "clearable",
      modelValue: "modelValue",
      passwordType: {
        showPassword: true
      }
    }), this.number = t({
      name: "el-input-number",
      modelValue: "modelValue",
      builder(e) {
        return b(this, e, {});
      }
    }), this.datePicker = t({
      name: "el-date-picker",
      modelValue: "modelValue",
      buildDateType(e) {
        return {
          name: "el-date-picker",
          type: e
        };
      }
    }), this.timePicker = t({
      name: "el-time-picker",
      modelValue: "modelValue"
    }), this.dropdown = t({
      name: "el-dropdown",
      command(e) {
        return {
          onCommand(a) {
            e(a);
          }
        };
      },
      slotName: "dropdown",
      renderMode: "slot"
    }), this.dropdownMenu = t({
      name: "el-dropdown-menu",
      command: () => ({})
    }), this.dropdownItem = t({
      name: "el-dropdown-item",
      command: "command"
    }), this.imageGroup = t({
      name: "fs-box"
    }), this.image = t({
      name: "el-image",
      buildPreviewBind: ({
        url: e,
        urls: a,
        previewUrl: n,
        previewUrls: l,
        index: o
      }) => ({
        "preview-src-list": l,
        "initial-index": o
      }),
      fallback: "error"
    }), this.progress = t({
      name: "el-progress"
    }), this.loading = t({
      name: "loading",
      type: "directive"
    }), this.upload = t({
      id: "uid",
      name: "el-upload",
      type: "",
      typeImageCard: "picture-card",
      typeImage: "picture",
      getStatusFromEvent(e) {
        return e == null ? void 0 : e.status;
      },
      getFileListFromEvent(e, a, n) {
        return n;
      },
      status: {
        success: "success",
        uploading: "uploading"
      },
      isSuccess(e) {
        return e.status === "success";
      },
      limitAdd: 1
    }), this.tabs = t({
      name: "el-tabs",
      modelValue: "modelValue",
      tabChange: "tabChange"
    }), this.tabPane = t({
      name: "el-tab-pane",
      key: "name",
      tab: "label"
    }), this.collapse = t({
      name: "el-collapse",
      modelValue: "modelValue",
      keyName: "name"
    }), this.collapseItem = t({
      name: "el-collapse-item",
      key: "name",
      titleSlotName: "title",
      /**
       * element collapse只支持title插槽
       */
      extraSlotName: "not_support_extra",
      builder(e) {
        return b(this, e, {
          slots: {
            [this.titleSlotName]() {
              return createVNode("div", {
                class: "fsel-collapse-item-title fsel-flex-row space-between"
              }, [createVNode("span", {
                class: "title-text"
              }, [e.titleSlot(), createTextVNode(" ")]), createVNode("span", {
                class: "title-extra"
              }, [e.extraSlot()])]);
            }
          }
        });
      }
    }), this.badge = t({
      name: "el-badge",
      value: "value",
      builder(e) {
        return b(this, e, {
          props: {
            [this.value]: e.value
          }
        });
      }
    }), this.tooltip = t({
      name: "el-tooltip",
      content: "content",
      trigger: "default"
    }), this.divider = t({
      name: "el-divider"
    }), this.popover = t({
      name: "el-popover",
      contentSlotName: "default",
      triggerSlotName: "reference",
      visible: "visible"
    }), f && (this.notification.instance = f.Notification, this.message.instance = f.Message, this.messageBox.instance = f.MessageBox);
  }
};
var k = dist_exports;
function j(w) {
  for (const f in k)
    w.component(f, k[f]);
}
function B() {
  const w = new W({
    Message: ElMessage,
    Notification: ElNotification,
    MessageBox: ElMessageBox
  });
  return i.set(w), w;
}
var X = {
  install(w, f = {}) {
    return f.setupIcons !== false && j(w), B();
  },
  set: B
};
export {
  W as Element,
  X as default
};
//# sourceMappingURL=@fast-crud_ui-element.js.map
