import * as api from './api';
import { dict, UserPageQuery, CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { ElNotification, ElMessage } from 'element-plus';
import { successMessage } from '/@/utils/message';

export const createCrudOptions = function (context?: any): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.getQualityCheckList(query);
  };

  const exportRequest = async (query: UserPageQuery) => {
    return await api.exportData(query);
  };

  // 查看详情处理函数
  const handleViewDetail = async (row: any) => {
    try {
      // 获取完整的质检记录详情（包含扣分项）
      const response = await api.getQualityCheckDetail(row.id);
      if (context?.context?.onViewDetail) {
        context.context.onViewDetail(response.data);
      }
    } catch (error) {
      ElMessage.error('获取质检详情失败');
    }
  };

  // 筛选客服处理函数
  const handleServiceFilter = (row: any) => {
    if (context?.context?.onServiceFilter) {
      context.context.onServiceFilter(row.service_id, row.service_name);
    }
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest: async () => { throw new Error('不支持添加操作'); },
        editRequest: async () => { throw new Error('不支持编辑操作'); },
        delRequest: async () => { throw new Error('不支持删除操作'); },
      },
      actionbar: {
        buttons: {
          add: {
            show: false, // 质检记录不支持手动添加
          },
          export: {
            text: '导出报告',
            title: '导出质检报告',
            type: 'primary',
            click() {
              ElNotification({
                title: '正在处理',
                message: '正在导出质检报告，请勿多次点击...',
                type: 'success',
              });
              return exportRequest(this.getSearchFormData());
            },
          }
        },
      },
      rowHandle: {
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            text: '查看详情',
            type: 'primary',
            size: 'small',
            click: ({ row }) => {
              handleViewDetail(row);
            }
          },
          filter: {
            text: '筛选客服',
            type: 'warning',
            size: 'small',
            click: ({ row }) => {
              handleServiceFilter(row);
            }
          },
          edit: {
            show: false, // 质检记录不支持编辑
          },
          remove: {
            show: false, // 质检记录不支持删除
          },
        },
      },
      search: {
        show: true,
        layout: 'multi-line',
        col: {
          span: 6,
        },
        options: {
          onSearch: (searchForm: any) => {
            // 处理搜索逻辑
            return searchForm;
          }
        }
      },
      table: {
        size: 'small',
        scroll: { x: 1800 }, // 设置横向滚动
      },
      columns: {
        session_id: {
          title: '会话ID',
          type: 'text',
          search: { 
            show: true,
            component: {
              props: {
                clearable: true,
              },
              placeholder: '请输入会话ID',
            },
          },
          column: {
            width: 120,
          },
        },
        service_name: {
          title: '客服姓名',
          type: 'text',
          search: { 
            show: true,
            component: {
              props: {
                clearable: true,
              },
              placeholder: '请输入客服姓名',
            },
          },
          column: {
            width: 100,
          },
        },
        service_id: {
          title: '客服ID',
          type: 'text',
          search: { 
            show: true,
            component: {
              props: {
                clearable: true,
              },
              placeholder: '请输入客服ID',
            },
          },
          column: {
            width: 100,
          },
        },
        game: {
          title: '游戏',
          type: 'dict-select',
          search: { 
            show: true,
            component: {
              filterable: true,
              clearable: true,
              placeholder: '请选择游戏',
            },
            col: { span: 2 },
          },
          column: {
            width: 120,
          },
          dict: dict({
            url: '/api/kcs/game/',
            value: 'id',
            label: 'name',
          }),
        },
        service_user: {
          title: '关联用户',
          column: {
            width: 100,
            cellRender: ({ row }) => {
              return row.user_info ? row.user_info.name : '-';
            },
          },
          search: {
            show: true,
            component: {
              filterable: true,
              clearable: true,
              placeholder: '请选择用户',
            },
          },
          addForm: { show: false },
          editForm: { show: false },
          type: 'dict-select',
          dict: dict({
            url: '/api/cs_manage/cs_quality_check/user_list/',
            value: 'id',
            label: 'name',
          }),
        },
        deduction_item: {
          title: '扣分项',
          type: 'text',
          search: {
            show: true,
            component: {
              props: {
                clearable: true,
              },
              placeholder: '请输入扣分项名称',
            },
          },
          addForm: { show: false },
          editForm: { show: false },
          column: { show: false },
        },
        only_deductions: {
          title: '只看扣分',
          type: 'radio',
          search: {
            show: true,
            component: {
              placeholder: '是否只显示有扣分的记录',
            },
          },
          addForm: { show: false },
          editForm: { show: false },
          column: { show: false },
          dict: [
            { value: '', label: '全部' },
            { value: 'true', label: '只看扣分' },
          ],
        },
        overall_score: {
          title: '总分',
          type: 'number',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 80,
            sortable: 'custom',
            cellRender: ({ row }) => {
              const score = row.overall_score || 0;
              let type = 'danger'; // 红色 - 较差
              if (score >= 90) type = 'success'; // 绿色 - 优秀
              else if (score >= 80) type = 'primary'; // 蓝色 - 良好
              else if (score >= 70) type = 'warning'; // 橙色 - 一般
              
              return (
                <el-tag type={type} size="small">
                  {score}
                </el-tag>
              );
            },
          },
        },
        total_deductions: {
          title: '总扣分',
          type: 'number',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 80,
            sortable: 'custom',
            cellRender: ({ row }) => {
              const deductions = row.total_deductions || 0;
              if (deductions > 0) {
                return (
                  <span style="color: #F56C6C; font-weight: bold;">
                    -{deductions}
                  </span>
                );
              }
              return <span>0</span>;
            },
          },
        },
        session_duration: {
          title: '会话时长(分钟)',
          type: 'number',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 120,
            cellRender: ({ row }) => {
              const duration = row.session_duration || 0;
              return Math.round(duration / 60);
            },
          },
        },
        deduction_count: {
          title: '扣分项数',
          type: 'number',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 80,
            cellRender: ({ row }) => {
              const count = row.deduction_count || 0;
              if (count > 0) {
                return (
                  <el-tag type="warning" size="small">
                    {count}
                  </el-tag>
                );
              }
              return <span>0</span>;
            },
          },
        },
        session_summary: {
          title: '会话总结',
          type: 'text',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 200,
            showOverflowTooltip: true,
            cellRender: ({ row }) => {
              const summary = row.session_summary || '';
              if (summary.length > 50) {
                return summary.substring(0, 50) + '...';
              }
              return summary;
            },
          },
        },
        analysis_summary: {
          title: '分析摘要',
          type: 'text',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            show: false, // 默认隐藏
            width: 200,
            showOverflowTooltip: true,
          },
        },
        session_start_time: {
          title: '会话开始时间',
          type: 'datetime',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            show: false, // 默认隐藏
            width: 160,
          },
        },
        session_end_time: {
          title: '会话结束时间',
          type: 'datetime',
          search: {
            show: false,
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            show: false, // 默认隐藏
            width: 160,
          },
        },
        create_datetime: {
          title: '质检时间',
          type: 'datetime',
          search: {
            show: true,
            component: {
              type: 'datetimerange',
              props: {
                clearable: true,
                shortcuts: [
                  {
                    text: '最近一周',
                    value: () => {
                      const end = new Date();
                      const start = new Date();
                      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                      return [start, end];
                    },
                  },
                  {
                    text: '最近一个月',
                    value: () => {
                      const end = new Date();
                      const start = new Date();
                      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                      return [start, end];
                    },
                  },
                  {
                    text: '最近三个月',
                    value: () => {
                      const end = new Date();
                      const start = new Date();
                      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                      return [start, end];
                    },
                  },
                ],
              },
            },
          },
          addForm: { show: false },
          editForm: { show: false },
          column: {
            width: 160,
            sortable: 'custom',
          },
        },
      },
    },
  };
  
};