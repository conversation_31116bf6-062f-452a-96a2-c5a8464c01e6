// 复用API文件中的类型定义
export * from './api';

// 额外的UI相关类型
export interface DateRange {
  start_date: string;
  end_date: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
  }[];
}

export interface StaffTableColumn {
  prop: string;
  label: string;
  width?: number | string;
  minWidth?: number | string;
  fixed?: boolean | string;
  sortable?: boolean;
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
}

// 加载状态
export interface LoadingState {
  dashboard: boolean;
  staffDetail: boolean;
  refresh: boolean;
}

// 排序状态
export interface SortState {
  prop: string;
  order: 'ascending' | 'descending' | null;
}

// 筛选状态
export interface FilterState {
  search: string;
  scoreRange: [number, number];
  efficiencyRange: [number, number];
}

// Dashboard状态
export interface DashboardState {
  data: import('./api').DashboardData | null;
  loading: LoadingState;
  sort: SortState;
  filter: FilterState;
  dateRange: DateRange;
}

// 客服详情Dialog状态
export interface StaffDetailDialogState {
  visible: boolean;
  data: import('./api').StaffDetailData | null;
  loading: boolean;
} 