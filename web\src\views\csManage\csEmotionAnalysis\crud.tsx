import * as api from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet, dict, compute, UserPageQuery, AddReq, DelReq, EditReq } from '@fast-crud/fast-crud';
import { ElTag, ElMessage, ElNotification } from 'element-plus';
import { inject, nextTick, ref, shallowRef } from 'vue';
import { successMessage } from '/@/utils/message';
import { auth } from '/@/utils/authFunction';

export const createCrudOptions = function ({ crudExpose, context }: CreateCrudOptionsProps): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        // 处理日期范围查询 - 使用Django REST framework的标准格式
        if (query.create_datetime && query.create_datetime.length === 2) {
            query.create_datetime__gte = query.create_datetime[0];
            query.create_datetime__lte = query.create_datetime[1];
            delete query.create_datetime;
        }
        return await api.GetList(query);
    };

    // 这些方法在当前业务中可能不需要，但保留定义以保持结构一致性
    const editRequest = async ({ form, row }: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    
    const delRequest = async ({ row }: DelReq) => {
        return await api.DelObj(row.id);
    };
    
    const addRequest = async ({ form }: AddReq) => {
        return await api.AddObj(form);
    };

    const exportRequest = async (query: UserPageQuery) => {
        if (!api.exportData) {
            ElMessage.warning('导出功能未实现');
            return;
        }
        return await api.exportData(query);
    };

    // 创建上下文事件
    context.event = ref({
        e: "",
        data: "",
    });

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: { 
                        show: false  // 隐藏添加按钮
                    },
                    export: {
                        text: '导出', 
                        title: '导出情绪分析数据', 
                        show: true,  // 暂时显示导出按钮，后续可配置权限
                        click() {
                            ElNotification({
                                title: '正在处理',
                                message: '正在导出数据，请勿多次点击...',
                                type: 'success',
                            });
                            return exportRequest(crudExpose!.getSearchFormData());
                        },
                    },
                },
            },
            rowHandle: {
                fixed: 'right',
                width: 200,
                buttons: {
                    view: {
                        show: false,
                    },
                    edit: {
                        show: false,
                    },
                    remove: {
                        show: false, // 隐藏删除按钮
                    },
                    viewDetails: {
                        text: "查看详情",
                        type: 'text',
                        click: async ({ row }) => {
                            // 查看分析详情 - 传递完整的row数据
                            context.event.value = {
                                e: "showDetails", 
                                data: row
                            };
                        }
                    },
                    showSession: {
						text: "查看会话",
						type: 'text',
						click: async (content) => {
							context.event.value = {e:"showSession",data:content.row}
						}
					},
                    manualAnalysis: {
						text: "重新分析",
						type: 'text',
						click: async ({ row }) => {
							ElMessage.info('正在提交重新分析任务...');
							try {
								const res = await api.manualAnalysis({
									session_ids: [row.session_id],
									force_reprocess: true
								});
								if (res.code === 2000) {
									ElMessage.success('重新分析任务已提交');
								} else {
									ElMessage.error(res.msg || '任务提交失败');
								}
							} catch (err) {
								ElMessage.error('任务提交失败: ' + err.message);
							}
						}
					},
                },
            },
            search: {
                show: true,
                layout: 'multi-line',
            },
            columns: {
                id: {
                    title: 'ID',
                    key: 'id',
                    align: 'center',
                    column: {
                        width: 100,
                    }
                },
                session_id: {
                    title: '会话ID',
                    key: 'session_id',
                    type: 'text',
                    search: { 
                        show: true,
                        component: {
                            props: { clearable: true }
                        }
                    },
                    column: {
                        width: 120,
                    }
                },
                create_datetime: {
                    title: '分析时间',
                    key: 'create_datetime',
                    type: 'datetime',
                    form: { show: false },
                    search: {
                        show: true,
                        component: {
                            type: 'datetimerange',
                            props: {
                                'start-placeholder': '开始时间',
                                'end-placeholder': '结束时间',
                                'value-format': 'YYYY-MM-DD HH:mm:ss',
                                'shortcuts': [{
                                    text: '最近一周',
                                    value: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 7), new Date()]
                                }, {
                                    text: '最近一个月',
                                    value: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), new Date()]
                                }, {
                                    text: '最近三个月',
                                    value: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 90), new Date()]
                                }, {
                                    text: '最近半年',
                                    value: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 180), new Date()]
                                }, {
                                    text: '最近一年',
                                    value: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 365), new Date()]
                                }]
                            }
                        }
                    },
                    column: {
                        width: 160,
                        formatter: ({ value }) => {
                            if (!value) return '-';
                            return new Date(value).toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                    }
                },
                service_name: {
                    title: '客服姓名',
                    key: 'service_name',
                    type: 'text',
                    search: { 
                        show: true,
                        component: {
                            props: { clearable: true }
                        }
                    },
                    column: {
                        width: 120,
                    }
                },
                dept_name: {
                    title: '部门',
                    key: 'dept_name', 
                    type: 'text',
                    form: { show: false },
                    search: { 
                        show: true,
                        component: {
                            props: { clearable: true }
                        }
                    },
                    column: {
                        width: 120,
                        formatter: ({ row }) => {
                            return row.dept_name || '-';
                        }
                    }
                },
                // 情绪分数（合并显示）
                emotion_scores: {
                    title: '情绪分数',
                    key: 'emotion_scores',
                    align: 'center',
                    form: { show: false },
                    search: { show: false },
                    column: {
                        width: 200,
                        formatter: ({ row }) => {
                            const initial = row.initial_emotion_score;
                            const final = row.final_emotion_score;
                            const change = row.emotion_change_score;
                            
                            if (initial === null || final === null || change === null) {
                                return <span>-</span>;
                            }
                            
                            const initialScore = parseFloat(initial);
                            const finalScore = parseFloat(final);
                            const changeScore = parseFloat(change);

                            const isFinalHigher = finalScore > initialScore;
                            
                            return (
                                <el-tooltip 
                                    content={`初始情绪: ${initialScore.toFixed(1)} → 最终情绪: ${finalScore.toFixed(1)} (变化: ${changeScore >= 0 ? '+' : ''}${changeScore.toFixed(1)})`}
                                    placement="top"
                                    trigger="hover"
                                >
                                    <div style={{ display: 'flex', gap: '4px', justifyContent: 'center', alignItems: 'center' }}>
                                        <ElTag 
                                            type='info'
                                            size="small"
                                        >
                                            {initialScore.toFixed(1)}
                                        </ElTag>
                                        <span style={{ fontSize: '12px', color: '#666' }}>→</span>
                                        <ElTag 
                                            type={isFinalHigher ? 'success' : 'danger'}
                                            size="small"
                                        >
                                            {finalScore.toFixed(1)}
                                        </ElTag>
                                        <span style={{ fontSize: '12px', color: '#666' }}>(</span>
                                        <ElTag 
                                            type={changeScore >= 0 ? 'success' : 'danger'}
                                            size="small"
                                        >
                                            {changeScore >= 0 ? '+' : ''}{changeScore.toFixed(1)}
                                        </ElTag>
                                        <span style={{ fontSize: '12px', color: '#666' }}>)</span>
                                    </div>
                                </el-tooltip>
                            );
                        }
                    }
                },
                emotion_change_level: {
                    title: '变化等级',
                    key: 'emotion_change_level',
                    type: 'text',
                    align: 'center',
                    form: { show: false },
                    search: {
                        show: true,
                        type: 'dict-select',
                        dict: dict({
                            data: [
                                { value: '大幅改善', label: '大幅改善' },
                                { value: '改善', label: '改善' },
                                { value: '稳定', label: '稳定' },
                                { value: '恶化', label: '恶化' },
                                { value: '大幅恶化', label: '大幅恶化' },
                            ]
                        }),
                        component: {
                            props: { clearable: true }
                        }
                    },
                    column: {
                        width: 100,
                        formatter: ({ value }) => {
                            const typeMap = {
                                '大幅改善': 'success',
                                '改善': 'success',
                                '稳定': 'info',
                                '恶化': 'warning',
                                '大幅恶化': 'danger',
                                '未知': 'info'
                            };
                            return <ElTag type={typeMap[value] || 'info'}>{value || '未知'}</ElTag>;
                        }
                    }
                },
                // 会话统计（合并显示）
                message_stats: {
                    title: '消息统计',
                    key: 'message_stats',
                    align: 'center',
                    form: { show: false },
                    search: { show: false },
                    column: {
                        width: 180,
                        formatter: ({ row }) => {
                            const userCount = row.user_message_count || 0;
                            const serviceCount = row.service_message_count || 0;
                            const totalCount = row.message_count || 0;
                            
                            return (
                                <el-tooltip 
                                    content={`用户消息: ${userCount} / 客服消息: ${serviceCount} / 总消息数: ${totalCount}`}
                                    placement="top"
                                    trigger="hover"
                                >
                                    <div style={{ display: 'flex', gap: '4px', justifyContent: 'center', alignItems: 'center' }}>
                                        <ElTag size="small" type="primary">{userCount}</ElTag>
                                        <span style={{ fontSize: '12px', color: '#666' }}>/</span>
                                        <ElTag size="small" type="success">{serviceCount}</ElTag>
                                        <span style={{ fontSize: '12px', color: '#666' }}>/</span>
                                        <ElTag size="small" type="info">{totalCount}</ElTag>
                                    </div>
                                </el-tooltip>
                            );
                        }
                    }
                },
                session_duration_minutes: {
                    title: '时长(分)',
                    key: 'session_duration_minutes',
                    type: 'number',
                    width: 90,
                    align: 'center',
                    form: { show: false },
                    column: {
                        formatter: ({ value }) => {
                            return value ? `${value}分` : '-';
                        }
                    }
                },
                // 摘要和评估
                conversation_summary: {
                    title: '会话摘要',
                    key: 'conversation_summary',
                    type: 'text',
                    form: { show: false },
                    search: { 
                        show: true,
                        component: {
                            props: { clearable: true }
                        }
                    },
                    column: {
                        width: 250,
                    }
                },
                overall_assessment: {
                    title: '整体评估',
                    key: 'overall_assessment',
                    type: 'text',
                    width: 200,
                    form: { show: false },
                    column: { show: false }, // 默认隐藏，可在列设置中开启
                    search: { show: false }
                },
            },
        },
    };
}; 