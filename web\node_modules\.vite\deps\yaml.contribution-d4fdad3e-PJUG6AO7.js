import {
  h
} from "./chunk-GBNBLBMS.js";
import "./chunk-R5IZP54R.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/yaml.contribution-d4fdad3e.mjs
h({
  id: "yaml",
  extensions: [".yaml", ".yml"],
  aliases: ["YAML", "yaml", "YML", "yml"],
  mimetypes: ["application/x-yaml", "text/x-yaml"],
  loader: () => import("./yaml-ae1a6075-7S7MJA2Q.js")
});
/*! Bundled license information:

@fast-crud/fast-extends/dist/yaml.contribution-d4fdad3e.mjs:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=yaml.contribution-d4fdad3e-PJUG6AO7.js.map
