{"version": 3, "sources": ["../../node_modules/.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/javascript/javascript.contribution.ts\nimport { registerLanguage } from \"../_.contribution.js\";\nregisterLanguage({\n  id: \"javascript\",\n  extensions: [\".js\", \".es6\", \".jsx\", \".mjs\", \".cjs\"],\n  firstLine: \"^#!.*\\\\bnode\",\n  filenames: [\"jakefile\"],\n  aliases: [\"JavaScript\", \"javascript\", \"js\"],\n  mimetypes: [\"text/javascript\"],\n  loader: () => {\n    if (false) {\n      return new Promise((resolve, reject) => {\n        __require([\"vs/basic-languages/javascript/javascript\"], resolve, reject);\n      });\n    } else {\n      return import(\"./javascript.js\");\n    }\n  }\n});\n"], "mappings": ";;;;;;;AAUAA,EAAiB;EACf,IAAI;EACJ,YAAY,CAAC,OAAO,QAAQ,QAAQ,QAAQ,MAAM;EAClD,WAAW;EACX,WAAW,CAAC,UAAU;EACtB,SAAS,CAAC,cAAc,cAAc,IAAI;EAC1C,WAAW,CAAC,iBAAiB;EAC7B,QAAQ,MAMG,OAAO,mCAAiB;AAGrC,CAAC;", "names": ["registerLanguage"]}