"""
简洁缓存管理器

提供基于精确参数匹配的缓存策略，避免数据混淆
"""

import logging
from typing import Dict, Any, Callable
from django.core.cache import cache

logger = logging.getLogger(__name__)


class CacheManager:
    """
    简洁缓存管理器
    
    设计原则：
    1. 精确参数匹配 - 每个唯一参数组合对应独立缓存
    2. 简单可靠 - 避免复杂的范围查找和数据合并
    3. 透明化 - 缓存逻辑对调用者透明
    """
    
    def __init__(self):
        self.CACHE_PREFIX = "staff_analytics"
        self.DEFAULT_TIMEOUT = 3600 * 12  # 12小时
    
    def _generate_cache_key(self, data_type: str, **params) -> str:
        """
        生成精确的缓存键
        
        Args:
            data_type: 数据类型标识
            **params: 查询参数
            
        Returns:
            str: 缓存键
        """
        # 将参数按键排序，确保相同参数不同顺序生成相同的键
        sorted_params = sorted(params.items())
        params_str = "_".join(f"{k}_{v}" for k, v in sorted_params)
        
        cache_key = f"{self.CACHE_PREFIX}:{data_type}:{params_str}"
        
        return cache_key
    
    def get_or_fetch(
        self, 
        data_type: str, 
        fetch_func: Callable[[], Any], 
        timeout: int = None,
        **params
    ) -> Any:
        """
        获取缓存数据或执行获取函数
        
        Args:
            data_type: 数据类型标识
            fetch_func: 数据获取函数
            timeout: 缓存超时时间（秒）
            **params: 查询参数
            
        Returns:
            Any: 数据
        """
        cache_key = self._generate_cache_key(data_type, **params)
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return cached_data
        
        # 缓存未命中，执行获取函数
        try:
            fresh_data = fetch_func()
            
            # 缓存新数据
            timeout = timeout or self.DEFAULT_TIMEOUT
            cache.set(cache_key, fresh_data, timeout)
            
            return fresh_data
            
        except Exception as e:
            logger.error(f"Failed to fetch data for cache key {cache_key}: {str(e)}")
            # 返回空数据而不是抛出异常，保证系统稳定性
            return {}
    
    def clear_cache_by_pattern(self, data_type: str = None) -> int:
        """
        清除匹配模式的缓存
        
        Args:
            data_type: 数据类型，如果为None则清除所有相关缓存
            
        Returns:
            int: 清除的缓存数量
        """
        # 注意：这里需要根据实际的缓存后端实现
        # Redis后端可以使用SCAN命令，内存缓存需要其他方式
        
        pattern = f"{self.CACHE_PREFIX}:{data_type}:*" if data_type else f"{self.CACHE_PREFIX}:*"
        
        # 简化实现：仅记录日志，实际清除需要根据缓存后端实现
        
        # TODO: 根据实际缓存后端实现具体的清除逻辑
        return 0
    
    def get_cache_info(self, data_type: str, **params) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Args:
            data_type: 数据类型
            **params: 查询参数
            
        Returns:
            Dict: 缓存信息
        """
        cache_key = self._generate_cache_key(data_type, **params)
        
        cached_data = cache.get(cache_key)
        
        return {
            'cache_key': cache_key,
            'cached': cached_data is not None,
            'data_type': data_type,
            'params': params
        }


# 全局缓存管理器实例
cache_manager = CacheManager() 